{"page": {"transactions": {"title": "取引", "empty": {"title": "取引なし", "desc": "<1>対応しているチェーン</1>で取引が見つかりません"}, "explain": {"approve": "{{project}}用に{{amount}} {{symbol}}を承認", "unknown": "契約の対話", "cancel": "保留中の取引をキャンセル"}, "txHistory": {"tipInputData": "取引にはメッセージが含まれています", "parseInputDataError": "メッセージの解析に失敗", "scamToolTip": "この取引は詐欺師によって発信され、詐欺トークン及びNFTを送信するものです。これに関与しないでください。"}, "modalViewMessage": {"title": "メッセージを表示"}, "filterScam": {"title": "スキャム取引を非表示にする", "btn": "スキャム取引を非表示", "loading": "読み込みには時間がかかる場合があり、データの遅延が発生する可能性があります。"}}, "chainList": {"title": "{{count}} chains Integrated", "mainnet": "メインネット", "testnet": "テストネット"}, "signTx": {"nftIn": "NFT入力", "gasLimitNotEnough": "ガスリミットが21000未満です。取引を提出できません", "gasLimitLessThanExpect": "ガスリミットが低い。取引が失敗する可能性が1%です", "gasLimitLessThanGasUsed": "ガスリミットが非常に低い。取引が失敗する可能性が95%です", "nativeTokenNotEngouthForGas": "ウォレットに十分なガスがありません", "nonceLowerThanExpect": "Nonceが低すぎます、最低は{{0}}です", "canOnlyUseImportedAddress": "署名にはインポートされたアドレスのみ使用できます", "multiSigChainNotMatch": "マルチシグアドレスはこのチェーンになく、取引を開始できません", "safeAddressNotSupportChain": "現在のセーフアドレスは{{0}}チェーンで対応していません", "noGasRequired": "ガスは必要ありません", "gasSelectorTitle": "ガス", "failToFetchGasCost": "ガスコストの取得に失敗", "gasMoreButton": "詳細", "manuallySetGasLimitAlert": "ガスリミットを手動で設定しました", "gasNotRequireForSafeTransaction": "セーフトランザクションにはガス料が必要ありません", "gasPriceTitle": "ガス価格（Gwei）", "maxPriorityFee": "最大優先手数料（Gwei）", "eip1559Desc1": "EIP-1559をサポートするチェーンでは、優先手数料は取引を処理するためのマイナーへのチップです。優先手数料を下げることで、最終的なガスコストを節約できますが、取引が処理されるまでに時間がかかる場合があります。", "eip1559Desc2": "Rabbyでは、優先手数料（チップ）= 最大料金 - 基本料金です。最大優先手数料を設定すると、基本料金がそれから差し引かれ、残りがマイナーにチップとして渡されます。", "hardwareSupport1559Alert": "ハードウェアウォレットのファームウェアがEIP 1559をサポートするバージョンにアップグレードされていることを確認してください", "gasLimitTitle": "ガスリミット", "recommendGasLimitTip": "推定{{est}}。現在{{current}}倍、お勧めは", "nonceTitle": "<PERSON><PERSON>", "gasLimitModifyOnlyNecessaryAlert": "必要な場合にのみ修正してください", "gasPriceMedian": "過去100回のオンチェーン取引の中央値：", "myNativeTokenBalance": "私のガス残高：", "gasLimitEmptyAlert": "ガスリミットを入力してください", "gasLimitMinValueAlert": "ガスリミットは21000以上である必要があります", "balanceChange": {"successTitle": "取引シミュレーションの結果", "failedTitle": "取引シミュレーションに失敗", "noBalanceChange": "残高変更なし", "tokenOut": "トークン出力", "tokenIn": "トークン入力", "errorTitle": "残高変更の取得に失敗", "notSupport": "取引シミュレーションはサポートされていません", "nftOut": "NFT出力"}, "enoughSafeSigCollected": "十分な署名が集まりました", "moreSafeSigNeeded": "{{0}} 件の確認がさらに必要", "safeAdminSigned": "署名済み", "swap": {"title": "トークンを交換", "payToken": "支払う", "receiveToken": "受け取る", "failLoadReceiveToken": "読み込みに失敗", "valueDiff": "価値の差", "simulationFailed": "トランザクションのシミュレーションに失敗", "simulationNotSupport": "このチェーンではトランザクションのシミュレーションはサポートされていません", "minReceive": "最小受け取り額", "slippageFailToLoad": "スリッページ許容値の読み込みに失敗", "slippageTolerance": "スリッページ許容値", "receiver": "受取人", "notPaymentAddress": "支払いアドレスではありません", "unknownAddress": "不明なアドレス"}, "crossChain": {"title": "クロスチェーン"}, "swapAndCross": {"title": "トークンを交換してクロスチェーン"}, "wrapToken": "トークンをラップ", "unwrap": "トークンのラップを解除", "send": {"title": "トークンを送信", "sendToken": "トークンを送る", "sendTo": "送る先", "receiverIsTokenAddress": "トークンアドレス", "contractNotOnThisChain": "このチェーンには契約アドレスがありません", "notTopupAddress": "トップアップアドレスではありません", "tokenNotSupport": "{{0}} はサポートされていません", "onMyWhitelist": "私のホワイトリスト内", "notOnThisChain": "このチェーンにはありません", "cexAddress": "CEXアドレス", "addressBalanceTitle": "アドレスの残高", "whitelistTitle": "ホワイトリスト", "notOnWhitelist": "私のホワイトリストにはありません", "fromMySeedPhrase": "私のシードフレーズから", "scamAddress": "詐欺アドレス", "fromMyPrivateKey": "私のプライベートキーから"}, "tokenApprove": {"title": "トークンの承認", "approveToken": "トークンを承認", "myBalance": "私の残高", "approveTo": "承認先", "eoaAddress": "EOAアドレス", "trustValueLessThan": "信頼値 ≤ {{value}}", "deployTimeLessThan": "展開時間 < {{value}} 日", "amountPopupTitle": "金額", "flagByRabby": "Rabbyによってフラグが付けられた", "contractTrustValueTip": "信頼値は、この契約に対して承認され、露出しているトータルトークンを指します。低い信頼値は、180日の非活動またはリスクを示しています。", "amount": "承認金額：", "exceed": "現在の残高を超えています"}, "revokeTokenApprove": {"title": "トークン承認を取り消し", "revokeFrom": "取り消し元", "revokeToken": "トークンを取り消す"}, "sendNFT": {"title": "NFTを送信", "nftNotSupport": "NFTはサポートされていません"}, "nftApprove": {"title": "NFTの承認", "approveNFT": "NFTを承認", "nftContractTrustValueTip": "信頼値は、この契約に対して承認され、露出しているトップNFTを指します。低い信頼値は、180日の非活動またはリスクを示しています。"}, "revokeNFTApprove": {"title": "NFT承認を取り消し", "revokeNFT": "NFTを取り消す"}, "nftCollectionApprove": {"title": "NFTコレクションの承認", "approveCollection": "コレクションを承認"}, "revokeNFTCollectionApprove": {"title": "NFTコレクション承認を取り消し", "revokeCollection": "コレクションを取り消す"}, "deployContract": {"title": "契約を展開", "descriptionTitle": "説明", "description": "スマートコントラクトを展開しています"}, "cancelTx": {"title": "保留中のトランザクションをキャンセル", "txToBeCanceled": "キャンセルされるトランザクション", "gasPriceAlert": "保留中のトランザクションをキャンセルするためには、現在のガス価格を{{value}} Gwei以上に設定してください"}, "submitMultisig": {"title": "マルチシグトランザクションを送信", "multisigAddress": "マルチシグアドレス"}, "contractCall": {"title": "契約を呼び出す", "operation": "操作", "operationABIDesc": "操作はABIからデコードされます", "operationCantDecode": "操作はデコードされません", "payNativeToken": "{{symbol}} を支払う", "receiver": "受取人アドレス", "suspectedReceiver": "例外アドレス"}, "revokePermit2": {"title": "Permit2トークン承認を取り消す"}, "unknownAction": "不明", "interactContract": "コントラクトに関与", "markAsTrust": "信頼されたとしてマーク", "markAsBlock": "ブロックされたとしてマーク", "interacted": "以前に関与", "neverInteracted": "関与したことがない", "transacted": "以前に取引", "neverTransacted": "取引したことがない", "fakeTokenAlert": "これはRabbyによってスキャムトークンとしてマークされたものです", "scamTokenAlert": "これはRabbyの検出に基づき、低品質およびスキャムトークンである可能性があります", "trusted": "信頼済み", "blocked": "ブロック済み", "noMark": "マーク<PERSON>し", "markRemoved": "マークを削除", "speedUpTooltip": "この加速された取引と元の取引のうち、最終的には一つだけが完了します", "signTransactionOnChain": "{{chain}} トランザクションに署名", "viewRaw": "生データを見る", "unknownActionType": "不明なアクションタイプ", "sigCantDecode": "この署名はRabbyによってデコードできませんが、それはリスクを意味するわけではありません", "nftCollection": "NFTコレクション", "floorPrice": "底値", "contractAddress": "コントラクトアドレス", "protocolTitle": "プロトコル", "deployTimeTitle": "デプロイされた時間", "popularity": "人気", "contractPopularity": "{{1}}でNo.{{0}}", "addressNote": "アドレスメモ", "myMarkWithContract": "{{chainName}} コントラクトに対する私のマーク", "myMark": "私のマーク", "collectionTitle": "コレクション", "addressTypeTitle": "アドレスの種類", "firstOnChain": "初めてのオンチェーン", "trustValue": "信頼値", "importedDelegatedAddress": "インポートされた委任アドレス", "noDelegatedAddress": "インポートされた委任アドレスがありません", "coboSafeNotPermission": "この委任アドレスは、このトランザクションを開始する権限がありません", "gasAccount": {"currentTxCost": "送金されたガス量：", "totalCost": "総コスト:", "estimatedGas": "推定Gas：", "sendGas": "現在のトランザクションに対するGasの送金:", "gasCost": "ガスをあなたのアドレスに転送する際のガスコスト：", "maxGas": "Max Gas: "}, "customRPCErrorModal": {"title": "カスタム RPC エラー", "button": "カスタムRPCを無効にする", "content": "ご使用のカスタムRPCは現在利用できません。これを無効にしてRabbyの公式RPCを使用して署名を続けることができます。"}, "transferOwner": {"description": "説明", "transferTo": "転送先", "title": "資産の所有権を移転"}, "swapLimitPay": {"title": "トークンスワップ制限支払い", "maxPay": "最大支払い"}, "batchRevokePermit2": {"title": "一括 Revoke Permit2 Approval"}, "revokePermit": {"title": "許可トークン承認を取り消す"}, "assetOrder": {"receiveAsset": "資産を受け取る", "title": "資産オーダー", "listAsset": "資産をリストする"}, "BroadcastMode": {"instant": {"desc": "トランザクションは直ちにネットワークにブロードキャストされます。", "title": "即時"}, "lowGas": {"title": "ガス節約", "desc": "ネットワークのガスが低い時にトランザクションがブロードキャストされます。"}, "mev": {"desc": "トランザクションは指定されたMEVノードにブロードキャストされます", "title": "MEV Guarded"}, "tips": {"notSupported": "サポートされていません", "walletConnect": "WalletConnectではサポートされていません", "notSupportChain": "このチェーンではサポートされていません", "customRPC": "カスタムRPCを使用するときはサポートされていません"}, "lowGasDeadline": {"1h": "1時間", "4h": "4h", "24h": "24h", "label": "タイムアウト"}, "title": "ブロードキャストモード"}, "SafeNonceSelector": {"explain": {"contractCall": "コントラクトコール", "send": "トークンを送信", "unknown": "不明なTransaction"}, "optionGroup": {"replaceTitle": "キュー内のトランザクションを置き換える", "recommendTitle": "推奨nonce"}, "option": {"new": "新しいトランザクション"}, "error": {"pendingList": "保留中のトランザクションを読み込めません、<1/><2>再試行</2>"}}, "coboSafeCreate": {"safeWalletTitle": "セーフ{Wallet}", "descriptionTitle": "説明", "title": "Cobo Safe を作成"}, "coboSafeModificationRole": {"title": "セーフロール変更を送信", "descriptionTitle": "説明", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationDelegatedAddress": {"descriptionTitle": "説明", "safeWalletTitle": "セーフ{Wallet}", "title": "委任されたアドレス変更を提出"}, "coboSafeModificationTokenApproval": {"descriptionTitle": "説明", "title": "トークン承認の修正を送信", "safeWalletTitle": "Safe{Wallet}"}, "common": {"interactContract": "コントラクトとインタラクトする", "descTipSafe": "署名は資産の変更やアドレスの所有権の確認を引き起こしません", "description": "説明", "descTipWarningAssets": "署名により資産が変更される可能性があります", "descTipWarningPrivacy": "署名によりアドレス所有権を検証できます。", "descTipWarningBoth": "署名は資産の変更を引き起こす可能性があり、アドレスの所有権を確認します。"}, "gasAccountForGas": "GasAccount の USD を使用してガス代を支払う", "nativeTokenForGas": "{{chainName}}でのガス料金の支払いに{{tokenName}}トークンを使用", "chain": "チェーン", "decodedTooltip": "この署名は <PERSON><PERSON> Wallet によってデコードされます。", "importedAddress": "インポートされたアドレス", "l2GasEstimateTooltip": "L2チェーンのガス見積もりにはL1ガス料金は含まれていません。実際の料金は現在の見積もりよりも高くなります。", "address": "アドレス", "yes": "はい", "no": "いいえ", "hasInteraction": "以前にインタラクションあり", "advancedSettings": "詳細設定", "contract": "スマートコントラクト", "protocol": "プロトコル", "amount": "量", "maxPriorityFeeDisabledAlert": "最初にGas Priceを設定してください", "label": "ラベル", "trustValueTitle": "信頼の価値", "addressSource": "アドレスソース", "typedDataMessage": "型付きデータに署名", "primaryType": "プライマリタイプ", "safeServiceNotAvailable": "現在、Safeサービスは利用できません。後でもう一度お試しください。", "safeTx": {"selfHostConfirm": {"button": "OK", "title": "RabbyのSafe Serviceに切り替える", "content": "Safe API は利用できません。Rabby によってデプロイされた Safe サービスに切り替えて、Safe を機能させ続けてください。<strong>すべての Safe 署名者は Rabby Wallet を使用してトランザクションを承認する必要があります。<strong>"}}}, "signFooterBar": {"requestFrom": "リクエスト元", "processRiskAlert": "署名する前に警告を処理してください", "ignoreAll": "すべて無視", "gridPlusConnected": "GridPlusが接続されています", "gridPlusNotConnected": "GridPlusは接続されていません", "connectButton": "接続", "connecting": "接続中...", "ledgerNotConnected": "Ledgerは接続されていません", "ledgerConnected": "Ledgerが接続されています", "signAndSubmitButton": "署名して送信", "walletConnect": {"connectedButCantSign": "接続されていますが、署名できません", "switchToCorrectAddress": "モバイルウォレットで正しいアドレスに切り替えてください", "switchChainAlert": "モバイルウォレットで{{chain}}に切り替えてください", "notConnectToMobile": "{{brand}}に接続されていません", "connected": "接続されており、署名する準備ができています", "howToSwitch": "切り替え方", "wrongAddressAlert": "モバイルウォレットで異なるアドレスに切り替えました。モバイルウォレットで正しいアドレスに切り替えてください", "connectBeforeSign": "{{0}}はRabbyに接続されていません、署名する前に接続してください", "chainSwitched": "モバイルウォレットで異なるチェーンに切り替えました。{{0}}にモバイルウォレットで切り替えてください", "latency": "レイテンシ", "requestSuccessToast": "リクエストは正常に送信されました", "sendingRequest": "署名リクエストを送信中", "signOnYourMobileWallet": "モバイルウォレットで署名してください", "requestFailedToSend": "署名リクエストの送信に失敗しました"}, "beginSigning": "署名プロセスを開始", "addressTip": {"onekey": "OneKeyアドレス", "trezor": "Trezorアドレス", "bitbox": "BitBox02アドレス", "keystone": "Keystoneアドレス", "airgap": "AirGapアドレス", "coolwallet": "CoolWalletアドレス", "privateKey": "プライベートキーアドレス", "seedPhrase": "シードフレーズアドレス", "watchAddress": "ウォッチオンリーアドレスで署名できません", "safe": "セーフアドレス", "coboSafe": "Cobo Argusアドレス", "seedPhraseWithPassphrase": "Seed Phrase アドレス（Passphrase）"}, "qrcode": {"signWith": "{{brand}}で署名", "failedToGetExplain": "説明の取得に失敗しました", "txFailed": "送信に失敗しました", "sigReceived": "署名が受け取られました", "sigCompleted": "トランザクションが送信されました", "getSig": "署名を取得", "qrcodeDesc": "署名するためにあなたの{{brand}}でスキャンしてください<br></br>署名した後、下のボタンをクリックして署名を受け取ってください", "misMatchSignId": "不一致のトランザクションデータです。トランザクションの詳細を確認してください", "unknownQRCode": "エラー：そのQRコードは識別できませんでした", "afterSignDesc": "署名後、{{brand}}のQRコードをPCカメラの前に置いてください"}, "ledger": {"resent": "再送", "signError": "Ledger署名エラー：", "notConnected": "あなたのウォレットは接続されていません。再接続してください", "siging": "署名リクエストを送信中", "txRejected": "トランザクションが拒否されました", "unlockAlert": "Ledgerを接続してロックを解除し、Ethereumを開いてください", "updateFirmwareAlert": "LedgerのファームウェアとEthereumアプリを更新してください", "txRejectedByLedger": "Ledger上でトランザクションが拒否されました", "blindSigTutorial": "Ledgerからのブラインド署名チュートリアル", "submitting": "署名完了。トランザクションを送信中", "resubmited": "再送信されました"}, "common": {"notSupport": "{{0}}はサポートされていません"}, "resend": "再試行", "submitTx": "トランザクションを送信", "testnet": "テストネット", "mainnet": "メインネット", "gasless": {"GetFreeGasToSign": "無料Gasを取得", "watchUnavailableTip": "Watch-only addressはFree Gasをサポートしていません", "unavailable": "ガス残高が不足しています。", "walletConnectUnavailableTip": "モバイルウォレットがWalletConnectを通じて接続されている場合、Free Gasはサポートされていません。", "notEnough": "Gas残高が足りません", "rabbyPayGas": "Rabbyが必要なガス料金を支払います– 署名するだけ", "customRpcUnavailableTip": "カスタムRPCはFree Gasをサポートしていません。"}, "gasAccount": {"gotIt": "了解しました", "chainNotSupported": "このチェーンはGasAccountでサポートされていません。", "login": "ログイン", "customRPC": "カスタムRPCを使用する場合はサポートされていません", "useGasAccount": "GasAccountを使用する", "loginFirst": "GasAccount にまずログインしてください。", "notEnough": "GasAccount が不足しています", "WalletConnectTips": "GasAccountではWalletConnectはサポートされていません。", "deposit": "入金", "loginTips": "GasAccountログインを完了するために、このトランザクションは破棄されます。ログイン後に再作成する必要があります。", "depositTips": "GasAccountの入金を完了するためには、この取引は破棄されます。入金後に再度作成する必要があります。"}, "keystone": {"txRejected": "トランザクションが拒否されました", "mismatchedWalletError": "ウォレットが一致しません。", "signWith": "{{method}}で署名に切り替え", "verifyPasswordError": "署名失敗、ロック解除後に再試行してください。", "siging": "署名リクエストを送信中", "shouldRetry": "エラーが発生しました。もう一度お試しください。", "unsupportedType": "エラー: トランザクションタイプがサポートされていないか、未知です。", "shouldOpenKeystoneHomePageError": "Keystone 3 Pro をホームページに表示させてください。", "misMatchSignId": "トランザクションデータが一致しません。トランザクションの詳細を確認してください。", "hardwareRejectError": "Keystoneリクエストがキャンセルされました。続行するには、再承認してください。", "qrcodeDesc": "スキャンして署名します。署名後、以下をクリックして署名を取得してください。USBの場合、再接続して認証し、署名プロセスを再開します。"}, "keystoneNotConnected": "Keystoneは接続されていません", "keystoneConnected": "Keystone が接続されました", "cancelConnection": "接続をキャンセル", "cancelAll": "すべての {{count}} 件の要求をDappからキャンセルします", "imKeyNotConnected": "imKeyは接続されていません", "imKeyConnected": "imKey が接続されています", "blockDappFromSendingRequests": "Dappが1分間リクエストを送信するのをブロック。", "cancelCurrentConnection": "現在の接続をキャンセル", "cancelTransaction": "トランザクションをキャンセル", "cancelCurrentTransaction": "現在のトランザクションをキャンセルする", "detectedMultipleRequestsFromThisDapp": "このDappからの複数のリクエストを検出しました。"}, "signTypedData": {"signTypeDataOnChain": "{{chain}}の型付きデータに署名", "safeCantSignText": "これはセーフアドレスであり、テキストに署名することはできません。", "permit": {"title": "許可トークン承認"}, "permit2": {"title": "許可2トークン承認", "sigExpireTimeTip": "この署名がオンチェーンで有効である期間", "sigExpireTime": "署名の有効期限", "approvalExpiretime": "承認の有効期限"}, "swapTokenOrder": {"title": "トークンオーダー"}, "sellNFT": {"title": "NFTオーダー", "receiveToken": "トークンを受け取る", "listNFT": "NFTをリストする", "specificBuyer": "特定の購入者"}, "signMultiSig": {"title": "トランザクションを確認"}, "createKey": {"title": "キーを作成"}, "verifyAddress": {"title": "アドレスを検証"}, "buyNFT": {"payToken": "トークンを支払う", "receiveNFT": "NFTを受け取る", "expireTime": "有効期限", "listOn": "リストオン"}, "contractCall": {"operationDecoded": "操作はメッセージからデコードされました"}, "safeCantSignTypedData": "これはSafeアドレスであり、EIP-712タイプのデータまたは文字列の署名のみをサポートしています。"}, "activities": {"title": "署名レコード", "signedTx": {"label": "トランザクション", "empty": {"title": "まだ署名されたトランザクションはありません", "desc": "Ra<PERSON>経由で署名されたすべてのトランザクションがここにリストされます。"}, "common": {"unlimited": "無制限", "unknownProtocol": "未知のプロトコル", "unknown": "未知", "speedUp": "加速", "cancel": "キャンセル", "pendingDetail": "保留中の詳細"}, "tips": {"pendingDetail": "最も高いガス価格を持つものがほとんどの場合、完了する唯一のトランザクションです", "canNotCancel": "スピードアップまたはキャンセルできません: 最初の保留中のtxnではありません", "pendingBroadcastRetryBtn": "再放送", "pendingBroadcastBtn": "今すぐBroadcast", "pendingBroadcastRetry": "ブロードキャストに失敗しました。最終試行: {{pushAt}}", "pendingBroadcast": "ガス節約モード: ネットワーク手数料が低くなるのを待っています。最大 {{deadline}} 時間待ちます。"}, "status": {"canceled": "キャンセル済み", "failed": "失敗", "submitFailed": "送信に失敗", "pending": "保留中", "pendingBroadcast": "保留中: to be broadcasted", "pendingBroadcasted": "保留中: broadcasted", "withdrawed": "クイックキャンセル", "pendingBroadcastFailed": "保留中: ブロードキャストに失敗しました"}, "txType": {"initial": "初期tx", "cancel": "キャンセルtx", "speedUp": "加速tx"}, "explain": {"unknown": "未知のトランザクション", "send": "{{amount}} {{symbol}}を送る", "cancel": "{{protocol}}の{{token}}承認をキャンセル", "approve": "{{protocol}}の{{count}} {{token}}を承認", "cancelNFTCollectionApproval": "{{protocol}}のNFTコレクション承認をキャンセル", "cancelSingleNFTApproval": "{{protocol}}の単一のNFT承認をキャンセル", "singleNFTApproval": "{{protocol}}の単一のNFT承認", "nftCollectionApproval": "{{protocol}}のNFTコレクション承認"}, "CancelTxPopup": {"options": {"removeLocalPendingTx": {"title": "未処理のローカルをクリア", "desc": "インターフェースから保留中のトランザクションを削除する"}, "quickCancel": {"tips": "未送信のトランザクションのみ対応", "title": "クイックキャンセル", "desc": "ブロードキャスト前にキャンセルすると、ガス料金がかかりません。"}, "onChainCancel": {"title": "オンチェーンキャンセル", "desc": "新しいキャンセルするためのトランザクション、ガスが必要です"}}, "removeLocalPendingTx": {"title": "トランザクションをローカルで削除", "desc": "このアクションにより、保留中のトランザクションがローカルで削除されます。\n保留中のトランザクションは今後も正常に送信される可能性があります。"}, "title": "トランザクションをキャンセル"}, "MempoolList": {"reBroadcastBtn": "再ブロードキャスト", "empty": "どのノードにも見つかりません", "title": "{{count}} RPC ノードに表示されました"}, "message": {"cancelSuccess": "キャンセルされました", "broadcastSuccess": "放送済み", "reBroadcastSuccess": "再送信されました", "deleteSuccess": "削除に成功しました"}, "gas": {"noCost": "ガス手数料なし"}, "SkipNonceAlert": {"clearPendingAlert": "{{chainName}} トランザクション ({{nonces}}) は3分以上保留されています。取引を再送信するには、<5></5> <6>ローカルで保留をクリア</6> <7></7> できます。", "alert": "Nonce #{{nonce}} は {{chainName}} チェーンでスキップされました。これにより、先に保留中のトランザクションが発生する可能性があります。<5></5> <6>チェーン上でトランザクションを送信</6> <7></7>して解決してください。"}, "PredictTime": {"failed": "パッキング時間の予測に失敗しました。", "time": "予想されるパック完了時刻: {{time}}", "noTime": "パッキング時間が予測されています"}, "CancelTxConfirmPopup": {"desc": "これにより、保留中のトランザクションがインターフェースから削除されます。その後、新しいトランザクションを開始できます。", "title": "ローカルで保留をクリア", "warning": "削除されたトランザクションは、置き換えが行われない限り、依然としてオンチェーンで確認される可能性があります。"}}, "signedText": {"label": "テキスト", "empty": {"title": "まだ署名されたテキストはありません", "desc": "Ra<PERSON>経由で署名されたすべてのテキストがここにリストされます。"}}}, "receive": {"title": "{{chain}}上で{{token}}を受け取る", "watchModeAlert1": "これはウォッチモードのアドレスです。", "watchModeAlert2": "資産を受け取るためにこれを使用してもよろしいですか？"}, "sendToken": {"addressNotInContract": "アドレスリストにありません。<1></1><2>連絡先に追加</2>", "AddToContactsModal": {"addedAsContacts": "連絡先として追加", "editAddr": {"placeholder": "アドレスメモを入力", "validator__empty": "アドレスメモを入力してください"}, "editAddressNote": "アドレスメモを編集", "error": "連絡先に追加に失敗"}, "allowTransferModal": {"error": "パスワードが正しくありません", "placeholder": "確認のためにパスワードを入力", "validator__empty": "パスワードを入力してください", "addWhitelist": "ホワイトリストに追加"}, "GasSelector": {"confirm": "確認", "level": {"$unknown": "未知", "custom": "カスタム", "fast": "即時", "normal": "速い", "slow": "標準"}, "popupDesc": "設定したガス価格に基づいて転送額からガス費用が予約されます", "popupTitle": "ガス価格（Gwei）を設定"}, "header": {"title": "送る"}, "modalConfirmAddToContacts": {"confirmText": "確認", "title": "連絡先に追加"}, "modalConfirmAllowTransferTo": {"cancelText": "キャンセル", "confirmText": "確認", "title": "確認のためにパスワードを入力"}, "sectionBalance": {"title": "残高"}, "sectionChain": {"title": "チェーン"}, "sectionFrom": {"title": "送り主"}, "sectionTo": {"addrValidator__empty": "アドレスを入力してください", "addrValidator__invalid": "このアドレスは無効です", "searchInputPlaceholder": "アドレスを入力または検索", "title": "送り先"}, "sendButton": "送信", "tokenInfoFieldLabel": {"chain": "チェーン", "contract": "コントラクトアドレス"}, "tokenInfoPrice": "価格", "whitelistAlert__disabled": "ホワイトリストが無効です。どのアドレスにも送金できます。", "whitelistAlert__notWhitelisted": "このアドレスはホワイトリストに含まれていません。<1 /> 一時的な送金許可を付与することに同意します。", "whitelistAlert__temporaryGranted": "一時的な許可が付与されました", "whitelistAlert__whitelisted": "このアドレスはホワイトリストに含まれています", "balanceWarn": {"gasFeeReservation": "ガス料金予約が必要です"}, "balanceError": {"insufficientBalance": "残高不足"}, "max": "最大", "sectionMsgDataForEOA": {"placeholder": "オプション", "title": "メッセージ", "currentIsOriginal": "現在の入力は元のデータです。UTF-8は次のとおりです：", "currentIsUTF8": "現在の入力はUTF-8です。元のデータは次のとおりです："}, "sectionMsgDataForContract": {"placeholder": "オプション", "title": "コントラクト呼び出し", "parseError": "コントラクト呼び出しのデコードに失敗しました", "simulation": "コントラクト呼び出しシミュレーション：", "notHexData": "サポートされているのは16進数データのみです"}, "blockedTransaction": "Blocked Transaction", "blockedTransactionCancelText": "私は知っている", "blockedTransactionContent": "このトランザクションは、OFAC制裁リストに載っているアドレスとやり取りします。"}, "sendTokenComponents": {"GasReserved": "ガスコストのために <1>0</1> {{ tokenName }} を予約しました", "SwitchReserveGas": "予備 Gas <1 />"}, "sendNFT": {"header": {"title": "送信"}, "sectionChain": {"title": "チェーン"}, "sectionFrom": {"title": "送信元"}, "sectionTo": {"title": "送信先", "addrValidator__empty": "アドレスを入力してください", "addrValidator__invalid": "このアドレスは無効です", "searchInputPlaceholder": "アドレスを入力または検索"}, "nftInfoFieldLabel": {"Collection": "コレクション", "Contract": "コントラクト", "sendAmount": "送信量"}, "sendButton": "送信", "whitelistAlert__disabled": "ホワイトリストが無効です。どのアドレスにも送金できます。", "whitelistAlert__whitelisted": "このアドレスはホワイトリストに含まれています", "whitelistAlert__temporaryGranted": "一時的な許可が付与されました", "whitelistAlert__notWhitelisted": "このアドレスはホワイトリストに含まれていません。<1 /> 一時的な送金許可を付与することに同意します。", "tipNotOnAddressList": "アドレスリストにはありません。", "tipAddToContacts": "連絡先に追加", "confirmModal": {"title": "確認するためのパスワードを入力"}}, "approvals": {"header": {"title": "{{ address }} の承認"}, "tab-switch": {"contract": "契約別", "assets": "資産別"}, "component": {"table": {"bodyEmpty": {"loadingText": "読み込み中...", "noMatchText": "該当なし", "noDataText": "承認なし"}}, "ApprovalContractItem": {"ApprovalCount_one": "承認", "ApprovalCount_other": "承認"}, "RevokeButton": {"btnText_zero": "取り消し", "btnText_one": "取り消し ({{count}})", "btnText_other": "取り消し ({{count}})", "permit2Batch": {"modalContent": "同じPermit2コントラクトからの承認は、同じ署名の下にまとめられます。", "modalTitle_other": "合計で <2>{{count}}</2> つの署名が必要です", "modalTitle_one": "<2>{{count}}</2> 件の署名が必要です"}}, "ViewMore": {"text": "詳細を表示"}}, "search": {"placeholder": "{{ type }} を名前/アドレスで検索"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "契約", "contractTrustValue": "契約信頼値", "revokeTrends": "24時間の取り消しトレンド", "myApprovedAssets": "承認された私の資産", "myApprovalTime": "私の承認時刻"}, "columnTip": {"contractTrustValue": "信頼値は、この契約に承認された総資産価値を指します。低い信頼値は、リスクまたは180日間の非活動を示します。", "contractTrustValueWarning": "契約信頼値 < $100,000", "contractTrustValueDanger": "契約信頼値 < $10,000"}}, "byAssets": {"columnTitle": {"asset": "資産", "type": "タイプ", "approvedAmount": "承認数量", "approvedSpender": "承認者", "myApprovalTime": "私の承認時刻"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "承認数量", "tipMyBalance": "私の残高"}}}}, "RevokeApprovalModal": {"subTitleTokenAndNFT": "承認されたトークンおよびNFT", "subTitleContract": "以下の契約への承認", "selectAll": "すべて選択", "confirm": "{{ selectedCount }} を確認", "title": "承認", "unSelectAll": "すべての選択を解除", "tooltipPermit2": "この承認はPermit2コントラクトを通じて承認されています：\n{{ permit2Id }}"}, "revokeModal": {"approvalCount_one": "{{count}} approval", "totalRevoked": "合計：", "batchRevoke": "一括Revoke", "resume": "続行", "cancelTitle": "残りのRevokeをキャンセルする", "confirmTitle": "ワンクリックで一括無効化", "signAndStartRevoke": "署名して取り消しを開始", "done": "完了", "pause": "一時停止", "confirmRevokePrivateKey": "シードフレーズまたはプライベートキーアドレスを使用して、1クリックで{{count}}件の承認を一括で取り消すことができます。", "confirm": "確認", "revokeOneByOne": "一つずつRevoke", "revoked": "無効化済み:", "cancelBody": "このページを閉じると、残りのrevokeは実行されません。", "approvalCount_zero": "{{count}} 件の承認", "confirmRevokeLedger": "Ledgerアドレスを使用すると、1クリックで{{count}}件の承認を一括で取り消すことができます。", "approvalCount_other": "{{count}} approvals", "connectLedger": "Ledger に接続", "submitTxFailed": "提出に失敗しました", "ledgerAlert": "LedgerデバイスでEthereum Appを開いてください。", "gasTooHigh": "Gas fee is high", "stillRevoke": "まだRevoke", "paused": "一時停止しました", "ledgerSending": "署名要求を送信中 ({{current}}/{{total}})", "defaultFailed": "トランザクションが失敗しました", "ledgerSended": "Ledgerでリクエストに署名してください ({{current}}/{{total}})", "revokeWithLedger": "Ledger でRevokeを開始", "waitInQueue": "キューで待機", "ledgerSigned": "署名済み。トランザクションを作成中 ({{current}}/{{total}})", "useGasAccount": "ガス残高が少なくなっています。GasAccountがガス料金をカバーします。", "simulationFailed": "シミュレーション失敗", "gasNotEnough": "Gasが不足しているため、送信できません"}}, "gasTopUp": {"title": "インスタントガストップアップ", "description": "別のチェーンで利用可能なトークンを送信してガスをチャージします。支払いが確認されると即座に転送され、確定を待つ必要はありません。", "topUpChain": "トップアップチェーン", "Amount": "数量", "Continue": "続行", "InsufficientBalance": "現在のチェーンのRabbyの契約アドレスには十分な残高がありません。後で再試行してください。", "hightGasFees": "このトップアップ金額は小さすぎます。対象ネットワークは高いガス手数料を必要とします。", "No_Tokens": "トークンがありません", "InsufficientBalanceTips": "残高不足", "payment": "ガストップアップ支払い", "Loading_Tokens": "トークンを読み込み中...", "Including-service-fee": "{{fee}} サービス料金を含む", "service-fee-tip": "ガストップアップのサービスを提供するため、Rabbyはトークンの変動とトップアップのガス手数料を負担しなければなりません。そのため、サービス料金として20％が請求されます。", "Confirm": "確認", "Select-from-supported-tokens": "サポートされているトークンから選択", "Value": "値", "Payment-Token": "支払いトークン", "Select-payment-token": "支払いトークンを選択", "Token": "トークン", "Balance": "残高"}, "swap": {"title": "スワップ", "pendingTip": "トランザクションが送信されました。トランザクションが長時間保留されている場合、設定で保留をクリアしようと試すことができます。", "Pending": "保留中", "completedTip": "チェーン上のトランザクション、データのデコードを行ってレコードを生成中", "Completed": "完了", "slippage_tolerance": "スリッページ許容値：", "actual-slippage": "実際のスリッページ：", "gas-x-price": "ガス価格：{{price}} Gwei。", "no-transaction-records": "トランザクション履歴はありません", "swap-history": "スワップ履歴", "InSufficientTip": "トランザクションのシミュレーションとガスの見積もりを行うには、残高が不足しています。元のアグリゲータの引用が表示されます", "testnet-is-not-supported": "カスタムネットワークはサポートされていません。", "not-supported": "サポートされていません", "slippage-adjusted-refresh-quote": "スリッページが調整されました。引用を更新してください。", "price-expired-refresh-quote": "価格の有効期限が切れました。引用を更新してください。", "approve-x-symbol": "{{symbol}} を承認", "swap-via-x": "{{name}} 経由でスワップ", "get-quotes": "引用を取得", "chain": "チェーン", "swap-from": "スワップ元", "to": "宛先", "search-by-name-address": "名前/アドレスで検索", "amount-in": "{{symbol}} での金額", "unlimited-allowance": "無制限の許可", "insufficient-balance": "残高が不足しています", "rabby-fee": "<PERSON><PERSON> 手数料", "minimum-received": "最低受取額", "there-is-no-fee-and-slippage-for-this-trade": "この取引には手数料とスリッページはありません", "approve-tips": "1. 承認 → 2. スワップ", "best": "ベスト", "unable-to-fetch-the-price": "価格を取得できません", "fail-to-simulate-transaction": "トランザクションのシミュレーションに失敗しました", "security-verification-failed": "セキュリティの検証に失敗しました", "need-to-approve-token-before-swap": "スワップ前にトークンを承認する必要があります", "this-exchange-is-not-enabled-to-trade-by-you": "この取引所はあなたによる取引が有効にされていません。", "enable-it": "有効にする", "this-token-pair-is-not-supported": "トークンペアはサポートされていません", "QuoteLessWarning": "受け取り額は Rabby トランザクションのシミュレーションからの見積もりです。DEXが提供するオファーは {{receive}} です。期待されるオファーより {{diff}} 少なく受け取ります。", "by-transaction-simulation-the-quote-is-valid": "トランザクションのシミュレーションによると、見積もりは有効です", "wrap-contract": "ラップ契約", "directlySwap": "{{symbol}} トークンをスマートコントラクトで直接ラップする", "rates-from-cex": "CEX からのレート", "edit": "編集", "tradingSettingTips": "{{viewCount}} つの取引所が引用を提供し、{{tradeCount}} つが取引を有効にしています", "the-following-swap-rates-are-found": "以下のスワップレートが見つかりました", "est-payment": "推定支払い:", "est-receiving": "推定受け取り:", "est-difference": "推定差額:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "選択したオファーは現在のレートと大きく異なるため、大きな損失の原因になる可能性があります", "rate": "レート", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "低いスリッページは高いボラティリティのため、トランザクションの失敗を引き起こす可能性があります", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "高いスリッページ許容度のため、トランザクションがフロントランされる可能性があります", "recommend-slippage": "フロントランを防ぐため、スリッページを<2>{{ slippage }}</2>% 以下に設定することをお勧めします", "slippage-tolerance": "スリッページ許容度", "select-token": "トークンを選択", "enable-exchanges": "取引所を有効にする", "exchanges": "取引所", "view-quotes": "引用を表示", "trade": "取引", "dex": "DEX", "cex": "CEX", "enable-trading": "取引を有効にする", "i-understand-and-accept-it": "理解し、受け入れます", "confirm": "確認", "tradingSettingTip1": "1. 一度有効にすると、取引所の契約と直接対話します", "tradingSettingTip2": "2. Rabby は取引所の契約から発生するリスクについて責任を負いません", "gas-fee": "ガス料金: {{gasUsed}}", "estimate": "見積もり:", "actual": "実際:", "rabbyFee": {"button": "了解しました", "rate": "手数料率", "title": "Rabby fee", "wallet": "ウォレット", "bridgeDesc": "Rabby Walletは常にトップアグリゲーターから可能な限り最良のレートを見つけ、それらのオファーの信頼性を検証します。Rabbyは0.25%の手数料を請求し、この手数料は見積もりに自動的に含まれます。", "swapDesc": "Rabby Walletは常にトップアグリゲーターから可能な限り最良のレートを見つけ、その提案の信頼性を確認します。Rabbyは0.25%の手数料（ラッピングには0%）を請求し、これは見積もりに自動的に含まれます。"}, "lowCreditModal": {"desc": "低い信用価値は、ハニーポットトークンや非常に低い流動性のような高いリスクを示すことが多いです。", "title": "このトークンはクレジット価値が低いです。"}, "max": "MAX", "sort-with-gas": "ガスでソート", "approve-swap": "承認してスワップ", "hidden-no-quote-rates_other": "{{count}} rates unavailable", "from": "から", "Gas-fee-too-high": "Gas fee が高すぎます。", "approve-and-swap": "{{name}} で承認してスワップする", "no-slippage-for-wrap": "ラップの場合、スリッページはありません", "no-fee-for-wrap": "Wrap に Rabby 手数料はかかりません。", "preferMEV": "MEV保護を優先", "hidden-no-quote-rates_one": "{{count}} rate unavailable", "two-step-approve": "2 つのトランザクションに署名してアローワンスを変更してください。", "process-with-two-step-approve": "2段階承認を進める", "usd-after-fees": "≈ {{usd}}", "no-fees-for-wrap": "Wrap に Rabby 手数料はかかりません。", "Auto": "自動", "no-quote-found": "見積もりが見つかりませんでした", "fetch-best-quote": "最適な見積もりを取得中", "source": "ソース", "price-impact": "価格への影響", "two-step-approve-details": "トークン USDT の許可変更には 2 つのトランザクションが必要です。まず、許可をゼロにリセットし、その後に新しい許可の値を設定する必要があります。", "No-available-quote": "利用できるレートはありません", "loss-tips": "{{usd}}を失っています。小さな市場で少額を試してください。", "preferMEVTip": "イーサリアムのスワップに対して「MEV Guarded」機能を有効にして、サンドイッチ攻撃のリスクを軽減します。注: カスタムRPCまたはウォレット接続アドレスを使用する場合、この機能はサポートされていません。"}, "manageAddress": {"no-address": "アドレスがありません", "no-match": "一致するものがありません", "current-address": "現在のアドレス", "address-management": "アドレス管理", "update-balance-data": "残高データを更新", "search": "検索", "manage-address": "アドレスを管理", "deleted": "削除済み", "whitelisted-address": "ホワイトリストされたアドレス", "addressTypeTip": "{{type}} によってインポート", "delete-desc": "削除する前に、資産を保護する方法を理解するために以下のポイントを考慮してください。", "delete-checklist-1": "このアドレスを削除すると、このアドレスの対応するプライベートキーとシードフレーズが削除され、Rabby はそれを回復できなくなります。", "delete-checklist-2": "プライベートキーまたはシードフレーズをバックアップし、削除の準備ができていることを確認します。", "confirm": "確認", "cancel": "キャンセル", "delete-private-key-modal-title_one": "{{count}} つのプライベートキーアドレスを削除", "delete-private-key-modal-title_other": "{{count}} つのプライベートキーアドレスを削除", "delete-seed-phrase-title_one": "シードフレーズとその{{count}}つのアドレスを削除", "delete-seed-phrase-title_other": "シードフレーズとその{{count}}つのアドレスを削除", "delete-title_one": "{{count}} つの{{brand}} アドレスを削除", "delete-title_other": "{{count}} つの{{brand}} アドレスを削除", "delete-empty-seed-phrase": "シードフレーズとそのアドレスは 0 つ削除", "hd-path": "HD パス:", "no-address-under-seed-phrase": "このシードフレーズの下にはまだアドレスをインポートしていません。", "add-address": "アドレスを追加", "delete-seed-phrase": "シードフレーズを削除", "confirm-delete": "削除を確認", "private-key": "プライベートキー", "seed-phrase": "シードフレーズ", "watch-address": "ウォッチアドレス", "backup-seed-phrase": "シードフレーズのバックアップ", "delete-all-addresses-but-keep-the-seed-phrase": "すべてのアドレスを削除してシードフレーズを保持", "delete-all-addresses-and-the-seed-phrase": "すべてのアドレスとシードフレーズを削除", "seed-phrase-delete-title": "シードフレーズを削除しますか？", "sort-by-balance": "残高で並べ替え", "sort-by-address-type": "アドレスタイプで並べ替え", "sort-address": "アドレスの並べ替え", "addNewAddress": "新しいアドレスを追加", "passphraseError": "パスフレーズが無効です", "sort-by-address-note": "アドレスノートで並べ替え", "enterPassphraseTitle": "パスフレーズを入力して署名", "enterThePassphrase": "パスフレーズを入力してください", "CurrentDappAddress": {"desc": "Dappアドレスを切り替える"}}, "dashboard": {"home": {"offline": "ネットワークが切断され、データが取得できません", "panel": {"swap": "スワップ", "send": "送信", "receive": "受信", "gasTopUp": "ガストップアップ", "queue": "キュー", "transactions": "トランザクション", "approvals": "承認", "feedback": "フィードバック", "more": "詳細", "manageAddress": "アドレス管理", "nft": "NFT", "ecology": "エコシステム", "rabbyPoints": "<PERSON><PERSON>", "bridge": "Bridge", "mobile": "Mobile Sync"}, "comingSoon": "近日公開", "soon": "まもなく", "refreshTheWebPageToTakeEffect": "効果を発揮するにはウェブページを更新してください", "rabbyIsInUseAndMetamaskIsBanned": "Rabbyが使用中でMetaMaskは禁止されています", "flip": "反転", "metamaskIsInUseAndRabbyIsBanned": "MetaMaskが使用中でRabbyは禁止されています", "transactionNeedsToSign": "トランザクションをサインする必要があります", "transactionsNeedToSign": "トランザクションをサインする必要があります", "view": "表示", "viewFirstOne": "最初の1つを表示", "rejectAll": "すべてを拒否", "pendingCount": "保留中1件", "pendingCountPlural": "{{countStr}} 件保留中", "queue": {"title": "キュー", "count": "{{count}} 件 in"}, "whatsNew": "新着情報", "importType": "{{type}} によってインポート", "chainEnd": "チェーン", "chain": "チェーン，", "missingDataTooltip": "現在の{{text}}のネットワーク問題により、残高が更新されない場合があります。"}, "recentConnection": {"disconnected": "切断済み", "rpcUnavailable": "カスタムRPCは利用できません", "metamaskTooltip": "このDappではMetaMaskを使用することを希望しています。設定 > MetaMask優先Dappsでいつでも設定を更新できます", "connected": "接続済み", "notConnected": "未接続", "connectedDapp": "Rabbyは現在のDappに接続されていません。接続するには、Dappのウェブページで接続ボタンを見つけてクリックしてください。", "noDappFound": "Dappが見つかりません", "disconnectAll": "すべて切断", "disconnectRecentlyUsed": {"title": "最近使用した<strong>{{count}}</strong>つのDAppを切断", "description": "固定されたDAppsは接続されたままです", "title_one": "<strong>{{count}}</strong> 個の接続されたDappを切断する", "title_other": "<strong>{{count}}</strong> 個の接続された Dapp を切断する"}, "title": "接続されたDapp", "pinned": "固定済み", "noPinnedDapps": "固定されたDappはありません", "dragToSort": "並べ替えるにはドラッグ", "recentlyConnected": "最近接続した", "noRecentlyConnectedDapps": "最近接続したDappはありません", "dapps": "<PERSON><PERSON>", "noConnectedDapps": "接続されているDappsはありません", "metamaskModeTooltip": "このDappでRabbyに接続できませんか？<1>MetaMaskモード</1>を有効にしてみてください。", "metamaskModeTooltipNew": "Rabby Walletは、Dappで「MetaMask」を選択すると接続されます。これを管理するには、[その他] > [MetaMaskとして偽装してRabbyを接続]に進んでください。"}, "feedback": {"directMessage": {"content": "ダイレクトメッセージ", "description": "DeBankのRabby Wallet公式とチャット"}, "proposal": {"content": "提案", "description": "<PERSON><PERSON> Walletに関する提案をDeBankに提出"}, "title": "フィードバック"}, "nft": {"empty": "サポートされているコレクションにNFTが見つかりません", "collectionList": {"collections": {"label": "コレクション"}, "all_nfts": {"label": "すべてのNFT"}}, "listEmpty": "まだNFTを取得していません", "modal": {"collection": "コレクション", "chain": "チェーン", "lastPrice": "最終価格", "purchaseDate": "購入日", "sendTooltip": "現在はERC 721およびERC 1155 NFTのみサポートされています", "send": "送信"}}, "rabbyBadge": {"imageLabel": "<PERSON><PERSON>", "title": "<PERSON><PERSON> バッジをクレームする", "enterClaimCode": "クレームコードを入力", "swapTip": "Rabby Wallet内で注目のDEXでスワップを完了する必要があります。", "goToSwap": "スワップに進む", "claim": "クレーム", "viewYourClaimCode": "クレームコードを表示", "noCode": "このアドレスでクレームコードを有効にしていません", "learnMoreOnDebank": "DeBankで詳細を確認", "rabbyValuedUserNo": "<PERSON><PERSON> 評価ユーザー番号{{num}}", "claimSuccess": "クレーム成功", "viewOnDebank": "DeBankで表示", "rabbyFreeGasUserNo": "<PERSON>bby Free Gas User No.{{num}}", "freeGasTitle": "無料ガスバッジを請求", "freeGasNoCode": "以下のボタンをクリックしてDeBankにアクセスし、現在のアドレスを使用してクレームコードを取得してください。", "learnMore": "詳細情報", "freeGasTip": "Free Gasを使用してトランザクションに署名してください。ガスが足りない場合は、'Free Gas' ボタンが自動的に表示されます。"}, "contacts": {"noDataLabel": "データなし", "noData": "データがありません", "oldContactList": "古い連絡先リスト", "oldContactListDescription": "連絡先とウォッチモードのアドレスの統合のため、古い連絡先はここにバックアップされ、しばらくしたらリストは削除されます。続けて使用する場合はタイムリーに追加してください。"}, "security": {"tokenApproval": "トークン承認", "nftApproval": "NFT承認", "comingSoon": "近日公開の機能", "title": "セキュリティ"}, "settings": {"lock": {"never": "無効"}, "7Days": "7日", "1Day": "1日", "4Hours": "4時間", "1Hour": "1時間", "10Minutes": "10分", "backendServiceUrl": "バックエンドサービスのURL", "inputOpenapiHost": "オープンAPIホストを入力してください", "pleaseCheckYourHost": "ホストを確認してください", "host": "ホスト", "reset": "初期設定に戻す", "save": "保存", "pendingTransactionCleared": "保留中のトランザクションがクリアされました", "clearPending": "ローカルで保留中をクリア", "clearPendingTip1": "この操作により、インターフェースから保留中のトランザクションが削除され、ネットワーク上で長時間保留されていることによって引き起こされる問題の解決に役立ちます。", "clearPendingTip2": "これは、アカウント残高に影響を与えたり、シードフレーズを再入力する必要はありません。すべての資産とアカウントの詳細は安全に保たれます。", "autoLockTime": "自動ロック時間", "claimRabbyBadge": "<PERSON><PERSON> バッジをクレーム！", "cancel": "キャンセル", "enableWhitelist": "ホワイトリストを有効にする", "disableWhitelist": "ホワイトリストを無効にする", "enableWhitelistTip": "有効にすると、Rabbyを使用してホワイトリスト内のアドレスにのみ資産を送信できます。", "disableWhitelistTip": "無効にすると、どのアドレスにでも資産を送信できます", "warning": "警告", "clearWatchAddressContent": "すべてのウォッチモードアドレスを削除してもよろしいですか？", "updateVersion": {"content": "新しいRabby Walletのアップデートが利用可能です。手動でアップデート方法を確認するにはクリックしてください。", "okText": "チュートリアルを見る", "successTip": "最新バージョンを使用中です", "title": "アップデート利用可能"}, "features": {"label": "機能", "lockWallet": "ウォレットをロック", "signatureRecord": "署名レコード", "manageAddress": "アドレスを管理", "connectedDapp": "接続されたDapp", "gasTopUp": "ガストップアップ", "searchDapps": "Dappsを検索", "rabbyPoints": "<PERSON><PERSON>"}, "settings": {"label": "設定", "enableWhitelistForSendingAssets": "資産の送信にホワイトリストを有効にする", "customRpc": "RPC URLを修正", "metamaskPreferredDapps": "MetaMask 優先Dapps", "currentLanguage": "現在の言語", "enableTestnets": "テストネットを有効にする", "themeMode": "テーマモード", "toggleThemeMode": "テーマモード", "customTestnet": "カスタムネットワークを追加", "metamaskMode": "MetaMaskとして偽装してRabbyを接続", "enableDappAccount": "Dapp アドレスを個別に切り替える\n"}, "aboutUs": "私たちについて", "currentVersion": "現在のバージョン", "updateAvailable": "アップデートが利用可能", "supportedChains": "統合されたチェーン", "followUs": "フォローする", "testnetBackendServiceUrl": "テストネットバックエンドサービスURL", "clearWatchMode": "ウォッチモードをクリア", "requestDeBankTestnetGasToken": "DeBankテストネットガストークンをリクエスト", "clearPendingWarningTip": "削除されたトランザクションは、置き換えられない限り、まだオンチェーンで確認される可能性があります。", "DappAccount": {"title": "Dappアドレスを個別に切り替える\n", "button": "有効化\n", "desc": "有効化後、各Dappに接続するアドレスを選択できます。メインアドレスの変更は、各Dappに接続されているアドレスに影響しません。\n"}}, "tokenDetail": {"blockedTip": "ブロックされたトークンはトークンリストに表示されません", "blocked": "ブロック済み", "selectedCustom": "このトークンはRabbyにリストされていません。カスタムでトークンリストに追加しました。", "notSelectedCustom": "このトークンはRabbyにリストされていません。オンにするとトークンリストに追加されます。", "customized": "カスタマイズ済み", "scamTx": "詐欺トランザクション", "txFailed": "失敗", "notSupported": "このチェーン上のトークンはサポートされていません", "swap": "スワップ", "send": "送金", "receive": "受取", "noTransactions": "トランザクションなし", "customizedButton": "カスタマイズ", "blockedButton": "ブロック", "customizedButtons": "カスタムトークン", "blockedButtons": "ブロックされたトークン", "SupportedExchanges": "サポートされている取引所", "customizedListTitle": "カスタムトークン", "Chain": "チェーン", "customizedListTitles": "カスタムトークン", "AddToMyTokenList": "トークンリストに追加", "noIssuer": "発行者情報は利用できません", "IssuerWebsite": "発行者のウェブサイト", "NoSupportedExchanges": "サポートされている取引所はありません", "NoListedBy": "リスティング情報は利用できません。", "OriginalToken": "オリジナルトークン", "customizedHasAddedTips": "トークンはRabbyによってリストされていません。手動でトークンリストに追加しました。", "BridgeProvider": "ブリッジプロバイダー", "blockedListTitles": "ブロックされたトークン", "BridgeIssue": "第三者によってブリッジされたトークン", "OriginIssue": "このブロックチェーン上でネイティブに発行されています", "maybeScamTips": "これは低品質のトークンであり、詐欺である可能性があります。", "myBalance": "私の残高", "blockedListTitle": "ブロックされたトークン", "verifyScamTips": "これは詐欺トークンです", "ContractAddress": "コントラクトアドレス", "blockedTips": "ブロックされたトークンはトークンリストに表示されません。", "TokenName": "トークン名", "ListedBy": "リストされたのは", "fdvTips": "市場の時価総額は、最大供給量が流通している場合です。完全希薄評価（FDV） = 価格 x 最大供給量。最大供給量が null の場合、FDV = 価格 x 総供給量。最大供給量および総供給量のいずれも定義されていないか無限の場合、FDV は表示されません。"}, "assets": {"usdValue": "USD 価値", "amount": "数量", "portfolio": {"nftTips": "このプロトコルが認識するフロア価格に基づいて計算されます。", "fractionTips": "リンクされたERC20トークンの価格に基づいて計算されます。"}, "tokenButton": {"subTitle": "このリストのトークンは総残高に追加されません"}, "table": {"assetAmount": "アセット / 量", "price": "価格", "useValue": "USD 価値", "healthRate": "健康率", "debtRatio": "負債比率", "unlockAt": "アンロックまで", "lentAgainst": "LENT AGAINST", "type": "タイプ", "strikePrice": "ストライク価格", "exerciseEnd": "行使終了", "tradePair": "トレードペア", "side": "サイド", "leverage": "レバレッジ", "PL": "P&L", "unsupportedPoolType": "サポートされていないプールタイプ", "claimable": "請求可能", "endAt": "終了日", "dailyUnlock": "デイリーアンロック", "pool": "プール", "token": "トークン", "balanceValue": "残高 / 価値", "percent": "パーセント", "summaryTips": "アセット価値を総資産価値で割ったもの", "summaryDescription": "すべてのプロトコル内のアセット（LPトークンなど）は統計計算のために基礎アセットに解決されます", "noMatch": "一致なし", "lowValueDescription": "低価値アセットがここに表示されます", "lowValueAssets": "{{count}}の低価値アセット", "lowValueAssets_0": "{{count}} 個の low value token", "lowValueAssets_one": "{{count}} low value token", "lowValueAssets_other": "{{count}} 個の低価値トークン"}, "noAssets": "アセットなし", "blockLinkText": "トークンをブロックするためのアドレスを検索", "blockDescription": "ユーザーによってブロックされたトークンがここに表示されます", "unfoldChain": "1つのチェーンを展開", "unfoldChainPlural": "{{moreLen}}のチェーンを展開", "customLinkText": "カスタムトークンを追加するためのアドレスを検索", "customDescription": "ユーザーによって追加されたカスタムトークンがここに表示されます", "comingSoon": "近日公開...", "searchPlaceholder": "トークン", "AddMainnetToken": {"searching": "トークンを検索中", "title": "カスタムトークンを追加", "selectChain": "チェーンを選択", "tokenAddress": "トークンアドレス", "tokenAddressPlaceholder": "トークンアドレス", "isBuiltInToken": "トークンは既にサポートされています。", "notFound": "トークンが見つかりません"}, "AddTestnetToken": {"tokenAddress": "トークンアドレス", "tokenAddressPlaceholder": "トークンアドレス", "selectChain": "チェーンを選択", "searching": "トークンを検索中", "title": "カスタムネットワークトークンを追加", "notFound": "トークンが見つかりません"}, "TestnetAssetListContainer": {"addTestnet": "ネットワーク", "add": "トークン"}, "addTokenEntryText": "トークン", "noTestnetAssets": "カスタムネットワーク資産なし", "customButtonText": "カスタムトークンを追加"}, "hd": {"howToConnectLedger": "Ledgerとの接続方法", "userRejectedTheRequest": "ユーザーがリクエストを拒否しました。", "ledger": {"doc1": "シングルLedgerを接続します", "doc2": "PINを入力してロックを解除します", "doc3": "Ethereumアプリを開きます", "reconnect": "うまくいかない場合は、<1>最初から再接続を試してみてください。</1>", "connected": "<PERSON>ger connected"}, "howToSwitch": "切り替え方法", "keystone": {"reconnect": "うまくいかない場合は、<1>最初から再接続してみてください。</1>", "doc3": "コンピュータへの接続を承認する", "title": "Keystone 3 Proがホームページにあることを確認してください", "doc2": "パスワードを入力して、ロックを解除してください。", "doc1": "Keystoneを1つ接続する"}, "imkey": {"doc1": "imKey を一つ接続する", "doc2": "ピンを入力してロックを解除"}, "ledgerIsDisconnected": "Ledger が接続されていません。", "howToConnectKeystone": "Keystone を接続する方法", "howToConnectImKey": "imKeyを接続する方法"}, "GnosisWrongChainAlertBar": {"warning": "Safe address does not support {{chain}}", "notDeployed": "あなたのSafeアドレスはこのチェーンではデプロイされていません。"}, "echologyPopup": {"title": "エコシステム"}, "MetamaskModePopup": {"title": "MetaMaskモード", "footerText": "その他 > MetaMaskモードでMetaMaskモードにさらにDappを追加", "desc": "RabbyをDappで接続できない場合は、MetaMaskモードを有効にし、MetaMaskオプションを選択して接続してください。", "enableDesc": "MetaMaskでのみ動作するDappの場合は有効にする", "toastSuccess": "有効になりました。ページを更新して再接続してください。"}, "offlineChain": {"tips": "{{chain}} Chainは{{date}}に統合されません。あなたの資産には影響はありませんが、総残高には含まれません。それらにアクセスするには、「More」でカスタムネットワークとして追加することができます。", "chain": "{{chain}} はまもなく統合されなくなります。"}, "recentConnectionGuide": {"button": "了解しました", "title": "こちらでDapp接続のためのアドレスを切り替えます"}}, "nft": {"floorPrice": "/ フロア価格:", "title": "NFT", "all": "全て", "starred": "スター付き ({{count}})", "empty": {"title": "スター付きNFTなし", "description": "「全て」からNFTを選択し、「スター付き」に追加できます"}, "noNft": "NFTがありません"}, "newAddress": {"title": "アドレスを追加", "importSeedPhrase": "シードフレーズをインポート", "importPrivateKey": "プライベートキーをインポート", "importMyMetamaskAccount": "私のMetaMaskアカウントをインポート", "addContacts": {"content": "連絡先を追加", "description": "ウォッチオンリーアドレスとしても使用できます", "required": "アドレスを入力してください", "notAValidAddress": "有効なアドレスではありません", "scanViaMobileWallet": "モバイルウォレットを使用してスキャン", "scanViaPcCamera": "PCカメラを使用してスキャン", "scanQRCode": "WalletConnect対応のウォレットでQRコードをスキャン", "walletConnect": "ウォレット接続", "walletConnectVPN": "VPNを使用するとWalletConnectが不安定になる場合があります。", "cameraTitle": "カメラでQRコードをスキャンしてください", "addressEns": "アドレス / ENS"}, "unableToImport": {"title": "インポートできません", "description": "QRコードベースの複数のハードウェアウォレットをインポートすることはサポートされていません。別のデバイスをインポートする前に、{{0}}からすべてのアドレスを削除してください。"}, "connectHardwareWallets": "ハードウェアウォレットを接続", "connectMobileWalletApps": "モバイルウォレットアプリを接続", "connectInstitutionalWallets": "機関ウォレットを接続", "createNewSeedPhrase": "新しいシードフレーズを作成", "importKeystore": "Keystoreをインポート", "selectImportMethod": "インポート方法を選択", "theSeedPhraseIsInvalidPleaseCheck": "シードフレーズが無効です。確認してください！", "seedPhrase": {"importTips": "1番目のフィールドにシークレットリカバリフレーズ全体を貼り付けることができます", "whatIsASeedPhrase": {"question": "シードフレーズとは何ですか？", "answer": "資産を制御するために使用される12、18、または24の単語フレーズです。"}, "isItSafeToImportItInRabby": {"question": "Rabbyでインポートするのは安全ですか?", "answer": "はい、それはブラウザー上でローカルに保存され、あなた以外はアクセスできません。"}, "importError": "[CreateMnemonics] 予期しないステップ {{0}}", "importQuestion4": "シードフレーズのバックアップを取らずにRabbyをアンインストールすると、Rabbyはそれを取り戻すことができません。", "riskTips": "始める前に、以下のセキュリティのヒントを読んで心に留めておいてください。", "showSeedPhrase": "シードフレーズを表示", "backup": "シードフレーズのバックアップ", "backupTips": "シードフレーズをバックアップするときは、他の誰も画面を見ていないことを確認してください", "copy": "シードフレーズをコピー", "saved": "フレーズを保存しました", "pleaseSelectWords": "単語を選択してください", "verificationFailed": "検証に失敗しました", "createdSuccessfully": "作成が成功しました", "verifySeedPhrase": "シードフレーズを検証", "fillInTheBackupSeedPhraseInOrder": "バックアップシードフレーズを順番に入力してください", "wordPhrase": "私は<1>{{count}}</1>単語フレーズを持っています", "clearAll": "すべてクリア", "slip39SeedPhrasePlaceholder_few": "{{count}} 番目のシードフレーズの共有をここに入力してください", "slip39SeedPhrase": "<0>{{SLIP39}}</0> シードフレーズを持っています。", "invalidContent": "無効なコンテンツ", "slip39SeedPhrasePlaceholder_other": "ここにあなたの{{count}}番目のシードフレーズシェアを入力してください", "passphrase": "パスフレーズ", "slip39SeedPhraseWithPassphrase": "私はパスフレーズ付きの<0>{{SLIP39}}</0>シードフレーズを持っています。", "pastedAndClear": "貼り付けとクリップボードをクリアしました", "slip39SeedPhrasePlaceholder_two": "ここに{{count}}番目のシードフレーズシェアを入力してください。", "inputInvalidCount_other": "{{count}} 件の入力がシードフレーズの規範に適合していません。確認してください。", "inputInvalidCount_one": "1入力がシードフレーズの基準に合致していません。ご確認ください。", "wordPhraseAndPassphrase": "私はPassphrase付きの<1>{{count}}</1>単語のフレーズを持っています。", "slip39SeedPhrasePlaceholder_one": "ここに{{count}}番目のシードフレーズシェアを入力してください。", "importQuestion2": "私のシードフレーズは私のデバイスのみに保存されています。Rabbyはそれにアクセスできません。", "importQuestion1": "もし私がシードフレーズを失ったり共有したりすると、資産へのアクセスを永久に失うことになります。", "importQuestion3": "Rabbyをアンインストールしてシードフレーズをバックアップしなかった場合、Rabbyでは回復できません。"}, "metamask": {"step1": "MetaMaskからシードフレーズまたはプライベートキーをエクスポートします <br /> <1>チュートリアルを表示するにはここをクリック<1/></1>", "step2": "Rabbyでシードフレーズまたはプライベートキーをインポートします", "step3": "インポートが完了し、すべてのアセットが自動的に表示されます <br />", "how": "MetaMaskアカウントをインポートする方法", "step": "ステップ", "importSeedPhrase": "シードフレーズまたはプライベートキーをインポート", "importSeedPhraseTips": "これはブラウザー上でローカルにのみ保存されます。Rabbyはあなたのプライベート情報にアクセスしません。", "tips": "ヒント:", "tipsDesc": "シードフレーズ/プライベートキーはMetaMaskまたは特定のウォレットに属しているわけではありません。それはあなたにしか属していません。"}, "privateKey": {"required": "プライベートキーを入力してください", "placeholder": "プライベートキーを入力してください", "whatIsAPrivateKey": {"question": "プライベートキーとは何ですか？", "answer": "資産を制御するために使用される文字列の組み合わせです。"}, "isItSafeToImportItInRabby": {"question": "Rabbyでそれをインポートするのは安全ですか？", "answer": "はい、それはブラウザー上でローカルに保存され、あなた以外はアクセスできません。"}, "isItPossibleToImportKeystore": {"question": "KeyStoreをインポートすることは可能ですか？", "answer": "はい、ここでKeyStoreをインポートできます。"}, "notAValidPrivateKey": "有効なプライベートキーではありません", "repeatImportTips": {"question": "このアドレスに切り替えますか？", "desc": "このアドレスはインポート済みです。"}}, "importedSuccessfully": "インポートが成功しました", "ledger": {"title": "Ledgerを接続", "cameraPermissionTitle": "Rabbyがカメラにアクセスする許可", "cameraPermission1": "ブラウザーポップアップでRabbyがカメラにアクセスできるように許可します", "allowRabbyPermissionsTitle": "Rabbyに以下の権限を許可:", "ledgerPermission1": "HIDデバイスに接続", "ledgerPermissionTip": "以下のポップアップウィンドウでLedgerへのアクセスを許可するために、\\\"許可\\\"をクリックしてください。", "permissionsAuthorized": "権限が許可されました", "nowYouCanReInitiateYourTransaction": "これでトランザクションを再開できます。", "allow": "許可", "error": {"ethereum_app_open_error": "LedgerデバイスにEthereumアプリをインストール/承認してください。", "ethereum_app_unconfirmed_error": "Ethereumアプリを開くリクエストを拒否しました。", "ethereum_app_not_installed_error": "LedgerデバイスにEthereumアプリをインストールしてください。", "running_app_close_error": "Ledger デバイスで実行中のアプリを閉じることに失敗しました。"}}, "walletConnect": {"connectYour": "接続する", "viaWalletConnect": "を介してWallet Connect", "connectedSuccessfully": "接続が成功しました", "qrCodeError": "ネットワークを確認するか、QRコードを更新してください", "qrCode": "QRコード", "url": "URL", "changeBridgeServer": "ブリッジサーバーを変更", "status": {"received": "スキャンが成功し、確認待ちです", "rejected": "接続がキャンセルされました。QRコードを再スキャンして再試行してください。", "brandError": "間違ったウォレットアプリです。", "brandErrorDesc": "接続には{{brandName}}を使用してください", "accountError": "アドレスが一致しません。", "accountErrorDesc": "モバイルウォレットでアドレスを切り替えてください", "connected": "接続済み", "duplicate": "インポートしようとしているアドレスは重複しています", "default": "{{brand}}でスキャン"}, "title": "{{brandName}}と接続", "disconnected": "切断済み", "accountError": {}, "tip": {"accountError": {"tip1": "接続済みですが、サインできません。", "tip2": "モバイルウォレットで正しいアドレスに切り替えてください"}, "disconnected": {"tip": "{{brandName}}に接続していません"}, "connected": {"tip": "{{brandName}}に接続しました"}}, "button": {"disconnect": "切断", "connect": "接続", "howToSwitch": "切り替え方法"}}, "hd": {"tooltip": {"removed": "アドレスがRabbyから削除されました", "added": "アドレスがRabbyに追加されました", "connectError": "接続が停止しました。ページを更新して再接続してください。", "disconnected": "ハードウェアウォレットに接続できません。再接続を試してみてください。"}, "waiting": "待機中", "clickToGetInfo": "チェーン上の情報を取得するにはクリック", "addToRabby": "<PERSON><PERSON>に追加", "basicInformation": "基本情報", "addresses": "アドレス", "loadingAddress": "{{0}}/{{1}} アドレスを読み込んでいます", "notes": "ノート", "getOnChainInformation": "チェーン上の情報を取得", "hideOnChainInformation": "チェーン上の情報を非表示", "usedChains": "使用されたチェーン", "firstTransactionTime": "最初のトランザクション時刻", "balance": "残高", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: Ledger公式のHDパス。最初の3つのアドレスにはチェーン上で使用されるアドレスが含まれています。", "bip44": "BIP44標準: BIP44プロトコルで定義されたHDパス。最初の3つのアドレスにはチェーン上で使用されるアドレスが含まれています。", "legacy": "Legacy: MEW / Mycryptoで使用されるHDパス。最初の3つのアドレスにはチェーン上で使用されるアドレスが含まれています。"}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: Ledger公式のHDパス。最初の3つのアドレスにはチェーン上で使用されないアドレスが含まれています。", "bip44": "BIP44標準: BIP44プロトコルで定義されたHDパス。最初の3つのアドレスにはチェーン上で使用されないアドレスが含まれています。", "legacy": "Legacy: MEW / Mycryptoで使用されるHDパス。最初の3つのアドレスにはチェーン上で使用されないアドレスが含まれています。"}}, "trezor": {"hdPathType": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。", "ledgerLive": "Ledger Live: Ledger公式のHDパス。", "legacy": "レガシー: MEW / Mycryptoで使用されるHDパス。"}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。", "ledgerLive": "Ledger Live: Ledger official HD path.", "legacy": "従来: MEW / Mycrypto によって使用される HD path。"}, "message": {"disconnected": "{{0}}接続が停止しました。ページを更新して接続し直してください。"}}, "onekey": {"hdPathType": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。"}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。"}}, "mnemonic": {"hdPathType": {"default": "デフォルト: シードフレーズをインポートするためのデフォルトHDパスが使用されます。", "ledgerLive": "Ledger Live: Ledger official HD path.", "bip44": "BIP44標準：BIP44プロトコルによって定義されたHDパス。", "legacy": "レガシー: MEW / Mycryptoで使用されるHDパス。"}, "hdPathTypeNoChain": {"default": "デフォルト: シードフレーズをインポートするためのデフォルトHDパスが使用されます。"}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: Ledger公式のHDパス。最初の3つのアドレスにはチェーン上で使用されるアドレスが含まれています。", "bip44": "BIP44標準: BIP44プロトコルで定義されたHDパス。最初の3つのアドレスにはチェーン上で使用されるアドレスが含まれています。", "legacy": "Legacy: MEW / Mycryptoで使用されるHDパス。最初の3つのアドレスにはチェーン上で使用されるアドレスが含まれています。"}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: Ledger公式のHDパス。最初の3つのアドレスにはチェーン上で使用されないアドレスが含まれています。", "bip44": "BIP44標準: BIP44プロトコルで定義されたHDパス。最初の3つのアドレスにはチェーン上で使用されないアドレスが含まれています。", "legacy": "Legacy: MEW / Mycryptoで使用されるHDパス。最初の3つのアドレスにはチェーン上で使用されないアドレスが含まれています。"}, "switch": {"title": "新しいGridPlusデバイスに切り替え", "content": "複数のGridPlusデバイスをインポートすることはサポートされていません。新しいGridPlusデバイスに切り替えると、現在のデバイスのアドレスリストは削除され、インポートプロセスが開始されます。"}, "switchToAnotherGridplus": "別のGridPlusに切り替える"}, "keystone": {"hdPathType": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。", "legacy": "レガシー：MEW / Mycryptoで使用されるHDパス。", "ledgerLive": "Ledger Live: Ledger official HD path. Ledger Live path では 10 個のアドレスしか管理できません。"}, "hdPathTypeNochain": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。", "legacy": "レガシー: MEW / Mycryptoで使用されるHDパス。", "ledgerLive": "Ledger Live: Ledger official HD path. Ledger Live path では 10 のアドレスしか管理できません。"}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。"}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44プロトコルで定義されたHDパス。"}, "disconnected": "BitBox02に接続できません。ページを更新して接続し直してください。理由: {{0}}"}, "selectHdPath": "HDパスを選択:", "selectIndexTip": "開始するアドレスのシリアル番号を選択:", "manageAddressFrom": "{{0}}から{{1}}のアドレスを管理", "advancedSettings": "高度な設定", "customAddressHdPath": "カスタムアドレスHDパス", "connectedToLedger": "Ledgerに接続", "connectedToTrezor": "Trezorに接続", "connectedToOnekey": "OneKeyに接続", "manageSeedPhrase": "シードフレーズを管理", "manageGridplus": "GridPlusを管理", "manageKeystone": "Keystoneを管理", "manageAirgap": "AirGapを管理", "manageCoolwallet": "CoolWalletを管理", "manageBitbox02": "BitBox02を管理", "manageNgraveZero": "NGRAVE ZEROを管理", "done": "完了", "addressesIn": "{{0}}のアドレス", "addressesInRabby": "Rabbyのアドレス{{0}}", "qrCode": {"switch": {"title": "新しい{{0}}デバイスに切り替え", "content": "{{0}}デバイスを複数インポートすることはサポートされていません。新しい{{0}}デバイスに切り替えると、現在のデバイスのアドレスリストは削除され、インポートプロセスが開始されます。"}, "switchAnother": "{{0}}に切り替える"}, "manageImtokenOffline": "imToken を管理", "manageImKey": "imKey を管理", "importBtn": "インポート ({{count}})"}, "importYourKeystore": "KeyStoreをインポート", "incorrectPassword": "パスワードが正しくありません", "keystore": {"description": "インポートしたいKeyStoreファイルを選択し、対応するパスワードを入力してください", "password": {"required": "パスワードを入力してください", "placeholder": "パスワード"}}, "coboSafe": {"inputSafeModuleAddress": "セーフモジュールのアドレスを入力", "invalidAddress": "無効なアドレス", "whichChainIsYourCoboAddressOn": "Coboアドレスが存在するチェーンを選択", "addCoboArgusAddress": "Cobo Argusアドレスを追加", "findTheAssociatedSafeAddress": "関連するセーフアドレスを検索", "import": "インポート"}, "imkey": {"title": "imKey に接続", "imkeyPermissionTip": "以下のポップアップウィンドウで「Allow」をクリックして、あなたの imKey へのアクセスを許可してください。"}, "keystone": {"keystonePermission1": "USBデバイスに接続", "noDeviceFoundError": "Keystone を一つ差し込んでください", "title": "Keystoneを接続", "allowRabbyPermissionsTitle": "Rabbyに以下の権限を許可する：", "deviceRejectedExportAddress": "Ra<PERSON> への接続を承認", "keystonePermissionTip": "以下のポップアップウィンドウで \"許可\" をクリックして、Keystone へのアクセスを承認し、Keystone 3 Pro がホームページにあることを確認してください。", "exportAddressJustAllowedOnHomePage": "エクスポートアドレスはホームページでのみ許可されています。", "deviceIsLockedError": "パスワードを入力してロックを解除してください", "deviceIsBusy": "デバイスは使用中です", "unknowError": "不明なエラーが発生しました。再試行してください。"}, "firefoxLedgerDisableTips": "Ledger は Firefox と互換性がありません。", "addFromCurrentSeedPhrase": "現在のシードフレーズから追加"}, "unlock": {"btn": {"unlock": "アンロック"}, "password": {"required": "アンロックするにはパスワードを入力してください", "placeholder": "アンロックするにはパスワードを入力してください", "error": "パスワードが正しくありません"}, "title": "<PERSON><PERSON>", "btnForgotPassword": "パスワードをお忘れですか?", "description": "EthereumおよびすべてのEVMチェーン対応の画期的なウォレット"}, "addToken": {"noTokenFound": "トークンが見つかりません", "tokenSupported": "トークンはRabbyでサポートされています", "tokenCustomized": "現在のトークンはカスタマイズされたものに既に追加されています", "tokenNotFound": "この契約アドレスからトークンが見つかりません", "title": "Rabbyにカスタムトークンを追加", "balance": "残高", "tokenOnMultiChains": "複数のチェーン上のトークンアドレスです。お好きなものを選んでください", "noTokenFoundOnThisChain": "このチェーン上でトークンが見つかりません", "hasAdded": "このトークンが追加されました。"}, "switchChain": {"title": "<PERSON><PERSON> にカスタムネットワークを追加", "chainNotSupport": "要求されたチェーンはまだRabbyでサポートされていません", "testnetTip": "テストネットに接続する前に、「詳細設定」の下にある「テストネットを有効にする」をオンにしてください", "unknownChain": "不明なチェーン", "requestsReceivedPlural": "{{count}} 件のリクエストを受信しました", "chainId": "チェーンID：", "requestsReceived": "1 リクエストを受信しました", "requestRabbyToSupport": "Rabbyにサポートをリクエスト", "addChain": "テストネットを追加", "chainNotSupportYet": "リクエストされたチェーンはまだRabbyによってサポートされていません", "chainNotSupportAddChain": "要求的链尚未被Rabby集成。您可以将其添加为カスタムテストネット。", "desc": "要求されたネットワークはまだRabbyに統合されていません 手動でカスタムネットワークとして追加することができます"}, "signText": {"title": "テキストを署名", "message": "メッセージ", "createKey": {"interactDapp": "Dappとのインタラクション", "description": "説明"}, "sameSafeMessageAlert": "同じメッセージが確認されました。追加の署名は必要ありません。"}, "securityEngine": {"yes": "はい", "no": "いいえ", "whenTheValueIs": "値が {{value}} の場合", "currentValueIs": "現在の値は {{value}} です", "viewRules": "セキュリティルールを表示", "undo": "元に戻す", "riskProcessed": "リスク処理済み", "ignoreAlert": "アラートを無視", "ruleDisabled": "セキュリティルールは無効になっています。安全性のためにいつでもオンにできます。", "unknownResult": "セキュリティルールが利用できないため、結果は不明です", "alertTriggerReason": "アラートがトリガーされた理由：", "understandRisk": "リスクを理解し、責任を受け入れます", "forbiddenCantIgnore": "無視できない禁止リスクが見つかりました。", "ruleDetailTitle": "ルールの詳細", "enableRule": "ルールを有効にする", "viewRiskLevel": "リスクレベルを表示"}, "connect": {"listedBy": "リストされた提供元", "sitePopularity": "サイトの人気", "myMark": "私のマーク", "flagByRabby": "Rabbyによってフラグ付け", "flagByMM": "MetaMaskによってフラグ付け", "flagByScamSniffer": "ScamSnifferによってフラグ付け", "verifiedByRabby": "Rabbyによって確認", "foundForbiddenRisk": "禁止されたリスクが見つかりました。接続はブロックされました。", "markAsTrustToast": "\"信頼されている\" としてマーク", "markAsBlockToast": "\"ブロックされている\" としてマーク", "markRemovedToast": "マークが削除されました", "title": "Dappに接続", "selectChainToConnect": "接続するチェーンを選択", "markRuleText": "私のマーク", "connectBtn": "接続", "noWebsite": "なし", "popularLevelHigh": "高", "popularLevelMedium": "中", "popularLevelLow": "低", "popularLevelVeryLow": "非常に低い", "noMark": "マーク<PERSON>し", "blocked": "ブロックされています", "trusted": "信頼されています", "addedToWhitelist": "ホワイトリストに追加されました", "addedToBlacklist": "ブラックリストに追加されました", "removedFromAll": "すべてのリストから削除されました", "notOnAnyList": "どのリストにも含まれていません", "onYourBlacklist": "あなたのブラックリストにあります", "onYourWhitelist": "あなたのホワイトリストにあります", "manageWhiteBlackList": "ホワイトリスト/ブラックリストの管理", "SignTestnetPermission": {"title": "署名許可"}, "ignoreAll": "すべて無視", "SelectWallet": {"desc": "インストールしたウォレットから選択する", "title": "ウォレットを選んで接続してください"}, "otherWalletBtn": "別のウォレットに接続", "connectAddress": "接続アドレス"}, "addressDetail": {"add-to-whitelist": "ホワイトリストに追加", "remove-from-whitelist": "ホワイトリストから削除", "address-detail": "アドレスの詳細", "backup-private-key": "プライベートキーをバックアップ", "backup-seed-phrase": "シードフレーズをバックアップ", "delete-address": "アドレスを削除", "delete-desc": "削除する前に、以下のポイントを考慮して、資産を保護する方法を理解してください。", "direct-delete-desc": "このアドレスは {{renderBrand}} アドレスであり、Rabbyはこのアドレスのプライベートキーやシードフレーズを保存していません。削除するだけです。", "admins": "管理者", "tx-requires": "トランザクションには <2>{{num}}</2> 回の確認が必要です", "edit-memo-title": "アドレスのメモを編集", "please-input-address-note": "アドレスのメモを入力してください", "address": "アドレス", "address-note": "アドレスのメモ", "assets": "資産", "qr-code": "QRコード", "source": "ソース", "hd-path": "HDパス", "manage-seed-phrase": "シードフレーズの管理", "manage-addresses-under-this-seed-phrase": "このシードフレーズの下のアドレスを管理", "safeModuleAddress": "セーフモジュールのアドレス", "coboSafeErrorModule": "アドレスが期限切れです。アドレスを削除して再インポートしてください。", "importedDelegatedAddress": "インポートされた委任アドレス", "manage-addresses-under": "この{{brand}}の下でアドレスを管理する"}, "preferMetamaskDapps": {"title": "MetaMask優先Dapps", "desc": "以下のDappsは、切り替えたウォレットに関係なく、MetaMask経由で接続されたままです。", "howToAdd": "追加方法", "howToAddDesc": "ウェブサイトを右クリックし、このオプションを見つけます", "empty": "Dappsがありません"}, "customRpc": {"opened": "オープン", "closed": "クローズ", "empty": "カスタムRPC URLなし", "title": "RPC URLを修正", "desc": "変更後、カスタムRPCはRabbyのノードを置き換えます。Rabbyのノードを使い続けるには、カスタムRPCを削除してください。", "add": "RPC URL を修正", "EditRPCModal": {"invalidRPCUrl": "無効なRPC URL", "invalidChainId": "無効なチェーンID", "rpcAuthFailed": "RPC認証に失敗しました", "title": "RPC URLを修正", "rpcUrl": "RPC URL", "rpcUrlPlaceholder": "RPC URLを入力してください"}, "EditCustomTestnetModal": {"title": "カスタムネットワークを追加", "quickAdd": "Chainlistから簡単に追加"}}, "requestDebankTestnetGasToken": {"title": "DeBankテストネットガストークンのリクエスト", "mintedTip": "Rabbyバッジの所有者は1日に1回リクエストできます", "notMintedTip": "リクエストはRabbyバッジの所有者のみ利用可能です", "claimBadgeBtn": "Rabbyバッジを請求", "time": "1日あたり", "requested": "本日リクエストしました", "requestBtn": "リクエスト"}, "safeQueue": {"title": "キュー", "sameNonceWarning": "これらのトランザクションは同じノンスを使用しているため競合します。1つを実行すると他のトランザクションが自動的に置き換えられます。", "loading": "保留中のトランザクションを読み込み中", "noData": "保留中のトランザクションはありません", "loadingFaild": "Safeサーバーの不安定性のため、データが利用できません。5分後に再度確認してください", "accountSelectTitle": "このトランザクションは任意のアドレスを使用して送信できます", "LowerNonceError": "ノンス {{nonce}} のトランザクションは先に実行する必要があります", "submitBtn": "トランザクションを送信", "unknownTx": "不明なトランザクション", "cancelExplain": "{{protocol}} の {{token}} 承認をキャンセル", "unknownProtocol": "不明なプロトコル", "approvalExplain": "{{protocol}} の {{token}} を承認する", "unlimited": "無制限", "action": {"send": "送信", "cancel": "保留中のトランザクションをキャンセル"}, "viewBtn": "表示", "ReplacePopup": {"options": {"send": "トークンを送信", "reject": "トランザクションを拒否"}, "title": "このトランザクションの置き換え方法を選択します", "desc": "署名されたトランザクションは削除できませんが、同じnonceを持つ新しいトランザクションで置き換えることができます。"}, "replaceBtn": "置換"}, "importSuccess": {"title": "インポート成功", "addressCount": "{{count}} 件のアドレス", "gnosisChainDesc": "このアドレスは {{count}} のチェーンでデプロイされました"}, "backupSeedPhrase": {"title": "シードフレーズのバックアップ", "alert": "このシードフレーズはあなたの資産の認証情報です。絶対に失くしたり他人に見せたりしないでください。さもないと、資産を永遠に失う可能性があります。安全な環境で表示し、注意して保管してください。", "clickToShow": "シードフレーズを表示するにはクリック", "copySeedPhrase": "シードフレーズをコピー", "qrCodePopupTitle": "QR Code", "showQrCode": "QRコードを表示", "qrCodePopupTips": "シードフレーズのQRコードを他人と共有しないでください。安全な環境で表示し、慎重に保管してください。"}, "backupPrivateKey": {"title": "プライベートキーのバックアップ", "alert": "このプライベートキーはあなたの資産の認証情報です。絶対に失くしたり他人に見せたりしないでください。さもないと、資産を永遠に失う可能性があります。安全な環境で表示し、注意して保管してください。", "clickToShow": "プライベートキーを表示するにはクリック", "clickToShowQr": "プライベートキーQRコードを表示するにはクリック"}, "ethSign": {"alert": "'eth_sign' で署名することは資産の損失につながる可能性があります。安全のため、Rabbyはこの方法をサポートしていません。"}, "createPassword": {"title": "パスワードを設定", "passwordRequired": "パスワードを入力してください", "passwordMin": "パスワードは少なくとも8文字必要です", "passwordPlaceholder": "パスワードは少なくとも8文字必要です", "confirmRequired": "パスワードを確認してください", "confirmError": "パスワードが一致しません", "confirmPlaceholder": "パスワードを確認してください", "agree": "私は<1/> <2>利用規約</2>を読んで同意します"}, "welcome": {"step1": {"title": "すべてのDappsにアクセス", "desc": "RabbyはMetaMaskがサポートするすべてのDappsに接続します"}, "step2": {"title": "セルフカストディアル", "desc": "プライベートキーはローカルに保存され、あなただけがアクセスできます", "btnText": "開始"}}, "importSafe": {"title": "セーフアドレスを追加", "placeholder": "アドレスを入力してください", "error": {"invalid": "有効なアドレスではありません", "required": "アドレスを入力してください"}, "loading": "このアドレスのデプロイされたチェーンを検索中", "gnosisChainDesc": "このアドレスは {{count}} のチェーンでデプロイされました"}, "importQrBase": {"desc": "{{brandName}}ハードウェアウォレットのQRコードをスキャンします", "btnText": "再試行"}, "bridge": {"showMore": {"title": "もっと表示", "source": "ブリッジソース"}, "settingModal": {"confirmModal": {"i-understand-and-accept-it": "私は理解し、受け入れます", "tip1": "有効にすると、このアグリゲーターから直接コントラクトとやり取りします。", "tip2": "2. Rabby は、このアグリゲーターのコントラクトから生じるいかなるリスクに対しても責任を負いません。", "title": "このアグリゲーターを使用して取引を有効にする"}, "SupportedBridge": "サポートされているBridge:", "title": "Bridge Aggregatorsで取引を有効にする", "confirm": "確認"}, "tokenPairDrawer": {"tokenPair": "トークンペア", "balance": "バランスバリュー", "noData": "サポートされているトークンペアはありません", "title": "サポートされているトークンペアから選択"}, "no-quote": "見積もりなし", "Pending": "保留中", "no-transaction-records": "取引記録なし", "history": "ブリッジ履歴", "To": "に", "title": "Bridge", "Select": "選択", "the-following-bridge-route-are-found": "ルートが見つかりました", "From": "から", "pendingTip": "Txが送信されました。Txが長時間保留状態の場合は、設定で保留をクリアすることを試みてください。", "select-chain": "チェーンを選択", "Balance": "残高：", "best": "最高", "actual": "実際:", "detail-tx": "詳細", "BridgeTokenPair": "Bridge Token Pair", "estimated-value": "≈ {{value}}", "completedTip": "チェーントランザクション、データをデコードして記録を生成", "bridgeTo": "橋接到", "Amount": "数量", "tokenPairPlaceholder": "トークンペアを選択", "slippage-adjusted-refresh-quote": "スリッページが調整されました。ルートをリフレッシュしてください。", "bridge-cost": "ブリッジコスト", "rabby-fee": "Rabby fee", "approve-and-bridge": "承認してブリッジする", "insufficient-balance": "残高不足", "bridge-via-x": "{{name}}でブリッジ", "approve-x-symbol": "{{symbol}} を承認", "gas-x-price": "ガス価格: {{price}} Gwei.", "duration": "{{duration}} 分", "price-expired-refresh-route": "価格が無効になりました。ルートを更新してください。", "aggregator-not-enabled": "このアグリゲーターはあなたによって取引が有効化されていません。", "recommendFromToken": "<1></1> からのブリッジの見積もりを取得", "getRoutes": "ルートを取得", "via-bridge": "{{bridge}} 経由", "need-to-approve-token-before-bridge": "ブリッジする前にトークンを承認する必要があります", "gas-fee": "GasFee: {{gasUsed}}", "estimate": "見積もり:", "no-route-found": "ルートが見つかりません", "Completed": "完了", "enable-it": "有効にする", "est-receiving": "受取予定：", "est-difference": "推定差：", "price-impact": "価格影響", "est-payment": "推定支払額:", "max-tips": "この値は、bridgeのガスコストを差し引いた額で計算されます。", "loss-tips": "損失しています {{usd}}。異なる金額を試してください。", "unlimited-allowance": "無制限の許可", "no-quote-found": "見積もりが見つかりませんでした。他のトークンペアをお試しください。"}, "pendingDetail": {"Header": {"predictTime": "予測されるパッケージング"}, "TxStatus": {"pendingBroadcasted": "保留中：放送済み", "completed": "完了", "reBroadcastBtn": "再放送", "pendingBroadcast": "保留中: 放送待ち"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcastedCount_ordinal_few": "{{count}} 回目の放送", "broadcasted": "最近ブロードキャストされた", "broadcastedCount_ordinal_one": "{{count}}回目の放送", "broadcastedCount_ordinal_two": "{{count}}番目のブロードキャスト", "created": "トランザクションが作成されました", "broadcastedCount_ordinal_other": "{{count}}回目の放送", "pending": "ステータスを確認しています…"}, "MempoolList": {"col": {"txStatus": "トランザクションステータス", "nodeOperator": "ノードオペレーター", "nodeName": "ノード名"}, "txStatus": {"notFound": "見つかりません", "appeared": "現れた", "appearedOnce": "一度登場"}, "title": "{{count}} RPCノードに現れました。"}, "PendingTxList": {"filterBaseFee": {"label": "Base fee の要件のみを満たしています", "tooltip": "ブロックのベース料金要件を満たすガス価格のトランザクションのみを表示"}, "col": {"action": "トランザクションアクション", "balanceChange": "残高の変動", "gasPrice": "Gas Price", "interact": "とやり取りする", "actionType": "アクションタイプ"}, "titleNotFound": "全ての保留中のトランザクションでランクがありません。", "titleSame": "GasPrice 同様の現在で #{{rank}} にランクイン", "titleSameNotFound": "同順位なし（同様の現在）", "title": "GasPrice がすべての保留中のトランザクションの中で #{{rank}} にランクされました"}, "Empty": {"noData": "データが見つかりませんでした"}, "PrePackInfo": {"col": {"prePackResults": "事前パック結果", "prePackContent": "プリパックコンテンツ", "expectations": "期待", "difference": "結果を確認"}, "type": {"receive": "受け取る", "pay": "支払う"}, "noLoss": "損失は見つかりませんでした", "loss": "{{lossCount}} loss found", "noError": "エラーは見つかりませんでした", "title": "Pre-pack Check", "error": "{{count}} 件のエラーが見つかりました", "desc": "最新ブロックでシミュレーションを実行しました。更新日時：{{time}}"}, "Predict": {"completed": "トランザクションが完了しました。", "predictFailed": "パッキング時間の予測に失敗しました", "skipNonce": "あなたのアドレスはEthereumチェーンでNonceがスキップされたため、現在のトランザクションを完了できません。"}}, "dappSearch": {"searchResult": {"totalDapps": "合計 <2>{{count}}</2> Dapps", "foundDapps": "<2>{{count}}</2> Dapps が見つかりました"}, "favorite": "お気に入り", "listBy": "Dapp は によってリストされました", "emptyFavorite": "お気に入りのDappがありません", "expand": "拡張", "selectChain": "チェーンを選択", "emptySearch": "Dappが見つかりませんでした"}, "rabbyPoints": {"claimItem": {"go": "行く", "claim": "請求", "claimed": "請求済み", "disabledTip": "現在は獲得できるポイントがありません", "earnTip": "1日1回の制限があります。00:00 UTC+0以降にポイントを獲得してください。"}, "claimModal": {"addressBalance": "ウォレットバランス", "claim": "請求", "rabbyUser": "<PERSON><PERSON> アクティブユーザー", "placeholder": "リファラルコードを入力して追加ポイントを獲得 (オプション)", "MetaMaskSwap": "MetaMask Swap", "snapshotTime": "スナップショット時間: {{time}}", "title": "初期ポイントを申請する", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "season2": "シーズン2", "rabbyValuedUserBadge": "<PERSON><PERSON> Valued User Badge", "walletBalance": "ウォレット残高", "cantUseOwnCode": "自分の紹介コードを使用することはできません。", "referral-code": "紹介コード", "activeStats": "アクティブステータス", "invalid-code": "無効なコード"}, "referralCode": {"verifyAddressModal": {"verify-address": "アドレスを確認", "cancel": "キャンセル", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "このアドレスの所有者であることを確認するために、このテキストメッセージに署名してください。", "sign": "署名"}, "confirm": "確認", "referral-code-already-exists": "紹介コードは既に存在します", "once-set-this-referral-code-is-permanent-and-cannot-change": "設定されると、この紹介コードは永久的で変更できません。", "my-referral-code": "私の紹介コード", "refer-a-new-user-to-get-50-points": "新しいユーザーを紹介して50ポイントを獲得", "set-my-code": "マイコードを設定する", "referral-code-available": "紹介コード利用可能", "referral-code-cannot-be-empty": "紹介コードを空にすることはできません。", "set-my-referral-code": "リファーラルコードを設定する", "referral-code-cannot-exceed-15-characters": "紹介コードは15文字を超えることはできません", "max-15-characters-use-numbers-and-letters-only": "最大 15 文字、数字と文字のみを使用してください。"}, "top-100": "トップ 100", "title": "<PERSON><PERSON>", "share-on": "シェアする", "out-of-x-current-total-points": "{{total}} 点数の合計中", "earn-points": "ポイントを獲得", "referral-code-copied": "紹介コードをコピーしました。", "initialPointsClaimEnded": "初期ポイント請求終了", "firstRoundEnded": "🎉 第1回のRabby Pointsは終了しました", "secondRoundEnded": "🎉 Rabby Points の第2ラウンドが終了しました", "code-set-successfully": "リファーラルコードが正常に設定されました"}, "customTestnet": {"CustomTestnetForm": {"rpcUrlRequired": "RPC URLを入力してください。", "name": "ネットワーク名", "nameRequired": "ネットワーク名を入力してください", "idRequired": "チェーンIDを入力してください", "rpcUrl": "RPC URL", "nativeTokenSymbolRequired": "通貨記号を入力してください", "id": "Chain ID", "blockExplorerUrl": "Block explorer URL（オプション）", "nativeTokenSymbol": "通貨記号"}, "AddFromChainList": {"tips": {"added": "このチェーンはすでに追加されています。", "supported": "<PERSON><PERSON> Walletによってすでに統合されているチェーン"}, "search": "カスタムネットワーク名またはIDを検索", "empty": "チェーンが見つかりませんでした", "title": "Chainlistから素早く追加"}, "signTx": {"title": "取引データ"}, "ConfirmModifyRpcModal": {"desc": "このチェーンはすでにRabbyによって統合されています。RPC URLを変更する必要がありますか？"}, "add": "カスタムネットワークを追加", "title": "カスタムネットワーク", "currency": "通貨", "id": "ID", "empty": "カスタムネットワークなし", "desc": "Rabby はカスタムネットワークの安全性を検証できません。信頼できるネットワークのみを追加してください。"}, "addChain": {"title": "<PERSON><PERSON> にカスタムネットワークを追加", "desc": "Rabbyはカスタムネットワークのセキュリティを確認できません。信頼できるネットワークのみを追加してください。"}, "sign": {"transactionSpeed": "トランザクションスピード"}, "ecology": {"sonic": {"home": {"migrateTitle": "移行", "airdrop": "エアドロップ", "arcadeBtn": "今すぐプレイ", "airdropBtn": "ポイントを獲得", "migrateBtn": "近日公開予定", "migrateDesc": "→", "airdropDesc": "~200 million SをOperaおよびSonicのユーザーに。", "arcadeDesc": "無料ゲームをプレイして、Sエアドロップのポイントを獲得しましょう。", "socialsTitle": "参加する", "earnBtn": "近日公開予定", "earnTitle": "収益", "earnDesc": "$S をステークする"}, "points": {"referralCode": "紹介コード", "referralCodeCopied": "紹介コードをコピーしました", "shareOn": "共有", "sonicPoints": "Sonic Points", "sonicArcade": "Sonic Arcade", "retry": "再試行", "errorDesc": "ポイントの読み込み中にエラーが発生しました。もう一度お試しください。", "pointsDashboard": "ポイントダッシュボード", "pointsDashboardBtn": "ポイントを獲得し始める", "getReferralCode": "紹介コードを取得", "errorTitle": "ポイントを読み込めません", "today": "今日", "sonicArcadeBtn": "プレイを開始"}}, "dbk": {"home": {"bridgeBtn": "ブリッジ", "bridgePoweredBy": "OP Superchain 提供技术支持", "mintNFT": "DBK Genesis NFT をミント", "mintNFTBtn": "ミント", "bridge": "DBK Chain にブリッジする", "mintNFTDesc": "DBK Chain の目撃者になろう"}, "bridge": {"tabs": {"withdraw": "引き出し", "deposit": "預け入れ"}, "info": {"receiveOn": "{{chainName}} で受信", "completeTime": "完了時間", "toAddress": "宛先", "gasFee": "Gas fee"}, "error": {"notEnoughBalance": "残高不足"}, "ActivityPopup": {"status": {"withdraw": "出金", "deposit": "預け入れ", "claimed": "クレーム済み", "challengePeriod": "チャレンジ期間", "rootPublished": "State root published", "readyToClaim": "受取り準備完了", "proved": "証明済み", "readyToProve": "準備完了証明", "waitingToProve": "ステートルート発表"}, "claimBtn": "請求", "proveBtn": "証明", "deposit": "入金", "empty": "まだアクティビティはありません", "withdraw": "出金", "title": "アクティビティ"}, "WithdrawConfirmPopup": {"step2": "Ethereumで証明する", "step1": "引き出しを開始", "question3": "ネットワーク手数料は概算であり、変更されることを理解しています。", "step3": "Ethereumで請求する", "title": "DBK Chain Withdrawal takes ～7日", "btn": "引き出し", "question2": "出金が開始されると、そのスピードアップやキャンセルはできないことを理解しています。", "question1": "資金を出金したことを証明した後、Ethereum で資金が請求可能になるまで約 7 日かかることを理解しています。", "tips": "引き出しは3つのステップが必要であり、1つのDBK Chainトランザクションと2つのEthereumトランザクションが必要です。"}, "labelFrom": "From", "labelTo": "に"}, "minNFT": {"minted": "ミント済み", "title": "DBK Genesis", "myBalance": "私の残高", "mintBtn": "ミント"}}}, "miniSignFooterBar": {"status": {"txSending": "署名リクエストの送信中", "txSigned": "署名済み。トランザクションを作成中", "txCreated": "トランザクションが作成されました。", "txSendings": "署名リクエストを送信中（{{current}}/{{total}}）"}, "signWithLedger": "Ledgerで署名"}, "gasAccount": {"history": {"noHistory": "履歴なし"}, "loginInTip": {"title": "USDC / USDTを入金", "gotIt": "了解しました", "login": "GasAccount にログイン", "desc": "すべてのチェーンでガス料金を支払う"}, "loginConfirmModal": {"title": "現在のアドレスでログイン", "desc": "確認すると、このアドレスにのみデポジットできます。"}, "logoutConfirmModal": {"logout": "ログアウト", "title": "現在のGasAccountからログアウト", "desc": "ログアウトするとGasAccountが無効になります。このアドレスでログインすることでGasAccountを復元できます。"}, "depositPopup": {"amount": "金額", "title": "入金", "invalidAmount": "500未満である必要があります", "desc": "RabbyのDeBank L2アカウントに手数料なしで入金し、いつでも引き出し可能。", "selectToken": "トークンを選択して入金", "token": "トークン"}, "withdrawPopup": {"noEnoughValuetBalance": "Vault Balance insufficient. チェーンを切り替えるか、後でもう一度お試しください。", "selectRecipientAddress": "受取人のアドレスを選択", "recipientAddress": "受取人アドレス", "withdrawalLimit": "出金限度", "to": "に", "selectAddr": "アドレスを選択", "destinationChain": "送信先チェーン", "selectDestinationChain": "宛先チェーンを選択", "amount": "量", "title": "出金", "selectChain": "チェーンを選択", "noEligibleChain": "引き出し可能なチェーンがありません。", "riskMessageFromChain": "リスク管理のため、引き出し限度額はこのチェーンから預け入れた合計金額に依存します。", "noEligibleAddr": "引き出し対象のアドレスがありません。", "noEnoughGas": "ガス料金をカバーするには金額が低すぎます", "deductGasFees": "受け取った金額からガス料金が差し引かれます", "riskMessageFromAddress": "リスク管理のため、引き出し制限はこのアドレスが預けた総額に依存します", "desc": "GasAccountの残高をDeBank L2ウォレットに引き出すことができます。必要に応じてサポートされているブロックチェーンに資金を転送するために、DeBank L2ウォレットにログインしてください。"}, "withdrawConfirmModal": {"button": "DeBankで表示", "title": "DeBank L2 Walletに転送されました"}, "GasAccountDepositTipPopup": {"gotIt": "了解しました", "title": "GasAccountを開いて入金する"}, "switchLoginAddressBeforeDeposit": {"title": "入金の前にアドレスを切り替える", "desc": "ログインアドレスに切り替えてください。"}, "noBalance": "残高なし", "title": "GasAccount", "withdraw": "引き出す", "deposit": "入金", "gasExceed": "GasAccountの残高は$1000を超えることはできません。", "risk": "現在のアドレスがリスキーであると検出されたため、この機能は利用できません。", "safeAddressDepositTips": "マルチシグアドレスは入金には対応していません。", "logout": "現在のGasAccountからログアウト", "gasAccountList": {"address": "アドレス", "gasAccountBalance": "Gas Balance"}, "switchAccount": "GasAccount を切り替える", "withdrawDisabledIAP": "引き出しは無効になっています。これは、バランスに法定通貨が含まれており、それらを引き出すことができないためです。トークン残高を引き出すには、サポートにお問い合わせください。"}, "safeMessageQueue": {"loading": "メッセージを読み込み中", "noData": "メッセージはありません"}, "newUserImport": {"guide": {"createNewAddress": "新しいアドレスを作成", "importAddress": "すでにアドレスを持っています。", "title": "<PERSON><PERSON> Walletへようこそ", "desc": "EthereumおよびすべてのEVMチェーンに対応する画期的なウォレット"}, "createNewAddress": {"showSeedPhrase": "シードフレーズを表示", "desc": "次のセキュリティのヒントをお読みいただき、覚えておいてください。", "tip1": "シードフレーズを失ったり共有したりすると、資産に恒久的にアクセスできなくなります。", "tip2": "私のシードフレーズは私のデバイスにのみ保存されています。Rabbyはアクセスできません。", "tip3": "Rabbyをアンインストールし、シードフレーズをバックアップしなかった場合、それはRabbyによって復元できません。", "title": "始める前に"}, "importList": {"title": "インポート方法を選択"}, "importPrivateKey": {"title": "秘密鍵のインポート", "pasteCleared": "貼り付けてクリップボードをクリアしました"}, "PasswordCard": {"form": {"password": {"label": "パスワード", "required": "パスワードを入力してください", "min": "パスワードは8文字以上である必要があります。", "placeholder": "パスワード（8文字以上）"}, "confirmPassword": {"label": "パスワードを確認", "notMatch": "パスワードが一致しません。", "required": "パスワードを確認してください", "placeholder": "パスワードを確認してください"}}, "title": "パスワードを設定", "desc": "ウォレットをアンロックし、データを暗号化するために使用されます。", "agree": "私は<1/><2>利用規約</2>と<4>プライバシーポリシー</4>に同意します"}, "successful": {"start": "スタート", "create": "作成に成功しました", "addMoreAddr": "このシードフレーズからさらにアドレスを追加します。", "addMoreFrom": "{{name}} からさらにアドレスを追加", "import": "インポートしました"}, "readyToUse": {"pin": "<PERSON><PERSON>et をピン留め", "guides": {"step1": "ブラウザー拡張アイコンをクリックしてください", "step2": "<PERSON><PERSON> Walletをピン留めする"}, "title": "あなたのRabbyウォレットの準備ができました！", "extensionTip": "クリック<1/>、次に<3/>", "desc": "<PERSON><PERSON> Walletを見つけてピン留めしてください。"}, "importSeedPhrase": {"title": "シードフレーズをインポート"}, "importOneKey": {"title": "OneKey", "connect": "OneKeyに接続", "tip2": "2. OneKeyデバイスを接続する", "tip1": "1. <1>OneKey Bridge<1/>をインストール", "tip3": "3. デバイスをアンロック"}, "importTrezor": {"connect": "Trezor に接続", "title": "<PERSON><PERSON><PERSON>", "tip2": "2. デバイスのロックを解除する", "tip1": "1. <PERSON><PERSON><PERSON> デバイスを差し込んでください。"}, "ImportGridPlus": {"tip1": "GridPlus デバイスを開いてください。", "connect": "GridPlus に接続", "title": "GridPlus", "tip2": "2. <PERSON><PERSON><PERSON> Connectorを通じて接続する"}, "importLedger": {"tip3": "Ethereum アプリを開きます。", "title": "Ledger", "tip1": "Ledgerデバイスを接続してください。", "connect": "Ledger を接続", "tip2": "PIN を入力してロックを解除します。"}, "importBitBox02": {"connect": "BitBox02 に接続", "tip3": "3. デバイスをアンロックします", "tip1": "1. <1>BitBoxBridge<1/>をインストールする", "title": "BitBox02", "tip2": "2. BitBox02 を接続します。"}, "importKeystone": {"qrcode": {"desc": "KeystoneハードウェアウォレットのQRコードをスキャンしてください"}, "usb": {"tip2": "パスワードを入力してロックを解除してください。", "tip3": "コンピュータへの接続を承認してください", "connect": "Keystone を接続", "desc": "Keystone 3 Pro がホームページにあることを確認してください。", "tip1": "Keystone デバイスを接続してください"}}, "importSafe": {"error": {"required": "アドレスを入力してください", "invalid": "有効なアドレスではありません"}, "title": "Safeアドレスを追加", "loading": "このアドレスのデプロイされたチェーンを検索しています", "placeholder": "セーフアドレスを入力"}}, "metamaskModeDapps": {"title": "許可されたDappsを管理する", "desc": "以下 Dapps 已启用 MetaMask モード。MetaMask オプションを選択して Rabby を接続できます。"}, "forgotPassword": {"home": {"title": "パスワードを忘れた場合", "button": "リセットプロセスを開始", "buttonNoData": "パスワードを設定", "descriptionNoData": "Rabby Walletはパスワードを保存せず、パスワードを取得するお手伝いはできません。忘れた場合は新しいパスワードを設定してください。", "description": "Rabby Walletはパスワードを保存せず、パスワードの復元をお手伝いすることができません。ウォレットをリセットして新しいウォレットを設定してください。"}, "reset": {"alert": {"title": "データは削除され、復元不可能になります。", "seed": "シードフレーズ", "privateKey": "プライベートキー"}, "tip": {"records": "署名記録", "whitelist": "ホワイトリスト設定", "safe": "インポートされた Safe Wallets", "watch": "連絡先と監視用アドレス", "hardware": "インポートされたハードウェアウォレット", "title": "データの保持先："}, "button": "リセットを確認", "title": "<PERSON><PERSON> をリセット", "confirm": "ボックスに<1>RESET</1>と入力して確認し、続行します。"}, "tip": {"title": "<PERSON><PERSON> Wallet リセットが完了しました", "description": "新しいパスワードを作成して続行してください", "buttonNoData": "アドレスを追加", "button": "パスワードを設定", "descriptionNoData": "アドレスを追加して開始する"}, "success": {"button": "完了", "description": "Rabby Wallet を使用する準備が整いました。", "title": "パスワードが正常に設定されました"}}, "eip7702": {"alert": "EIP-7702 はまだサポートされていません"}, "metamaskModeDappsGuide": {"toast": {"disabled": "偽装が無効になりました。Dappを更新してください。", "enabled": "偽装が有効になりました。Dapp を更新して再接続してください。"}, "step1": "ステップ 1", "step2": "ステップ 2", "step2Desc": "MetaMaskを介して更新して接続", "noDappFound": "Dapp が見つかりません", "step1Desc": "Rabbyに現在のDappでMetaMaskとして偽装を許可する", "manage": "許可されたDappsを管理", "title": "MetaMaskとして偽装してRabbyを接続する", "alert": "Dappに接続できません。<PERSON><PERSON> Walletがオプションとして表示されていないためですか？"}, "syncToMobile": {"downloadGooglePlay": "Google Play", "steps2Description": "あなたのQRコードには機密データが含まれています。これを秘密にして、決して他人と共有しないでください。", "downloadAppleStore": "App Store", "steps1": "Rabby Mobile をダウンロードする", "description": "あなたのアドレスデータは完全にオフラインで保持され、暗号化され、QRコードを通じて安全に転送されます。", "steps2": "2. <PERSON><PERSON> Mobileでスキャン", "title": "<PERSON><PERSON> Extensionからモバイルへウォレットアドレスを同期", "clickToShowQr": "クリックしてアドレスを選択し、QRコードを表示します", "disableSelectAddress": "{{type}}アドレスのサポートされていない同期", "disableSelectAddressWithPassphrase": "パスフレーズを使用した{{type}}アドレスのサポートされていない同期", "disableSelectAddressWithSlip39": "Slip39を使用して{{ypyped}}アドレスのサポートされていない同期", "selectedLenAddressesForSync_one": "selected {{len}} syncのアドレス", "selectedLenAddressesForSync_other": "selected {{len}} syncのアドレス", "selectAddress": {"title": "アドレスを選択して同期する"}}, "search": {"sectionHeader": {"NFT": "NFT", "Defi": "<PERSON><PERSON><PERSON>", "token": "トークン", "AllChains": "すべてのチェーン"}, "header": {"placeHolder": "検索", "searchPlaceHolder": "トークン名 / アドレスを検索"}, "tokenItem": {"gasToken": "ガストークン", "Issuedby": "発行元", "FDV": "FDV", "listBy": "{{name}}によるリスト", "verifyDangerTips": "これは詐欺トークンです", "scamWarningTips": "これは低品質のトークンであり、詐欺の可能性があります。"}, "searchWeb": {"noResults": "結果がありません", "searchTips": "ウェブを検索する", "title": "すべての結果", "searching": "結果は", "noResult": "結果はありません"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "一致するアドレスがありません", "AddressItem": {"whitelistedAddressTip": "ホワイトリストに登録されたアドレス"}}, "AccountSelectDrawer": {"btn": {"cancel": "キャンセル", "proceed": "続行"}}, "AddressList": {"AddressItem": {"addressTypeTip": "{{type}}によってインポートされました"}}, "AuthenticationModal": {"passwordError": "パスワードが正しくありません", "passwordRequired": "パスワードを入力してください", "passwordPlaceholder": "確認のためのパスワードを入力"}, "ConnectStatus": {"connecting": "接続中...", "connect": "接続", "gridPlusConnected": "GridPlusが接続されています", "gridPlusNotConnected": "GridPlusは接続されていません", "ledgerNotConnected": "Ledgerは接続されていません", "ledgerConnected": "Ledgerが接続されています", "imKeyConnected": "imKey is connected", "keystoneConnected": "Keystone が接続されました", "imKeyrNotConnected": "imKeyは接続されていません", "keystoneNotConnected": "Keystone is not connected"}, "Contact": {"AddressItem": {"notWhitelisted": "このアドレスはホワイトリストに登録されていません", "whitelistedTip": "ホワイトリストに登録されたアドレス"}, "EditModal": {"title": "アドレスのノートを編集"}, "EditWhitelist": {"backModalTitle": "変更を破棄", "backModalContent": "行った変更は保存されません", "title": "ホワイトリストを編集", "tip": "ホワイトリストに登録したいアドレスを選択して保存してください。", "save": "ホワイトリストに保存 ({{count}})"}, "ListModal": {"title": "アドレスを選択", "whitelistEnabled": "ホワイトリストが有効です。ホワイトリストに登録されたアドレスにのみ資産を送信できるか、\"設定\"で無効にできます", "whitelistDisabled": "ホワイトリストが無効です。どのアドレスにも資産を送信できます", "editWhitelist": "ホワイトリストを編集", "whitelistUpdated": "ホワイトリストが更新されました", "authModal": {"title": "ホワイトリストに保存"}}}, "LoadingOverlay": {"loadingData": "データを読み込み中..."}, "MultiSelectAddressList": {"imported": "インポート済み"}, "NFTNumberInput": {"erc1155Tips": "残高は {{amount}} です", "erc721Tips": "ERC 721のNFTは1つしか一度に送信できません"}, "TiledSelect": {"errMsg": "シードフレーズの順序が間違っています。確認してください"}, "Uploader": {"placeholder": "JSONファイルを選択"}, "WalletConnectBridgeModal": {"title": "ブリッジサーバーのURL", "requiredMsg": "ブリッジサーバーホストを入力してください", "invalidMsg": "ホストを確認してください", "restore": "初期設定に戻す"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "統合ネットワーク", "testnet": "カスタムネットワーク"}}, "ChainSelectorModal": {"searchPlaceholder": "チェーンを検索", "noChains": "チェーンがありません", "addTestnet": "カスタムネットワークを追加"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "資産 / 量"}, "price": {"title": "価格"}, "usdValue": {"title": "USD価値"}}, "searchInput": {"placeholder": "名前またはアドレスで検索"}, "header": {"title": "トークンを選択"}, "noTokens": "トークンがありません", "noMatch": "一致するものがありません", "noMatchSuggestion": "{{ chainName }}の契約アドレスを検索してみてください", "bridge": {"value": "値", "token": "トークン", "liquidity": "流動性", "high": "高い", "low": "低い", "liquidityTips": "過去の取引量が多いほど、ブリッジが成功する可能性が高くなります。"}, "hot": "ホット", "recent": "最近", "common": "一般", "chainNotSupport": "このチェーンはサポートされていません"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "コレクション", "Chain": "チェーン", "PurschaseDate": "購入日", "LastPrice": "最終価格"}}, "signPermissionCheckModal": {"title": "このDappに対してテストネットでのサインを許可しています", "reconnect": "Dappを再接続"}, "testnetCheckModal": {"title": "\"詳細\"の下で\"テストネットを有効にする\"をオンにしてください"}, "EcologyNavBar": {"providedBy": "{{chainName}} 提供"}, "EcologyNoticeModal": {"title": "通知", "notRemind": "再び通知しない", "desc": "以下のサービスは、サードパーティーのエコシステムパートナーによって直接提供されます。Rabby Walletはこれらのサービスのセキュリティに対して責任を負いません。"}, "ReserveGasPopup": {"title": "ガスを予約する", "instant": "即時", "normal": "ノーマル", "doNotReserve": "ガスを予約しない", "fast": "速い"}, "OpenExternalWebsiteModal": {"title": "Ra<PERSON> Wallet を離れようとしています", "button": "続行", "content": "外部のウェブサイトを訪れようとしています。Ra<PERSON> Walletはこのサイトの内容やセキュリティに責任を負いません。"}, "TokenChart": {"price": "価格", "holding": "ホールディングバリュー"}, "externalSwapBrideDappPopup": {"selectADapp": "Dappを選択してください", "chainNotSupported": "このチェーンではサポートされていません", "noQuotesForChain": "このチェーンにはまだ利用可能な引用がありません", "help": "このチェーンの公式チームにサポートをお問い合わせください", "noDapp": "利用できるDappsはありません", "thirdPartyDappToProceed": "サードパーティのDappを使用して進めてください", "viewDappOptions": "Dappオプションを見る", "noDapps": "このチェーンには利用可能なDappがありません", "bridgeOnDapp": "Dapp上のブリッジ", "swapOnDapp": "Dapp での交換\n"}, "AccountSelectorModal": {"title": "アドレスを選択", "searchPlaceholder": "アドレスを検索"}}, "global": {"appName": "Rabbyウォレット", "appDescription": "EthereumおよびすべてのEVMチェーン向けの画期的なウォレット", "copied": "コピー済み", "confirm": "確認", "next": "次へ", "back": "戻る", "ok": "OK", "refresh": "リフレッシュ", "failed": "失敗", "scamTx": "詐欺トランザクション", "gas": "ガス", "unknownNFT": "不明なNFT", "copyAddress": "アドレスのコピー", "watchModeAddress": "ウォッチモードアドレス", "assets": "資産", "Confirm": "確認", "Cancel": "キャンセル", "Clear": "クリア", "Save": "保存", "confirmButton": "確認", "cancelButton": "キャンセル", "backButton": "戻る", "proceedButton": "進む", "editButton": "編集", "addButton": "追加", "closeButton": "閉じる", "Deleted": "削除済み", "Loading": "読み込み中", "nonce": "<PERSON><PERSON>", "Balance": "残高", "Done": "完了", "Nonce": "<PERSON><PERSON>", "notSupportTesntnet": "カスタムネットワークには対応していません。", "tryAgain": "再試行してください"}, "background": {"error": {"noCurrentAccount": "現在のアカウントがありません", "invalidChainId": "無効なチェーンID", "notFindChain": "チェーン {{chain}} が見つかりません", "unknownAbi": "不明な契約ABI", "invalidAddress": "有効なアドレスではありません", "notFoundGnosisKeyring": "Gnosisキーリングが見つかりません", "notFoundTxGnosisKeyring": "Gnosisキーリング内でトランザクションが見つかりません", "addKeyring404": "addKeyringの実行に失敗しました、キーリングが未定義です", "emptyAccount": "現在のアカウントは空です", "generateCacheAliasNames": "[GenerateCacheAliasNames]: 少なくとも1つのアドレスが必要です", "invalidPrivateKey": "プライベートキーが無効です", "invalidJson": "入力ファイルが無効です", "invalidMnemonic": "シードフレーズが無効です、確認してください！", "notFoundKeyringByAddress": "アドレスでキーリングを見つけることができません", "txPushFailed": "トランザクションプッシュに失敗しました", "unlock": "ウォレットをアンロックする必要があります", "duplicateAccount": "インポートしようとしているアカウントは重複しています", "canNotUnlock": "前のボールトなしにはアンロックできません"}, "transactionWatcher": {"submitted": "トランザクションが提出されました", "more": "詳細情報を表示するにはクリック", "completed": "トランザクションが完了しました", "failed": "トランザクションが失敗しました", "txCompleteMoreContent": "{{chain}} #{{nonce}} 完了しました。クリックして詳細をご覧ください。", "txFailedMoreContent": "{{chain}} #{{nonce}} が失敗しました。クリックして詳細を表示します。"}, "alias": {"HdKeyring": "シードフレーズによって作成", "simpleKeyring": "プライベートキーによってインポート", "watchAddressKeyring": "コンタクト"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "シードフレーズで作成", "SimpleKeyring": "プライベートキーでインポート", "WatchAddressKeyring": "コンタクト"}, "IMPORTED_HD_KEYRING": "シードフレーズでインポート", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "メインネットおよびテストネット", "TESTNET": "テストネットのみ"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "Seed Phrase (Passphrase) でインポート"}}