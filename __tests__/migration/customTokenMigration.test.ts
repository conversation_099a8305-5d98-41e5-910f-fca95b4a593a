/**
 * @jest-environment jsdom
 */
import customTokenMigration from '../../src/migrations/customTokenMigration';

const data = {
  preference: {
    addedToken: {
      '******************************************': [
        '******************************************',
        '******************************************',
      ],
      '******************************************': [
        '******************************************',
      ],
    },
  },
};

test('should migrate data', () => {
  return customTokenMigration
    .migrator(data, [
      { id: '******************************************', chain: 'bsc' },
      { id: '******************************************', chain: 'cro' },
      { id: '******************************************', chain: 'eth' },
    ])
    .then((result) => {
      expect(result!.preference.addedToken).toEqual({
        '******************************************': [
          'bsc:******************************************',
          'cro:******************************************',
        ],
        '******************************************': [
          'eth:******************************************',
        ],
      });
    });
});

const data1 = {
  preference: {
    addedToken: {},
  },
};
test('do nothing if empty', () => {
  return customTokenMigration.migrator(data1).then((result) => {
    expect(result!.preference.addedToken).toEqual({});
  });
});
