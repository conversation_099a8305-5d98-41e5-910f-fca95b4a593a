{"page": {"transactions": {"empty": {"desc": "Nenhuma transação encontrada em <1>chains suportados</1>", "title": "Sem transações"}, "explain": {"unknown": "Interação com Contratos", "approve": "Aprovar {{amount}} {{symbol}} para {{project}}", "cancel": "Cancelou uma transação pendente"}, "txHistory": {"tipInputData": "A transação inclui uma mensagem", "parseInputDataError": "<PERSON><PERSON><PERSON> mensagem falhou", "scamToolTip": "Esta transação é iniciada por golpistas para enviar tokens e NFTs de golpe. Por favor, evite interagir com ela."}, "modalViewMessage": {"title": "Ver Mensagem"}, "filterScam": {"btn": "Ocultar transações de golpe", "title": "Ocultar transações de scam", "loading": "Carregando pode levar um momento e atrasos nos dados são possíveis."}, "title": "Transações"}, "chainList": {"title": "{{count}} chains Integrados", "testnet": "Testnets", "mainnet": "Mainnets"}, "signTx": {"gasAccount": {"estimatedGas": "Gas estimado:", "totalCost": "Custo total:", "sendGas": "A transferência de Gas para você pela transação atual:", "gasCost": "Custo de gás para transferir gás para o seu endereço:", "maxGas": "Max Gas:", "currentTxCost": "Quantidade de Gas enviada para o seu endereço:"}, "balanceChange": {"nftOut": "NFT fora", "tokenOut": "Token fora", "failedTitle": "Simulação Falhou", "noBalanceChange": "Nenhuma alteração de saldo", "successTitle": "Resultados da Simulação", "notSupport": "Simulação Não Suportada", "errorTitle": "Falha ao buscar alteração de saldo", "tokenIn": "Token em"}, "customRPCErrorModal": {"title": "Erro RPC Personalizado", "button": "Desativar RPC Personalizado", "content": "Seu RPC personalizado não está disponível no momento. Você pode desativá-lo e continuar assinando usando o RPC oficial do Rabby."}, "swap": {"valueDiff": "<PERSON>or diff", "unknownAddress": "Endereço desconhecido", "payToken": "<PERSON><PERSON>", "receiver": "Receptor", "title": "Trocar Token", "simulationFailed": "A simulação da transação falhou", "slippageTolerance": "Tolerância de slippage", "failLoadReceiveToken": "Falha ao carregar", "simulationNotSupport": "Simulação de transação não suportada nesta cadeia", "receiveToken": "<PERSON><PERSON><PERSON>", "minReceive": "<PERSON><PERSON><PERSON>", "slippageFailToLoad": "Falha ao carregar", "notPaymentAddress": "Não é o endereço de pagamento"}, "crossChain": {"title": "Cross Chain"}, "swapAndCross": {"title": "Trocar Token e Cross Chain"}, "transferOwner": {"transferTo": "Transferir para", "title": "Transferir Propriedade de Ativos", "description": "Descrição"}, "swapLimitPay": {"title": "Trocar Limite do Token Pagar", "maxPay": "Máxi<PERSON>aga<PERSON>"}, "send": {"sendToken": "Enviar token", "title": "<PERSON><PERSON><PERSON>", "addressBalanceTitle": "Saldo do endereço", "sendTo": "Enviar para", "notOnThisChain": "Não nesta cadeia", "fromMyPrivateKey": "Do meu keyring", "whitelistTitle": "Lista branca", "fromMySeedPhrase": "Do minha seed phrase", "tokenNotSupport": "{{0}} n<PERSON> suportado", "cexAddress": "CEX endereço", "contractNotOnThisChain": "Não nesta cadeia", "notTopupAddress": "Não é um endereço de depósito", "scamAddress": "Endereço de golpe", "notOnWhitelist": "Não está na minha lista de permissões", "receiverIsTokenAddress": "Endereço do token", "onMyWhitelist": "Na minha lista de permissões"}, "tokenApprove": {"myBalance": "<PERSON><PERSON> saldo", "flagByRabby": "<PERSON><PERSON> por <PERSON>", "amountPopupTitle": "Aprovar valor", "eoaAddress": "EOA", "deployTimeLessThan": "< {{value}} dias", "trustValueLessThan": "≤ {{value}}", "approveToken": "Aprovar token", "approveTo": "<PERSON><PERSON><PERSON> para", "title": "Aprovação de Token", "amount": "<PERSON><PERSON><PERSON>:", "contractTrustValueTip": "O valor de confiança refere-se ao valor total dos ativos gastos por este contrato. Um baixo valor de confiança indica risco ou inatividade por 180 dias.", "exceed": "Excede seu saldo atual"}, "revokeTokenApprove": {"revokeToken": "Revogar token", "title": "<PERSON><PERSON><PERSON>ova<PERSON> Token", "revokeFrom": "<PERSON><PERSON><PERSON>"}, "sendNFT": {"title": "Enviar NFT", "nftNotSupport": "NFT não suportado"}, "nftApprove": {"title": "Aprovação NFT", "nftContractTrustValueTip": "O valor de confiança refere-se ao maior valor de NFT gasto por este contrato. Um valor de confiança baixo indica risco ou inatividade por 180 dias.", "approveNFT": "Aprovar NFT"}, "revokeNFTApprove": {"revokeNFT": "Revogar NFT", "title": "Revogar Aprovação de NFT"}, "nftCollectionApprove": {"approveCollection": "<PERSON><PERSON><PERSON>", "title": "Aprovação da Coleção NFT"}, "revokeNFTCollectionApprove": {"revokeCollection": "<PERSON><PERSON><PERSON>", "title": "Revogar Coleção de NFT"}, "deployContract": {"descriptionTitle": "Descrição", "title": "Implantar um Contrato", "description": "Você está implantando um contrato inteligente"}, "cancelTx": {"txToBeCanceled": "Transação a ser cancelada", "title": "Cancelar Transação Pendente", "gasPriceAlert": "Defina o preço atual do gás para mais de {{value}} Gwei para cancelar a transação pendente."}, "submitMultisig": {"multisigAddress": "Endereço Multisig", "title": "Enviar Transação Multisig"}, "contractCall": {"operation": "Operação", "receiver": "Endereço do Receptor", "payNativeToken": "Pague {{symbol}}", "title": "Chamada de Contrato", "operationABIDesc": "A operação é decodificada a partir do ABI", "suspectedReceiver": "Endereço de Exceção", "operationCantDecode": "A operação não está decodificada"}, "revokePermit2": {"title": "Revoque a Aprovação do Permit2"}, "batchRevokePermit2": {"title": "Revogar Aprovação do Permissão2 em Lote"}, "revokePermit": {"title": "<PERSON><PERSON><PERSON> de Aprovação de Token"}, "assetOrder": {"listAsset": "Listar ativo", "title": "Ordem de Ativos", "receiveAsset": "Receber ativo"}, "BroadcastMode": {"instant": {"title": "Instant", "desc": "As transações serão imediatamente transmitidas para a rede"}, "lowGas": {"title": "Economia de Gas", "desc": "As transações serão transmitidas quando o gás da rede estiver baixo."}, "mev": {"title": "MEV Guarded", "desc": "As transações serão transmitidas para o nó MEV designado"}, "tips": {"customRPC": "Não suportado ao usar RPC personalizado", "walletConnect": "Não suportado pelo WalletConnect", "notSupportChain": "Não suportado nesta cadeia", "notSupported": "Não suportado"}, "lowGasDeadline": {"1h": "1h", "4h": "4h", "24h": "24h", "label": "Tempo limite"}, "title": "Modo de Transmissão"}, "safeTx": {"selfHostConfirm": {"button": "OK", "title": "Mude para o Serviço Seguro do Rabby", "content": "A API do Safe não está disponível. Mude para o serviço Safe implantado pelo Rabby para manter seu Safe funcional. <strong>Todos os signatários do Safe devem usar a Rabby Wallet para autorizar transações.<strong>"}}, "SafeNonceSelector": {"explain": {"contractCall": "Chamada de Contrato", "send": "<PERSON><PERSON><PERSON>", "unknown": "Transação Desconhecida"}, "optionGroup": {"replaceTitle": "Substitua a transação na Fila", "recommendTitle": "<PERSON><PERSON> recomendado"}, "option": {"new": "Nova Transação"}, "error": {"pendingList": "Falha ao carregar transações pendentes, <1/><2>Tente novamente</2>"}}, "coboSafeCreate": {"title": "Crie Cobo Safe", "descriptionTitle": "Descrição", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationRole": {"descriptionTitle": "Descrição", "title": "Enviar Modificação de Função do Safe", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationDelegatedAddress": {"descriptionTitle": "Descrição", "safeWalletTitle": "Safe{Wallet}", "title": "Submeter Modificação de Endereço Delegado"}, "coboSafeModificationTokenApproval": {"descriptionTitle": "Descrição", "safeWalletTitle": "Safe{Wallet}", "title": "Enviar Modificação da Aprovação do Token"}, "common": {"interactContract": "Interagir contrato", "descTipWarningPrivacy": "A assinatura pode verificar a posse do endereço", "descTipWarningAssets": "A assinatura pode causar alteração de ativos", "descTipSafe": "A assinatura não causa alteração de ativos nem verifica a propriedade do endereço.", "descTipWarningBoth": "A assinatura pode causar alteração de ativos e verificar a posse do endereço.", "description": "Descrição"}, "noGasRequired": "Nenhum gás necessário", "gasSelectorTitle": "Gas", "gasMoreButton": "<PERSON><PERSON>", "gasLimitLessThanExpect": "O limite de Gas é baixo. Há 1% de chance de a transação falhar.", "multiSigChainNotMatch": "Endereços multifirma não estão nesta cadeia e não podem iniciar transações.", "canOnlyUseImportedAddress": "Você não pode assinar transações com um endereço somente para visualização.", "nftIn": "NFT em", "gasLimitNotEnough": "O limite de Gas é menor que 21000. A transação não pode ser enviada.", "nonceLowerThanExpect": "<PERSON>ce é muito baixo, o mínimo deve ser {{0}}", "nativeTokenNotEngouthForGas": "O saldo de Gas não é suficiente para a transação.", "gasPriceTitle": "Gas Price (Gwei)", "nonceTitle": "<PERSON><PERSON>", "gasLimitLessThanGasUsed": "O limite de Gas é muito baixo. Há 95% de chance de que a transação possa falhar.", "myNativeTokenBalance": "<PERSON><PERSON>ldo <PERSON>:", "maxPriorityFee": "Max Priority Fee (Gwei)", "safeAddressNotSupportChain": "O endereço seguro atual não é suportado na cadeia {{0}}.", "manuallySetGasLimitAlert": "Você definiu manualmente o limite de Gas para", "gasLimitTitle": "Limite de gás", "failToFetchGasCost": "Falha ao estimar gás", "gasNotRequireForSafeTransaction": "Taxa de gas não é necessária para transações Safe.", "gasAccountForGas": "Use USD from my GasAccount to pay for gas", "recommendGasLimitTip": "Est. {{est}}. Current {{current}}x, recomendar", "gasLimitModifyOnlyNecessaryAlert": "Modifique apenas quando necessário", "gasPriceMedian": "Mediana das últimas 100 transações on-chain:", "hardwareSupport1559Alert": "Certifique-se de que o firmware da sua hardware wallet foi atualizado para a versão que suporta EIP 1559.", "gasLimitEmptyAlert": "Por favor, insira o limite de gas", "nativeTokenForGas": "Use {{tokenName}} token on {{chainName}} para pagar pelo gas", "gasLimitMinValueAlert": "O limite de Gas deve ser superior a 21000", "safeAdminSigned": "<PERSON><PERSON><PERSON>", "eip1559Desc2": "A<PERSON> no <PERSON>, a Taxa de Prioridade (Dica) = Taxa Máxima - Taxa Base. Depois de definir a Taxa de Prioridade Máxima, a Taxa Base será deduzida dela e o restante será destinado como gorjeta para os mineradores.", "enoughSafeSigCollected": "Assinatura suficiente coletada", "wrapToken": "<PERSON><PERSON>", "unwrap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eip1559Desc1": "Nas cadeias que suportam o EIP-1559, a Taxa de Prioridade é a gorjeta para os mineradores processarem sua transação. Você pode economizar no custo final do gás ao reduzir a Taxa de Prioridade, o que pode levar mais tempo para que a transação seja processada.", "chain": "Cadeia", "nftCollection": "Coleção de NFT", "floorPrice": "Preço mínimo", "markAsTrust": "Marcado como confiável", "trusted": "<PERSON><PERSON><PERSON><PERSON>", "interactContract": "Interagir contrato", "contractAddress": "Endereço do contrato", "neverInteracted": "Nunca interagiu antes", "fakeTokenAlert": "Este é um token fraudulento marcado pelo <PERSON>bby.", "neverTransacted": "Nunca transacionou antes", "interacted": "Interagido antes", "noMark": "Nenhuma marca", "blocked": "Bloqueado", "signTransactionOnChain": "Assinar Transação {{chain}}", "viewRaw": "Ver Raw", "unknownAction": "Tipo de Assinatura Desconhecido", "protocolTitle": "Protocolo", "decodedTooltip": "Esta assinatura é decodificada pelo Rabby <PERSON>", "deployTimeTitle": "Hora de implantação", "speedUpTooltip": "Esta transação acelerada e a transação original, das quais apenas uma será concluída eventualmente.", "importedAddress": "Endereço importado", "unknownActionType": "Tipo de Ação Desconhecido", "scamTokenAlert": "Isto é potencialmente um token de baixa qualidade e golpe com base na detecção do Rabby.", "transacted": "Transacionado antes", "sigCantDecode": "Esta assinatura não pode ser decodificada pela Ra<PERSON>", "safeServiceNotAvailable": "O serviço Safe não está disponível no momento, por favor, tente novamente mais tarde.", "markAsBlock": "Marcado como bloqueado", "markRemoved": "Marc<PERSON> removida", "firstOnChain": "Prime on-chain", "trustValue": "Valor de confiança", "collectionTitle": "Coleção", "myMark": "Minha marca", "myMarkWithContract": "Minha marca no contrato {{chainName}}", "contractPopularity": "Não.{{0}} em {{1}}", "importedDelegatedAddress": "Endereço delegado importado", "moreSafeSigNeeded": "Precisa de mais {{0}} assinaturas para confirmar", "addressNote": "Nota do endereço", "addressTypeTitle": "Tipo de endereço", "coboSafeNotPermission": "Este endereço delegado não tem permissão para iniciar esta transação", "noDelegatedAddress": "Nenhum endereço delegado importado", "l2GasEstimateTooltip": "A estimativa de gás para a cadeia L2 não inclui a taxa de gás L1. A taxa real será maior do que a estimativa atual.", "no": "Não", "yes": "<PERSON>m", "protocol": "Protocolo", "address": "Endereço", "popularity": "Popularidade", "hasInteraction": "Interagido antes", "maxPriorityFeeDisabledAlert": "Por favor, defina o <PERSON> primeiro", "primaryType": "Tipo primá<PERSON>", "addressSource": "Fonte do Endereço", "label": "Etiqueta", "typedDataMessage": "<PERSON><PERSON><PERSON>", "advancedSettings": "Configurações Avançadas", "trustValueTitle": "Valor de confiança", "contract": "Endereço do contrato inteligente", "amount": "Quantidade"}, "signFooterBar": {"gasless": {"notEnough": "O saldo de gás não é suficiente", "customRpcUnavailableTip": "RPCs personalizados não são suportados para Free Gas", "watchUnavailableTip": "Endereço apenas de visualização não é suportado para Free Gas", "GetFreeGasToSign": "Obtenha <PERSON>", "walletConnectUnavailableTip": "A carteira móvel conectada via WalletConnect não é suportada para Free Gas", "rabbyPayGas": "<PERSON><PERSON> pagar<PERSON> pelo gás necessário – basta assinar.", "unavailable": "Seu saldo de Gas não é suficiente"}, "gasAccount": {"customRPC": "Não suportado ao usar RPC personalizado", "notEnough": "GasAccount não é suficiente", "login": "Entrar", "gotIt": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "loginFirst": "Por favor, fa<PERSON> login no GasAccount primeiro.", "depositTips": "Para completar o depósito do GasAccount, esta transação será descartada. Você precisará refazê-la após o depósito.", "chainNotSupported": "Esta cadeia não é suportada pelo GasAccount.", "loginTips": "Para completar o login do GasAccount, esta transação será descartada. Você precisará refazê-la após o login.", "WalletConnectTips": "WalletConnect não é suportado pelo GasAccount.", "useGasAccount": "Use GasAccount"}, "walletConnect": {"signOnYourMobileWallet": "Por favor, assine na sua wallet móvel.", "latency": "Latência", "connected": "Conectado e pronto para assinar", "howToSwitch": "Como alternar", "connectBeforeSign": "{{0}} não está conectado a<PERSON>, por favor conecte antes de assinar.", "connectedButCantSign": "<PERSON><PERSON><PERSON><PERSON>, mas incapaz de assinar.", "chainSwitched": "Você mudou para uma rede diferente na carteira móvel. Por favor, mude para {{0}} na carteira móvel.", "switchChainAlert": "Por favor, troque para {{chain}} na carteira móvel", "notConnectToMobile": "Não conectado a {{brand}}", "wrongAddressAlert": "Você trocou para um endereço diferente na carteira móvel. Por favor, mude para o endereço correto na carteira móvel.", "switchToCorrectAddress": "Por favor, troque para o endereço correto na wallet móvel.", "sendingRequest": "Enviando solicitação de assinatura", "requestSuccessToast": "Solicitação enviada com sucesso", "requestFailedToSend": "Falha ao enviar a solicitação de assinatura"}, "addressTip": {"onekey": "Endereço OneKey", "coolwallet": "CoolWallet endereço", "keystone": "Keystone endereço", "airgap": "AirGap address", "bitbox": "Endereço BitBox02", "trezor": "Endereço Trezor", "safe": "Endereço seguro", "privateKey": "Endereço da Chave Privada", "coboSafe": "Cobo Argus Endereço", "seedPhraseWithPassphrase": "Frase Semente endereço (Palavra-passe)", "seedPhrase": "Seed Phrase endereço", "watchAddress": "Incapaz de assinar com endereço apenas para visualização"}, "qrcode": {"sigReceived": "Assinatura recebida", "failedToGetExplain": "Falha ao obter explicação", "sigCompleted": "Transação criada", "getSig": "Obter assinatura", "misMatchSignId": "Dados da transação incongruentes. Por favor, verifique os detalhes da transação.", "txFailed": "<PERSON><PERSON><PERSON> ao criar", "unknownQRCode": "Erro: Não conseguimos identificar esse QR code", "afterSignDesc": "Após assinar, coloque o QR code no {{brand}} em frente à câmera do seu PC", "signWith": "Assine com {{brand}}", "qrcodeDesc": "Escaneie com seu {{brand}} para assinar<br></br><PERSON><PERSON><PERSON> assinar, clique no botão abaixo para receber a assinatura"}, "keystone": {"mismatchedWalletError": "Carteira incompatível", "siging": "Enviando solicitação de assinatura", "signWith": "Mude para {{method}} para assinar", "verifyPasswordError": "Falha na assinatura, por favor tente novamente após desbloquear", "shouldOpenKeystoneHomePageError": "Certifique-se de que o Keystone 3 Pro está na página inicial", "shouldRetry": "Ocorreu um erro. Por favor, tente novamente.", "unsupportedType": "Erro: O tipo de transação não é suportado ou é desconhecido.", "hardwareRejectError": "A solicitação do Keystone foi cancelada. Para continuar, por favor, reautorize.", "qrcodeDesc": "Escaneie para assinar. Após assinar, clique abaixo para obter a assinatura. Para USB, reconecte e autorize para iniciar o processo de assinatura novamente.", "misMatchSignId": "Dados da transação incongruentes. Por favor, verifique os detalhes da transação.", "txRejected": "Transação rejeitada"}, "ledger": {"resent": "<PERSON><PERSON><PERSON><PERSON>", "notConnected": "Sua carteira não está conectada. Por favor, reconecte.", "siging": "Enviando solicitação de assinatura", "signError": "Erro de assinatura do Ledger:", "txRejectedByLedger": "A transação foi rejeitada no seu Ledger", "updateFirmwareAlert": "Por favor, atualize o firmware e o aplicativo Ethereum no seu Ledger.", "resubmited": "Reenviado", "submitting": "Assinado. Criando transação", "txRejected": "Transação rejeitada", "unlockAlert": "<PERSON>r <PERSON>, conecte e desbloqueie seu Led<PERSON>, abra o Ethereum nele.", "blindSigTutorial": "Tutorial de Assinatura Cega da Ledger"}, "common": {"notSupport": "{{0}} não é suportado"}, "ignoreAll": "<PERSON><PERSON><PERSON> tudo", "connecting": "Conectando...", "ledgerNotConnected": "Ledger não está conectado", "requestFrom": "Solicitação de", "gridPlusNotConnected": "GridPlus não está conectado", "gridPlusConnected": "GridPlus está conectado", "ledgerConnected": "Ledger está conectado", "processRiskAlert": "Por favor, processe o alerta antes de assinar", "keystoneConnected": "Keystone está conectado", "keystoneNotConnected": "Keystone não está conectado", "connectButton": "Conectar", "beginSigning": "Iniciar processo de assinatura", "signAndSubmitButton": "<PERSON><PERSON><PERSON>", "cancelTransaction": "Cancelar Transação", "mainnet": "<PERSON>e Principal", "resend": "Tentar novamente", "detectedMultipleRequestsFromThisDapp": "Detectados múltiplos pedidos des<PERSON>", "cancelConnection": "<PERSON><PERSON><PERSON>", "imKeyConnected": "imKey está conectado", "imKeyNotConnected": "imKey não está conectado", "submitTx": "Enviar Transação", "cancelAll": "<PERSON><PERSON>ar to<PERSON> as {{count}} solicitações do Dapp", "cancelCurrentConnection": "<PERSON><PERSON><PERSON> a conexão atual", "testnet": "Testnet", "cancelCurrentTransaction": "Cancelar transação atual", "blockDappFromSendingRequests": "Bloquear Dapp de enviar solicitações por 1 min"}, "signTypedData": {"permit": {"title": "<PERSON><PERSON><PERSON>"}, "permit2": {"sigExpireTime": "Tempo de expiração da assinatura", "sigExpireTimeTip": "A duração para que esta assinatura seja válida on-chain", "title": "Permit2 Aprovação de Token", "approvalExpiretime": "Tempo de expiração da aprovação"}, "swapTokenOrder": {"title": "Ordem do Token"}, "sellNFT": {"title": "NFT Pedido", "listNFT": "Listar NFT", "specificBuyer": "Comprador específico", "receiveToken": "Receber token"}, "signMultiSig": {"title": "Confirmar <PERSON>"}, "createKey": {"title": "Criar Key"}, "verifyAddress": {"title": "Verificar Endereço"}, "buyNFT": {"receiveNFT": "Receber NFT", "listOn": "Lista em", "payToken": "Pagar token", "expireTime": "Tempo de expiração"}, "contractCall": {"operationDecoded": "A operação é decodificada da mensagem"}, "safeCantSignText": "Este é um endereço Safe, e não pode ser usado para assinar texto.", "signTypeDataOnChain": "<PERSON><PERSON>ar Dad<PERSON> Tipados {{chain}}", "safeCantSignTypedData": "Este é um endereço Safe, e suporta apenas assinar dados tipados EIP-712 ou strings."}, "activities": {"signedTx": {"empty": {"desc": "<PERSON><PERSON> as transaç<PERSON><PERSON> assinadas via <PERSON><PERSON> serão listadas aqui.", "title": "Nenhuma transação assinada ainda"}, "common": {"unlimited": "ilimitado", "pendingDetail": "<PERSON><PERSON><PERSON> pendente", "speedUp": "<PERSON><PERSON><PERSON>", "unknown": "Desconhecido", "unknownProtocol": "Protocolo desconhecido", "cancel": "<PERSON><PERSON><PERSON>"}, "tips": {"pendingBroadcastBtn": "Transmitir agora", "pendingBroadcastRetry": "Falha na transmissão. Última tentativa: {{pushAt}}", "pendingDetail": "Apenas uma transação será concluída, e quase sempre é a que tem o maior preço de gás.", "canNotCancel": "Não é possível acelerar ou cancelar: Não é a primeira txn pendente", "pendingBroadcastRetryBtn": "Re-broadcast", "pendingBroadcast": "Modo de economia de gás: aguardando taxas de rede mais baixas. Máx {{deadline}}h de espera."}, "status": {"failed": "Fal<PERSON>", "canceled": "Cancelado", "pending": "Pendente", "withdrawed": "Cancelamento rápido", "pendingBroadcastFailed": "Pendente: Falha na transmissão", "pendingBroadcast": "Pendente: a ser transmitido", "submitFailed": "Falha ao submeter", "pendingBroadcasted": "Pendente: transmitido"}, "txType": {"cancel": "Cancelar tx", "speedUp": "Acelere tx", "initial": "tx inicial"}, "explain": {"singleNFTApproval": "Aprovação de NFT Única para {{protocol}}", "cancelNFTCollectionApproval": "Cancelar a Aprovação da Coleção NFT para {{protocol}}", "approve": "Aprovar {{count}} {{token}} para {{protocol}}", "nftCollectionApproval": "Aprovação de Coleção NFT para {{protocol}}", "cancelSingleNFTApproval": "Cancelar Aprovação Única de NFT para {{protocol}}", "cancel": "Cancelar {{token}} Aprovar para {{protocol}}", "unknown": "Transação Desconhecida", "send": "Envie {{amount}} {{symbol}}"}, "CancelTxPopup": {"options": {"quickCancel": {"desc": "<PERSON><PERSON><PERSON> antes da transmissão, sem taxa de gás", "tips": "Apenas suportado para transações que não foram transmitidas", "title": "Cancelamento Rápido"}, "onChainCancel": {"desc": "Nova transação para cancelar, requer gas", "title": "Cancelar na cadeia"}, "removeLocalPendingTx": {"desc": "Remover a transação pendente da interface", "title": "Limpar Pendências Localmente"}}, "removeLocalPendingTx": {"title": "Excluir Transação Localmente", "desc": "Esta ação irá deletar a transação pendente localmente. A transação pendente pode ainda ser submetida com sucesso no futuro."}, "title": "Cancelar transação"}, "MempoolList": {"empty": "Não encontrado em nenhum nó", "reBroadcastBtn": "Re-broadcast", "title": "Apar<PERSON>eu em {{count}} nós RPC"}, "message": {"cancelSuccess": "Cancelado", "reBroadcastSuccess": "Re-transmitido", "deleteSuccess": "Excluído com sucesso", "broadcastSuccess": "Transmitido"}, "gas": {"noCost": "Sem custo de Gas"}, "SkipNonceAlert": {"alert": "Nonce #{{nonce}} pulado na cadeia {{chainName}}. Isso pode causar transações pendentes à frente. <5></5> <6>Enviar um tx</6> <7></7> na cadeia para resolver.", "clearPendingAlert": "{{chainName}} Transação ({{nonces}}) está pendente há mais de 3 minutos. Você pode <5></5> <6>Limpar Pendente Localmente</6> <7></7> e reenviar a transação."}, "PredictTime": {"noTime": "O tempo de embalagem está sendo previsto", "failed": "Falha na previsão do tempo de empacotamento", "time": "Previsto para ser embalado em {{time}}"}, "CancelTxConfirmPopup": {"warning": "A transação removida ainda pode ser confirmada na cadeia, a menos que seja substituída.", "title": "Limpar Pendente Localmente", "desc": "Isso removerá a transação pendente da sua interface. Você pode então iniciar uma nova transação."}, "label": "Transações"}, "signedText": {"empty": {"desc": "Todos os textos assinados via <PERSON><PERSON> serão listados aqui.", "title": "Nenhum texto assinado ainda"}, "label": "Texto"}, "title": "Registro de Assinatura"}, "receive": {"watchModeAlert1": "Este é um endereço em Modo de Observação.", "title": "Receber {{token}} na {{chain}}", "watchModeAlert2": "Você tem certeza de que deseja usá-lo para receber ativos?"}, "sendToken": {"AddToContactsModal": {"editAddr": {"placeholder": "Digite o endereço Nota", "validator__empty": "Por favor, insira uma anotação de endereço"}, "addedAsContacts": "Adicionado como contatos", "error": "Falha ao adicionar aos contatos", "editAddressNote": "Editar nota de endereço"}, "allowTransferModal": {"error": "senha incorreta", "placeholder": "Digite a <PERSON>ha para Confirmar", "validator__empty": "Por favor, insira a senha", "addWhitelist": "Adicionar à lista de permissões"}, "GasSelector": {"level": {"fast": "<PERSON><PERSON><PERSON>", "custom": "Personalizado", "normal": "<PERSON><PERSON><PERSON><PERSON>", "$unknown": "Desconhecido", "slow": "Normal"}, "confirm": "Confirmar", "popupTitle": "Definir Preço do Gás (Gwei)", "popupDesc": "O custo de gás será reservado da quantia da transferência com base no preço do gás que você definir."}, "header": {"title": "Enviar"}, "modalConfirmAddToContacts": {"confirmText": "Confirmar", "title": "Adicionar aos contatos"}, "modalConfirmAllowTransferTo": {"title": "Digite a senha para confirmar", "confirmText": "Confirmar", "cancelText": "<PERSON><PERSON><PERSON>"}, "sectionBalance": {"title": "<PERSON><PERSON>"}, "sectionChain": {"title": " cadeia"}, "sectionFrom": {"title": "De"}, "sectionTo": {"title": "Para", "searchInputPlaceholder": "Pesquisar ou inserir endereço", "addrValidator__empty": "Por favor, insira o endereço", "addrValidator__invalid": "Este endereço é inválido"}, "tokenInfoFieldLabel": {"chain": "Cadeia", "contract": "Endereço do Contrato"}, "balanceWarn": {"gasFeeReservation": "Reserva de taxa de Gas necessária"}, "balanceError": {"insufficientBalance": "<PERSON><PERSON> insuficiente"}, "sectionMsgDataForEOA": {"currentIsOriginal": "A entrada atual é Dados Originais. UTF-8 é:", "title": "Mensagem", "currentIsUTF8": "A entrada atual é UTF-8. Os dados originais são:", "placeholder": "Opcional"}, "sectionMsgDataForContract": {"title": "Chamada de contrato", "notHexData": "Apenas dados hex suportados", "parseError": "Falha ao decodificar a chamada do contrato", "simulation": "Simulação de chamada de contrato:", "placeholder": "Opcional"}, "addressNotInContract": "Não na lista de endereços. <1></1><2>Adicionar aos contatos</2>", "tokenInfoPrice": "Preço", "whitelistAlert__temporaryGranted": "Permissão temporária concedida", "sendButton": "Enviar", "whitelistAlert__whitelisted": "O endereço está na lista de permissões", "whitelistAlert__disabled": "Lista de permissões desativada. Você pode transferir para qualquer endereço.", "whitelistAlert__notWhitelisted": "O endereço não está na lista de permissões. <1 /> Concordo em conceder permissão temporária para transferência.", "max": "MÁXIMO", "blockedTransactionCancelText": "Eu sei", "blockedTransaction": "Transação Bloqueada", "blockedTransactionContent": "Esta transação interage com um endereço que está na lista de sanções da OFAC."}, "sendTokenComponents": {"SwitchReserveGas": "Reservar Gas <1 />", "GasReserved": "Reservado <1>0</1> {{ tokenName }} para custo de gás"}, "sendNFT": {"header": {"title": "Enviar"}, "sectionChain": {"title": "Cadeia"}, "sectionFrom": {"title": "De"}, "sectionTo": {"title": "Para", "searchInputPlaceholder": "Pesquisar ou digitar endereço", "addrValidator__invalid": "Este endereço é inválido", "addrValidator__empty": "Por favor, insira o endereço"}, "nftInfoFieldLabel": {"Contract": "Contrato", "Collection": "Coleção", "sendAmount": "Enviar Quantia"}, "confirmModal": {"title": "Digite a <PERSON>ha para Confirmar"}, "whitelistAlert__whitelisted": "O endereço está na lista branca", "whitelistAlert__temporaryGranted": "Permissão temporária concedida", "sendButton": "Enviar", "whitelistAlert__notWhitelisted": "O endereço não está na lista de permissões. <1 /> Concordo em conceder permissão temporária para transferir.", "tipAddToContacts": "Adicionar aos contatos", "whitelistAlert__disabled": "Lista branca desativada. Você pode transferir para qualquer endereço.", "tipNotOnAddressList": "Não na lista de endereços."}, "approvals": {"header": {"title": "Aprovações em {{ address }}"}, "tab-switch": {"contract": "Por Contratos", "assets": "<PERSON>r At<PERSON>"}, "component": {"table": {"bodyEmpty": {"noDataText": "<PERSON><PERSON>", "noMatchText": "Sem correspondência", "loadingText": "Carregando..."}}, "ApprovalContractItem": {"ApprovalCount_one": "Aprovação", "ApprovalCount_other": "Aprovações"}, "RevokeButton": {"permit2Batch": {"modalContent": "As aprovações do mesmo contrato Permit2 seriam agrupadas sob a mesma assinatura.", "modalTitle_other": "Um total de <2>{{count}}</2> assinaturas é necessário", "modalTitle_one": "Um total de <2>{{count}}</2> assinaturas é necessário"}, "btnText_other": "Revogar ({{count}})", "btnText_zero": "Revoke", "btnText_one": "Revogar ({{count}})"}, "ViewMore": {"text": "Ver mais"}}, "search": {"placeholder": "Pesquisar {{ type }} por nome/endereço"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "Contrato", "myApprovedAssets": "Meus Ativos Aprovados", "contractTrustValue": "Valor de Confiança do Contrato", "revokeTrends": "Tendências de Revogação 24h", "myApprovalTime": "Meu Tempo de Aprovação"}, "columnTip": {"contractTrustValueWarning": "O valor de confiança do contrato < $100,000", "contractTrustValueDanger": "O valor de confiança do contrato < $10.000", "contractTrustValue": "O valor de confiança refere-se ao valor total de ativos gastos por este contrato. Um valor de confiança baixo indica risco ou inatividade por 180 dias."}}, "byAssets": {"columnTitle": {"approvedAmount": "Quantidade Aprovada", "type": "Tipo", "asset": "Ativo", "approvedSpender": "<PERSON><PERSON><PERSON>", "myApprovalTime": "Meu Tempo de Aprovação"}, "columnCell": {"approvedAmount": {"tipMyBalance": "<PERSON><PERSON>", "tipApprovedAmount": "Quantidade Aprovada"}}}}, "RevokeApprovalModal": {"selectAll": "Selecionar Tudo", "confirm": "Confirmar {{ selectedCount }}", "unSelectAll": "<PERSON><PERSON><PERSON>", "title": "Aprovações", "subTitleTokenAndNFT": "Token e NFT Aprovados", "tooltipPermit2": "Esta aprovação é aprovada via contrato Permit2:  \n{{ permit2Id }}", "subTitleContract": "Aprovado para os seguintes Contratos"}, "revokeModal": {"totalRevoked": "Total:", "revoked": "Revogado:", "confirm": "Confirmar", "cancelTitle": "Cancelar Revogações Restantes", "revokeOneByOne": "Revogar um por um", "approvalCount_other": "{{count}} aprovaç<PERSON>es", "approvalCount_one": "{{count}} aprovação", "signAndStartRevoke": "<PERSON><PERSON><PERSON> e Começar a Revogar", "gasNotEnough": "Gas insuficiente para enviar", "pause": "Pausar", "approvalCount_zero": "{{count}} aprovação", "gasTooHigh": "A taxa de Gas está alta", "batchRevoke": "<PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON><PERSON><PERSON>", "cancelBody": "Se você fechar esta página, as revocações restantes não serão executadas.", "confirmTitle": "Revogar em Lote com Um Clique", "done": "<PERSON><PERSON>", "confirmRevokeLedger": "Usando um endereço Ledger, você pode revogar em lote {{count}} aprovações com 1 clique.", "confirmRevokePrivateKey": "Usando uma frase-semente ou endereço de chave privada, você pode revogar em lote {{count}} aprovações com 1 clique.", "submitTxFailed": "Falha ao Enviar", "defaultFailed": "Transação falhou", "revokeWithLedger": "Comece a Revogar com Ledger", "connectLedger": "Conectar Ledger", "simulationFailed": "Simulação Falhou", "paused": "<PERSON><PERSON><PERSON>", "waitInQueue": "Aguarde na fila", "ledgerSending": "Enviando solicitação de assinatura ({{current}}/{{total}})", "useGasAccount": "Seu saldo de gás está baixo. Sua GasAccount cobrirá as taxas de gás.", "ledgerAlert": "Por favor, abra o aplicativo Ethereum no seu dispositivo Ledger", "ledgerSigned": "Assinado. C<PERSON>do trans<PERSON> ({{current}}/{{total}})", "stillRevoke": "<PERSON><PERSON>", "ledgerSended": "Por favor, assine a solicitação no Ledger ({{current}}/{{total}})"}}, "gasTopUp": {"Value": "Valor", "Confirm": "Confirmar", "No_Tokens": "<PERSON><PERSON><PERSON>", "payment": "Pagamento de Reabastecimento de Gas", "Amount": "Quantidade", "Loading_Tokens": "Carregando Tokens...", "InsufficientBalanceTips": "<PERSON><PERSON> insuficiente", "Payment-Token": "<PERSON><PERSON>", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "topUpChain": "Recarregar a Cadeia", "InsufficientBalance": "<PERSON>ão há saldo suficiente no endereço do contrato do Rabby para a cadeia atual. Por favor, tente novamente mais tarde.", "hightGasFees": "Esse valor de recarga é muito pequeno porque a rede alvo exige altas taxas de gas.", "title": "Recarga Instantânea de Gas", "Select-from-supported-tokens": "Selecione entre os tokens suportados", "description": "Recarregue gás enviando-nos tokens disponíveis em outra cadeia. Transferência instantânea assim que seu pagamento for confirmado, sem esperar que seja irreversível.", "service-fee-tip": "Ao fornecer o serviço de recarga de Gas, a Rabby tem que suportar a perda da flutuação de tokens e a taxa de gás para a recarga. Portanto, uma taxa de serviço de 20% é cobrada.", "Including-service-fee": "Incluindo {{fee}} taxa de serviço", "Balance": "<PERSON><PERSON>", "Token": "Token", "Select-payment-token": "Selecionar token de pagamento"}, "swap": {"rabbyFee": {"title": "<PERSON><PERSON>", "wallet": "<PERSON><PERSON><PERSON>", "rate": "Taxa de pagamento", "button": "<PERSON><PERSON><PERSON>", "swapDesc": "A Rabby Wallet sempre encontrará a melhor taxa possível entre os principais agregadores e verificará a confiabilidade de suas ofertas. A Rabby cobra uma taxa de 0,25% (0% para wrapping), que está automaticamente incluída na cotação.", "bridgeDesc": "A Rabby Wallet sempre encontrará a melhor taxa possível entre os principais agregadores e verificará a confiabilidade de suas ofertas. A Rabby cobra uma taxa de 0,25%, que está automaticamente incluída na cotação."}, "lowCreditModal": {"desc": "Um baixo valor de crédito geralmente sinaliza alto risco, como um token honeypot ou liquidez muito baixa.", "title": "Este token tem um baixo valor de crédito"}, "chain": "Cadeia", "Pending": "Pendente", "approve-swap": "Aprovar e Trocar", "unlimited-allowance": "Limite ilimitado", "swap-from": "T<PERSON>car <PERSON>", "price-expired-refresh-quote": "Preço expirou. Atualize a cotação.", "swap-via-x": "<PERSON><PERSON><PERSON> via {{name}}", "get-quotes": "Obter cota<PERSON>", "title": "Trocar", "actual-slippage": "Desvio Real:", "to": "Para", "amount-in": "Quantidade em {{symbol}}", "search-by-name-address": "Pesquisar por Nome / Endereço", "from": "De", "testnet-is-not-supported": "Rede de personalização não é suportada", "no-transaction-records": "Nenhum registro de transação", "not-supported": "Não suportado", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completedTip": "Transação na cadeia, decodificando dados para gerar registro", "swap-history": "Histórico de troca", "slippage_tolerance": "Tolerância de slippage:", "approve-and-swap": "<PERSON><PERSON><PERSON> e Trocar via {{name}}", "slippage-adjusted-refresh-quote": "Slippage ajustado. Atualizar cotação.", "gas-x-price": "Preço do gás: {{price}} Gwei.", "pendingTip": "Tx enviado. Se a tx estiver pendente por muitas horas, você pode tentar limpar a pendência nas configurações.", "approve-x-symbol": "Aprovar {{symbol}}", "InSufficientTip": "Saldo insuficiente para simular transações e estimar gás. As cotações originais do agregador são exibidas.", "sort-with-gas": "Classificar com gás", "wrap-contract": "Wrap Contract", "the-following-swap-rates-are-found": "Encontradas as seguintes taxas", "minimum-received": "<PERSON>or mínimo recebido", "rates-from-cex": "Taxas de CEX", "est-payment": "<PERSON><PERSON><PERSON>:", "no-fee-for-wrap": "Sem taxa do Rabby para Wrap", "rabby-fee": "<PERSON><PERSON>", "fail-to-simulate-transaction": "Falha ao simular a transação", "insufficient-balance": "<PERSON><PERSON> insuficiente", "by-transaction-simulation-the-quote-is-valid": "Por simulação de transação, a cotação é válida", "unable-to-fetch-the-price": "Não foi possível obter o preço", "edit": "<PERSON><PERSON>", "directlySwap": "Envolvendo tokens {{symbol}} diretamente com o contrato inteligente", "enable-it": "Ativar isso", "need-to-approve-token-before-swap": "Precisa aprovar o token antes da troca", "tradingSettingTips": "{{viewCount}} exchanges oferecem cotações, e {{tradeCount}} permitem negociação", "preferMEV": "Prefer MEV Guarded", "approve-tips": "1.<PERSON><PERSON><PERSON> → 2.<PERSON><PERSON><PERSON>", "security-verification-failed": "Verificação de segurança falhou", "Gas-fee-too-high": "Taxa de gás muito alta", "there-is-no-fee-and-slippage-for-this-trade": "Não há deslizamento para esta negociação", "this-token-pair-is-not-supported": "Par de tokens não é suportado", "hidden-no-quote-rates_one": "{{count}} taxa indisponível", "hidden-no-quote-rates_other": "{{count}} taxas indisponíveis", "best": "Mel<PERSON>", "no-slippage-for-wrap": "Sem slippage para Wrap", "this-exchange-is-not-enabled-to-trade-by-you": "Esta exchange não está habilitada para ser negociada por você.", "QuoteLessWarning": "O valor recebido é estimado a partir da simulação de transação do Rabby. A oferta fornecida pelo dex é {{receive}}. Você receberá {{diff}} a menos do que a oferta esperada.", "preferMEVTip": "Ative o recurso \"MEV Guarded\" para trocas Ethereum para reduzir os riscos de ataque de sanduíche. Nota: este recurso não é suportado se você usar um RPC personalizado ou um endereço de conexão de carteira.", "fetch-best-quote": "Buscando a me<PERSON>hor cotação", "view-quotes": "Ver cotações", "est-receiving": "<PERSON><PERSON><PERSON> <PERSON><PERSON>:", "est-difference": "<PERSON><PERSON><PERSON>:", "no-fees-for-wrap": "Sem taxa do Rabby para Wrap", "exchanges": "Trocas", "two-step-approve": "Assine 2 transações para alterar a autorização", "actual": "Atual:", "enable-trading": "Ativar Negociação", "select-token": "Selecionar <PERSON>", "confirm": "Confirmar", "enable-exchanges": "Habilitar Exchanges", "process-with-two-step-approve": "Prossiga com a aprovação em duas etapas", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "A transação pode ser antecipada devido à alta tolerância ao deslizamento.", "i-understand-and-accept-it": "Eu entendo e aceito isso", "gas-fee": "GasFee: {{gasUsed}}", "estimate": "Estimar:", "rate": "Taxa", "recommend-slippage": "Para evitar front-running, recomendamos um deslizamento de <2>{{ slippage }}</2>%", "tradingSettingTip1": "1. <PERSON>a vez habilitado, você interagirá com o contrato diretamente a partir da exchange.", "tradingSettingTip2": "2. <PERSON><PERSON> n<PERSON> responsável por quaisquer riscos decorrentes do contrato das exchanges.", "max": "MÁXIMO", "dex": "<PERSON>", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Deslizamento baixo pode causar transações falhadas devido à alta volatilidade.", "cex": "Cex", "slippage-tolerance": "Tolerância de slippage", "usd-after-fees": "≈ {{usd}}", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "A oferta selecionada difere muito da taxa atual, o que pode causar grandes perdas.", "two-step-approve-details": "O token USDT requer 2 transações para mudar a autorização. Primeiro, você precisaria redefinir a autorização para zero, e só então definir o novo valor de autorização.", "source": "Fonte", "Auto": "Auto", "no-quote-found": "Nenhuma cotação encontrada", "No-available-quote": "Nenhum orçamento disponível", "loss-tips": "Você está perdendo {{usd}}. Tente um valor menor em um mercado pequeno.", "price-impact": "Impacto de Preço", "trade": "Negociar"}, "bridge": {"showMore": {"source": "Fonte da Ponte", "title": "<PERSON><PERSON> mais"}, "settingModal": {"confirmModal": {"title": "Ative a negociação com este agregador", "tip2": "2. <PERSON><PERSON> n<PERSON> responsável por quaisquer riscos decorrentes do contrato deste agregador.", "i-understand-and-accept-it": "Eu entendo e aceito isso", "tip1": "1. <PERSON>a vez ativado, você interagirá diretamente com o contrato a partir deste agregador."}, "SupportedBridge": "Ponte Suportada:", "title": "Ativar Agregadores de Ponte para negociar", "confirm": "Confirme"}, "tokenPairDrawer": {"tokenPair": "<PERSON><PERSON> <PERSON>", "title": "Selecione um par de tokens suportados", "noData": "Nenhum Par de Token Suportado", "balance": "Valor do Saldo"}, "no-quote": "Sem Cotação", "From": "De", "Balance": "Saldo:", "Pending": "Pendente", "no-transaction-records": "Nenhum registro de transação", "history": "Hist<PERSON><PERSON><PERSON> da ponte", "title": "<PERSON><PERSON>", "estimate": "Estimativa:", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "To": "Para", "Select": "Selecionar", "the-following-bridge-route-are-found": "Rota encontrada", "no-quote-found": "Nenhuma cotação encontrada. Por favor, tente outros pares de tokens.", "pendingTip": "Tx enviado. Se a tx estiver pendente por muitas horas, você pode tentar limpar pendente nas configurações.", "select-chain": "Selecionar Cadeia", "completedTip": "Transação na cadeia, decodificando dados para gerar registro", "actual": "Atual:", "insufficient-balance": "<PERSON><PERSON> insuficiente", "detail-tx": "<PERSON><PERSON><PERSON>", "BridgeTokenPair": "Bridge Token Pair", "duration": "{{duration}} min", "getRoutes": "Obter rotas", "rabby-fee": "Rabby taxa", "est-payment": "<PERSON><PERSON><PERSON>:", "best": "Mel<PERSON>", "estimated-value": "≈ {{value}}", "no-route-found": "Nenhuma rota encontrada", "Amount": "Quantidade", "gas-x-price": "Preço do gás: {{price}} Gwei.", "est-difference": "<PERSON><PERSON><PERSON>:", "need-to-approve-token-before-bridge": "É necessário aprovar o token antes da ponte", "gas-fee": "GasFee: {{gasUsed}}", "via-bridge": "via {{bridge}}", "approve-x-symbol": "Aprovar {{symbol}}", "enable-it": "Ative-o", "slippage-adjusted-refresh-quote": "<PERSON><PERSON>. Atualizar rota.", "recommendFromToken": "Bridge from <1></1> para uma cotação disponível", "est-receiving": "<PERSON><PERSON><PERSON> <PERSON><PERSON>:", "tokenPairPlaceholder": "Selecionar Par de Tokens", "approve-and-bridge": "Aprovar e Atravessar", "price-expired-refresh-route": "Preço expirado. Refresque a rota.", "aggregator-not-enabled": "Este agregador não está habilitado para ser negociado por você.", "bridge-cost": "<PERSON><PERSON><PERSON>", "bridge-via-x": "Bridge no {{name}}", "bridgeTo": "Ponte Para", "unlimited-allowance": "Moralidade ilimitada", "price-impact": "Impacto de Preço", "loss-tips": "Você está perdendo {{usd}}. Tente um valor diferente.", "max-tips": "Este valor é calculado subtraindo o custo de gás para a bridge."}, "manageAddress": {"whitelisted-address": "Endereço na lista branca", "manage-address": "Gerenciar Endereço", "address-management": "Gerenciamento de Endereços", "search": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar", "no-match": "Sem correspondência", "no-address": "Nenhum endereço", "update-balance-data": "Atualizar dados de saldo", "delete-checklist-2": "Confirmo que fiz o backup da chave privada ou da Seed Phrase e estou pronto para deletá-la agora.", "delete-desc": "<PERSON><PERSON> de <PERSON>gar, tenha em mente os seguintes pontos para entender como proteger seus ativos.", "addressTypeTip": "Importado por {{type}}", "deleted": "删除了", "delete-checklist-1": "Eu entendo que se eu excluir este endereço, a chave privada e a frase semente correspondentes a este endereço serão exclusas e a Rabby NÃO será capaz de recuperá-las.", "cancel": "<PERSON><PERSON><PERSON>", "delete-all-addresses-and-the-seed-phrase": "Excluir todos os endereços e a frase-semente", "backup-seed-phrase": "Backup Seed Phrase", "private-key": "<PERSON>ve <PERSON>", "confirm-delete": "Confirmar exclusão", "addNewAddress": "Adicionar Nov<PERSON> Endereço", "sort-address": "Ordenar Endereço", "delete-title_other": "Excluir {{count}} endereços {{brand}}", "delete-seed-phrase": "Excluir frase-semente", "enterThePassphrase": "<PERSON><PERSON><PERSON> a <PERSON>", "watch-address": "<PERSON><PERSON><PERSON>", "delete-private-key-modal-title_other": "Excluir {{count}} endereços de chave privada", "add-address": "<PERSON><PERSON><PERSON><PERSON>", "delete-title_one": "Excluir {{count}} endereço(s) {{brand}}", "delete-seed-phrase-title_one": "Delete seed phrase e seu {{count}} endereço", "seed-phrase-delete-title": "Deletar frase semente?", "delete-private-key-modal-title_one": "Excluir {{count}} endereço de chave privada", "passphraseError": "Frase secreta inválida", "delete-all-addresses-but-keep-the-seed-phrase": "Excluir todos os endereços, mas manter a frase-semente", "current-address": "Endereço Atual", "sort-by-balance": "Ordenar por saldo", "no-address-under-seed-phrase": "Você ainda não importou nenhum endereço sob esta frase semente.", "delete-empty-seed-phrase": "Excluir frase semente e seu endereço 0", "delete-seed-phrase-title_other": "Excluir a frase semente e seus {{count}} endereços", "sort-by-address-note": "Ordenar por nota de endereço", "enterPassphraseTitle": "<PERSON><PERSON>e a <PERSON> para <PERSON>", "hd-path": "HD caminho:", "seed-phrase": "Frase Semente", "sort-by-address-type": "Classificar por tipo de endereço", "CurrentDappAddress": {"desc": "Alterar o endereço do Dapp\n"}}, "dashboard": {"home": {"panel": {"swap": "Trocar", "more": "<PERSON><PERSON>", "rabbyPoints": "<PERSON><PERSON>", "mobile": "Mobile Sync", "nft": "NFT", "ecology": "Ecossistema", "receive": "<PERSON><PERSON><PERSON>", "send": "Enviar", "manageAddress": "Gerenciar Endereço", "approvals": "Aprovações", "gasTopUp": "Recarregar Gas", "feedback": "<PERSON><PERSON><PERSON>", "transactions": "Transações", "bridge": "<PERSON><PERSON>", "queue": "<PERSON><PERSON>"}, "queue": {"title": "<PERSON><PERSON>", "count": "{{count}} em"}, "offline": "A rede está desconectada e nenhum dado foi obtido.", "view": "<PERSON>er", "viewFirstOne": "<PERSON>er o primeiro", "rejectAll": "<PERSON><PERSON><PERSON><PERSON>", "transactionsNeedToSign": "transações precisam ser assinadas", "soon": "Em breve", "comingSoon": "Em breve", "whatsNew": "O que há de novo", "refreshTheWebPageToTakeEffect": "Atualize a página da web para que tenha efeito.", "flip": "<PERSON><PERSON><PERSON>", "pendingCountPlural": "{{countStr}} Pendentes", "transactionNeedsToSign": "a transação precisa ser assinada", "rabbyIsInUseAndMetamaskIsBanned": "Rabby está em uso e Metamask está banido", "pendingCount": "1 Pendente", "metamaskIsInUseAndRabbyIsBanned": "MetaMask está em uso e Rabby está banido.", "chain": "cadeia,", "chainEnd": "cadeia", "missingDataTooltip": "O saldo pode não ser atualizado devido a problemas de rede atuais com {{text}}.", "importType": "Importado por {{type}}"}, "recentConnection": {"disconnectRecentlyUsed": {"description": "DApps fixados permanecerão conectados", "title": "Desconectar <strong>{{count}}</strong> DApps usados recentemente", "title_one": "Desconectar <strong>{{count}}</strong> Dapp conectado", "title_other": "Desconectar <strong>{{count}}</strong> Dapps conectados"}, "notConnected": "Não conectado", "recentlyConnected": "Recentemente conectado", "disconnected": "Desconectado", "connected": "Conectado", "disconnectAll": "Desconectar Todos", "noPinnedDapps": "<PERSON><PERSON><PERSON> dapp fixado", "noDappFound": "<PERSON><PERSON><PERSON> encontrado", "dapps": "<PERSON><PERSON>", "noConnectedDapps": "<PERSON><PERSON><PERSON> cone<PERSON>", "rpcUnavailable": "O RPC personalizado não está disponível", "title": "<PERSON><PERSON>", "pinned": "Fixado", "noRecentlyConnectedDapps": "Nenhum <PERSON> conectado recentemente", "dragToSort": "Arraste para classificar", "connectedDapp": "Rabby não está conectado ao Dapp atual. Para se conectar, encontre e clique no botão de conexão na página da web do Dapp.", "metamaskTooltip": "Você prefere usar MetaMask com este dapp. Atualize essas configurações a qualquer momento em Configurações > Dapps Preferidos do MetaMask", "metamaskModeTooltip": "Não consegue conectar o Rabby neste Dapp? Tente ativar o <1>Modo MetaMask</1>", "metamaskModeTooltipNew": "A Rabby Wallet se conectará quando você selecionar \"MetaMask\" no Dapp. Você pode gerenciar isso em <PERSON> > Conectar Rabby disfarçando-se de MetaMask."}, "feedback": {"directMessage": {"content": "Mensagem Direta", "description": "Converse com o Rabby Wallet Official no DeBank"}, "proposal": {"content": "Proposta", "description": "Envie uma proposta para <PERSON><PERSON> Wallet no DeBank"}, "title": "<PERSON><PERSON><PERSON>"}, "nft": {"collectionList": {"collections": {"label": "Coleções"}, "all_nfts": {"label": "Todos os NFTs"}}, "modal": {"chain": "Cadeia", "lastPrice": "Último <PERSON>", "purchaseDate": "Data de Compra", "send": "Enviar", "collection": "Coleção", "sendTooltip": "Apenas NFTs ERC 721 e ERC 1155 são suportados por enquanto."}, "listEmpty": "Você ainda não recebeu nenhum NFT.", "empty": "Nenhum NFT encontrado nas Coleções suportadas"}, "rabbyBadge": {"imageLabel": "rabby badge", "rabbyValuedUserNo": "<PERSON><PERSON>{{num}}", "rabbyFreeGasUserNo": "<PERSON><PERSON> Gas Grátis No.{{num}}", "viewOnDebank": "Veja no DeBank", "goToSwap": "Vá para Swap", "viewYourClaimCode": "Veja seu código de reivindicação no DeBank", "freeGasTitle": "Reivindique o Emblema de Gas Grátis para", "noCode": "Você não ativou o código de reivindicação para este endereço", "title": "Reivindique o emblema Rabby para", "learnMoreOnDebank": "<PERSON><PERSON> mais no DeBank", "claim": "Reivindicar", "enterClaimCode": "Digite o código de reivindicação", "claimSuccess": "Reivindicação Bem-Sucedida", "freeGasTip": "Por favor, assine uma transação usando Free Gas. O botão 'Free Gas' aparecerá automaticamente quando o seu gás não for suficiente.", "freeGasNoCode": "Por favor, clique no botão abaixo para visitar DeBank e obter o código de reivindicação usando seu endereço atual primeiro.", "swapTip": "Você precisa concluir uma troca com uma dex notável dentro da <PERSON> Wallet primeiro.", "learnMore": "<PERSON><PERSON> mais"}, "contacts": {"noData": "Sem dados", "noDataLabel": "sem dados", "oldContactList": "Lista de Contatos Antiga", "oldContactListDescription": "Devido à fusão de contatos e endereços do modelo de vigilância, os antigos contatos serão salvos para você aqui e, após algum tempo, deletaremos a lista. Por favor, adicione a tempo se continuar a usar."}, "security": {"comingSoon": "Mais recursos chegando em breve", "nftApproval": "NFT Aprovação", "title": "Segurança", "tokenApproval": "Aprovação de Token"}, "settings": {"lock": {"never": "Nunca"}, "updateVersion": {"okText": "Veja o Tutorial", "title": "Atualização Disponível", "successTip": "Você está usando a versão mais recente", "content": "Uma nova atualização para a Rabby Wallet está disponível. Clique para verificar como atualizar manualmente."}, "features": {"rabbyPoints": "<PERSON><PERSON>", "gasTopUp": "Reabastecimento de Gas", "manageAddress": "Gerenciar Endereço", "lockWallet": "Bloquear Carteira", "connectedDapp": "<PERSON><PERSON>", "signatureRecord": "Registro de Assinatura", "searchDapps": "Buscar Dapps", "label": "Características"}, "settings": {"toggleThemeMode": "<PERSON><PERSON>", "label": "Configurações", "themeMode": "<PERSON><PERSON>", "customTestnet": "Adicionar Rede Personalizada", "metamaskPreferredDapps": "MetaMask Dapps Preferidos", "metamaskMode": "Conecte o Rabby disfarçado como MetaMask", "currentLanguage": "Idioma atual", "customRpc": "Modificar URL RPC", "enableWhitelistForSendingAssets": "Ativar Lista Branca Para Envio de Ativos", "enableTestnets": "Ativar Testnets", "enableDappAccount": "Alterar Endereço do Dapp Independentemente\n"}, "cancel": "<PERSON><PERSON><PERSON>", "7Days": "7 dias", "host": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Aviso", "1Day": "1 dia", "enableWhitelist": "Habilita<PERSON>list", "pendingTransactionCleared": "Transação pendente resolvida", "reset": "Restaurar configuração inicial", "autoLockTime": "Tempo de bloqueio automático", "4Hours": "4 horas", "pleaseCheckYourHost": "Por favor, verifique seu host", "backendServiceUrl": "URL do Serviço Backend", "1Hour": "1 hora", "save": "<PERSON><PERSON>", "10Minutes": "10 minutos", "clearPending": "Limpar Pendente Localmente", "inputOpenapiHost": "Por favor, insira o host do openapi", "claimRabbyBadge": "Reivindique o emblema Rabby!", "disableWhitelist": "<PERSON><PERSON><PERSON>list", "enableWhitelistTip": "Uma vez ativado, você só pode enviar ativos para os endereços na lista branca usando Rabby.", "clearPendingTip2": "Não afeta os saldos da sua conta nem requer que você reintroduza sua frase semente. Todos os ativos e detalhes da conta permanecem seguros.", "disableWhitelistTip": "Você pode enviar ativos para qualquer endereço uma vez desabilitado", "clearPendingTip1": "Esta ação remove a transação pendente da sua interface, ajudando a resolver problemas causados por longas durações de pendência na rede.", "followUs": "Siga-nos", "supportedChains": "Chains Integradas", "currentVersion": "<PERSON><PERSON><PERSON>", "testnetBackendServiceUrl": "URL do Serviço de Backend Testnet", "clearWatchMode": "Limpar <PERSON> de Vigilância", "updateAvailable": "Atualização disponível", "clearWatchAddressContent": "Você se certifique de excluir todos os endereços do Modo de Observação?", "requestDeBankTestnetGasToken": "Solicitar Token de Gás da Testnet DeBank", "clearPendingWarningTip": "A transação removida pode ainda ser confirmada on-chain, a menos que seja substituída.", "aboutUs": "Sobre nós", "DappAccount": {"title": "Alternar o endereço do Dapp Independentemente\n", "button": "Ativar\n", "desc": "Uma vez ativado, pode escolher qual endereço conectar a cada Dapp individualmente. Alterar seu endereço principal não afetará o endereço conectado a cada Dapp.\n"}}, "tokenDetail": {"txFailed": "<PERSON><PERSON><PERSON>", "scamTx": "Scam tx", "customized": "Personalizado", "noTransactions": "Sem Transações", "blocked": "Bloqueado", "customizedButton": "token personalizado", "customizedButtons": "tokens personalizados", "blockedTip": "Token bloqueado não será exibido na lista de tokens.", "selectedCustom": "O token não está listado pelo Rabby. Você o adicionou manualmente à lista de tokens.", "blockedButton": "token bloqueado", "send": "Enviar", "notSupported": "O token nesta cadeia não é suportado", "receive": "<PERSON><PERSON><PERSON>", "swap": "Trocar", "blockedButtons": "tokens bloqueados", "notSelectedCustom": "O token não está listado pelo Rabby. Ele será adicionado à lista de tokens se você ativar.", "OriginalToken": "Token Original", "blockedTips": "Tokens bloqueados não serão exibidos na lista de tokens", "verifyScamTips": "Este é um token de golpe", "SupportedExchanges": "Exchanges Suportados", "blockedListTitle": "token bloqueado", "Chain": "Cadeia", "TokenName": "Nome do Token", "blockedListTitles": "tokens bloqueados", "OriginIssue": "Nativamente emitido nesta blockchain", "myBalance": "<PERSON><PERSON>", "ListedBy": "Listados por", "IssuerWebsite": "Website do Emissor", "BridgeProvider": "<PERSON><PERSON><PERSON>", "AddToMyTokenList": "Adicionar à minha lista de tokens", "maybeScamTips": "Este é um token de baixa qualidade e pode ser um golpe", "ContractAddress": "Endereço do Contrato", "NoListedBy": "Nenhuma informação de listagem disponível", "noIssuer": "Nenhuma informação do emissor disponível", "BridgeIssue": "Token interligado por um terceiro", "customizedHasAddedTips": "O token não está listado pelo Rabby. Você o adicionou manualmente à lista de tokens.", "customizedListTitle": "token personalizado", "fdvTips": "O valor de mercado se a oferta máxima estivesse em circulação. Avaliação Total Dilutada (FDV) = Preço x Oferta Máxima. Se a Oferta Máxima for nula, FDV = Preço x Oferta Total. Se nem a Oferta Máxima nem a Oferta Total estiverem definidas ou forem infinitas, o FDV não é exibido.", "NoSupportedExchanges": "Nenhuma exchange suportada disponível", "customizedListTitles": "tokens personalizados"}, "assets": {"portfolio": {"fractionTips": "Calcule com base no preço do token ERC20 vinculado.", "nftTips": "Calculado com base no preço mínimo reconhecido por este protocolo."}, "tokenButton": {"subTitle": "Os tokens desta lista não serão adicionados ao saldo total"}, "table": {"debtRatio": "<PERSON><PERSON><PERSON> Dívida", "assetAmount": "Ativo / Quantia", "unlockAt": "Desbloquear em", "useValue": "Valor em USD", "healthRate": "Taxa de saúde", "price": "Preço", "lentAgainst": "EMPRÉSTIMO CONTRA", "pool": "POOL", "unsupportedPoolType": "Tipo de pool não suportado", "percent": "Porcentagem", "claimable": "Reivindicável", "strikePrice": "Preço de exercício", "summaryTips": "Valor do ativo dividido pelo patrimônio líquido total", "PL": "P&L", "dailyUnlock": "Desbloqueio diário", "endAt": "Terminar em", "lowValueAssets_0": "{{count}} token de baixo valor", "type": "Tipo", "noMatch": "Sem correspondência", "leverage": "Alavancar", "balanceValue": "Saldo / Valor", "token": "Token", "lowValueAssets_one": "{{count}} token de baixo valor", "exerciseEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summaryDescription": "Todos os ativos nos protocolos (por exemplo, tokens LP) são resolvidos para os ativos subjacentes para cálculos estatísticos.", "lowValueDescription": "Ativos de baixo valor serão exibidos aqui", "lowValueAssets_other": "{{count}} tokens de baixo valor", "side": "Lado", "tradePair": "Par de negociação"}, "AddMainnetToken": {"title": "<PERSON><PERSON><PERSON><PERSON>", "notFound": "Token não encontrado", "searching": "<PERSON><PERSON><PERSON>", "tokenAddress": "Endereço do Token", "tokenAddressPlaceholder": "Endereço do Token", "isBuiltInToken": "Token já suportado", "selectChain": "Selecionar cadeia"}, "AddTestnetToken": {"notFound": "Token não encontrado", "searching": "<PERSON><PERSON><PERSON><PERSON>", "selectChain": "Selecionar cadeia", "tokenAddressPlaceholder": "Endereço do Token", "title": "Ad<PERSON>onar <PERSON> de Rede Personalizada", "tokenAddress": "Endereço do Token"}, "TestnetAssetListContainer": {"addTestnet": "Rede", "add": "Token"}, "amount": "VALOR", "usdValue": "VALOR EM USD", "blockLinkText": "Pesquisar endereço para bloqueio de token", "unfoldChain": "Desdobrar 1 cadeia", "customButtonText": "Adicionar token personalizado", "customDescription": "Tokens personalizados adicionados por você serão exibidos aqui.", "blockDescription": "Os tokens bloqueados por você serão exibidos aqui.", "noAssets": "<PERSON><PERSON><PERSON> ativo", "comingSoon": "Em Breve...", "unfoldChainPlural": "Desdobrar {{more<PERSON>en}} cadeias", "searchPlaceholder": "Tokens", "addTokenEntryText": "Token", "noTestnetAssets": "Nenhum ativo de rede personalizado"}, "hd": {"ledger": {"doc2": "Digite o pin para desbloquear", "doc1": "Conecte um único Ledger", "doc3": "Abra o aplicativo Ethereum", "connected": "Ledger conectado", "reconnect": "Se não funcionar, tente <1>reconectar desde o início.</1>"}, "keystone": {"doc1": "Conecte um único Keystone", "doc3": "<PERSON><PERSON><PERSON> ao computador", "doc2": "Digite a senha para desbloquear", "title": "Certifique-se de que seu Keystone 3 Pro esteja na página inicial", "reconnect": "Se não funcionar, por favor tente <1>reconectar do início.</1>"}, "imkey": {"doc2": "Digite o pin para desbloquear", "doc1": "Conecte um único imKey"}, "howToConnectLedger": "Como Conectar o Ledger", "howToConnectKeystone": "Como Conectar Keystone", "userRejectedTheRequest": "O usuário rejeitou a solicitação.", "howToConnectImKey": "Como Conectar imKey", "ledgerIsDisconnected": "Seu ledger não está conectado.", "howToSwitch": "Como mudar"}, "GnosisWrongChainAlertBar": {"notDeployed": "Seu endereço Safe não está implantado nesta rede."}, "echologyPopup": {"title": "Ecossistema"}, "MetamaskModePopup": {"toastSuccess": "Ativado. Atualize a página para reconectar.", "title": "<PERSON><PERSON>", "footerText": "<PERSON><PERSON><PERSON> ma<PERSON> ao Modo MetaMask em Mais > Modo MetaMask", "desc": "Se você não conseguir conectar o Rabby em um Dapp, ative o Modo MetaMask e conecte-se selecionando a opção MetaMask.", "enableDesc": "Habilitar se o Dapp só funcionar com MetaMask"}, "offlineChain": {"chain": "{{chain}} em breve não será integrado.", "tips": "A cadeia {{chain}} não será integrada em {{date}}. Seus ativos não serão afetados, mas não estarão incluídos no seu saldo total. Para acessá-los, você pode adicioná-los como uma rede personalizada em \"Mais\"."}, "recentConnectionGuide": {"button": "Entendido\n", "title": "Alterar o endereço para a ligação à aplicação aqui\n"}}, "nft": {"empty": {"title": "Sem NFT Favoritado", "description": "Você pode selecionar NFT de \"Todos\" e adicionar a \"Favoritos\""}, "all": "Todos", "starred": "<PERSON><PERSON> ({{count}})", "floorPrice": "/ Preço Mínimo:", "title": "NFT", "noNft": "Sem NFT"}, "newAddress": {"addContacts": {"notAValidAddress": "Não é um endereço válido", "content": "<PERSON><PERSON><PERSON><PERSON>", "description": "Você também pode usá-lo como um endereço apenas para visualização.", "required": "Por favor, insira o endereço", "walletConnect": "Conexão da carteira", "scanViaPcCamera": "Escanear via câmera do PC", "scanViaMobileWallet": "Digitalizar via mobile wallet", "addressEns": "Endereço / ENS", "cameraTitle": "Por favor, escaneie o código QR com sua câmera", "walletConnectVPN": "WalletConnect será instável se você usar VPN.", "scanQRCode": "Digitalize QR codes com carteiras compatíveis com WalletConnect"}, "unableToImport": {"title": "Incapaz de importar", "description": "Importar várias carteiras de hardware baseadas em QR não é suportado. Por favor, exclua todos os endereços de {{0}} antes de importar outro dispositivo."}, "seedPhrase": {"whatIsASeedPhrase": {"question": "O que é uma Seed Phrase?", "answer": "Uma frase de 12, 18 ou 24 palavras usada para controlar seus ativos."}, "isItSafeToImportItInRabby": {"question": "É seguro importá-lo no Rabby?", "answer": "<PERSON><PERSON>, será armazenado localmente no seu navegador e somente acessível a você."}, "showSeedPhrase": "Mostrar Frase Semente", "importError": "[CreateMnemonics] passo inesperado {{0}}", "importTips": "Você pode colar toda a sua frase secreta de recuperação no 1º campo.", "backup": "Backup Seed Phrase", "riskTips": "<PERSON><PERSON> de começar, por favor, leia e mantenha em mente as seguintes dicas de segurança.", "importQuestion4": "Se eu desinstalar o <PERSON>bby sem fazer backup da frase-semente, o <PERSON>bby não pode recuperá-la para mim.", "clearAll": "<PERSON><PERSON> tudo", "copy": "Copiar frase semente", "verifySeedPhrase": "Verificar Frase Semente", "createdSuccessfully": "Criado com sucesso", "invalidContent": "<PERSON><PERSON><PERSON><PERSON>", "saved": "Eu salvei a frase", "verificationFailed": "Verificação falhou", "slip39SeedPhrase": "Eu tenho uma <0>{{SLIP39}}</0> Frase Semente", "passphrase": "Frase secreta", "slip39SeedPhrasePlaceholder_one": "Insira suas {{count}}ª partes da frase semente aqui", "pastedAndClear": "Colado e área de transferência limpa", "slip39SeedPhrasePlaceholder_two": "Digite suas {{count}}ª partes da frase semente aqui", "fillInTheBackupSeedPhraseInOrder": "Preencha a frase de recuperação em ordem.", "slip39SeedPhrasePlaceholder_few": "Insira suas {{count}}ª partes da frase-semente aqui", "backupTips": "Certifique-se de que ninguém mais esteja olhando para a sua tela ao fazer o backup da seed phrase.", "slip39SeedPhraseWithPassphrase": "Eu tenho uma frase-semente <0>{{SLIP39}}</0> com frase de acesso.", "pleaseSelectWords": "Por favor, selecione as palavras", "slip39SeedPhrasePlaceholder_other": "Insira suas {{count}}ª partes da frase semente aqui", "wordPhrase": "Eu tenho uma frase de <1>{{count}}</1> palavras", "inputInvalidCount_one": "1 entrada não conforma os padrões da Seed Phrase, por favor verifique.", "inputInvalidCount_other": "{{count}} entradas não estão em conformidade com as normas <PERSON>rase, por favor verifique.", "wordPhraseAndPassphrase": "Eu tenho uma frase de <1>{{count}}</1> palavras com Passphrase.", "importQuestion1": "Se eu perder ou compartilhar minha seed phrase, perderei o acesso aos meus ativos permanentemente.", "importQuestion2": "Minha frase semente é armazenada apenas no meu dispositivo. <PERSON><PERSON> não pode acessá-la.", "importQuestion3": "Se eu desinstalar o Rabby sem fazer backup da minha frase-semente, não poderá ser recuperado pelo <PERSON>."}, "metamask": {"step": "Passo", "how": "Como importar minha conta MetaMask?", "step2": "Importar a frase semente ou chave privada no Rabby", "importSeedPhrase": "Importar a frase-semente ou chave privada", "tips": "Dicas:", "importSeedPhraseTips": "Ele será armazenado apenas localmente no navegador. <PERSON>bby nunca terá acesso às suas informações privadas.", "step1": "Exporte a frase-semente ou a chave privada do MetaMask <br /> <1>Clique para ver o tutorial <1/></1>", "step3": "A importação foi concluída e todos os seus ativos irão <br /> aparecer automaticamente", "tipsDesc": "A sua frase-semente/chave privada não pertence à MetaMask ou a qualquer carteira específica; ela pertence apenas a você."}, "privateKey": {"whatIsAPrivateKey": {"answer": "Uma sequência de letras e números usada para controlar seus ativos.", "question": "O que é uma chave privada?"}, "repeatImportTips": {"desc": "Este endereço foi importado.", "question": "Você deseja mudar para este endereço?"}, "isItSafeToImportItInRabby": {"question": "É seguro importá-lo no Rabby?", "answer": "<PERSON><PERSON>, ele será armazenado localmente no seu navegador e apenas acessível a você."}, "isItPossibleToImportKeystore": {"question": "É possível importar KeyStore?", "answer": "<PERSON><PERSON>, você pode <1> importar KeyStore </1> aqui."}, "placeholder": "Digite sua chave privada", "required": "Por favor, insira a chave privada", "notAValidPrivateKey": "Não é uma chave privada válida"}, "ledger": {"error": {"running_app_close_error": "Falha ao fechar o aplicativo em execução no seu dispositivo Ledger.", "ethereum_app_unconfirmed_error": "Você negou o pedido para abrir o aplicativo Ethereum.", "ethereum_app_open_error": "Por favor, instale/aceite o aplicativo Ethereum no seu dispositivo Ledger.", "ethereum_app_not_installed_error": "Por favor, instale o aplicativo Ethereum no seu dispositivo Ledger."}, "nowYouCanReInitiateYourTransaction": "Agora você pode re-iniciar sua transação.", "permissionsAuthorized": "Permissões Autorizadas", "allow": "<PERSON><PERSON><PERSON>", "title": "Conectar Ledger", "ledgerPermission1": "Conectar a um dispositivo HID", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> que o Rabby tenha permissões para:", "cameraPermission1": "<PERSON><PERSON><PERSON> que <PERSON> acesse a câmera na janela pop-up do navegador", "cameraPermissionTitle": "<PERSON><PERSON><PERSON> que <PERSON> acesse a câmera", "ledgerPermissionTip": "Por favor, clique em \"Allow\" abaixo e autorize o acesso ao seu Ledger na seguinte janela pop-up."}, "imkey": {"title": "Conectar imKey", "imkeyPermissionTip": "Por favor, clique em \"Permitir\" abaixo e autorize o acesso ao seu imKey na seguinte janela pop-up."}, "keystone": {"title": "Conectar Keystone", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> que <PERSON> tenha permissões para:", "noDeviceFoundError": "Conecte um único Keystone", "keystonePermission1": "Conecte-se a um dispositivo USB", "deviceIsLockedError": "Insira a senha para desbloquear", "deviceRejectedExportAddress": "<PERSON><PERSON><PERSON> a<PERSON>", "exportAddressJustAllowedOnHomePage": "Exportar endereço permitido apenas na página inicial", "unknowError": "<PERSON><PERSON> desconhecido, por favor tente novamente", "deviceIsBusy": "Dispositivo está ocupado", "keystonePermissionTip": "Por favor, clique em \"Allow\" abaixo para autorizar o acesso ao seu Keystone na janela pop-up a seguir e assegure-se de que seu Keystone 3 Pro esteja na página inicial."}, "walletConnect": {"status": {"accountError": "Endereço não corresponde.", "accountErrorDesc": "Por favor, troque o endereço na sua carteira móvel", "connected": "Conectado", "received": "Scan bem-sucedido. Aguardando confirmação.", "default": "Escanear com seu {{brand}}", "brandError": "Aplicativo de carteira errado.", "rejected": "Conexão cancelada. Por favor, escaneie o código QR para tentar novamente.", "duplicate": "O endereço que você está tentando importar é duplicado.", "brandErrorDesc": "Por favor, use {{brandName}} para se conectar"}, "accountError": {}, "tip": {"accountError": {"tip2": "Por favor, mude para o endereço correto na carteira móvel.", "tip1": "<PERSON><PERSON><PERSON><PERSON>, mas incapaz de assinar."}, "disconnected": {"tip": "Não conectado ao {{brandName}}"}, "connected": {"tip": "Conectado ao {{brandName}}"}}, "button": {"disconnect": "Desconectar", "connect": "Conectar", "howToSwitch": "Como mudar"}, "disconnected": "Desconectado", "url": "URL", "qrCode": "Código QR", "title": "Conectar-se com {{brandName}}", "connectYour": "Conecte seu", "changeBridgeServer": "<PERSON><PERSON> servidor de ponte", "connectedSuccessfully": "Conectado com sucesso", "qrCodeError": "Por favor, verifique sua rede ou atualize o código QR", "viaWalletConnect": "via Wallet Connect"}, "hd": {"tooltip": {"removed": "O endereço é removido do Rabby", "connectError": "A conexão foi interrompida. Por favor, atualize a página para conectar novamente.", "added": "O endereço foi adicionado ao Rabby", "disconnected": "Não foi possível conectar à hardware wallet. Por favor, tente reconectar."}, "ledger": {"hdPathType": {"bip44": "Padrão BIP44: HDpath definido pelo protocolo BIP44. Nos primeiros 3 endereços, há endereços usados on-chain.", "ledgerLive": "Ledger Live: caminho HD oficial da Ledger. Nos primeiros 3 endereços, há endereços usados na cadeia.", "legacy": "Legado: Caminho HD usado pelo MEW / Mycrypto. Nos primeiros 3 endereços, há endereços usados na cadeia."}, "hdPathTypeNoChain": {"legacy": "Legado: caminho HD usado pela MEW / Mycrypto. Nos primeiros 3 endereços, não há endereços usados na blockchain.", "bip44": "Padrão BIP44: caminho HD definido pelo protocolo BIP44. Nos primeiros 3 endereços, não há endereços utilizados na cadeia.", "ledgerLive": "Ledger Live: Caminho HD oficial da Ledger. Nos primeiros 3 endereços, não há endereços usados na cadeia."}}, "trezor": {"hdPathType": {"bip44": "BIP44: HDpath definido pelo protocolo BIP44.", "legacy": "Legado: caminho HD usado pelo MEW / Mycrypto.", "ledgerLive": "Ledger Live: caminho HD oficial da Ledger."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: caminho HD oficial da Ledger.", "bip44": "BIP44: HDpath definido pelo protocolo BIP44.", "legacy": "Legado: caminho HD usado pelo MEW / Mycrypto."}, "message": {"disconnected": "{{0}}Connect parou. <PERSON>r favor, atualize a página para conectar novamente."}}, "onekey": {"hdPathType": {"bip44": "BIP44: HDpath definido pelo protocolo BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDpath definido pelo protocolo BIP44."}}, "mnemonic": {"hdPathType": {"legacy": "Legado: caminho HD usado pelo MEW / Mycrypto.", "default": "Padrão: O caminho HD padrão para importar uma frase semente é utilizado.", "ledgerLive": "Ledger Live: caminho HD oficial da Ledger.", "bip44": "Padrão BIP44: HDpath definido pelo protocolo BIP44."}, "hdPathTypeNoChain": {"default": "Padrão: O caminho HD padrão para importar uma frase seed é utilizado."}}, "gridplus": {"hdPathType": {"bip44": "Padrão BIP44: HDpath definido pelo protocolo BIP44. Nos primeiros 3 endereços, existem endereços utilizados on-chain.", "legacy": "Legado: caminho HD usado pelo MEW / Mycrypto. Nos primeiros 3 endereços, há endereços utilizados on-chain.", "ledgerLive": "Ledger Live: Caminho HD oficial da Ledger. Nos primeiros 3 endereços, existem endereços usados na blockchain."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: caminho HD oficial da Ledger. Nos primeiros 3 endereços, não há endereços usados na cadeia.", "legacy": "Legado: caminho HD usado pelo MEW / Mycrypto. Nos 3 primeiros endereços, não há endereços usados na cadeia.", "bip44": "Padrão BIP44: caminho HD definido pelo protocolo BIP44. Nos primeiros 3 endereços, não há endereços usados on-chain."}, "switch": {"title": "Mude para um novo dispositivo GridPlus", "content": "Não é suportado importar vários dispositivos GridPlus. Se você mudar para um novo dispositivo GridPlus, a lista de endereços do dispositivo atual será removida antes de iniciar o processo de importação."}, "switchToAnotherGridplus": "Mude para outro GridPlus"}, "keystone": {"hdPathType": {"ledgerLive": "Ledger Live: Caminho HD oficial da Ledger. Você só pode gerenciar 10 endereços com o caminho Ledger Live.", "legacy": "Legado: caminho HD utilizado pelo MEW / Mycrypto.", "bip44": "BIP44: HDpath definido pelo protocolo BIP44."}, "hdPathTypeNochain": {"legacy": "Legado: caminho HD usado pelo MEW / Mycrypto.", "bip44": "BIP44: HDpath definido pelo protocolo BIP44.", "ledgerLive": "Ledger Live: caminho HD oficial da Ledger. Você pode gerenciar apenas 10 endereços com o caminho Ledger Live."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: HDpath definido pelo protocolo BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDpath definido pelo protocolo BIP44."}, "disconnected": "Não foi possível conectar ao BitBox02. <PERSON><PERSON> favor, atualize a página para conectar novamente. Motivo: {{0}}"}, "qrCode": {"switch": {"title": "Mudar para um novo dispositivo {{0}}", "content": "Não é suportado importar múltiplos dispositivos {{0}}. Se você trocar para um novo dispositivo {{0}}, a lista de endereços do dispositivo atual será removida antes de iniciar o processo de importação."}, "switchAnother": "<PERSON><PERSON> para outro {{0}}"}, "balance": "<PERSON><PERSON>", "getOnChainInformation": "Obter informações on-chain", "hideOnChainInformation": "Esconder informações on-chain", "waiting": "Aguardando", "firstTransactionTime": "Hora da primeira transação", "notes": "Notas", "loadingAddress": "Carregando {{0}}/{{1}} endereços", "addToRabby": "Adicionar a<PERSON>", "usedChains": "Chains utilizados", "basicInformation": "Informações básicas", "clickToGetInfo": "Clique para obter as informações na cadeia", "addresses": "Endereços", "selectIndexTip": "Selecione o número de série dos endereços para começar:", "manageAddressFrom": "Gerenciar endereço de {{0}} para {{1}}", "selectHdPath": "Selecione o caminho HD:", "done": "<PERSON><PERSON>", "manageImKey": "Gerenciar imKey", "manageKeystone": "Gerenciar Keystone", "advancedSettings": "Configurações Avançadas", "connectedToLedger": "Conectado ao Ledger", "manageAirgap": "Gerenciar AirGap", "manageImtokenOffline": "Gerenciar imToken", "manageBitbox02": "Gerenciar BitBox02", "manageNgraveZero": "Gerenciar NGRAVE ZERO", "manageGridplus": "Gerenciar GridPlus", "addressesIn": "Endereços em {{0}}", "manageSeedPhrase": "Gerenciar Frase Semente", "connectedToOnekey": "Conectado ao OneKey", "importBtn": "Importar ({{count}})", "addressesInRabby": "Endereços no Rabby{{0}}", "manageCoolwallet": "Gerenciar CoolWallet", "customAddressHdPath": "Caminho HD de Endereço Personalizado", "connectedToTrezor": "Conectado ao Trezor"}, "keystore": {"password": {"placeholder": "<PERSON><PERSON>", "required": "Por favor, insira a Senha"}, "description": "Selecione o arquivo keystore que deseja importar e insira a senha correspondente."}, "coboSafe": {"addCoboArgusAddress": "Adicionar endereço Cobo Argus", "whichChainIsYourCoboAddressOn": "Em qual cadeia está seu endereço cobo?", "invalidAddress": "Endereço inválido", "inputSafeModuleAddress": "Endereço do Módulo Safe Input", "findTheAssociatedSafeAddress": "Encontre o endereço seguro associado", "import": "Importar"}, "title": "Adicionar um Endereço", "importSeedPhrase": "Importar Seed Phrase", "importPrivateKey": "Importar Chave Privada", "importMyMetamaskAccount": "Importar minha conta MetaMask", "connectHardwareWallets": "Conectar Wallets de Hardware", "connectMobileWalletApps": "Conectar Aplicativos de Carteira Móvel", "importKeystore": "Importar KeyStore", "selectImportMethod": "Selecionar Método de Importação", "firefoxLedgerDisableTips": "Ledger não é compatível com o Firefox", "createNewSeedPhrase": "Criar Nova Frase Sementes", "connectInstitutionalWallets": "Conectar Carteiras Institucionais", "theSeedPhraseIsInvalidPleaseCheck": "A frase-semente é inválida, por favor verifique!", "importedSuccessfully": "Importado com sucesso", "incorrectPassword": "senha incorreta", "importYourKeystore": "Importar Seu KeyStore", "addFromCurrentSeedPhrase": "Adicionar a partir da frase-semente atual"}, "unlock": {"btn": {"unlock": "Desb<PERSON>que<PERSON>"}, "password": {"error": "senha incorreta", "required": "Insira a senha para desbloquear", "placeholder": "Digite a senha para desbloquear"}, "title": "<PERSON><PERSON>", "btnForgotPassword": "Esque<PERSON>u a senha?", "description": "A carteira revolucionária para Ethereum e todas as cadeias EVM"}, "addToken": {"title": "Adicione um token personalizado ao Ra<PERSON>", "tokenSupported": "Token foi suportado no Rabby", "noTokenFoundOnThisChain": "Nenhum token encontrado nesta cadeia", "hasAdded": "Você foi adicionado a este token.", "tokenOnMultiChains": "Endereço do token em múltiplas cadeias. Por favor, escolha um.", "noTokenFound": "Nenhum token encontrado", "balance": "<PERSON><PERSON>", "tokenCustomized": "O token atual já foi adicionado ao personalizado.", "tokenNotFound": "Token não encontrado a partir deste endereço de contrato"}, "switchChain": {"chainId": "ID da Cadeia:", "requestsReceived": "1 solicitação recebida", "addChain": "<PERSON><PERSON><PERSON><PERSON>", "chainNotSupportAddChain": "A cadeia solicitada ainda não está integrada pelo Rabby. Você pode adicioná-la como uma Testnet personalizada.", "chainNotSupport": "A cadeia solicitada ainda não é suportada pelo Rabby.", "requestsReceivedPlural": "{{count}} solicita<PERSON><PERSON><PERSON> recebidas", "chainNotSupportYet": "A cadeia solicitada ainda não é suportada pelo Rabby.", "title": "Adicione uma Rede Personalizada ao Rabby", "requestRabbyToSupport": "Solicitar suporte do Rabby", "unknownChain": "Rede desconhecida", "testnetTip": "Por favor, ative \"Habilitar Testnets\" em \"Mais\" antes de se conectar às testnets.", "desc": "A rede solicitada ainda não está integrada pelo Rabby. Você pode adicioná-la como uma rede personalizada manualmente."}, "signText": {"createKey": {"description": "Descrição", "interactDapp": "Interagir Dapp"}, "title": "<PERSON><PERSON><PERSON>", "message": "Mensagem de Texto", "sameSafeMessageAlert": "A mesma mensagem é confirmada; nenhuma assinatura adicional é necessária."}, "securityEngine": {"viewRules": "Ver regras de segurança", "ruleDetailTitle": "Detalhe do Risco", "no": "Não", "yes": "<PERSON>m", "undo": "<PERSON><PERSON><PERSON>", "currentValueIs": "O valor atual é {{value}}", "alertTriggerReason": "Motivo do alerta acionado:", "viewRiskLevel": "Ver nível de risco", "ignoreAlert": "Ignore o alerta", "enableRule": "Ative a regra", "forbiddenCantIgnore": "Encontrou um risco proibido que não pode ser ignorado.", "whenTheValueIs": "quando o valor é {{value}}", "understandRisk": "Eu entendo e aceito a responsabilidade por qualquer perda.", "ruleDisabled": "As regras de segurança foram desativadas. Para sua segurança, você pode ativá-las a qualquer momento.", "riskProcessed": "Alerta de risco foi ignorada", "unknownResult": "Resultado desconhecido porque o motor de segurança não está disponível no momento."}, "connect": {"SignTestnetPermission": {"title": "Assinatura de permissão"}, "SelectWallet": {"title": "Selecione uma Wallet para Conectar", "desc": "Selecione entre as carteiras que você instalou"}, "flagByScamSniffer": "<PERSON><PERSON>elo <PERSON>", "myMark": "<PERSON><PERSON>", "verifiedByRabby": "Verificado por <PERSON>", "flagByRabby": "Destacado por Rabby", "sitePopularity": "Popularidade do site", "foundForbiddenRisk": "Encontrados riscos proibidos. Conexão está bloqueada.", "flagByMM": "<PERSON><PERSON> pelo MetaMask", "listedBy": "Listados por", "otherWalletBtn": "Conectar com outra carteira", "onYourWhitelist": "Na sua whitelist", "markRuleText": "<PERSON><PERSON> marcador", "ignoreAll": "<PERSON><PERSON><PERSON> tudo", "trusted": "<PERSON><PERSON><PERSON><PERSON>", "popularLevelLow": "Baixo", "noMark": "Sem marca", "popularLevelHigh": "Alta", "markAsBlockToast": "Marcar como \"Bloqueado\"", "addedToWhitelist": "Adicionado à sua lista branca", "noWebsite": "<PERSON><PERSON><PERSON>", "blocked": "Bloqueado", "title": "Conectar ao Dapp", "addedToBlacklist": "Adicionado à sua lista negra", "removedFromAll": "<PERSON><PERSON><PERSON><PERSON> as listas", "connectBtn": "Conectar", "selectChainToConnect": "Selecione uma cadeia para conectar.", "notOnAnyList": "Não está em nenhuma lista", "manageWhiteBlackList": "Gerenciar lista branca/lista negra", "onYourBlacklist": "Na sua lista negra", "popularLevelMedium": "Média", "markAsTrustToast": "Marcar como \"Confiável\"", "popularLevelVeryLow": "<PERSON><PERSON>", "markRemovedToast": "Marc<PERSON> removida", "connectAddress": "Ligar Porta\n"}, "addressDetail": {"add-to-whitelist": "Adicionar à Lista Branca", "remove-from-whitelist": "Remover da Lista Branca", "address-detail": "Detalhe do Endereço", "assets": "Ativos", "safeModuleAddress": "Endereço do Módulo Seguro", "source": "Fonte", "manage-seed-phrase": "Gerenciar Seed Phrase", "backup-private-key": "<PERSON><PERSON><PERSON>up da Chave Privada", "delete-address": "Excluir endereço", "address": "Endereço", "admins": "Administradores", "qr-code": "Código QR", "coboSafeErrorModule": "O endereço expirou, por favor delete e importe o endereço novamente.", "tx-requires": "Qualquer transação requer <2>{{num}}</2> <PERSON><PERSON><PERSON><PERSON><PERSON>", "hd-path": "Caminho HD", "backup-seed-phrase": "Backup Seed Phrase", "address-note": "Anotação de Endereço", "manage-addresses-under-this-seed-phrase": "Gere<PERSON>ie endereços sob esta Seed Phrase", "manage-addresses-under": "Gere<PERSON>ie endereços sob este {{brand}}", "edit-memo-title": "Editar nota de endereço", "please-input-address-note": "Por favor, insira a nota do endereço", "importedDelegatedAddress": "Endereço delegado importado", "delete-desc": "<PERSON><PERSON> de você deleta<PERSON>, mantenha os seguintes pontos em mente para entender como proteger seus ativos.", "direct-delete-desc": "Este endereço é um endereço {{renderBrand}}, a Rabby não armazena a chave privada ou a frase-semente para este endereço, você pode simplesmente excluí-lo."}, "preferMetamaskDapps": {"howToAddDesc": "Clique com o botão direito no site e encontre esta opção", "empty": "<PERSON><PERSON>", "title": "Dapps Preferidos do MetaMask", "howToAdd": "Como Adicionar", "desc": "<PERSON><PERSON> <PERSON><PERSON><PERSON> dapps permanecerão conectados através do MetaMask, independentemente da carteira que você alternou."}, "customRpc": {"EditRPCModal": {"invalidChainId": "Chain ID inválido", "rpcUrlPlaceholder": "Digite a URL RPC", "invalidRPCUrl": "URL RPC inválido", "title": "Modificar URL RPC", "rpcUrl": "RPC URL", "rpcAuthFailed": "Autenticação RPC falhou"}, "EditCustomTestnetModal": {"quickAdd": "Adicionar rapidamente do Chainlist", "title": "Adicionar Rede Personalizada"}, "title": "Modificar URL RPC", "opened": "Abe<PERSON>o", "closed": "<PERSON><PERSON><PERSON>", "empty": "Sem URL RPC personalizada", "add": "Modificar URL RPC", "desc": "<PERSON>a vez modificado, o RPC personalizado substituirá o nó da Rabby. Para continuar usando o nó da Ra<PERSON>, exclua o RPC personalizado."}, "requestDebankTestnetGasToken": {"requestBtn": "Solicitação", "requested": "Você solicitou hoje", "claimBadgeBtn": "Reivindique o Badge Rabby", "notMintedTip": "Solicitação disponível apenas para detentores de Rabby <PERSON>ge", "time": "Por dia", "title": "Solicitar Token de Gas da Testnet DeBank", "mintedTip": "Os detentores de Rabby Badge podem solicitar uma vez por dia"}, "safeQueue": {"action": {"cancel": "Cancelar Transação Pendente", "send": "Enviar"}, "ReplacePopup": {"options": {"send": "<PERSON><PERSON><PERSON>", "reject": "Rejeitar Transação"}, "title": "Selecione como substituir esta transação", "desc": "Uma transação assinada não pode ser removida, mas pode ser substituída por uma nova transação com o mesmo nonce."}, "title": "Fila ({{total}})", "accountSelectTitle": "Você pode enviar esta transação usando qualquer endereço.", "loading": "Carregando transações pendentes", "unknownProtocol": "Protocolo desconhecido", "cancelExplain": "Cancelar {{token}} Aprovar para {{protocol}}", "LowerNonceError": "A transação com nonce {{nonce}} precisa ser executada primeiro.", "sameNonceWarning": "Essas transações entram em conflito, pois usam o mesmo nonce. Executar uma substituirá automaticamente a outra(s).", "submitBtn": "Enviar transação", "approvalExplain": "Aprovar {{count}} {{token}} para {{protocol}}", "unknownTx": "Transação Desconhecida", "unlimited": "ilimitado", "noData": "Nenhuma transação pendente", "replaceBtn": "Substituir", "viewBtn": "<PERSON>er", "loadingFaild": "Devido à instabilidade do servidor Safe, os dados não estão disponíveis, por favor, verifique novamente após 5 minutos."}, "importSuccess": {"addressCount": "{{count}} endere<PERSON>os", "gnosisChainDesc": "Este endereço foi encontrado implantado em {{count}} cadeias", "title": "Importado com sucesso"}, "backupSeedPhrase": {"clickToShow": "Clique para mostrar a Seed Phrase", "title": "Backup Seed Phrase", "showQrCode": "Mostrar Código QR", "qrCodePopupTitle": "Código QR", "copySeedPhrase": "Copiar frase seeds", "qrCodePopupTips": "Nunca compartilhe o código QR da frase-semente com mais ninguém. Por favor, visualize-o em um ambiente seguro e mantenha-o com cuidado.", "alert": "Esta Seed Phrase é a credencial para seus ativos. NÃO a perca ou a revele a outros, caso contr<PERSON>rio, você pode perder seus ativos para sempre. Por favor, visualize-a em um ambiente seguro e mantenha-a com cuidado."}, "backupPrivateKey": {"clickToShowQr": "Clique para mostrar o código QR da chave privada", "title": "Backup Private Key", "clickToShow": "Clique para mostrar a chave privada", "alert": "Esta Chave Privada é a credencial para os seus ativos. NÃO a perca nem a revele a outros, caso contrário, você pode perder seus ativos para sempre. Por favor, visualize-a em um ambiente seguro e mantenha-a com cuidado."}, "ethSign": {"alert": "Assinar com 'eth_sign' pode levar à perda de ativos. Para a sua segurança, Rabby não suporta este método."}, "createPassword": {"title": "<PERSON><PERSON><PERSON>", "confirmRequired": "Por favor, confirme a senha", "confirmPlaceholder": "Confirme a senha", "passwordPlaceholder": "A senha deve ter pelo menos 8 caracteres.", "passwordRequired": "Por favor, insira a senha", "passwordMin": "A senha deve ter pelo menos 8 caracteres.", "confirmError": "As senhas não correspondem", "agree": "Eu li e concordo com os<1/> <2>Termos de Uso</2> e <4>Política de Privacidade</4>"}, "welcome": {"step1": {"title": "Acesse Todos os Dapps", "desc": "<PERSON><PERSON> se conecta a todas as <PERSON><PERSON> que o MetaMask suporta"}, "step2": {"btnText": "<PERSON><PERSON><PERSON>", "desc": "As chaves privadas são armazenadas localmente com acesso exclusivo a você.", "title": "Auto-custodial"}}, "importSafe": {"error": {"required": "Por favor, insira o endereço", "invalid": "Não é um endereço válido"}, "title": "Adicionar endereço Safe", "loading": "Procurando a cadeia implantada deste endereço", "gnosisChainDesc": "Este endereço foi encontrado implantado em {{count}} cadeias", "placeholder": "Por favor, insira o endereço"}, "importQrBase": {"desc": "Escaneie o código QR na carteira de hardware {{brandName}}", "btnText": "Tente Novamente"}, "pendingDetail": {"Header": {"predictTime": "Previsto para estar cheio em"}, "TxStatus": {"reBroadcastBtn": "Re-transmit", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingBroadcasted": "Pendente: Transmitido", "pendingBroadcast": "Pendente: A ser transmitido"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcasted": "Recentemente transmitido", "broadcastedCount_ordinal_other": "{{count}}ª transmissão", "created": "Transação criada", "broadcastedCount_ordinal_one": "{{count}}ª transmissão", "pending": "Verificando status...", "broadcastedCount_ordinal_few": "{{count}}ª transmissão", "broadcastedCount_ordinal_two": "{{count}}º transmissão"}, "MempoolList": {"col": {"txStatus": "Estado da transação", "nodeName": "Nome do nó", "nodeOperator": "Operador de nó"}, "txStatus": {"notFound": "Não encontrado", "appeared": "<PERSON><PERSON><PERSON><PERSON>", "appearedOnce": "Apareceu uma vez"}, "title": "Apar<PERSON>eu em {{count}} nós RPC"}, "PendingTxList": {"filterBaseFee": {"label": "Apenas atende ao requisito de taxa base", "tooltip": "Mostre apenas transações cujo Gas Price atende aos requisitos de Base fee do bloco."}, "col": {"interact": "Interagir com", "actionType": "Tipo de ação", "action": "Ação de Transação", "balanceChange": "Mudança de saldo", "gasPrice": "Preço do Gas"}, "titleSameNotFound": "Sem Classificação Igual à Atual", "titleSame": "GasPrice Classifica #{{rank}} Igual ao Atual", "titleNotFound": "Sem Classificação em Todas as Transações Pendentes", "title": "GasPrice ocupa a posição #{{rank}} em todas as Txs Pendentes"}, "Empty": {"noData": "<PERSON>enhum dado encontrado"}, "PrePackInfo": {"col": {"difference": "Verificar resultados", "prePackResults": "Pré-embalar resultados", "expectations": "Expectativas", "prePackContent": "<PERSON><PERSON><PERSON><PERSON>embalado"}, "type": {"pay": "<PERSON><PERSON>", "receive": "<PERSON><PERSON><PERSON>"}, "noLoss": "<PERSON><PERSON>huma perda encontrada", "loss": "{{lossCount}} perda encontrada", "desc": "Simulação executada no último bloco, atualizado {{time}}", "error": "{{count}} erro encontrado", "title": "Verificação Pré-embalagem", "noError": "Nenhum erro encontrado"}, "Predict": {"predictFailed": "Falha na previsão do tempo de empacotamento", "completed": "Transação Concluída", "skipNonce": "Seu endereço tem Nonce pulado na cadeia Ethereum, causando a impossibilidade de conclusão da transação atual."}}, "dappSearch": {"searchResult": {"totalDapps": "Total <2>{{count}}</2> Dapps", "foundDapps": "Encontrados <2>{{count}}</2> Dapps"}, "listBy": "O Dapp foi listado por", "emptySearch": "<PERSON><PERSON><PERSON> encontrado", "selectChain": "Selecionar Cadeia", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "expand": "Expand", "emptyFavorite": "<PERSON><PERSON><PERSON>"}, "rabbyPoints": {"claimItem": {"disabledTip": "Nenhum ponto a ser reclamado agora", "claimed": "Reivindicado", "go": "<PERSON>r", "claim": "Reivindicar", "earnTip": "Limite di<PERSON>rio uma vez. <PERSON><PERSON>, ganhe pontos após 00:00 UTC+0"}, "claimModal": {"rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "MetaMaskSwap": "MetaMask Swap", "claim": "Reivindicar", "addressBalance": "<PERSON><PERSON>eira", "walletBalance": "<PERSON><PERSON>", "title": "Reivindicar Pontos Iniciais", "placeholder": "Digite o Código de Referência para pontos extras (opcional)", "rabbyUser": "<PERSON><PERSON>", "rabbyValuedUserBadge": "Distintivo de Usuário Valorizado do Rabby", "snapshotTime": "<PERSON><PERSON> do Snapshot: {{time}}", "referral-code": "Código de Referência", "invalid-code": "c<PERSON><PERSON> in<PERSON>", "cantUseOwnCode": "Você não pode usar seu próprio código de referência.", "activeStats": "Status Ativo", "season2": "Temporada 2"}, "referralCode": {"verifyAddressModal": {"cancel": "<PERSON><PERSON><PERSON>", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "Por favor, assine esta mensagem de texto para verificar que você é o proprietário deste endereço.", "sign": "<PERSON><PERSON><PERSON>", "verify-address": "Verificar Endereço"}, "referral-code-available": "Código de referência disponível", "set-my-code": "Defina meu código", "referral-code-cannot-exceed-15-characters": "O código de referência não pode exceder 15 caracteres", "referral-code-already-exists": "O código de referência já existe", "max-15-characters-use-numbers-and-letters-only": "Máx 15 caracteres, use apenas números e letras.", "refer-a-new-user-to-get-50-points": "Indique um novo usuário para ganhar 50 pontos", "set-my-referral-code": "Definir meu código de referência", "referral-code-cannot-be-empty": "O código de referência não pode estar vazio", "my-referral-code": "Meu código de referência", "once-set-this-referral-code-is-permanent-and-cannot-change": "Uma vez definido, este código de referência é permanente e não pode ser alterado.", "confirm": "Confirmar"}, "title": "<PERSON><PERSON>", "share-on": "Compartil<PERSON> em", "top-100": "Top 100", "out-of-x-current-total-points": "Fora de {{total}} Pontos Totais Distribuídos", "earn-points": "<PERSON><PERSON><PERSON>", "referral-code-copied": "Código de referência copiado", "firstRoundEnded": "🎉 A primeira rodada de Rabby Points terminou", "code-set-successfully": "Código de referência definido com sucesso", "initialPointsClaimEnded": "Reclamação de Pontos Iniciais encerrada", "secondRoundEnded": "🎉 A segunda rodada de Rabby Points terminou"}, "customTestnet": {"CustomTestnetForm": {"name": "Nome da rede", "rpcUrl": "RPC URL", "nameRequired": "Por favor, insira o nome da rede", "id": "ID da Cadeia", "rpcUrlRequired": "Por favor, insira a URL RPC", "nativeTokenSymbol": "Símbolo da moeda", "nativeTokenSymbolRequired": "Por favor, insira o símbolo da moeda", "idRequired": "Por favor, insira o id da cadeia", "blockExplorerUrl": "URL do explorador de blocos (Opcional)"}, "AddFromChainList": {"tips": {"supported": "Chain já integrado pela Rabby Wallet", "added": "Você já adicionou esta cadeia"}, "empty": "Nenhuma cadeia encontrada", "search": "Pesquisar nome ou ID de rede personalizada", "title": "Adicionar rapidamente do Chainlist"}, "signTx": {"title": "Dados da Transação"}, "ConfirmModifyRpcModal": {"desc": "A cadeia já está integrada pelo Rabby. Você precisa modificar sua URL RPC?"}, "id": "ID", "currency": "<PERSON><PERSON>", "title": "Rede Personalizada", "add": "Adicionar Rede Personalizada", "empty": "Sem Rede Personalizada", "desc": "Rabby não pode verificar a segurança de redes personalizadas. Por favor, adicione apenas redes confiáveis."}, "addChain": {"desc": "Rabby não pode verificar a segurança de redes personalizadas. Por favor, adicione apenas redes confiáveis.", "title": "Adicionar Rede Personalizada ao Rabby"}, "sign": {"transactionSpeed": "Velocidade da Transação"}, "ecology": {"sonic": {"home": {"earnBtn": "Em breve", "airdropBtn": "Ganhe pontos", "earnTitle": "Ganhar", "earnDesc": "Aposte seu $S", "migrateTitle": "<PERSON><PERSON><PERSON>", "arcadeBtn": "<PERSON><PERSON> agora", "migrateBtn": "Em breve", "airdropDesc": "~200 milhões S para usuários no Opera e Sonic.", "airdrop": "Airdrop", "migrateDesc": "请提供需要翻译的英文文案。", "arcadeDesc": "Jogue jogos gratuitos para ganhar pontos para o S airdrop.", "socialsTitle": "Envolva-se"}, "points": {"referralCode": "Código de referência", "getReferralCode": "Obter código de referência", "sonicArcadeBtn": "Comece a jogar", "pointsDashboard": "<PERSON><PERSON>", "retry": "Tentar novamente", "pointsDashboardBtn": "Comece a ganhar pontos", "errorTitle": "Incapaz de carregar pontos", "shareOn": "Compartil<PERSON> em", "referralCodeCopied": "Código de referência copiado", "today": "Hoje", "sonicArcade": "Sonic Arcade", "errorDesc": "Houve um erro ao carregar seus pontos. Por favor, tente novamente.", "sonicPoints": "Pontos Sonic"}}, "dbk": {"home": {"bridgeBtn": "<PERSON><PERSON>", "mintNFTBtn": "Mint", "bridge": "Bridge To DBK Chain", "mintNFT": "Minte DBK Genesis NFT", "bridgePoweredBy": "Impulsionado pela OP Superchain", "mintNFTDesc": "<PERSON>ja uma testemunha da DBK Chain"}, "bridge": {"tabs": {"withdraw": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"gasFee": "Taxa de Gas", "toAddress": "Para abordar", "completeTime": "Tempo de conclusão", "receiveOn": "Re<PERSON>ber em {{chainName}}"}, "error": {"notEnoughBalance": "<PERSON><PERSON> insuficiente"}, "ActivityPopup": {"status": {"proved": "Provado", "readyToProve": "Pronto para provar", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "rootPublished": "Estado raiz publicado", "challengePeriod": "Período de contestação", "waitingToProve": "Raiz de estado publicada", "readyToClaim": "Pronto para reivindicar", "withdraw": "<PERSON><PERSON><PERSON>", "claimed": "Reclamado"}, "empty": "<PERSON><PERSON><PERSON><PERSON> atividade ainda", "withdraw": "<PERSON><PERSON><PERSON>", "claimBtn": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "proveBtn": "Comprove", "title": "Atividades"}, "WithdrawConfirmPopup": {"step1": "In<PERSON>ar re<PERSON>", "btn": "<PERSON><PERSON><PERSON>", "step2": "Prove no Ethereum", "question2": "Entendo que uma vez que um saque é iniciado, ele não pode ser acelerado ou cancelado.", "step3": "Reivindique no Ethereum", "question1": "Eu entendo que levará cerca de 7 dias até que meus fundos sejam recuperáveis no Ethereum após eu comprovar minha retirada.", "question3": "Eu entendo que as taxas de rede são aproximadas e irão mudar.", "tips": "A retirada envolve um processo de 3 etapas, exigindo 1 transação do DBK Chain e 2 transações do Ethereum.", "title": "Retirada da DBK Chain leva cerca de ~7 dias"}, "labelTo": "Para", "labelFrom": "De"}, "minNFT": {"myBalance": "<PERSON><PERSON>", "title": "DBK Genesis", "minted": "Mintado", "mintBtn": "Mint"}}}, "miniSignFooterBar": {"status": {"txCreated": "Transação criada", "txSigned": "Assinado. Criando transação", "txSending": "Enviando solicitação de assinatura", "txSendings": "Enviando solicitação de assinatura ({{current}}/{{total}})"}, "signWithLedger": "Assine com Ledger"}, "gasAccount": {"history": {"noHistory": "<PERSON><PERSON> his<PERSON>ó<PERSON>"}, "loginInTip": {"login": "Entrar no GasAccount", "desc": "<PERSON><PERSON> as Taxas de Gas em Todas as <PERSON><PERSON>", "title": "Depositar USDC / USDT", "gotIt": "<PERSON><PERSON><PERSON>"}, "loginConfirmModal": {"desc": "<PERSON>a vez confirmado, você só pode depositá-lo neste endereço.", "title": "Selecione um endereço para fazer login"}, "gasAccountList": {"address": "Endereço", "gasAccountBalance": "Saldo de Gas"}, "logoutConfirmModal": {"desc": "Sair desativa o GasAccount. Você pode restaurar seu GasAccount fazendo login com este endereço.", "title": "<PERSON><PERSON> da conta GasAccount atual", "logout": "<PERSON><PERSON>"}, "depositPopup": {"amount": "Quantidade", "token": "Token", "title": "<PERSON><PERSON><PERSON><PERSON>", "invalidAmount": "Deve ter menos de 500", "selectToken": "Selecione o Token para Depositar", "desc": "Deposite na conta DeBank L2 da Rabby sem taxas adicionais—saque a qualquer momento."}, "withdrawPopup": {"to": "Para", "amount": "Quantidade", "destinationChain": "Chain de destino", "noEligibleChain": "Nenhuma cadeia elegível para retirada", "selectChain": "Selecionar Cadeia", "selectAddr": "Selecionar Endereço", "withdrawalLimit": "Limite de retirada", "recipientAddress": "Endereço do destinatário", "selectRecipientAddress": "Selecione o endereço do destinatário", "selectDestinationChain": "Selecione a cadeia de destino", "riskMessageFromAddress": "Devido ao controle de risco, o limite de retirada depende do valor total que este endereço depositou.", "noEnoughValuetBalance": "<PERSON>do <PERSON>ault insuficiente. Altere a cadeia ou tente novamente mais tarde.", "noEligibleAddr": "Nenhum endereço elegível para retirada", "deductGasFees": "O valor recebido ir<PERSON> deduzir as taxas de gás.", "noEnoughGas": "Valor muito baixo para cobrir as taxas de gás", "riskMessageFromChain": "Devido ao controle de risco, o limite de retirada depende da quantia total depositada a partir desta cadeia.", "title": "<PERSON><PERSON><PERSON>", "desc": "Você pode retirar o saldo da sua GasAccount para a sua carteira DeBank L2. Faça login na sua carteira DeBank L2 para transferir os fundos para uma blockchain suportada, conforme necessário."}, "withdrawConfirmModal": {"button": "Veja no DeBank", "title": "Transferido para sua carteira DeBank L2"}, "GasAccountDepositTipPopup": {"title": "Abra GasAccount e Deposite", "gotIt": "<PERSON><PERSON><PERSON>"}, "switchLoginAddressBeforeDeposit": {"title": "Mude o endereço antes de depositar", "desc": "Por favor, mude para o seu endereço de login."}, "deposit": "<PERSON><PERSON><PERSON><PERSON>", "noBalance": "<PERSON>m saldo", "withdraw": "<PERSON><PERSON><PERSON>", "switchAccount": "<PERSON><PERSON>", "logout": "<PERSON><PERSON> da conta GasAccount atual", "gasExceed": "O saldo do GasAccount não pode exceder $1000", "risk": "Seu endereço atual foi detectado como arriscado, então este recurso não está disponível.", "title": "GasAccount", "safeAddressDepositTips": "Endereços multisig não são suportados para depósitos.", "withdrawDisabledIAP": "Os saques estão desabilitados porque seu saldo inclui fundos fiat, que não podem ser retirados. Entre em contato com o suporte para retirar seu saldo de tokens."}, "safeMessageQueue": {"loading": "Carregando mensagens", "noData": "Sem mensagens"}, "newUserImport": {"guide": {"importAddress": "Eu já tenho um endereço", "createNewAddress": "Crie um novo endereço", "title": "<PERSON><PERSON>-vindo ao <PERSON>", "desc": "A carteira que muda o jogo para Ethereum e todas as cadeias EVM"}, "createNewAddress": {"showSeedPhrase": "Mostrar Frase Semente", "desc": "Por favor, leia e mantenha as seguintes dicas de segurança em mente", "tip2": "Minha frase semente é armazenada apenas no meu dispositivo. <PERSON><PERSON> não pode acessá-la.", "tip1": "Se eu perder ou compartilhar minha seed phrase, perderei acesso aos meus ativos permanentemente.", "tip3": "Se eu desinstalar o Rabby sem fazer backup da minha frase semente, ela não pode ser recuperada pelo <PERSON>.", "title": "<PERSON><PERSON>"}, "importList": {"title": "Selecionar Método de Importação"}, "importPrivateKey": {"title": "Importar Chave Privada", "pasteCleared": "Colado e área de transferência limpa"}, "PasswordCard": {"form": {"password": {"label": "<PERSON><PERSON>", "min": "A senha deve ter pelo menos 8 caracteres.", "required": "Por favor, insira a senha", "placeholder": "Senha (mín<PERSON> de 8 caracteres)"}, "confirmPassword": {"required": "Por favor, confirme a senha", "label": "Confirm<PERSON> a <PERSON>", "notMatch": "As senhas não coincidem", "placeholder": "Confirmar <PERSON>"}}, "agree": "Eu concordo com os<1/> <2>Termos de Uso</2> e <4>Política de Privacidade</4>", "title": "<PERSON><PERSON><PERSON>", "desc": "Ele será usado para desbloquear a wallet e criptografar dados."}, "successful": {"import": "Importado com sucesso", "start": "<PERSON><PERSON><PERSON>", "addMoreFrom": "Adicionar mais endere<PERSON> de {{name}}", "create": "Criado com sucesso", "addMoreAddr": "Adicione mais endereços a partir desta Seed Phrase"}, "readyToUse": {"pin": "<PERSON><PERSON><PERSON>", "guides": {"step2": "<PERSON><PERSON><PERSON>", "step1": "Clique no ícone da extensão do navegador"}, "title": "Sua carteira Rabby está pronta!", "desc": "Encontre Rabby Wallet e fixe-o", "extensionTip": "Clique <1/> e depois <3/>"}, "importSeedPhrase": {"title": "Importar Frase Semente"}, "importOneKey": {"connect": "Conectar OneKey", "tip3": "3. Desbloque<PERSON> seu dispositivo", "tip2": "2. Conecte seu dispositivo OneKey", "title": "OneKey", "tip1": "1. <PERSON><PERSON><PERSON> <1>OneKey Bridge<1/>"}, "importTrezor": {"title": "<PERSON><PERSON><PERSON>", "tip2": "2. Desbloqueie seu dispositivo", "connect": "Conectar <PERSON>", "tip1": "1. Conecte seu dispositivo Trezor"}, "ImportGridPlus": {"title": "GridPlus", "tip1": "1. Abra seu dispositivo GridPlus", "tip2": "2. Conecte-se através do Lattice Connector", "connect": "Conectar GridPlus"}, "importLedger": {"title": "Ledger", "tip2": "Digite seu PIN para desbloquear.", "tip3": "Abra o aplicativo Ethereum.", "tip1": "Conecte seu dispositivo Ledger.", "connect": "Conectar Ledger"}, "importBitBox02": {"connect": "Conecte o BitBox02", "title": "BitBox02", "tip1": "1. <PERSON><PERSON>e o <1>BitBoxBridge<1/>", "tip3": "3. Desbloque<PERSON> seu dispositivo", "tip2": "2. Conecte seu BitBox02"}, "importKeystone": {"qrcode": {"desc": "Escaneie o código QR na carteira de hardware {{brandName}}"}, "usb": {"tip1": "Conecte seu dispositivo Keystone", "connect": "Conectar Keystone", "tip2": "Digite sua senha para desbloquear", "desc": "Certifique-se de que seu Keystone 3 Pro esteja na página inicial.", "tip3": "Aprovar a conexão com o seu computador"}}, "importSafe": {"error": {"required": "Por favor, insira o endereço", "invalid": "Endereço inválido"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Procurando a cadeia implantada deste endereço", "placeholder": "Insira o endereço seguro"}}, "metamaskModeDapps": {"title": "Gerenciar Dapps <PERSON>", "desc": "Modo MetaMask ativado para os seguintes Dapps. Você pode conectar o Rabby selecionando a opção MetaMask."}, "forgotPassword": {"home": {"title": "Esqueceu a Senha", "button": "Iniciar Processo de Redefinição", "description": "A Rabby Wallet não armazena sua senha e não pode ajudá-lo a recuperá-la. Redefina sua carteira para configurar uma nova.", "buttonNoData": "<PERSON><PERSON><PERSON>", "descriptionNoData": "A carteira Rabby não armazena sua senha e não pode ajudá-lo a recuperá-la. Defina uma nova senha se você a esqueceu."}, "reset": {"alert": {"privateKey": "<PERSON>ve <PERSON>", "title": "Os dados serão excluídos e irrecuperáveis:", "seed": "Frase Semente"}, "tip": {"safe": "Carteiras Seguras Importadas", "records": "Registros de Assinatura", "whitelist": "Configurações da Lista Branca", "hardware": "Carteiras de Hardware Importadas", "title": "Os dados serão mantidos:", "watch": "Contatos e Endereços Somente para Observação"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "button": "Confirmar <PERSON>efini<PERSON>", "confirm": "Digite <1>RESET</1> na caixa para confirmar e prosseguir"}, "tip": {"title": "<PERSON><PERSON>et Redefinição Concluída", "descriptionNoData": "Adicione seu endereço para começar", "description": "Crie uma nova senha para continuar", "button": "<PERSON><PERSON><PERSON>", "buttonNoData": "<PERSON><PERSON><PERSON><PERSON>"}, "success": {"title": "Senha definida com sucesso", "description": "Você está pronto para usar o <PERSON><PERSON>", "button": "Pronto"}}, "eip7702": {"alert": "EIP-7702 ainda não é suportado"}, "metamaskModeDappsGuide": {"toast": {"enabled": "Disfarce ativado. Atualize o Dapp para reconectar.", "disabled": "Disfarce desativada. Atualize o Dapp."}, "step2": "Passo 2", "manage": "Gerenciar Dapps <PERSON>", "noDappFound": "<PERSON><PERSON><PERSON> encontrado", "alert": "Não é possível conectar a um Dapp porque o Rabby Wallet não aparece como uma opção?", "title": "Conecte o Rabby disfarçando-se de MetaMask", "step1Desc": "<PERSON><PERSON><PERSON> que o Rabby se disfarce de MetaMask no Dapp atual", "step2Desc": "Atualizar e conectar via MetaMask", "step1": "Passo 1"}, "syncToMobile": {"selectAddress": {"title": "Selecionar Endereços para Sincronizar"}, "disableSelectAddress": "Sync não suportado para endereço {{type}}", "downloadGooglePlay": "Google Play", "steps1": "1. <PERSON><PERSON> o Rabby Mobile", "downloadAppleStore": "Loja de Aplicativos", "disableSelectAddressWithPassphrase": "Sync não suportado para endereço {{type}} com passphrase", "selectedLenAddressesForSync_other": "Selecionados {{len}} endereços para sincronização", "steps2": "2. Escaneie com Rabby Mobile", "clickToShowQr": "Clique para Selecionar Endereço e Mostrar Código QR", "selectedLenAddressesForSync_one": "Selecionado {{len}} endereço para sincronização", "title": "Sincronizar Endereço da Carteira da Extensão Rabby para o Móvel", "description": "Seus dados de endereço permanecem totalmente offline, criptografados e transferidos com segurança via um código QR.", "disableSelectAddressWithSlip39": "A sincronização não é suportada para endereços {{type}} com slip39", "steps2Description": "*Seu código QR contém dados sensíveis. Mantenha-o em privado e nunca o compartilhe com ninguém."}, "search": {"sectionHeader": {"token": "Tokens", "Defi": "<PERSON><PERSON><PERSON>", "NFT": "NFT", "AllChains": "<PERSON><PERSON> as <PERSON>s"}, "header": {"searchPlaceHolder": "Pesquisar Nome / Endereço do Token", "placeHolder": "Buscar"}, "tokenItem": {"Issuedby": "Emitido por", "verifyDangerTips": "Este é um token fraudulento", "listBy": "Liste por {{name}}", "FDV": "FDV", "scamWarningTips": "Este é um token de baixa qualidade e pode ser uma fraude.", "gasToken": "Token de Gas"}, "searchWeb": {"title": "Todos os Resultados", "searchTips": "Pesquisar na web", "noResults": "<PERSON><PERSON>", "noResult": "Nenhum resultado para", "searching": "Resultados para"}}}, "component": {"AccountSearchInput": {"AddressItem": {"whitelistedAddressTip": "Endereço na lista branca"}, "noMatchAddress": "Sem endereço correspondente"}, "AccountSelectDrawer": {"btn": {"cancel": "<PERSON><PERSON><PERSON>", "proceed": "Prosseguir"}}, "AddressList": {"AddressItem": {"addressTypeTip": "Importado por {{type}}"}}, "AuthenticationModal": {"passwordError": "senha incorreta", "passwordPlaceholder": "Digite a senha para confirmar", "passwordRequired": "Por favor, insira a senha"}, "ConnectStatus": {"keystoneNotConnected": "Keystone não está conectado", "gridPlusConnected": "GridPlus está conectado", "connecting": "Conectando...", "imKeyrNotConnected": "imKey não está conectado", "connect": "Conectar", "gridPlusNotConnected": "GridPlus não está conectado", "ledgerConnected": "Ledger está conectado", "keystoneConnected": "Keystone está conectado", "ledgerNotConnected": "Ledger não está conectado", "imKeyConnected": "imKey está conectado"}, "Contact": {"AddressItem": {"whitelistedTip": "Endereço na lista branca", "notWhitelisted": "Este endereço não está na lista de permissões"}, "EditModal": {"title": "Editar nota do endereço"}, "EditWhitelist": {"tip": "Selecione o endereço que você deseja colocar na lista de permissões e salve.", "backModalContent": "As alterações que você fez não serão salvas", "save": "Salvar na Lista Branca ({{count}})", "backModalTitle": "Descartar Alterações", "title": "<PERSON><PERSON>"}, "ListModal": {"authModal": {"title": "Salvar na Lista de Permissão"}, "title": "Selecionar Endereço", "whitelistUpdated": "Lista de Permissão Atualizada", "editWhitelist": "<PERSON><PERSON>", "whitelistDisabled": "A lista branca está desativada. Você pode enviar ativos para qualquer endereço.", "whitelistEnabled": "A lista branca está ativada. Você só pode enviar ativos para um endereço na lista branca ou pode desativá-la em \"Configurações\"."}}, "LoadingOverlay": {"loadingData": "Carregando dados..."}, "MultiSelectAddressList": {"imported": "<PERSON><PERSON><PERSON><PERSON>"}, "NFTNumberInput": {"erc721Tips": "Apenas um NFT de ERC 721 pode ser enviado por vez", "erc1155Tips": "<PERSON>u saldo é {{amount}}"}, "TiledSelect": {"errMsg": "A ordem da frase semente está errada, por favor verifique"}, "Uploader": {"placeholder": "Selecione um arquivo JSON"}, "WalletConnectBridgeModal": {"title": "URL do servidor Bridge", "invalidMsg": "Por favor, verifique seu host", "requiredMsg": "Por favor, insira o host do servidor de ponte", "restore": "Restaurar configurações iniciais"}, "PillsSwitch": {"NetSwitchTabs": {"testnet": "Rede Personalizada", "mainnet": "Rede Integrada"}}, "ChainSelectorModal": {"searchPlaceholder": "Pesquisar cadeia", "addTestnet": "Adicionar Rede Personalizada", "noChains": "Sem cadeias"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "ATIVO / QUANTIA"}, "price": {"title": "PREÇO"}, "usdValue": {"title": "VALOR USD"}}, "bridge": {"high": "Alta", "low": "Baixo", "value": "Valor", "liquidity": "Liquidez", "liquidityTips": "Quanto maior o volume de negócios históricos, maior a probabilidade de sucesso da ponte.", "token": "Token"}, "searchInput": {"placeholder": "Pesquisar por Nome / Endereço"}, "header": {"title": "Selecionar <PERSON>"}, "common": "Comum", "recent": "<PERSON><PERSON>", "noTokens": "<PERSON><PERSON><PERSON>", "noMatch": "Sem correspondência", "noMatchSuggestion": "Tente procurar o endereço do contrato na {{ chainName }}", "hot": "<PERSON><PERSON>", "chainNotSupport": "Esta cadeia não é suportada"}, "ModalPreviewNFTItem": {"FieldLabel": {"PurschaseDate": "Data de Compra", "LastPrice": "Último <PERSON>", "Chain": "Cadeia", "Collection": "Coleção"}}, "signPermissionCheckModal": {"title": "Você só permite que este Dapp assine em testnets", "reconnect": "Reconectar Dapp"}, "testnetCheckModal": {"title": "Por favor, ative \"Habilitar Testnets\" em \"Mais\" antes de assinar em testnets."}, "EcologyNavBar": {"providedBy": "Fornecido por {{chainName}}"}, "EcologyNoticeModal": {"title": "Aviso", "notRemind": "Não me lembre novamente", "desc": "Os seguintes serviços serão fornecidos diretamente por parceiros do Ecossistema de terceiros. A <PERSON><PERSON> assume responsabilidade pela segurança desses serviços."}, "ReserveGasPopup": {"normal": "Normal", "instant": "Instant", "fast": "<PERSON><PERSON><PERSON><PERSON>", "doNotReserve": "Não reserve Gas", "title": "Reservar Gas"}, "OpenExternalWebsiteModal": {"button": "<PERSON><PERSON><PERSON><PERSON>", "title": "Você está saindo da <PERSON>", "content": "Você está prestes a visitar um site externo. A Rabby <PERSON>et não é responsável pelo conteúdo ou pela segurança deste site."}, "TokenChart": {"holding": "<PERSON><PERSON><PERSON>", "price": "Preço"}, "externalSwapBrideDappPopup": {"viewDappOptions": "Ver Opções de Dapp", "noDapp": "<PERSON><PERSON><PERSON> disponível", "noQuotesForChain": "Ainda não há cotações disponíveis para esta cadeia", "help": "Por favor, contate a equipe oficial desta cadeia para suporte.", "thirdPartyDappToProceed": "Por favor, utilize um Dapp de terceiros para prosseguir.", "selectADapp": "Selecione um Dapp", "chainNotSupported": "Não suportado nesta cadeia", "swapOnDapp": "Trocas em aplicativos descentralizados\n", "bridgeOnDapp": "<PERSON><PERSON> no <PERSON>\n", "noDapps": "Nenhum aplicativo descentralizado disponível nesta cadeia.\n"}, "AccountSelectorModal": {"title": "Selecionar endereço\n", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>\n"}}, "global": {"ok": "OK", "scamTx": "Scam tx", "copied": "Copiado", "gas": "Gas", "back": "Voltar", "confirm": "Confirmar", "appName": "<PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "next": "Próximo", "failed": "Fal<PERSON>", "appDescription": "A carteira que muda o jogo para Ethereum e todas as cadeias EVM", "Loading": "Carregando", "Confirm": "Confirmar", "Nonce": "<PERSON><PERSON>", "Balance": "<PERSON><PERSON>", "confirmButton": "Confirmar", "Save": "<PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Done": "<PERSON><PERSON>", "unknownNFT": "NFT desconhecido", "watchModeAddress": "Endereço do Modo de Vigilância", "cancelButton": "<PERSON><PERSON><PERSON>", "proceedButton": "<PERSON><PERSON><PERSON><PERSON>", "backButton": "Voltar", "editButton": "<PERSON><PERSON>", "notSupportTesntnet": "Não suportado para rede personalizada", "assets": "ativos", "Deleted": "删除", "Clear": "Limpar", "tryAgain": "Tente Novamente", "closeButton": "<PERSON><PERSON><PERSON>", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "nonce": "nonce", "copyAddress": "Copie o endereço"}, "background": {"error": {"notFoundTxGnosisKeyring": "Nenhuma transação no Gnosis keyring encontrada", "invalidChainId": "ID de cadeia inválido", "notFoundGnosisKeyring": "<PERSON><PERSON><PERSON> keyring Gnosis encontrado", "notFindChain": "Não é possível encontrar a cadeia {{chain}}", "unknownAbi": "abi de contrato desconhecido", "noCurrentAccount": "<PERSON><PERSON><PERSON><PERSON> conta atual", "invalidAddress": "Endereço inválido", "txPushFailed": "Transação push falhou", "invalidJson": "o arquivo de entrada é inválido", "emptyAccount": "a conta atual está vazia", "addKeyring404": "falha ao adicionar keyring, keyring está indefinido", "notFoundKeyringByAddress": "Não foi possível encontrar o keyring pelo endereço", "generateCacheAliasNames": "[GenerateCacheAliasNames]: é necessário pelo menos um endereço", "invalidMnemonic": "A frase semente é inválida, por favor, verifique!", "unlock": "você precisa desbloquear a carteira primeiro", "duplicateAccount": "A conta que você está tentando importar é duplicada.", "invalidPrivateKey": "A chave privada é inválida", "canNotUnlock": "Não é possível desbloquear sem um cofre anterior"}, "transactionWatcher": {"submitted": "Transação submetida", "completed": "Transação concluída", "more": "clique para ver mais informações", "txCompleteMoreContent": "{{chain}} #{{nonce}} concluído. Clique para ver mais.", "txFailedMoreContent": "{{chain}} #{{nonce}} falhou. Clique para ver mais.", "failed": "Transação falhou"}, "alias": {"simpleKeyring": "<PERSON>ve <PERSON>", "HdKeyring": "Frase Semente", "watchAddressKeyring": "Contato"}}, "constant": {"KEYRING_TYPE_TEXT": {"WatchAddressKeyring": "Contato", "HdKeyring": "<PERSON><PERSON><PERSON> por Seed Phrase", "SimpleKeyring": "Importado por Chave Privada"}, "SIGN_PERMISSION_OPTIONS": {"TESTNET": "Apenas Testnets", "MAINNET_AND_TESTNET": "Mainnet & Testnet"}, "IMPORTED_HD_KEYRING": "<PERSON><PERSON><PERSON><PERSON> pela <PERSON>rase", "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "<PERSON><PERSON>rtado pela Fr<PERSON> Semente (Passphrase)"}}