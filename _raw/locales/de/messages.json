{"page": {"transactions": {"title": "Transaktionen", "empty": {"title": "<PERSON>ine Transaktionen", "desc": "Keine Transaktionen auf <1>unterstützten Blockchains</1> gefunden"}, "explain": {"approve": "{{amount}} {{symbol}} für {{project}} genehmigen", "unknown": "Smart Contract-Interaktion", "cancel": "Eine ausstehende Transaktion wurde abgebrochen"}, "txHistory": {"tipInputData": "Die Transaktion enthält eine Nachricht", "parseInputDataError": "Nachricht konnte nicht analysiert werden", "scamToolTip": "Diese Transaktion wird von Betrügern initiiert, um Betrugstoken und NFTs zu senden. Bitte unterlassen Sie jegliche Interaktion damit."}, "modalViewMessage": {"title": "Nachricht anzeigen"}, "filterScam": {"loading": "Das Laden kann einen Moment dauern, und Datenverzögerungen sind möglich", "title": "Betrugstransaktionen ausblenden", "btn": "Betrügerische Transaktionen ausblenden"}}, "chainList": {"title": "{{count}} chains integriert", "mainnet": "Mainnets", "testnet": "Testnets"}, "signTx": {"nftIn": "NFT eingehend", "gasLimitNotEnough": "Das Gas-Limit ist kleiner als 21000. Transaktion kann nicht gesendet werden", "gasLimitLessThanExpect": "Das Gas-Limit ist niedrig. Es besteht eine 1%ige Chance, dass die Transaktion fehlschlägt.", "gasLimitLessThanGasUsed": "Das Gas-Limit ist zu niedrig. Es besteht eine 95%ige Chance, dass die Transaktion fehlschlägt.", "nativeTokenNotEngouthForGas": "Sie haben nicht genügend Gas in Ihrer Wallet", "nonceLowerThanExpect": "<PERSON>ce ist zu niedrig, das Minimum sollte {{0}} sein", "canOnlyUseImportedAddress": "Sie können nur importierte Adressen zum Signieren verwenden", "multiSigChainNotMatch": "Multi-Signatur Adressen sind nicht auf dieser Chain verfügbar und können keine Transaktionen initiieren", "safeAddressNotSupportChain": "Die aktuelle Safe-Adresse wird auf der {{0}}-Chain nicht unterstützt", "noGasRequired": "<PERSON><PERSON>", "gasSelectorTitle": "Gas", "failToFetchGasCost": "Fehler beim Abrufen der Gaskosten", "gasMoreButton": "<PERSON><PERSON>", "manuallySetGasLimitAlert": "Sie haben das Gas-Limit manuell eingestellt auf", "gasNotRequireForSafeTransaction": "Für Safe-Transaktionen ist keine Gasgebühr erforderlich", "gasPriceTitle": "Gaspreis (Gwei)", "maxPriorityFee": "Maximale Prioritätsgebühr (Gwei)", "eip1559Desc1": "<PERSON>i Blockchains, die EIP-1559 un<PERSON><PERSON><PERSON><PERSON><PERSON>, ist die Prioritätsgebühr das Trinkgeld für die Miner, um Ihre Transaktion zu bearbeiten. Sie können Ihre Gaskosten senken, indem Sie die Prioritätsgebühr verringern, was jedoch eine längere Bearbeitungszeit für Ihre Transaktion zur Folge haben kann.", "eip1559Desc2": "Hier bei Rabby ist die Prioritätsgebühr (Trinkgeld) = <PERSON> - Basisgebühr. Nachdem Sie die maximale Prioritätsgebühr festgelegt haben, wird die Basisgebühr davon abgezogen und der Rest wird den Minern als Trinkgeld gegeben.", "hardwareSupport1559Alert": "<PERSON><PERSON><PERSON>, dass Ihre Hardware-Wallet-Firmware auf die Version aktualisiert wurde, die EIP 1559 unterstützt", "gasLimitTitle": "Gas-Limit", "recommendGasLimitTip": "Geschätzt: {{est}}. Aktuell {{current}}x, em<PERSON><PERSON>len ", "nonceTitle": "<PERSON><PERSON>", "gasLimitModifyOnlyNecessaryAlert": "<PERSON>ur bei <PERSON><PERSON><PERSON>", "gasPriceMedian": "Median der letzten 100 On-Chain-Transaktionen: ", "myNativeTokenBalance": "Mein Gas Guthaben: ", "gasLimitEmptyAlert": "Bitte geben Sie das Gas-Limit ein", "gasLimitMinValueAlert": "Das Gas-Limit sollte mehr als 21000 betragen", "balanceChange": {"successTitle": "Transaktionssimulations-Ergebnisse", "failedTitle": "Transaktionssimulation fehlgeschlagen", "noBalanceChange": "<PERSON><PERSON>", "tokenOut": "Token abgezogen", "tokenIn": "<PERSON><PERSON> hinz<PERSON>gt", "errorTitle": "Fehler beim Abrufen der Saldoänderung", "notSupport": "Transaktionssimulation wird nicht unterstützt", "nftOut": "NFT entfernt"}, "enoughSafeSigCollected": "Ausreichend Signaturen gesammelt", "moreSafeSigNeeded": "{{0}} weitere Signaturen erforderlich", "safeAdminSigned": "<PERSON><PERSON><PERSON>", "swap": {"title": "<PERSON><PERSON> tauschen", "payToken": "<PERSON><PERSON><PERSON><PERSON>", "receiveToken": "Empfangen", "failLoadReceiveToken": "Laden fehlgeschlagen", "valueDiff": "Wertdifferenz", "simulationFailed": "Transaktionssimulation fehlgeschlagen", "simulationNotSupport": "Transaktionssimulation wird für diese Blockchain nicht unterstützt", "minReceive": "Mindestempfang", "slippageFailToLoad": "Kursabweichungs-Toleranz kann nicht geladen werden", "slippageTolerance": "Kursabweichungs-Toleranz", "receiver": "<PERSON><PERSON><PERSON><PERSON>", "notPaymentAddress": "Nicht die Zahlungsadresse", "unknownAddress": "Unbekannte Adresse"}, "crossChain": {"title": "Cross Chain Übertragung"}, "swapAndCross": {"title": "Token tauschen und Cross Chain übertragen"}, "wrapToken": "<PERSON><PERSON>", "unwrap": "Unwrap Token", "send": {"title": "Token senden", "sendToken": "Token senden", "sendTo": "Senden an", "receiverIsTokenAddress": "<PERSON><PERSON>", "contractNotOnThisChain": "Smart Contract auf dieser Chain nicht verfügbar", "notTopupAddress": "<PERSON><PERSON>", "tokenNotSupport": "{{0}} wird nicht unterstützt", "onMyWhitelist": "<PERSON><PERSON> <PERSON>", "notOnThisChain": "Nicht auf dieser Chain", "cexAddress": "CEX Adresse", "addressBalanceTitle": "<PERSON><PERSON><PERSON><PERSON>", "whitelistTitle": "Whitelist", "notOnWhitelist": "<PERSON>cht auf meiner Whitelist", "fromMySeedPhrase": "<PERSON> Seed-Phrase", "fromMyPrivateKey": "<PERSON> private key", "scamAddress": "Betrugsadresse"}, "tokenApprove": {"title": "To<PERSON>", "approveToken": "Token genehmigen", "myBalance": "<PERSON><PERSON>", "approveTo": "Genehmigen für", "eoaAddress": "EOA Adresse", "trustValueLessThan": "Vertrauenswert ≤ {{value}}", "deployTimeLessThan": "Bereitstellungszeit < {{value}} Tage", "amountPopupTitle": "Betrag", "flagByRabby": "<PERSON><PERSON><PERSON>", "contractTrustValueTip": "Der Vertrauenswert bezieht sich auf den gesamten genehmigten und diesem Smart Contract ausgesetzten Vermögenswert. Ein niedriger Vertrauenswert deutet auf ein Risiko oder aber eine Inaktivität von 180 Tagen oder mehr hin.", "amount": "Genehmigungsbetrag:", "exceed": "Übersteigt Ihr aktuelles Guthaben"}, "revokeTokenApprove": {"title": "Token-Genehmigung widerrufen", "revokeFrom": "<PERSON><PERSON><PERSON><PERSON> von", "revokeToken": "Token widerrufen"}, "sendNFT": {"title": "NFT senden", "nftNotSupport": "NFT wird nicht unterstützt"}, "nftApprove": {"title": "NFT-Genehmigung", "approveNFT": "NFT genehmigen", "nftContractTrustValueTip": "Der Vertrauenswert bezieht sich auf den am höchsten bewerteten NFT, der diesem Smart Contract ausgesetzt ist. Ein niedriger Vertrauenswert deutet auf ein Risiko oder aber eine Inaktivität von 180 Tagen oder mehr hin."}, "revokeNFTApprove": {"title": "NFT-Genehmigung widerrufen", "revokeNFT": "NFT widerrufen"}, "nftCollectionApprove": {"title": "Genehmigung für NFT-Sammlung", "approveCollection": "<PERSON><PERSON><PERSON>"}, "revokeNFTCollectionApprove": {"title": "Genehmigung für NFT-Sammlung widerrufen", "revokeCollection": "Sammlungs-Genehmigung widerrufen"}, "deployContract": {"title": "Einen Smart Contract bereitstellen", "descriptionTitle": "Beschreibung", "description": "<PERSON>e stellen einen Smart Contract bereit"}, "cancelTx": {"title": "Ausstehende Transaktion abbrechen", "txToBeCanceled": "Abzubrechende Transaktion", "gasPriceAlert": "Legen Sie den aktuellen Gaspreis höher als {{value}} Gwei fest, um die ausstehende Transaktion abzubrechen"}, "submitMultisig": {"title": "Multisig-Transaktion einreichen", "multisigAddress": "Multisig-Adresse"}, "contractCall": {"title": "Smart Contract aufrufen", "operation": "Operation", "operationABIDesc": "Die Operation wird aus dem ABI dekodiert", "operationCantDecode": "Operation kann nicht dekodiert werden", "payNativeToken": "<PERSON><PERSON><PERSON><PERSON> {{symbol}}", "suspectedReceiver": "Ausnahmeadresse", "receiver": "Empfängeradresse"}, "revokePermit2": {"title": "Permit2-Token Genehmigung widerrufen"}, "unknownAction": "Unbekannt", "interactContract": "Mit Smart Contract interagieren", "markAsTrust": "Als vertrauenswürdig markiert", "markAsBlock": "Als blockiert markiert", "interacted": "<PERSON><PERSON><PERSON>", "neverInteracted": "<PERSON><PERSON> zuvor interagiert", "transacted": "<PERSON><PERSON><PERSON>", "neverTransacted": "<PERSON><PERSON> zu<PERSON>", "fakeTokenAlert": "<PERSON>s ist ein Betrugstoken, mark<PERSON><PERSON>", "scamTokenAlert": "Dies ist potenziell ein minderwertiger und betrügerischer Token basierend auf Rabbys Erkennung", "trusted": "<PERSON><PERSON><PERSON><PERSON>", "blocked": "<PERSON><PERSON><PERSON>", "noMark": "<PERSON><PERSON>", "markRemoved": "Markierung entfernt", "speedUpTooltip": "Die beschleunigte Transaktion und die ursprüngliche Transaktion, nur eine davon wird abgeschloßen werden.", "signTransactionOnChain": "Signiere {{chain}} Transaktion", "viewRaw": "<PERSON><PERSON><PERSON>n anzeigen", "unknownActionType": "Unbekannter Aktionstyp", "sigCantDecode": "Diese Signatur kann von <PERSON> nicht dekodiert werden", "nftCollection": "NFT Sammlung", "floorPrice": "Mindestpreis (Floor)", "contractAddress": "Adresse des Smart Contracts", "protocolTitle": "Protokoll", "deployTimeTitle": "Bereitstellungszeit", "popularity": "Beliebtheit", "contractPopularity": "Nr. {{0}} auf {{1}}", "addressNote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myMarkWithContract": "<PERSON><PERSON> zu {{chainName}} Smart Contract", "myMark": "<PERSON><PERSON>", "collectionTitle": "<PERSON><PERSON><PERSON>", "addressTypeTitle": "<PERSON><PERSON><PERSON><PERSON>", "firstOnChain": "Erstmals auf der Chain", "trustValue": "Vertrauenswert", "importedDelegatedAddress": "Importierte delegierte Adresse", "noDelegatedAddress": "Keine importierte delegierte Adresse", "coboSafeNotPermission": "Diese delegierte Adresse hat keine Berechtigung, diese Transaktion zu starten", "SafeNonceSelector": {"explain": {"contractCall": "Smart Contract-Interaktion", "send": "Token senden", "unknown": "Unbekannte Transaktion"}, "optionGroup": {"recommendTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replaceTitle": "Ersetze die Transaktion in der Warteschlange"}, "option": {"new": "Neue Transaktion"}, "error": {"pendingList": "Fehler beim Laden der ausstehenden Transaktionen, <1/><2>E<PERSON>ut versuchen</2>"}}, "gasAccount": {"totalCost": "Gesamtkosten:", "estimatedGas": "Geschätztes Gas: ", "currentTxCost": "Die Gas-Menge, die an Ihre Adresse gesendet wurde:", "maxGas": "Max Gas: ", "gasCost": "Gaskosten für die Übertragung von Gas an Ihre Adresse:", "sendGas": "Der Gastransfer für die aktuelle Transaktion an Sie:"}, "customRPCErrorModal": {"content": "Ihr benutzerdefinierter RPC ist derzeit nicht verfügbar. Sie können ihn deaktivieren und weiterhin mit Rabby's offiziellem RPC signieren.", "button": "Deaktivieren benutzerdefinierter RPC", "title": "Benutzerdefinierter RPC-Fehler"}, "transferOwner": {"transferTo": "Übertragen an", "description": "Beschreibung", "title": "Übertragung des Vermögensbesitzes"}, "swapLimitPay": {"title": "Swap-Token-Limit-Zahlung", "maxPay": "Maximaler Betrag"}, "batchRevokePermit2": {"title": "Sammelwiderruf der Permit2-Zulassung"}, "revokePermit": {"title": "Genehmigung für Genehmigung von Token widerrufen"}, "assetOrder": {"receiveAsset": "<PERSON><PERSON> empfangen", "title": "Vermögensreihenfolge", "listAsset": "<PERSON><PERSON> auflisten"}, "BroadcastMode": {"instant": {"desc": "Transaktionen werden sofort an das Netzwerk gesendet.", "title": "Sofort"}, "lowGas": {"title": "Gas-sparend", "desc": "Transaktionen werden gesendet, wenn die Netzwerk-Gebühr niedrig ist."}, "mev": {"desc": "Transaktionen werden an den bezeichneten MEV-Knoten gesendet", "title": "MEV Guarded"}, "tips": {"notSupportChain": "<PERSON><PERSON> dieser Chain nicht unterstützt", "walletConnect": "Von WalletConnect nicht unterstützt", "notSupported": "<PERSON>cht unterstützt", "customRPC": "Bei Verwendung von benutzerdefiniertem RPC nicht unterstützt"}, "lowGasDeadline": {"1h": "1h", "4h": "4h", "label": "Zeitüberschreitung", "24h": "24h"}, "title": "Broadcast-Modus"}, "coboSafeCreate": {"safeWalletTitle": "Safe{Wallet}", "descriptionTitle": "Beschreibung", "title": "Cobo Safe erstellen"}, "coboSafeModificationRole": {"title": "Rollenänderung sicher einreichen", "safeWalletTitle": "<PERSON><PERSON>{Wallet}", "descriptionTitle": "Beschreibung"}, "coboSafeModificationDelegatedAddress": {"title": "Delegierte Adressänderung einreichen", "descriptionTitle": "Beschreibung", "safeWalletTitle": "<PERSON><PERSON>{Wallet}"}, "coboSafeModificationTokenApproval": {"title": "Token Approval Modification senden", "descriptionTitle": "Beschreibung", "safeWalletTitle": "<PERSON><PERSON>{Wallet}"}, "common": {"description": "Beschreibung", "descTipWarningAssets": "Unterschrift kann Asset-Änderung verursachen", "interactContract": "Mit Vertrag interagieren", "descTipSafe": "Die Signatur führt nicht zu einer Vermögensänderung oder verifiziert den Adressbesitz.", "descTipWarningPrivacy": "Die Signatur kann den Adressbesitz verifizieren.", "descTipWarningBoth": "Die Signatur kann eine Änderung der Vermögenswerte verursachen und den Adressbesitz verifizieren."}, "nativeTokenForGas": "Verwenden Sie den {{tokenName}}-Token auf {{chainName}}, um <PERSON> zu bezahlen", "gasAccountForGas": "Verwende USD von meinem GasAccount, um Gas zu bezahlen", "decodedTooltip": "Diese Signatur wird vom Rabby Wallet decodiert.", "chain": "Chain", "l2GasEstimateTooltip": "Die Schätzung des Gasverbrauchs für die L2-Kette umfasst nicht die L1-Gebühr. Die tatsächliche Gebühr wird höher sein als die aktuelle Schätzung.", "importedAddress": "Importierte Adresse", "typedDataMessage": "Typed-Daten signieren", "hasInteraction": "<PERSON><PERSON><PERSON>", "contract": "Smart contract", "no": "<PERSON><PERSON>", "advancedSettings": "Erweiterte Einstellungen", "yes": "<PERSON>a", "label": "Label", "trustValueTitle": "Vertrauenswert", "protocol": "Protokoll", "addressSource": "Address Source", "amount": "Betrag", "address": "<PERSON><PERSON><PERSON>", "maxPriorityFeeDisabledAlert": "Bitte zuerst den Gaspreis festlegen", "primaryType": "Primärtyp", "safeServiceNotAvailable": "Der sichere Dienst ist derzeit nicht verfügbar, bitte versuchen Si<PERSON> es später erneut.", "safeTx": {"selfHostConfirm": {"button": "OK", "title": "Wechseln Sie zum Rabby Safe Service", "content": "Safe API ist nicht verfügbar. Wechseln Sie zum von Rabby bereitgestellten Safe-Dienst, um Ihren Safe funktionsfähig zu halten. <strong>Alle Safe-Signierer müssen Rabby Wallet verwenden, um Transaktionen zu autorisieren.<strong>"}}}, "signFooterBar": {"requestFrom": "<PERSON><PERSON><PERSON> <PERSON>", "processRiskAlert": "Bitte bearbeiten Sie zuerst den Risiko-Hinweis", "ignoreAll": "Alle ignorieren", "gridPlusConnected": "GridPlus ist verbunden", "gridPlusNotConnected": "GridPlus ist nicht verbunden", "connectButton": "Verbinden", "connecting": "Verbindung wird hergestellt...", "ledgerNotConnected": "Ledger ist nicht verbunden", "ledgerConnected": "Ledger ist verbunden", "signAndSubmitButton": "Signieren und absenden", "walletConnect": {"connectedButCantSign": "Verbunden, kann aber nicht signieren.", "switchToCorrectAddress": "Bitte wechseln Sie in der mobilen Wallet zur richtigen Adresse", "switchChainAlert": "Bitte wechseln Sie in der mobilen Wallet zu {{chain}}", "notConnectToMobile": "Nicht verbunden mit {{brand}}", "connected": "Verbunden und bereit zum Signieren", "howToSwitch": "So wechseln Sie", "wrongAddressAlert": "<PERSON><PERSON> haben in der mobilen Wallet eine andere Adresse ausgewählt. Bitte wechseln Sie in der mobilen Wallet zur richtigen Adresse", "connectBeforeSign": "{{0}} ist nicht mit Rabby verbunden, bitte vor dem Signieren verbinden", "chainSwitched": "Sie haben in der mobilen Wallet eine andere Blockchain ausgewählt. Bitte wechseln Sie zu {{0}} in der mobilen Wallet", "latency": "<PERSON><PERSON>", "requestSuccessToast": "Anfrage erfolgreich gesendet", "sendingRequest": "Signaturanfrage wird gesendet", "signOnYourMobileWallet": "Bitte signieren Sie auf Ihrer mobilen Wallet.", "requestFailedToSend": "Signaturanfrage konnte nicht gesendet werden"}, "beginSigning": "Signaturprozess beginnen", "addressTip": {"onekey": "OneKey-<PERSON><PERSON><PERSON>", "trezor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bitbox": "BitBox02-<PERSON><PERSON><PERSON>", "keystone": "Keystone-<PERSON><PERSON><PERSON>", "airgap": "AirGap-Adresse", "coolwallet": "CoolWallet-<PERSON><PERSON><PERSON>", "privateKey": "Private Key-<PERSON><PERSON><PERSON>", "seedPhrase": "Seed Phrase-<PERSON><PERSON><PERSON>", "watchAddress": "Signatur mit reiner Beobachtungsadresse nicht möglich", "safe": "Safe-<PERSON><PERSON><PERSON>", "coboSafe": "Cobo Argus Adresse", "seedPhraseWithPassphrase": "Seed Phrase Adresse (Passphrase)"}, "qrcode": {"signWith": "Signieren mit {{brand}}", "failedToGetExplain": "Erklärung konnte nicht abgerufen werden", "txFailed": "Einreichen fehlgeschlagen", "sigReceived": "Signatur erhalten", "sigCompleted": "Transaktion eingereicht", "getSig": "Signatur holen", "qrcodeDesc": "<PERSON>anne<PERSON> Sie mit Ihrem {{brand}} zum Signieren<br></br><PERSON>ch dem Signieren, klicken Sie auf den untenstehenden Button, um die Signatur zu empfangen", "misMatchSignId": "Inkongruente Transaktionsdaten. Bitte überprüfen Sie die Transaktionsdetails.", "unknownQRCode": "Fehler: <PERSON>sen QR-Code konnten wir nicht erkennen", "afterSignDesc": "Nach dem Signieren, halten Sie den QR-Code auf Ihrem {{brand}} vor die Kamera Ihres PCs"}, "ledger": {"resent": "<PERSON><PERSON><PERSON> gesendet", "signError": "Ledger Signierungsfehler:", "notConnected": "<PERSON>hre Wallet ist nicht verbunden. Bitte erneut verbinden.", "siging": "Signaturanfrage wird gesendet", "txRejected": "Transaktion abgelehnt", "unlockAlert": "Bitte schließen Sie Ihren Ledger an und entsperren sie ihn, öffnen Sie auf dem Ledger die Ethereum-App", "updateFirmwareAlert": "Bitte aktualisieren Sie die Firmware und die Ethereum App auf Ihrem Ledger", "txRejectedByLedger": "Transaktion wurde auf Ihrem Ledger abgelehnt", "blindSigTutorial": "Anleitung für Blindsignatur von Ledger", "submitting": "Signiert. Transaktion wird eingereicht", "resubmited": "Erneut eingereicht"}, "common": {"notSupport": "{{0}} wird nicht unterstützt"}, "resend": "<PERSON><PERSON><PERSON> versuchen", "submitTx": "Transaktion einreichen", "testnet": "Testnet", "mainnet": "Mainnet", "cancelTransaction": "Transaktion abbrechen", "detectedMultipleRequestsFromThisDapp": "<PERSON><PERSON><PERSON> Anfra<PERSON> von dieser <PERSON>", "cancelCurrentTransaction": "Aktuelle Transaktion abbrechen", "cancelAll": "Alle {{count}} <PERSON><PERSON><PERSON> abbrechen", "blockDappFromSendingRequests": "Dapp-Anfragen für 10 Min. blockieren", "cancelConnection": "Verbindung abbrechen", "cancelCurrentConnection": "Aktuelle Verbindung abbrechen", "gasless": {"unavailable": "Ihr Gas-Guthaben ist nicht ausreichend", "notEnough": "Gas-Guthaben ist nicht ausreichend", "rabbyPayGas": "<PERSON><PERSON> übernimmt die notwendigen Gasgebühren – einfach nur unterschreiben", "customRpcUnavailableTip": "Benutzerdefinierte RPCs werden für Free Gas nicht unterstützt", "GetFreeGasToSign": "Erhalten Sie kostenloses Gas", "watchUnavailableTip": "Watch-only-<PERSON><PERSON>e wird für Free Gas nicht unterstützt", "walletConnectUnavailableTip": "Über Brieftasche verbunden mit WalletConnect wird nicht für Free Gas unterstützt."}, "gasAccount": {"deposit": "Einzahlung", "notEnough": "GasAccount ist nicht genug", "gotIt": "Verstanden", "login": "Einloggen", "WalletConnectTips": "GasAccount unterstützt WalletConnect nicht", "customRPC": "Nicht unterstützt bei der Verwendung eines benutzerdefinierten RPC", "loginFirst": "Bitte loggen Sie sich zuerst bei GasAccount ein。", "useGasAccount": "Verwenden Sie GasAccount", "chainNotSupported": "<PERSON><PERSON> wird von Gas<PERSON>unt nicht unterstützt", "loginTips": "Um den GasAccount-Login abzuschließen, wird diese Transaktion verworfen. Sie müssen sie nach dem Login erneut erstellen.", "depositTips": "Um die GasAccount-Einzahlung abzuschließen, wird diese Transaktion verworfen. Sie müssen sie nach der Einzahlung erneut erstellen."}, "keystone": {"verifyPasswordError": "Signatur fehlgeschlagen, bitte versuchen Sie es nach dem Entsperren erneut.", "hardwareRejectError": "Keystone-Anfrage wurde abgebrochen. Um fortzufahren, bitte erneut autorisieren.", "mismatchedWalletError": "Nicht übereinstimmendes Wallet", "signWith": "Wechseln Sie zu {{method}} zum Signieren", "unsupportedType": "Fehler: Der Transaktionstyp wird nicht unterstützt oder ist unbekannt.", "shouldOpenKeystoneHomePageError": "<PERSON><PERSON><PERSON>, dass Ihr Keystone 3 Pro auf der Startseite ist", "shouldRetry": "Ein Fehler ist aufgetreten. Bitte erneut versuchen.", "txRejected": "Transaktion abgelehnt", "qrcodeDesc": "<PERSON><PERSON><PERSON>, um zu signieren. Nach der Signatur unten klicken, um die Signatur zu erhalten. Bei USB erneut verbinden und autorisieren, um den Signiervorgang erneut zu starten.", "misMatchSignId": "Inkongruente Transaktionsdaten. Bitte überprüfen Sie die Transaktionsdetails.", "siging": "Sendeanforderung wird gesendet"}, "keystoneNotConnected": "Keystone ist nicht verbunden", "imKeyConnected": "imKey ist verbunden", "keystoneConnected": "Keystone ist verbunden", "imKeyNotConnected": "imKey ist nicht verbunden"}, "signTypedData": {"signTypeDataOnChain": "Signiere {{chain}} typisierte Daten", "safeCantSignText": "Dies ist eine Safe-Adresse und kann nicht zum Signieren von Text verwendet werden.", "permit": {"title": "Token-Genehmigung"}, "permit2": {"title": "Permit2 Token-Genehmigung", "sigExpireTimeTip": "<PERSON>, für die diese Signatur auf der Blockchain gültig ist", "sigExpireTime": "Ablaufzeit der Signatur", "approvalExpiretime": "Ablaufzeit der Genehmigung"}, "swapTokenOrder": {"title": "Token-Auftrag"}, "sellNFT": {"title": "NFT-Auftrag", "receiveToken": "Token erhalten", "listNFT": "NFT anbieten", "specificBuyer": "Bestim<PERSON><PERSON>"}, "signMultiSig": {"title": "Transaktion bestätigen"}, "createKey": {"title": "Schlüssel erstellen"}, "verifyAddress": {"title": "Adresse überprüfen"}, "buyNFT": {"payToken": "<PERSON><PERSON> zahlen", "receiveNFT": "NFT erhalten", "expireTime": "Ablaufzeit", "listOn": "<PERSON><PERSON><PERSON> auf"}, "contractCall": {"operationDecoded": "Der Vorgang wird aus der Nachricht decodiert"}, "safeCantSignTypedData": "Dies ist eine Safe-Adresse, und sie unterstützt nur das Signieren von EIP-712-typisierten Daten oder Strings."}, "activities": {"title": "Signaturverlauf", "signedTx": {"label": "Transaktionen", "empty": {"title": "Noch keine signierten Transaktionen", "desc": "Alle über Rabby signierten Transaktionen werden hier aufgelistet."}, "common": {"unlimited": "unbegrenzt", "unknownProtocol": "Unbekanntes Protokoll", "unknown": "Unbekannt", "speedUp": "Beschleunigen", "cancel": "Abbrechen", "pendingDetail": "Details ausstehend"}, "tips": {"pendingDetail": "Es wird nur eine Transaktion abgeschlossen, und es ist fast immer diejenige mit dem höchsten Gaspreis", "canNotCancel": "Kann nicht beschleunigen oder abbrechen: Nicht die erste ausstehende Transaktion", "pendingBroadcastRetryBtn": "<PERSON><PERSON><PERSON> senden", "pendingBroadcastBtn": "Jetzt übertragen", "pendingBroadcastRetry": "Broadcast fehlgeschlagen. Letzter Versuch: {{pushAt}}", "pendingBroadcast": "Gas-sparender Modus: Warten auf niedrigere Netzwerkgebühren. Max. {{deadline}}h Wartezeit."}, "status": {"canceled": "Abgebrochen", "failed": "Fehlgeschlagen", "submitFailed": "Einreichen fehlgeschlagen", "pending": "<PERSON><PERSON><PERSON><PERSON>", "withdrawed": "<PERSON><PERSON><PERSON> abbrechen", "pendingBroadcasted": "Ausstehend: gesendet", "pendingBroadcast": "Ausstehend: wird gesendet", "pendingBroadcastFailed": "Ausstehend: Übertragung fehlgeschlagen"}, "txType": {"initial": "Erste Transaktion", "cancel": "Abbruch-Transaktion", "speedUp": "Beschleunigungs-Transaktion"}, "explain": {"unknown": "Unbekannte Transaktion", "send": "Sende {{amount}} {{symbol}}", "cancel": "{{token}} Genehmigung für {{protocol}} widerrufen", "approve": "Genehmige {{count}} {{token}} für {{protocol}}", "cancelNFTCollectionApproval": "NFT-Sammlungsgenehmigung für {{protocol}} widerrufen", "cancelSingleNFTApproval": "Einzelne NFT-Genehmigung für {{protocol}} widerrufen", "singleNFTApproval": "Einzelne NFT-Genehmigung für {{protocol}}", "nftCollectionApproval": "NFT-Sammlungsgenehmigung für {{protocol}}"}, "CancelTxPopup": {"options": {"removeLocalPendingTx": {"title": "Ausstehende lokal löschen", "desc": "Entfernen Sie die ausstehende Transaktion aus der Oberfläche"}, "quickCancel": {"desc": "Vor dem Senden abbrechen, keine Gasgebühr", "title": "<PERSON><PERSON><PERSON>", "tips": "<PERSON>ur für Transaktionen, die noch nicht gesendet wurden, unterstützt."}, "onChainCancel": {"title": "On-chain Cancel", "desc": "Neue Transaktion zum Stornieren, erfordert <PERSON>"}}, "removeLocalPendingTx": {"title": "Transaktion lokal löschen", "desc": "Durch diese Aktion wird die ausstehende Transaktion lokal gelöscht. \nDie ausstehende Transaktion kann auch in Zukunft erfolgreich eingereicht werden."}, "title": "Transaktion abbrechen"}, "MempoolList": {"empty": "In keinem Knoten gefunden", "reBroadcastBtn": "<PERSON><PERSON><PERSON> senden", "title": "<PERSON><PERSON><PERSON> in {{count}} RPC-Knoten"}, "message": {"broadcastSuccess": "Übertragen", "cancelSuccess": "Abgebrochen", "reBroadcastSuccess": "<PERSON><PERSON><PERSON> gesendet", "deleteSuccess": "Erfolg<PERSON><PERSON>"}, "gas": {"noCost": "<PERSON><PERSON>"}, "SkipNonceAlert": {"clearPendingAlert": "{{chainName}} Transaktion ({{nonces}}) hängt seit über 3 Minuten fest. Sie können <5></5> <6>Lokale ausstehende Transaktion löschen</6> <7></7> und die Transaktion erneut einreichen.", "alert": "Nonce #{{nonce}} auf der {{chainName}}-<PERSON><PERSON> übersprungen. Dies kann zu ausstehenden Transaktionen führen. <5></5> <6>Reichen Sie eine Transaktion</6> <7></7> auf der Kette ein, um das Problem zu lösen."}, "PredictTime": {"time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> verpackt in {{time}}", "noTime": "Die Verpackungszeit wird vorhergesagt。", "failed": "Vorhersage der Verpackungszeit fehlgeschlagen"}, "CancelTxConfirmPopup": {"title": "Lösche Lokale Ausstehende", "warning": "Die entfernte Transaktion kann möglicherweise immer noch on-chain bestätigt werden, es sei denn, sie wird ersetzt.", "desc": "<PERSON><PERSON><PERSON> wird die ausstehende Transaktion von Ihrer Oberfläche entfernt. Sie können dann eine neue Transaktion initiieren."}}, "signedText": {"label": "Text", "empty": {"title": "Noch keine signierten Texte", "desc": "Alle über Rabby signierten Texte werden hier aufgelistet."}}}, "receive": {"title": "<PERSON>rhalte {{token}} auf {{chain}}", "watchModeAlert1": "Dies ist eine Beobachtungsmodus-Adresse.", "watchModeAlert2": "Sind <PERSON><PERSON> sicher, dass Sie sie verwenden möchten, um Vermögenswerte zu erhalten?"}, "sendToken": {"addressNotInContract": "<PERSON>cht in der Adressliste. <1></1><2>Zu Kontakten hinzufügen</2>", "AddToContactsModal": {"addedAsContacts": "Als Kontakt hinzugefügt", "editAddr": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>", "validator__empty": "<PERSON>te Adressnotiz e<PERSON>ben"}, "editAddressNote": "<PERSON><PERSON><PERSON><PERSON>", "error": "Fehler beim Hinzufügen zu Kontakten"}, "allowTransferModal": {"error": "falsches Passwort", "placeholder": "Passwort zur Bestätigung eingeben", "validator__empty": "Bitte Passwort eingeben", "addWhitelist": "Zur Whitelist hinzufügen"}, "GasSelector": {"confirm": "Bestätigen", "level": {"$unknown": "Unbekannt", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fast": "Sofort", "normal": "<PERSON><PERSON><PERSON>", "slow": "Standard"}, "popupDesc": "Die Gaskosten werden auf Grundlage des von Ihnen festgelegten Gaspreises vom Überweisungsbetrag abgezogen", "popupTitle": "Gaspreis festlegen (Gwei)"}, "header": {"title": "Senden"}, "modalConfirmAddToContacts": {"confirmText": "Bestätigen", "title": "Zu Kontakten hinzufügen"}, "modalConfirmAllowTransferTo": {"cancelText": "Abbrechen", "confirmText": "Bestätigen", "title": "Passwort zur Bestätigung eingeben"}, "sectionBalance": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "sectionChain": {"title": "Chain"}, "sectionFrom": {"title": "<PERSON>"}, "sectionTo": {"addrValidator__empty": "<PERSON><PERSON> Adresse e<PERSON>ben", "addrValidator__invalid": "Diese Adresse ist ungültig", "searchInputPlaceholder": "<PERSON><PERSON>e e<PERSON>ben oder suchen", "title": "An"}, "sendButton": "Senden", "tokenInfoFieldLabel": {"chain": "Chain", "contract": "Smart Contract Adresse"}, "tokenInfoPrice": "Pre<PERSON>", "whitelistAlert__disabled": "Whitelist deaktiviert. Sie können an jede Adresse überweisen.", "whitelistAlert__notWhitelisted": "Die Adresse ist nicht auf der Whitelist. <1 /> Ich gewähre vorübergehende Überweisungsberechtigung.", "whitelistAlert__temporaryGranted": "Vorübergehende Berechtigung gewährt", "whitelistAlert__whitelisted": "Die Adresse ist auf der Whitelist", "balanceWarn": {"gasFeeReservation": "Reservierung der Gaskosten erforderlich"}, "balanceError": {"insufficientBalance": "Unzureichendes G<PERSON>aben"}, "max": "MAX", "sectionMsgDataForEOA": {"placeholder": "Optional", "title": "Nachricht", "currentIsOriginal": "Die aktuelle Eingabe sind Originaldaten. UTF-8 ist:", "currentIsUTF8": "Die aktuelle Eingabe ist UTF-8. Originaldaten sind:"}, "sectionMsgDataForContract": {"placeholder": "Optional", "title": "Smart Contract-Interaktion", "parseError": "Interaktion konnte nicht dekodiert werden", "simulation": "Simulation Contract-Interaktion:", "notHexData": "<PERSON>ur He<PERSON>-Daten werden unterstützt"}, "blockedTransaction": "Blockierte Transaktion", "blockedTransactionCancelText": "<PERSON>ch weiß", "blockedTransactionContent": "Diese Transaktion interagiert mit einer Adresse, die auf der OFAC-Sanktionsliste steht."}, "sendTokenComponents": {"GasReserved": "<1>0</1> {{ tokenName }} für Gaskosten reserviert", "SwitchReserveGas": "Reservieren Gas <1 />"}, "sendNFT": {"header": {"title": "Senden"}, "sectionChain": {"title": "Chain"}, "sectionFrom": {"title": "<PERSON>"}, "sectionTo": {"title": "An", "addrValidator__empty": "<PERSON><PERSON> Adresse e<PERSON>ben", "addrValidator__invalid": "Diese Adresse ist ungültig", "searchInputPlaceholder": "<PERSON><PERSON>e e<PERSON>ben oder suchen"}, "nftInfoFieldLabel": {"Collection": "<PERSON><PERSON><PERSON>", "Contract": "Smart Contract", "sendAmount": "<PERSON><PERSON><PERSON> senden"}, "sendButton": "Senden", "whitelistAlert__disabled": "Whitelist deaktiviert. Sie können an jede Adresse überweisen.", "whitelistAlert__whitelisted": "Die Adresse ist auf der Whitelist", "whitelistAlert__temporaryGranted": "Vorübergehende Erlaubnis erteilt", "whitelistAlert__notWhitelisted": "Die Adresse ist nicht auf der Whitelist. <1 /> Ich stimme zu, vorübergehend die Erlaubnis zum Übertragen zu erteilen.", "tipNotOnAddressList": "<PERSON>cht in der Adressliste.", "tipAddToContacts": "Zu Kontakten hinzufügen", "confirmModal": {"title": "Geben Sie das Passwort zur Bestätigung ein"}}, "approvals": {"header": {"title": "Genehmigungen auf {{ address }}"}, "tab-switch": {"contract": "Nach Smart Contracts", "assets": "Nach Vermögenswerten"}, "component": {"table": {"bodyEmpty": {"loadingText": "Laden...", "noMatchText": "<PERSON><PERSON>", "noDataText": "<PERSON><PERSON>"}}, "ApprovalContractItem": {"ApprovalCount_one": "<PERSON><PERSON><PERSON><PERSON>", "ApprovalCount_other": "Genehmigungen"}, "RevokeButton": {"btnText_zero": "Widerrufen", "btnText_one": "Widerrufen ({{count}})", "btnText_other": "Widerrufen ({{count}})", "permit2Batch": {"modalTitle_other": "Insgesamt sind <2>{{count}}</2> Signaturen erforderlich", "modalContent": "Genehmigungen desselben Permit2-Vertrags würden mit derselben Signatur zusammengefasst.", "modalTitle_one": "Insgesamt ist eine Signatur von <2>{{count}}</2> <PERSON><PERSON><PERSON><PERSON>."}}, "ViewMore": {"text": "<PERSON><PERSON> anzeigen"}}, "search": {"placeholder": "{{ type }} nach Name/Adresse suchen"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "Smart Contract", "contractTrustValue": "Smart Contract-Vertrauenswert", "revokeTrends": "24h Widerrufstrends", "myApprovedAssets": "<PERSON><PERSON> gene<PERSON> Vermögenswerte", "myApprovalTime": "Meine Genehmigungszeit"}, "columnTip": {"contractTrustValue": "Der Vertrauenswert bezieht sich auf den gesamten genehmigten und diesem Smart Contract ausgesetzten Vermögenswert. Ein niedriger Vertrauenswert deutet entweder auf Risiko, oder auf Inaktivität für 180 Tage hin.", "contractTrustValueWarning": "Smart Contract-Vertrauenswert < $100.000", "contractTrustValueDanger": "Smart Contract-Vertrauenswert < $10.000"}}, "byAssets": {"columnTitle": {"asset": "Vermögenswert", "type": "<PERSON><PERSON>", "approvedAmount": "Genehmigter Betrag", "approvedSpender": "Gene<PERSON>igter Ausgeber", "myApprovalTime": "Meine Genehmigungszeit"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "Genehmigter Betrag", "tipMyBalance": "<PERSON><PERSON>"}}}}, "RevokeApprovalModal": {"subTitleTokenAndNFT": "Genehmigter Token und NFT", "subTitleContract": "Genehmigt für die folgenden Smart Contract", "selectAll": "Alle auswählen", "confirm": "Bestätigen {{ selectedCount }}", "title": "Genehmigungen", "tooltipPermit2": "Diese Genehmigung wird über den Permit2-Vertrag genehmigt:\n{{ permit2Id }}", "unSelectAll": "Alle abwählen"}, "revokeModal": {"totalRevoked": "Gesamt:", "batchRevoke": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON>", "connectLedger": "Verbinden Sie Ledger", "confirm": "Bestätigen", "waitInQueue": "In der Warteschlange warten", "approvalCount_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "revoked": "Widerrufen:", "paused": "<PERSON><PERSON><PERSON><PERSON>", "gasTooHigh": "Gas-Gebühr ist hoch", "submitTxFailed": "Fehlgeschlagenes Senden", "cancelTitle": "Verbleibende Widerrufe abbrechen", "defaultFailed": "Transaktion fehlgeschlagen", "ledgerSigned": "Signiert. Erstelle Transaktion ({{current}}/{{total}})", "ledgerSending": "Signaturanfrage wird gesendet ({{current}}/{{total}})", "gasNotEnough": "Unzureichendes Gas zur Einreichung", "revokeOneByOne": "Einzeln widerrufen", "confirmTitle": "Ein-Klick-Batch-Widerruf", "signAndStartRevoke": "Sign and Start Revoke", "approvalCount_zero": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON>", "approvalCount_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "cancelBody": "<PERSON>n Si<PERSON> diese Se<PERSON> schließen, werden die verbleibenden Revokes nicht ausgeführt.", "confirmRevokePrivateKey": "<PERSON><PERSON><PERSON><PERSON> eines Seed-Phrase oder Private-Key-Ad<PERSON>e können Si<PERSON> {{count}} Genehmigungen mit nur einem Klick widerrufen.", "useGasAccount": "Ihr Gasguthaben ist niedrig. Ihr GasAccount übernimmt die Gasgebühren.", "ledgerAlert": "Bitte öffnen Sie die Ethereum-App auf Ihrem Ledger-Gerät。", "revokeWithLedger": "<PERSON><PERSON> Ledger beginnen zu widerrufen", "simulationFailed": "Simulation fehlgeschlagen", "ledgerSended": "Bitte unterschreiben Sie die Anfrage auf Ledger ({{current}}/{{total}})", "confirmRevokeLedger": "<PERSON><PERSON><PERSON><PERSON> einer Ledger-Ad<PERSON>e können Si<PERSON> {{count}} Genehmigungen mit einem Klick widerrufen.", "stillRevoke": "Noch widerrufen", "resume": "Fortfahren"}}, "gasTopUp": {"title": "Gas aufladen", "description": "Laden Sie Gas auf, indem Sie uns verfügbare Token von einer anderen Blockchain senden. Sofortige Überweisung, sobald Ihre Zahlung bestätigt ist.", "topUpChain": "Auflade-Chain", "Amount": "Betrag", "Continue": "<PERSON><PERSON>", "InsufficientBalance": "<PERSON><PERSON> gibt nicht genug <PERSON> in <PERSON><PERSON>'s Smart Contract-Adresse für die aktuelle Blockchain. Bitte versuchen Sie es später erneut.", "hightGasFees": "Dieser Aufladebetrag ist zu gering, da das Zielnetzwerk hohe Gasgebühren erfordert.", "No_Tokens": "<PERSON><PERSON>", "InsufficientBalanceTips": "Unzureichendes G<PERSON>aben", "payment": "Gas Aufladezahlung", "Loading_Tokens": "Token werden geladen...", "Including-service-fee": "Einschließlich {{fee}} Servicegebühr", "service-fee-tip": "Durch die Bereitstellung des Dienstes zum Gas-Aufladen muss Rabby den Verlust durch Preisschwankungen und die Gasgebühr für das Aufladen tragen. Daher wird eine Servicegebühr von 20% berechnet.", "Confirm": "Bestätigen", "Select-from-supported-tokens": "Wählen Sie aus unterstützten Token", "Value": "Wert", "Payment-Token": "Zahlungs-Token", "Select-payment-token": "Wählen Sie den Zahlungs-Token", "Token": "Token", "Balance": "<PERSON><PERSON><PERSON><PERSON>"}, "swap": {"title": "Tauschen", "pendingTip": "Transaktion eingereicht. Wenn die Transaktion lange ausstehend bleibt, könne<PERSON> ve<PERSON>uchen, ausstehende Transaktionen in den Einstellungen zu löschen.", "Pending": "<PERSON><PERSON><PERSON><PERSON>", "completedTip": "Transaktion auf der Blockchain, Daten werden dekodiert, um eine Transaktionsaufzeichnung zu erstellen", "Completed": "Abgeschlossen", "slippage_tolerance": "Toleranz für Kursabweichungen:", "actual-slippage": "Tatsächliche Kursabweichung:", "gas-x-price": "Gaspreis: {{price}} Gwei.", "no-transaction-records": "<PERSON><PERSON>", "swap-history": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "InSufficientTip": "Unzureichendes Guthaben für Transaktionssimulation und Gasschätzung. Ursprüngliche Aggregator-Angebote werden angezeigt", "testnet-is-not-supported": "Benutzerdefiniertes Netzwerk wird nicht unterstützt", "not-supported": "Wird nicht unterstützt", "slippage-adjusted-refresh-quote": "Kursabweichung angepasst. Angebot aktualisieren.", "price-expired-refresh-quote": "Preis abgelaufen. Angebot aktualisieren.", "approve-x-symbol": "{{symbol}} genehmigen", "swap-via-x": "<PERSON>sch über {{name}}", "get-quotes": "Angebote erhalten", "chain": "Chain", "swap-from": "<PERSON><PERSON> <PERSON>", "to": "in", "search-by-name-address": "Suche nach Name / Adresse", "amount-in": "Betrag in {{symbol}}", "unlimited-allowance": "Unbegrenzte Erlaubnis", "insufficient-balance": "Unzureichendes G<PERSON>aben", "rabby-fee": "<PERSON><PERSON>", "minimum-received": "Minimum erhalten", "there-is-no-fee-and-slippage-for-this-trade": "<PERSON><PERSON><PERSON> diesen Handel gibt es keine Gebühr und keine Kursabweichung", "approve-tips": "1.<PERSON><PERSON><PERSON><PERSON> → 2.<PERSON><PERSON>", "best": "Beste", "unable-to-fetch-the-price": "<PERSON><PERSON> kann nicht abgerufen werden", "fail-to-simulate-transaction": "Transaktionssimulation fehlgeschlagen", "security-verification-failed": "Sicherheitsüberprüfung fehlgeschlagen", "need-to-approve-token-before-swap": "Token muss vor dem Tausch genehmigt werden", "this-exchange-is-not-enabled-to-trade-by-you": "Diese Börse ist für Si<PERSON> nicht zum Handeln aktiviert.", "enable-it": "Aktivieren", "this-token-pair-is-not-supported": "Token-<PERSON><PERSON> wird nicht unterstützt", "QuoteLessWarning": "Der Empfangsbetrag wird aus der Rabby-Transaktionssimulation geschätzt. <PERSON> Angebot, das von Dex bereitgestellt wird, beträgt {{receive}}. Sie erhalten {{diff}} weniger als das erwartete Angebot.", "by-transaction-simulation-the-quote-is-valid": "Durch Transaktionssimulation ist das Angebot gültig", "wrap-contract": "Wrap Contract", "directlySwap": "<PERSON><PERSON><PERSON> Wrapping von {{symbol}} Token mit dem Smart Contract", "rates-from-cex": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "tradingSettingTips": "{{viewCount}} <PERSON><PERSON><PERSON> bieten Angebote an, und {{tradeCount}} ermöglichen den Handel", "the-following-swap-rates-are-found": "Die folgenden Kurse wurden gefunden", "est-payment": "Geschätzte Zahlung:", "est-receiving": "Geschätzt Empfang:", "est-difference": "Geschätzte Differenz:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "Ausgewähltes Angebot weicht stark vom aktuellen Kurs ab, kann große Verluste verursachen", "rate": "<PERSON><PERSON>", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "<PERSON><PERSON><PERSON><PERSON> kann aufgrund hoher Volatilität zu fehlgeschlagenen Transaktionen führen", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "Transaktion könnte wegen hoher Toleranz für Kursabweichungen Opfer von Front-Running werden", "recommend-slippage": "Um Front-Running zu verhindern, empfehlen wir eine Kursabweichung von <2>{{ slippage }}</2>%", "slippage-tolerance": "Toleranz für Kursabweichungen", "select-token": "Token auswählen", "enable-exchanges": "<PERSON><PERSON><PERSON> aktivier<PERSON>", "exchanges": "<PERSON><PERSON><PERSON>", "view-quotes": "<PERSON><PERSON><PERSON>", "trade": "<PERSON><PERSON>", "dex": "<PERSON>", "cex": "Cex", "enable-trading": "Handel aktivieren", "i-understand-and-accept-it": "Ich verstehe und akzeptiere es", "confirm": "Bestätigen", "tradingSettingTip1": "1. <PERSON><PERSON> aktiv<PERSON>, interagieren Sie direkt mit dem Smart Contract der Börse", "tradingSettingTip2": "2. <PERSON><PERSON> haftet nicht für Risiken, die aus den Smart Contracts der Börsen entstehen", "gas-fee": "Gas Gebühr: {{gasUsed}}", "estimate": "Schätzung:", "actual": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "rabbyFee": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wallet": "Brieftasche", "rate": "Gebührensatz", "swapDesc": "<PERSON>bby <PERSON>et wird immer den bestmöglichen Kurs von den besten Aggregatoren finden und die Zuverlässigkeit ihrer Angebote überprüfen. Rabby erhebt eine Gebühr von 0,25 % (0 % für Wrapping), die automatisch in das Angebot einfließt.", "bridgeDesc": "Rabby Wallet findet stets den bestmöglichen Kurs von erstklassigen Aggregatoren und überprüft die Zuverlässigkeit ihrer Angebote. Rabby erhebt eine Gebühr von 0,25 %, die automatisch in das Angebot einbezogen wird.", "button": "Verstanden"}, "lowCreditModal": {"title": "Dieses To<PERSON> hat einen niedrigen Kreditwert", "desc": "Ein niedriger Kreditwert signalisiert oft ein hohes Risiko, wie bei einem Honeypot-Token oder sehr geringer Liquidität."}, "Auto": "Auto", "sort-with-gas": "Mit Gas sortieren", "no-fee-for-wrap": "<PERSON><PERSON>-Gebühr für Wrap", "no-quote-found": "<PERSON><PERSON> gefunden", "usd-after-fees": "≈ {{usd}}", "Gas-fee-too-high": "Gas-Gebühr zu hoch", "source": "<PERSON><PERSON>", "fetch-best-quote": "Beste Angebote werden abgerufen", "no-slippage-for-wrap": "<PERSON><PERSON> für Wrap", "hidden-no-quote-rates_one": "{{count}} rate nicht verfügbar", "max": "MAX", "from": "<PERSON>", "price-impact": "Preiseinfluss", "preferMEV": "Bevorzugen Sie MEV Guarded", "No-available-quote": "<PERSON><PERSON> ve<PERSON><PERSON><PERSON>", "two-step-approve": "Unterzeichnen Sie 2 Transaktionen, um die Erlaubnis zu ändern", "approve-swap": "Genehmigen und Tauschen", "no-fees-for-wrap": "<PERSON><PERSON>-Gebühr für Wrap", "hidden-no-quote-rates_other": "{{count}} <PERSON>n nicht verfügbar", "process-with-two-step-approve": "Fahren Sie mit der zweistufigen Genehmigung fort", "approve-and-swap": "Genehmigen und tauschen über {{name}}", "loss-tips": "Sie verlieren {{usd}}. Versuchen Sie es mit einem kleineren Betrag in einem kleinen Markt.", "two-step-approve-details": "Token USDT erfordert 2 Transaktionen, um die Freigabe zu ändern. Zuerst müssen Sie die Freigabe auf Null zurücksetzen und erst dann einen neuen Freigabewert festlegen.", "preferMEVTip": "Aktivieren Sie die Funktion \"MEV Guarded\" für Ethereum-Swaps, um das Risiko von Sandwich-Angriffen zu verringern. Hinweis: Diese Funktion wird nicht unterstützt, wenn Sie eine benutzerdefinierte RPC- oder Wallet-Verbindungsadresse verwenden."}, "manageAddress": {"no-address": "<PERSON><PERSON>", "no-match": "<PERSON><PERSON>", "current-address": "Aktuelle Adresse", "address-management": "Adressverwaltung", "update-balance-data": "Guthaben aktualisieren", "search": "<PERSON><PERSON>", "manage-address": "<PERSON><PERSON><PERSON> verwalten", "deleted": "Gelöscht", "whitelisted-address": "Whitelist-<PERSON><PERSON><PERSON>", "addressTypeTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {{type}}", "delete-desc": "<PERSON><PERSON>, <PERSON>ten Si<PERSON> die folgenden Punkte, um zu verstehen, wie Sie Ihre Vermögenswerte schützen können.", "delete-checklist-1": "<PERSON>ch verstehe, dass, wenn ich diese Adresse lösche, Private Key & Seed Phrase dieser Adresse gelöscht werden und Rabby sie NICHT wiederherstellen kann.", "delete-checklist-2": "<PERSON>ch bestätige, dass ich den Private Key oder die Seed Phrase gesichert habe und bin jetzt bereit, die Adresse zu löschen.", "confirm": "Bestätigen", "cancel": "Abbrechen", "delete-private-key-modal-title_one": "<PERSON><PERSON><PERSON> von {{count}} Private Key Adresse", "delete-private-key-modal-title_other": "<PERSON><PERSON><PERSON> von {{count}} Private <PERSON> Adressen", "delete-seed-phrase-title_one": "Seed Phrase und ihre {{count}} Adresse löschen", "delete-seed-phrase-title_other": "Seed Phrase und ihre {{count}} Adressen löschen", "delete-title_one": "{{count}} {{brand}} <PERSON><PERSON><PERSON>", "delete-title_other": "{{count}} {{brand}} Adressen löschen", "delete-empty-seed-phrase": "Seed Phrase löschen (keine Adresse)", "hd-path": "HD Pfad:", "no-address-under-seed-phrase": "Sie haben keine Adressen unter dieser Seed Phrase importiert.", "add-address": "<PERSON><PERSON><PERSON>", "delete-seed-phrase": "Seed Phrase löschen", "confirm-delete": "Löschen bestätigen", "private-key": "Private Key", "seed-phrase": "Seed Phrase", "watch-address": "<PERSON><PERSON><PERSON> be<PERSON>", "backup-seed-phrase": "Seed Phrase sichern", "delete-all-addresses-but-keep-the-seed-phrase": "Alle Adressen löschen, aber die Seed Phrase behalten", "delete-all-addresses-and-the-seed-phrase": "Alle Adressen und die Seed Phrase löschen", "seed-phrase-delete-title": "Seed Phrase löschen?", "sort-by-balance": "<PERSON><PERSON> sortieren", "sort-by-address-type": "<PERSON><PERSON> sortieren", "sort-by-address-note": "<PERSON><PERSON> Adressnotiz sortieren", "sort-address": "<PERSON><PERSON><PERSON> sortieren", "enterThePassphrase": "<PERSON><PERSON><PERSON> Sie die Passphrase ein", "passphraseError": "Passphrase ungültig", "addNewAddress": "Neue Adresse hinzufügen", "enterPassphraseTitle": "G<PERSON>en Sie die Passphrase zum Signieren ein", "CurrentDappAddress": {"desc": "Wechseln Sie die Dapp-Adresse"}}, "dashboard": {"home": {"offline": "Vom Netzwerk getrennt, es werden keine Daten abgerufen", "panel": {"swap": "Tauschen", "send": "Senden", "receive": "Empfangen", "gasTopUp": "Gas aufladen", "queue": "Warteschlange", "transactions": "Transaktionen", "approvals": "Genehmigungen", "feedback": "Rückmeldung", "more": "<PERSON><PERSON>", "manageAddress": "<PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "nft": "NFT", "rabbyPoints": "<PERSON><PERSON>", "bridge": "Bridge", "ecology": "Ökosystem", "mobile": "Mobile Sync"}, "comingSoon": "Demnächst", "soon": "Bald", "refreshTheWebPageToTakeEffect": "Laden Sie die Webseite neu, um die Änderungen zu übernehmen", "rabbyIsInUseAndMetamaskIsBanned": "<PERSON><PERSON> wird verwendet und MetaMask ist gesperrt", "flip": "Umkehren", "metamaskIsInUseAndRabbyIsBanned": "MetaMask wird verwendet und Rabby ist gesperrt", "transactionNeedsToSign": "Transaktion muss signiert werden", "transactionsNeedToSign": "Transaktionen müssen signiert werden", "view": "<PERSON><PERSON><PERSON>", "viewFirstOne": "<PERSON><PERSON><PERSON> anzeigen", "rejectAll": "<PERSON>e <PERSON>en", "pendingCount": "1 ausstehend", "pendingCountPlural": "{{countStr}} ausstehend", "queue": {"title": "Warteschlange", "count": "{{count}} drin"}, "whatsNew": "<PERSON> g<PERSON><PERSON>'s Neues", "importType": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {{type}}", "chainEnd": "<PERSON><PERSON>", "chain": "kette,", "missingDataTooltip": "Der Kontostand wird möglicherweise aufgrund aktueller Netzwerkprobleme mit {{text}} nicht aktualisiert."}, "recentConnection": {"disconnected": "Getrennt", "rpcUnavailable": "Das benutzerdefinierte RPC ist nicht verfügbar", "metamaskTooltip": "Sie bevorzugen die Verwendung von MetaMask mit dieser Dapp. Sie können diese Einstellung jederzeit anpassen unter Einstellungen > Bevorzugte MetaMask Dapps", "connected": "Verbunden", "notConnected": "Nicht verbunden", "connectedDapp": "<PERSON>bby ist nicht mit der aktuellen Dapp verbunden. Um eine Verbindung herzustellen, suchen Sie den Verbindungsknopf auf der Webseite der Dapp und klicken Si<PERSON> darauf.", "noDappFound": "<PERSON><PERSON> gefunden", "disconnectAll": "Alle trennen", "disconnectRecentlyUsed": {"title": "<PERSON><PERSON><PERSON> kürzlich verwendete <strong>{{count}}</strong> <PERSON><PERSON>", "description": "Angeheftete Dapps bleiben verbunden", "title_other": "<PERSON><PERSON><PERSON> <strong>{{count}}</strong> verbund<PERSON>", "title_one": "<PERSON><PERSON><PERSON> <strong>{{count}}</strong> verbund<PERSON>"}, "title": "Verbundene Dapps", "pinned": "Angeheftet", "noPinnedDapps": "<PERSON><PERSON> an<PERSON>", "dragToSort": "Zum Sortieren ziehen", "recentlyConnected": "<PERSON><PERSON><PERSON>lich verbunden", "noRecentlyConnectedDapps": "<PERSON><PERSON> kü<PERSON> verbundenen <PERSON>", "dapps": "<PERSON><PERSON>", "noConnectedDapps": "<PERSON><PERSON> verbund<PERSON>", "metamaskModeTooltip": "<PERSON>nn <PERSON> in dieser Da<PERSON> nicht verbinden? Versuchen Sie, den <1>MetaMask-Modus</1> zu aktivieren.", "metamaskModeTooltipNew": "<PERSON><PERSON>et wird verbunden, wenn <PERSON><PERSON> \"MetaMask\" in der Dapp auswählen. <PERSON><PERSON> können dies unter Mehr > <PERSON><PERSON><PERSON><PERSON>, indem Sie sich als MetaMask ausgeben, verwalten."}, "feedback": {"directMessage": {"content": "Direkte Nachricht", "description": "Cha<PERSON> mit <PERSON><PERSON> Wallet Official auf DeBank"}, "proposal": {"content": "Vorschlag", "description": "Vorschlag für Rabby Wallet auf DeBank einreichen"}, "title": "<PERSON><PERSON><PERSON>"}, "nft": {"empty": "<PERSON><PERSON>s in unterstützten Sammlungen gefunden", "collectionList": {"collections": {"label": "Sammlungen"}, "all_nfts": {"label": "Alle NFTs"}}, "listEmpty": "Sie haben noch keine NFT erhalten", "modal": {"collection": "<PERSON><PERSON><PERSON>", "chain": "Chain", "lastPrice": "<PERSON><PERSON><PERSON>", "purchaseDate": "<PERSON><PERSON><PERSON><PERSON>", "sendTooltip": "Derzeit werden nur ERC 721 und ERC 1155 NFTs unterstützt", "send": "Senden"}}, "rabbyBadge": {"imageLabel": "<PERSON><PERSON>", "title": "Beanspruchen Sie das Rabby Abzeichen für", "enterClaimCode": "Geben Sie den Anspruchscode ein", "swapTip": "<PERSON><PERSON> müssen zu<PERSON>t einen Tausch mit einem bekannten DEX innerhalb von Ra<PERSON> Wallet abschließen.", "goToSwap": "Zum Tauschen gehen", "claim": "Beanspruchen", "viewYourClaimCode": "<PERSON><PERSON> Sie Ihren Anspruchscode", "noCode": "Sie haben keinen Anspruchscode für diese Adresse", "learnMoreOnDebank": "Erfahren Sie mehr auf DeBank", "rabbyValuedUserNo": "<PERSON><PERSON> geschät<PERSON>ter Benutzer Nr.{{num}}", "claimSuccess": "Erfolgreich beansprucht", "viewOnDebank": "<PERSON>f DeBank anzeigen", "rabbyFreeGasUserNo": "<PERSON>bby Free Gas User Nr.{{num}}", "learnMore": "<PERSON><PERSON> er<PERSON>", "freeGasNoCode": "Bitte klicken Sie auf den untenstehenden Button, um DeBank zu besuchen und den Anspruchscode mit Ihrer aktuellen Adresse abzurufen.", "freeGasTitle": "Beanspruchen Sie das kostenlose Gas-Abzeichen für", "freeGasTip": "Bitte signieren Sie eine Transaktion mit Free Gas. Die Schaltfläche 'Free Gas' wird automatisch angezeigt, wenn Ihr Gas nicht ausreicht."}, "contacts": {"noDataLabel": "keine <PERSON>", "noData": "<PERSON><PERSON>", "oldContactList": "Alte Kontaktliste", "oldContactListDescription": "Aufgrund der Zusammenführung von Kontakten und Beobachtungsadressen werden die alten Kontakte hier für Si<PERSON> gesichert und nach einiger Zeit gelöscht. Bitte rechtzeitig hinzufügen, wenn Sie die Kontakte weiterhin verwenden möchten."}, "security": {"tokenApproval": "Token-Genehmigung", "nftApproval": "NFT-Genehmigung", "comingSoon": "Mehr Funktionen in Kürze verfügbar", "title": "Sicherheit"}, "settings": {"lock": {"never": "<PERSON><PERSON>"}, "7Days": "7 Tage", "1Day": "1 Tag", "4Hours": "4 Stunden", "1Hour": "1 Stunde", "10Minutes": "10 Minuten", "backendServiceUrl": "Backend Service URL", "inputOpenapiHost": "<PERSON><PERSON> geben Sie den OpenAPI-Host ein", "pleaseCheckYourHost": "Bitte überprüfen Sie Ihren Host", "host": "Host", "reset": "Ursprüngliche Einstellung wiederherstellen", "save": "Speichern", "pendingTransactionCleared": "Ausstehende Transaktionen gelöscht", "clearPending": "Lokal schwebende löschen", "clearPendingTip1": "Diese Aktion entfernt die ausstehende Transaktion aus Ihrer Benutzeroberfläche und hilft dabei, <PERSON><PERSON> zu lösen, die durch lange ausstehende Zeiten im Netzwerk verursacht werden.", "clearPendingTip2": "<PERSON>s beeinträchtigt nicht Ihre Kontostände oder erfordert das erneute Eingeben Ihrer Seed-Phrase. Alle Vermögenswerte und Kontodaten bleiben sicher.", "autoLockTime": "Automatische Sperrzeit", "claimRabbyBadge": "<PERSON><PERSON>n beanspru<PERSON>!", "cancel": "Abbrechen", "enableWhitelist": "Whitelist aktivieren", "disableWhitelist": "Whitelist <PERSON><PERSON><PERSON><PERSON><PERSON>", "enableWhitelistTip": "<PERSON><PERSON> a<PERSON><PERSON>, könne<PERSON> Si<PERSON> mit Rabby nur an Adressen in der Whitelist senden.", "disableWhitelistTip": "<PERSON><PERSON> können Assets an jede Adresse senden, sobald die Whitelist deaktiviert ist", "warning": "<PERSON><PERSON><PERSON>", "clearWatchAddressContent": "Sind <PERSON> sicher, dass Si<PERSON> alle Adressen im Beobachtungsmodus löschen möchten?", "updateVersion": {"content": "Ein neues Update für Rabby Wallet ist verfügbar. Klick<PERSON> Si<PERSON>, um zu erfahren, wie Sie manuell aktualisieren können.", "okText": "Anleitung", "successTip": "Sie verwenden die neueste Version", "title": "Update verfügbar"}, "features": {"label": "Funktionen", "lockWallet": "Wallet sperren", "signatureRecord": "Signaturverlauf", "manageAddress": "<PERSON><PERSON><PERSON> verwalten", "connectedDapp": "Verbundene Dapps", "rabbyPoints": "<PERSON><PERSON>", "searchDapps": "<PERSON><PERSON> durch<PERSON>", "gasTopUp": "Gas Aufladen"}, "settings": {"label": "Einstellungen", "enableWhitelistForSendingAssets": "Whitelist für das Senden von Vermögenswerten aktivieren", "customRpc": "RPC-URL ändern", "metamaskPreferredDapps": "Bevorzugte MetaMask Dapps", "currentLanguage": "Aktuelle Sprache", "enableTestnets": "Testnets aktivieren", "toggleThemeMode": "Themenmodus", "themeMode": "Themenmodus", "customTestnet": "Benutzerdefiniertes Netzwerk hinzufügen", "metamaskMode": "Verbinden Si<PERSON>, indem Si<PERSON> sich als MetaMask ausgeben", "enableDappAccount": "DApps-Adress<PERSON> unabhängig wechseln\n"}, "aboutUs": "Über uns", "currentVersion": "Aktuelle Version", "updateAvailable": "Update verfügbar", "supportedChains": "Integrierte Chains", "followUs": "Folge uns", "testnetBackendServiceUrl": "Testnet Backend Service URL", "clearWatchMode": "Beobachtungsmodus zurücksetzen", "requestDeBankTestnetGasToken": "Testnet Gas Token von DeBank anfordern", "clearPendingWarningTip": "Die entfernte Transaktion kann möglicherweise dennoch on-chain bestätigt werden, es sei denn, sie wird ersetzt.", "DappAccount": {"button": "Aktivieren\n", "title": "DApp-Adressen unabhängig wechseln\n", "desc": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> Sie unabhängig für jedes DApp die gewünschte Adresse auswählen.  Eine Änderung Ihrer Haupt-Adress<PERSON> wirkt sich nicht auf die Adresse aus, die für jedes DApp verbunden ist.\n"}}, "tokenDetail": {"blockedTip": "Blockierte Token werden nicht in der Tokenliste angezeigt", "blocked": "<PERSON><PERSON><PERSON>", "selectedCustom": "Der Token ist nicht von <PERSON> gelistet. Sie haben ihn benutzerdefiniert zur Tokenliste hinzugefügt.", "notSelectedCustom": "Der Token ist nicht von <PERSON> gelistet. Er wird zur Tokenliste hinzugefügt, wenn Sie ihn aktivieren.", "customized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scamTx": "Betrugs-Transaktion", "txFailed": "Fehlgeschlagen", "notSupported": "Der Token auf dieser Chain wird nicht unterstützt", "swap": "Tauschen", "send": "Senden", "receive": "Empfangen", "noTransactions": "<PERSON>ine Transaktionen", "customizedButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockedButton": "block<PERSON><PERSON>", "customizedButtons": "benutzerdefinierte Token", "blockedButtons": "blockierte Tokens", "NoListedBy": "<PERSON><PERSON> Listungsinformationen verfügbar", "AddToMyTokenList": "Füge meiner Token-Liste hinzu", "OriginalToken": "Original Token", "blockedListTitles": "blockierte Tokens", "myBalance": "<PERSON><PERSON>", "SupportedExchanges": "Unterstützte Börsen", "NoSupportedExchanges": "<PERSON><PERSON> unterstütz<PERSON> Börsen verfügbar", "TokenName": "Token-Name", "customizedListTitle": "benutzerdefinierter Token", "blockedListTitle": "blockierte Token", "verifyScamTips": "Das ist ein Betrugstoken", "IssuerWebsite": "Herausgeber-Website", "blockedTips": "Blockierte Token werden nicht in der Tokenliste angezeigt.", "noIssuer": "<PERSON><PERSON>ausgeberinformationen verfügbar", "ListedBy": "<PERSON>f<PERSON><PERSON><PERSON><PERSON><PERSON> von", "ContractAddress": "Vertragsadresse", "customizedListTitles": "benutzerdefinierte Tokens", "OriginIssue": "Nativ auf dieser Blockchain ausgegeben", "BridgeProvider": "Bridge-Anbieter", "customizedHasAddedTips": "Das Token ist nicht von <PERSON> gelistet. Sie haben es manuell zur Tokenliste hinzugefügt.", "maybeScamTips": "Dies ist ein Token von geringer Qualität und könnte ein Betrug sein.", "Chain": "<PERSON><PERSON>", "BridgeIssue": "<PERSON><PERSON>, die von einem Dritten überbrückt wurden", "fdvTips": "Die Marktkapitalisierung, wenn das maximale Angebot im Umlauf ist. Vollständig verwässerte Bewertung (FDV) = Preis x maximales Angebot. Wenn das maximale Angebot null ist, FDV = Preis x Gesamtes Angebot. Wenn weder das maximale Angebot noch das gesamte Angebot definiert oder unendlich sind, wird die FDV nicht angezeigt."}, "assets": {"usdValue": "USD WERT", "amount": "MENGE", "portfolio": {"nftTips": "Be<PERSON><PERSON>nung basiert auf dem Mindestpreis (Floor), der von diesem Protokoll anerkannt wird.", "fractionTips": "Berechnung basiert auf dem Preis des verknüpften ERC20-Tokens."}, "tokenButton": {"subTitle": "Der Token in dieser Liste wird nicht zum Gesamtsaldo hinzugefügt"}, "table": {"assetAmount": "Vermögenswert / Menge", "price": "Pre<PERSON>", "useValue": "USD Wert", "healthRate": "Gesundheitsrate", "debtRatio": "Verschuldungsquote", "unlockAt": "Entsperren um", "lentAgainst": "VERLIEHEN GEGEN", "type": "<PERSON><PERSON>", "strikePrice": "Ausübungspreis", "exerciseEnd": "Ende der Ausübungsfrist", "tradePair": "Handelspaar", "side": "Seite", "leverage": "<PERSON><PERSON>", "PL": "G&V", "unsupportedPoolType": "Nicht unterstützter Pool-Typ", "claimable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "endAt": "Endet um", "dailyUnlock": "Tägliche Entsperrung", "pool": "POOL", "token": "Token", "balanceValue": "Guthaben / Wert", "percent": "Prozent", "summaryTips": "Vermögenswert geteilt durch das gesamte Nettovermögen", "summaryDescription": "Alle Vermögenswerte in Protokollen (z.B. LP-Token) werden in die zugrunde liegenden Vermögenswerte aufgeschlüsselt, um statistische Berechnungen durchzuführen", "noMatch": "<PERSON><PERSON>", "lowValueDescription": "Vermögenswerte mit geringem Wert werden hier angezeigt", "lowValueAssets": "{{count}} Vermögenswerte mit geringem Wert", "lowValueAssets_other": "{{count}} niedrigwertige Token", "lowValueAssets_one": "{{count}} low value token", "lowValueAssets_0": "{{count}} Low-Value-Token"}, "noAssets": "<PERSON><PERSON>", "blockLinkText": "<PERSON><PERSON><PERSON> suchen, um Token zu blockieren", "blockDescription": "Von Ihnen blockierte Token werden hier angezeigt", "unfoldChain": "1 Chain aufklappen", "unfoldChainPlural": "{{moreLen}} Chains aufklappen", "customLinkText": "<PERSON><PERSON><PERSON> suchen, um benutzerdefinierten Token hinzuzufügen", "customDescription": "Von <PERSON>en hinzugefügte benutzerdefinierte Token werden hier angezeigt", "comingSoon": "Demnächst...", "searchPlaceholder": "Tokens", "AddMainnetToken": {"notFound": "Token nicht gefunden", "searching": "Token suchen", "isBuiltInToken": "Token bereits unterstützt", "tokenAddressPlaceholder": "Token Address", "title": "Benutzerdefiniertes Token hinzufügen", "selectChain": "<PERSON>ählen Sie die Kette aus", "tokenAddress": "Token Address"}, "AddTestnetToken": {"tokenAddress": "Token-<PERSON><PERSON><PERSON>", "searching": "Token suchen", "notFound": "Token nicht gefunden", "title": "Benutzerdefiniertes Netzwerktoken hinzufügen", "tokenAddressPlaceholder": "Token-<PERSON><PERSON><PERSON>", "selectChain": "<PERSON><PERSON> au<PERSON>wählen"}, "TestnetAssetListContainer": {"add": "Token", "addTestnet": "Netzwerk"}, "customButtonText": "Benutzerdefiniertes Token hinzufügen", "noTestnetAssets": "<PERSON><PERSON> benutzerdefinierten Netzwerk-Assets", "addTokenEntryText": "Token"}, "hd": {"howToConnectLedger": "Wie man den Ledger verbindet", "userRejectedTheRequest": "Der Benutzer hat die Anfrage abgelehnt.", "ledger": {"doc1": "Einen einzelnen Ledger anschließen", "doc2": "P<PERSON> e<PERSON>ben, um zu entsperren", "doc3": "Ethereum App öffnen", "reconnect": "<PERSON>n es nicht funktion<PERSON>t, versuchen Si<PERSON>, <1>von <PERSON> an erneut zu verbinden.</1>", "connected": "Ledger verbunden"}, "howToSwitch": "Wie man wechselt", "keystone": {"title": "<PERSON><PERSON><PERSON>, dass sich Ihr Keystone 3 Pro auf der Startseite befindet", "doc1": "Schließen Sie einen einzelnen Keystone an", "reconnect": "Wenn es nicht funktion<PERSON>t, versuchen Sie bitte <1>von <PERSON> an erneut zu verbinden.</1>", "doc2": "Geben Sie das Passwort ein, um zu entsperren", "doc3": "Genehmigung der Verbindung zum Computer"}, "imkey": {"doc1": "Stecken Si<PERSON> einen einzelnen imKey ein", "doc2": "<PERSON><PERSON><PERSON> Si<PERSON> die PIN ein, um zu entsperren"}, "howToConnectKeystone": "So verbinden Sie Keystone", "ledgerIsDisconnected": "Ihr Ledger ist nicht verbunden", "howToConnectImKey": "So verbinden Sie imKey"}, "GnosisWrongChainAlertBar": {"warning": "Die Safe-Adresse unterstützt {{chain}} nicht", "notDeployed": "Ihre Safe-Adresse ist nicht auf dieser Chain bereitgestellt."}, "echologyPopup": {"title": "Ökosystem"}, "MetamaskModePopup": {"footerText": "<PERSON><PERSON><PERSON> zu MetaMask Mode hinzufügen in Mehr > MetaMask Mode", "enableDesc": "Akt<PERSON><PERSON><PERSON>, wenn die Dapp nur mit MetaMask funktioniert", "toastSuccess": "Aktiviert. Aktualisieren Sie die Seite, um die Verbindung wiederherzustellen.", "title": "MetaMask Mode", "desc": "<PERSON><PERSON> <PERSON><PERSON> in einer DApp nicht verbinden können, aktivieren Sie den MetaMask-Modus und verbinden Si<PERSON> sich, indem Sie die MetaMask-Option auswählen."}, "offlineChain": {"chain": "{{chain}} wird bald nicht integriert sein.", "tips": "Die {{chain}} Chain wird am {{date}} nicht integriert. Ihre Vermögenswerte werden nicht beeinträchtigt, aber sie werden nicht in Ihrem Gesamtguthaben enthalten sein. Um darauf zuzugreifen, können Sie es unter „Mehr“ als benutzerdefiniertes Netzwerk hinzufügen."}, "recentConnectionGuide": {"button": "Verstanden", "title": "Wechseln Sie die Adresse für die Dapp-Verbindung hier"}}, "nft": {"floorPrice": "/ Mindestpreis (Floor):", "title": "NFT", "all": "Alle", "starred": "Favoriten ({{count}})", "empty": {"title": "Keine favorisierten NFTs", "description": "Sie können NFTs aus \"Alle\" auswählen und zu \"Favoriten\" hinzufügen"}, "noNft": "<PERSON>ine <PERSON>"}, "newAddress": {"title": "<PERSON><PERSON><PERSON>", "importSeedPhrase": "Seed Phrase importieren", "importPrivateKey": "Private Key importieren", "importMyMetamaskAccount": "MetaMask-Account importieren", "addContacts": {"content": "Kontakte hinzufügen", "description": "<PERSON>e können auch eine Beobachtungs-Adresse verwenden", "required": "<PERSON>te geben Si<PERSON> eine Adresse ein", "notAValidAddress": "<PERSON><PERSON> gültige Adresse", "scanViaMobileWallet": "Über mobile Wallet scannen", "scanViaPcCamera": "Über PC-<PERSON><PERSON><PERSON> scannen", "scanQRCode": "QR-Codes mit WalletConnect-kompatibler Wallet scannen", "walletConnect": "Wallet verbinden", "walletConnectVPN": "WalletConnect wird instabil sein, wenn Sie VPN verwenden.", "cameraTitle": "<PERSON>te scannen Sie den QR-Code mit Ihrer Kamera", "addressEns": "Adresse / ENS"}, "unableToImport": {"title": "Import nicht möglich", "description": "Das Importieren von mehreren QR-basierten Hardware-Wallets wird nicht unterstützt. Bitte löschen Sie alle Adressen von {{0}}, bevor <PERSON>e ein weiteres Gerät importieren."}, "connectHardwareWallets": "Hardware-Wallets verbinden", "connectMobileWalletApps": "Mobile Wallet-Apps verbinden", "connectInstitutionalWallets": "Institutionelle Wallets verbinden", "createNewSeedPhrase": "Neue Seed Phrase erstellen", "importKeystore": "KeyStore importieren", "selectImportMethod": "Importmethode auswählen", "theSeedPhraseIsInvalidPleaseCheck": "Die Seed Phrase ist ungültig, bitte überprüfen <PERSON> sie!", "seedPhrase": {"importTips": "Sie können Ihre gesamte Seed Phrase im ersten Feld einfügen", "whatIsASeedPhrase": {"question": "Was ist eine Seed Phrase?", "answer": "<PERSON><PERSON> 12, 18 oder 24-<PERSON><PERSON><PERSON><PERSON><PERSON>, die zur Steuerung Ihrer Vermögenswerte verwendet wird."}, "isItSafeToImportItInRabby": {"question": "Ist es sicher, sie in <PERSON>bby zu importieren?", "answer": "<PERSON><PERSON>, sie wird lokal in Ihrem Browser gespeichert und ist nur für Sie zugänglich."}, "importError": "[CreateMnemonics] uner<PERSON><PERSON> {{0}}", "importQuestion4": "<PERSON>n ich <PERSON><PERSON> deinstalliere, ohne die Seed Phrase zu sichern, kann <PERSON><PERSON> sie nicht für mich wiederherstellen.", "riskTips": "<PERSON><PERSON> beginnen, lesen Si<PERSON> bitte die folgenden Sicherheitstipps und behalten Sie sie im Kopf.", "showSeedPhrase": "Seed Phrase anzeigen", "backup": "Seed Phrase sichern", "backupTips": "<PERSON><PERSON><PERSON>, dass niemand sonst Ihren Bildschirm sieht, wenn Sie die Seed Phrase sichern", "copy": "Seed Phrase kopieren", "saved": "Ich habe die Phrase gespeichert", "pleaseSelectWords": "Bitte wählen Sie Wörter aus", "verificationFailed": "Verifizierung fehlgeschlagen", "createdSuccessfully": "Erfolgreich erstellt", "verifySeedPhrase": "Seed Phrase überprüfen", "fillInTheBackupSeedPhraseInOrder": "Füllen Sie die Sicherungs-Seed Phrase in der richtigen Reihenfolge aus", "wordPhrase": "Ich habe eine <1>{{count}}</1>-Wort-Phrase", "clearAll": "Alle löschen", "invalidContent": "Ungültiger Inhalt", "slip39SeedPhrasePlaceholder_one": "<PERSON><PERSON><PERSON> hier Ihre {{count}}. Seed-Phrase-<PERSON><PERSON><PERSON> ein", "inputInvalidCount_one": "1 Input stimmt nicht mit den Seed Phrase-Normen überein, bitte überprüfen.", "slip39SeedPhraseWithPassphrase": "Ich habe eine <0>{{SLIP39}}</0> Seed Phrase mit Passphrase", "passphrase": "Passphrase", "pastedAndClear": "Eingefügt und Zwischenablage geleert", "slip39SeedPhrasePlaceholder_two": "<PERSON><PERSON><PERSON> hier Ihre {{count}}. Seed-Phrase-<PERSON><PERSON><PERSON> ein", "wordPhraseAndPassphrase": "Ich habe einen <1>{{count}}</1>-<PERSON><PERSON>-<PERSON><PERSON> mit Passphrase", "inputInvalidCount_other": "{{count}} Eingaben entsprechen nicht den Seed Phrase-Normen, bitte überprüfen.", "slip39SeedPhrase": "Ich habe eine <0>{{SLIP39}}</0>-Seed Phrase", "slip39SeedPhrasePlaceholder_other": "<PERSON><PERSON><PERSON> hier <PERSON> {{count}}ten Seed-Phrase-Anteil ein", "slip39SeedPhrasePlaceholder_few": "<PERSON><PERSON><PERSON>e hier Ihre {{count}}te Seed-Phrase-<PERSON><PERSON> ein", "importQuestion2": "<PERSON><PERSON>-Phrase wird nur auf meinem Gerät gespeichert. <PERSON><PERSON> kann nicht darauf zugreifen.", "importQuestion1": "Wenn ich meinen Seed-Phrase verliere oder teile, verliere ich den Zugriff auf meine Vermögenswerte dauerhaft.", "importQuestion3": "<PERSON>n ich <PERSON><PERSON> deinstalliere, ohne meinen Seed-Ph<PERSON> zu sichern, kann er von <PERSON> nicht wiederhergestellt werden."}, "metamask": {"step1": "Seed Phrase oder Private Key aus MetaMask exportieren <br /> <1><PERSON><PERSON><PERSON> <PERSON> hi<PERSON>, um das Tutorial anzuzeigen <1/></1>", "step2": "Seed Phrase oder Private Key in Rabby importieren", "step3": "Import abgeschlossen, alle Ihre Vermögenswerte werden automatisch angezeigt", "how": "Wie importiere ich meinen MetaMask-Account?", "step": "<PERSON><PERSON><PERSON>", "importSeedPhrase": "Seed Phrase oder Private Key importieren", "importSeedPhraseTips": "Wird nur lokal im Browser gespeichert. Rabby hat niemals Zugriff auf Ihre privaten Informationen.", "tips": "Tipp:", "tipsDesc": "<PERSON>hre Seed Phrase / Ihr Private Key gehören nicht zu MetaMask oder einer bestimmten Wallet; sie gehören nur Ihnen."}, "privateKey": {"required": "<PERSON>te geben Si<PERSON> den Private Key ein", "placeholder": "<PERSON><PERSON>en Sie Ihren Private Key ein", "whatIsAPrivateKey": {"question": "Was ist ein Private Key?", "answer": "Eine Zeichenfolge aus Buchstaben und Zahlen, die zur Steuerung Ihrer Vermögenswerte verwendet wird."}, "isItSafeToImportItInRabby": {"question": "Ist es sicher, ihn in <PERSON><PERSON> zu importieren?", "answer": "<PERSON><PERSON>, er wird lokal in Ihrem Browser gespeichert und ist nur für Sie zugänglich."}, "isItPossibleToImportKeystore": {"question": "Ist es möglich, KeyStore zu importieren?", "answer": "<PERSON><PERSON>, <PERSON><PERSON> k<PERSON>n KeyStore hier importieren."}, "notAValidPrivateKey": "<PERSON><PERSON> Private Key", "repeatImportTips": {"desc": "Diese Ad<PERSON>e wurde bereits importiert.", "question": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> zu dieser Adresse wechseln?"}}, "importedSuccessfully": "Erfolgreich importiert", "ledger": {"title": "Ledger verbinden", "cameraPermissionTitle": "Erlauben Sie Rabby den Zugriff auf die Kamera", "cameraPermission1": "Erlauben Sie Rabby den Zugriff auf die Kamera im Browser-Popup", "allowRabbyPermissionsTitle": "Berechtigungen erteilen für:", "ledgerPermission1": "Verbindung zu einem HID-Gerät", "ledgerPermissionTip": "Bitte klicken Sie auf \"Erlauben\" unten und genehmigen Sie den Zugriff auf Ihren Ledger im folgenden Popup-Fenster.", "permissionsAuthorized": "Berechtigungen autorisiert", "nowYouCanReInitiateYourTransaction": "Jetzt können Sie Ihre Transaktion erneut starten.", "allow": "Erlauben", "error": {"ethereum_app_not_installed_error": "Bitte installieren Sie die Ethereum-App auf Ihrem Ledger-Gerät.", "ethereum_app_unconfirmed_error": "Sie haben die Anfrage zum Öffnen der Ethereum-App abgelehnt.", "ethereum_app_open_error": "Bitte installiere/akzeptiere die Ethereum-App auf deinem Ledger-Gerät.", "running_app_close_error": "Fehler beim Schließen der laufenden App auf deinem Ledger-Gerät."}}, "walletConnect": {"connectYour": "Verbinden Sie Ihre", "viaWalletConnect": "über Wallet Connect", "connectedSuccessfully": "Erfolgreich verbunden", "qrCodeError": "Bitte überprüfen Sie Ihr Netzwerk oder aktualisieren Sie den QR-Code", "qrCode": "QR-Code", "url": "URL", "changeBridgeServer": "Bridge-<PERSON>", "status": {"received": "Erfolgreich gescannt. Warten auf Bestätigung", "rejected": "Verbindung abgebrochen. Bitte scannen Sie den QR-Code erneut, um es nochmals zu versuchen.", "brandError": "Falsche Wallet-App.", "brandErrorDesc": "Bitte verwenden Sie {{brandName}}, um sich zu verbinden", "accountError": "<PERSON><PERSON>e stimmt nicht überein.", "accountErrorDesc": "Bitte wechseln Sie die Adresse in Ihrer mobilen Wallet", "connected": "Verbunden", "duplicate": "<PERSON> Adresse, die Si<PERSON> importieren möchten, ist bereits vorhanden", "default": "Scannen Sie den Code mit Ihrer {{brand}}"}, "title": "Verbinden Sie sich mit {{brandName}}", "disconnected": "Getrennt", "accountError": {}, "tip": {"accountError": {"tip1": "Verbunden, aber kann nicht signieren.", "tip2": "Bitte wechseln Sie zur richtigen Adresse in Ihrer mobilen Wallet"}, "disconnected": {"tip": "Nicht mit {{brandName}} verbunden"}, "connected": {"tip": "Mit {{brandName}} verbunden"}}, "button": {"disconnect": "<PERSON><PERSON><PERSON>", "connect": "Verbinden", "howToSwitch": "Wie wechselt man"}}, "hd": {"tooltip": {"removed": "Die Adresse wurde aus Rabby entfernt", "added": "Die Adresse wurde zu Rabby hinzugefügt", "connectError": "Die Verbindung wurde unterbrochen. Bitte aktualisieren Sie die Seite, um erneut zu verbinden.", "disconnected": "Konnte keine Verbindung zur Hardware-Wallet herstellen. Bitte versuchen Sie, erneut zu verbinden."}, "waiting": "<PERSON><PERSON>", "clickToGetInfo": "<PERSON><PERSON><PERSON>, um die Informationen auf der Chain abzurufen", "addToRabby": "<PERSON><PERSON>", "basicInformation": "Grundinformation", "addresses": "<PERSON><PERSON><PERSON>", "loadingAddress": "Lade {{0}}/{{1}} <PERSON><PERSON><PERSON>", "notes": "Notizen", "getOnChainInformation": "On-Chain-Informationen abrufen", "hideOnChainInformation": "On-Chain-Informationen ausblenden", "usedChains": "Verwendete Chains", "firstTransactionTime": "Erste Transaktionszeit", "balance": "<PERSON><PERSON><PERSON><PERSON>", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: Offizieller Ledger HD-Pfad. In den ersten 3 <PERSON>ressen befinden sich Adressen, die On-Chain verwendet werden.", "bip44": "BIP44-Standard: HD-P<PERSON>d, wie im BIP44-Protokoll definiert. In den ersten 3 Adressen befinden sich Adressen, die On-Chain verwendet werden.", "legacy": "Legacy: <PERSON><PERSON><PERSON><PERSON><PERSON>, der von <PERSON> / Mycrypto verwendet wird. In den ersten 3 Adressen befinden sich Adressen, die On-Chain verwendet werden."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: Offizieller Ledger HD-Pfad. In den ersten 3 <PERSON>ressen befinden sich keine Adressen, die On-Chain verwendet werden.", "bip44": "BIP44-Standard: HD-P<PERSON>d, wie im BIP44-Protokoll definiert. In den ersten 3 Adressen befinden sich keine Adressen, die On-Chain verwendet werden.", "legacy": "Legacy: <PERSON><PERSON><PERSON><PERSON><PERSON>, der von <PERSON> / Mycrypto verwendet wird. In den ersten 3 Adressen befinden sich keine Adressen, die On-Chain verwendet werden."}}, "trezor": {"hdPathType": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert.", "ledgerLive": "Ledger Live: Ledger offizielle HD-Pfad.", "legacy": "Legacy: HD-<PERSON><PERSON><PERSON> verwen<PERSON> von <PERSON> / Mycrypto."}, "hdPathTypeNoChain": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert.", "legacy": "Legacy: Von <PERSON> / Mycrypto verwendeter HD-Pfad.", "ledgerLive": "Ledger Live: Ledger officialer HD-Pfad."}, "message": {"disconnected": "{{0}} Die Verbindung wurde unterbrochen. Bitte aktualisieren Sie die Seite, um erneut zu verbinden."}}, "onekey": {"hdPathType": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert."}, "hdPathTypeNoChain": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert."}}, "mnemonic": {"hdPathType": {"default": "Standard: Der Standard-HD-Pfad für den Seed Phrase-Import wird verwendet.", "ledgerLive": "Ledger Live: Ledger official HD-Pfad.", "legacy": "Legacy: <PERSON><PERSON><PERSON><PERSON><PERSON>, der von <PERSON> / Mycrypto verwendet wird.", "bip44": "BIP44 Standard: <PERSON>, der durch das BIP44-Protokoll definiert wird."}, "hdPathTypeNoChain": {"default": "Standard: Der Standard-HD-Pfad für den Seed Phrase-Import wird verwendet."}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: Offizieller Ledger HD-Pfad. In den ersten 3 <PERSON>ressen befinden sich Adressen, die On-Chain verwendet werden.", "bip44": "BIP44-Standard: HD-P<PERSON>d, wie im BIP44-Protokoll definiert. In den ersten 3 Adressen befinden sich Adressen, die On-Chain verwendet werden.", "legacy": "Legacy: <PERSON><PERSON><PERSON><PERSON><PERSON>, der von <PERSON> / Mycrypto verwendet wird. In den ersten 3 Adressen befinden sich Adressen, die On-Chain verwendet werden."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: Offizieller Ledger HD-Pfad. In den ersten 3 <PERSON>ressen befinden sich keine Adressen, die On-Chain verwendet werden.", "bip44": "BIP44-Standard: HD-P<PERSON>d, wie im BIP44-Protokoll definiert. In den ersten 3 Adressen befinden sich keine Adressen, die On-Chain verwendet werden.", "legacy": "Legacy: <PERSON><PERSON><PERSON><PERSON><PERSON>, der von <PERSON> / Mycrypto verwendet wird. In den ersten 3 Adressen befinden sich keine Adressen, die On-Chain verwendet werden."}, "switch": {"title": "Wechseln Sie zu einem neuen GridPlus-Gerät", "content": "Es kann nur ein GridPlus-Gerät importiert werden. Wenn Si<PERSON> zu einem neuen GridPlus-Gerät wechseln, wird die Adresseliste des aktuellen Geräts vor dem Start des Importvorgangs gelöscht."}, "switchToAnotherGridplus": "Wechseln Sie zu einem anderen GridPlus Gerät"}, "keystone": {"hdPathType": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert.", "legacy": "Legacy: <PERSON><PERSON><PERSON><PERSON><PERSON>, der von <PERSON> / Mycrypto verwendet wird.", "ledgerLive": "Ledger Live: Ledger offizieller HD-Pfad. Sie können nur 10 Adressen mit dem Ledger Live-Pfad verwalten."}, "hdPathTypeNochain": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert.", "legacy": "Legacy: Von <PERSON> / Mycrypto verwendeter HD-Pfad.", "ledgerLive": "Ledger Live: Ledger official HD path. Sie können nur 10 Adressen mit Ledger Live-Pfad verwalten."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert."}, "hdPathTypeNoChain": {"bip44": "BIP44: HD-<PERSON><PERSON>d, wie im BIP44-<PERSON><PERSON><PERSON> definiert."}, "disconnected": "<PERSON><PERSON> ist nicht möglich, eine Verbindung zu BitBox02 herzustellen. Bitte aktualisieren Sie die Seite, um es erneut zu versuchen. Grund: {{0}}"}, "selectHdPath": "Wählen Sie den HD-Pfad:", "selectIndexTip": "Wählen Sie die Seriennummer der Adressen, ab der gestartet werden soll:", "manageAddressFrom": "<PERSON><PERSON><PERSON> {{0}} bis {{1}} ver<PERSON>ten", "advancedSettings": "Erweiterte Einstellungen", "customAddressHdPath": "Benutzerdefinierter HD-Pfad", "connectedToLedger": "Mit Ledger verbunden", "connectedToTrezor": "<PERSON><PERSON> verbunden", "connectedToOnekey": "Mit OneKey verbunden", "manageSeedPhrase": "Seed Phrase verwalten", "manageGridplus": "GridPlus verwalten", "manageKeystone": "Keystone verwalten", "manageAirgap": "AirGap verwalten", "manageCoolwallet": "CoolW<PERSON><PERSON> verwalten", "manageBitbox02": "BitBox02 verwalten", "manageNgraveZero": "NGRAVE ZERO verwalten", "done": "<PERSON><PERSON><PERSON>", "addressesIn": "<PERSON><PERSON><PERSON> in {{0}}", "addressesInRabby": "<PERSON><PERSON><PERSON> in Rabby{{0}}", "qrCode": {"switch": {"title": "Wechseln Sie zu einem neuen {{0}}-Gerät", "content": "Es kann nur ein {{0}}-Geräte importiert werden. Wenn Si<PERSON> zu einem neuen {{0}}-<PERSON><PERSON><PERSON><PERSON>, wird Adresseliste des aktuellen Geräts vor dem Start des Importvorgangs gelöscht."}, "switchAnother": "Wechseln Sie zu einem anderen {{0}}"}, "importBtn": "Importieren ({{count}})", "manageImtokenOffline": "<PERSON>erwalten Sie imToken", "manageImKey": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> imKey"}, "importYourKeystore": "Importieren Sie Ihren KeyStore", "incorrectPassword": "Falsches Passwort", "keystore": {"description": "W<PERSON><PERSON>en Sie die KeyStore-Datei aus, die Si<PERSON> importieren möchten, und geben Sie das entsprechende Passwort ein.", "password": {"required": "<PERSON>te geben Sie das Passwort ein", "placeholder": "Passwort"}}, "coboSafe": {"inputSafeModuleAddress": "Adresse des Safe Module eingeben", "invalidAddress": "Ungültige Adresse", "whichChainIsYourCoboAddressOn": "Auf welcher Chain befindet sich Ihre Cobo-Adresse", "addCoboArgusAddress": "Cobo Argus-Adresse hinzufügen", "findTheAssociatedSafeAddress": "Suchen Sie die zugehörige sichere Adresse", "import": "Importieren"}, "imkey": {"imkeyPermissionTip": "Klicken Sie bitte unten auf \"Erlauben\" und autorisieren Sie den Zugriff auf Ihr imKey im folgenden Popup-Fenster.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "keystone": {"deviceIsBusy": "Gerät ist beschäftigt", "title": "Keystone verbinden", "deviceRejectedExportAddress": "Genehmigung zur Verbindung mit Rabby", "deviceIsLockedError": "Geben Sie das Passwort ein, um zu entsperren", "noDeviceFoundError": "Schließen Sie einen einzelnen Keystone an", "keystonePermission1": "Verbinden Sie mit einem USB-Gerät", "exportAddressJustAllowedOnHomePage": "Export-Adresse nur auf der Startseite erlaubt", "allowRabbyPermissionsTitle": "Erlaube Rabby-Berechtigungen, um:", "unknowError": "<PERSON><PERSON><PERSON><PERSON>hler, bitte versuche es erneut", "keystonePermissionTip": "Bitte klicken Sie unten auf \"Erl<PERSON>ben\", um den Zugriff auf Ihren Keystone im folgenden Popup-Fenster zu autorisieren, und stellen <PERSON> sicher, dass sich Ihr Keystone 3 Pro auf der Startseite befindet."}, "firefoxLedgerDisableTips": "Ledger ist nicht mit Firefox kompatibel", "addFromCurrentSeedPhrase": "Aus aktuellem Seed-Phrase hinzufügen"}, "unlock": {"btn": {"unlock": "Entsperren"}, "password": {"required": "Geben Sie das Passwort zum Entsperren ein", "placeholder": "Geben Sie das Passwort zum Entsperren ein", "error": "Falsches Passwort"}, "title": "<PERSON><PERSON>", "description": "Die revolutionäre Wallet für Ethereum und alle EVM-Chains", "btnForgotPassword": "Passwort vergessen?"}, "addToken": {"noTokenFound": "<PERSON><PERSON> gefunden", "tokenSupported": "Token wurde in Rabby unterstützt", "tokenCustomized": "Der aktuelle Token wurde bereits hinzugefügt", "tokenNotFound": "Token nicht auf dieser Smart Contract-Adresse gefunden", "title": "<PERSON>igener Token zu Rabby hinzufügen", "balance": "<PERSON><PERSON><PERSON><PERSON>", "tokenOnMultiChains": "Token-Adresse auf mehreren Chains. Bitte wählen Si<PERSON> eine aus", "noTokenFoundOnThisChain": "<PERSON><PERSON> auf dieser Chain gefunden", "hasAdded": "Sie haben diesen Token hinzugefügt."}, "switchChain": {"title": "Benutzerdefiniertes Netzwerk zu Rabby hinzufügen", "chainNotSupport": "Die angeforderte Blockchain wird von <PERSON> noch nicht unterstützt", "testnetTip": "Bitte aktivieren Si<PERSON> unter \"<PERSON><PERSON>\" die Option \"Testnets aktivieren\", bevor <PERSON> sich mit Testnets verbinden", "chainNotSupportYet": "Die angeforderte Blockchain wird von <PERSON> noch nicht unterstützt", "chainId": "Chain ID:", "unknownChain": "Unbekannte Chain", "requestsReceived": "1 Anfrage erhalten", "requestsReceivedPlural": "{{count}} <PERSON><PERSON><PERSON> er<PERSON>en", "requestRabbyToSupport": "<PERSON><PERSON> um Unterstützung bitten", "addChain": "Testnet hinzufügen", "desc": "Das angeforderte Netzwerk ist noch nicht von Rabby integriert. Sie können es manuell als benutzerdefiniertes Netzwerk hinzufügen.", "chainNotSupportAddChain": "Die angeforderte Blockchain ist von <PERSON> noch nicht integriert. Sie können sie als Custom Testnet hinzufügen."}, "signText": {"title": "Text signieren", "message": "Nachricht", "createKey": {"interactDapp": "<PERSON><PERSON> interagieren", "description": "Beschreibung"}, "sameSafeMessageAlert": "Die gleiche Nachricht wird bestätigt; keine zusätzliche Signatur ist erforderlich."}, "securityEngine": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "whenTheValueIs": "<PERSON><PERSON> der Wert {{value}} betr<PERSON>gt", "currentValueIs": "Aktueller Wert ist {{value}}", "viewRules": "Sicherheitsregeln anzeigen", "undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "riskProcessed": "<PERSON><PERSON><PERSON>", "ignoreAlert": "Die Warnung ignorieren", "ruleDisabled": "Sicherheitsregeln wurden deaktiviert. Sie können sie jederzeit wieder aktivieren, um Ihre Sicherheit zu gewährleisten.", "unknownResult": "Unbekanntes Ergebnis, da die Sicherheitsregel nicht verfügbar ist", "alertTriggerReason": "Grund für das Auslösen der Warnung:", "understandRisk": "Ich verstehe und übernehme die Verantwortung für mögliche Verluste", "forbiddenCantIgnore": "Verbotenes Risiko gefunden, das nicht ignoriert werden kann.", "ruleDetailTitle": "Regeldetail", "enableRule": "Die Regel aktivieren", "viewRiskLevel": "Risikolevel anzeigen"}, "connect": {"listedBy": "<PERSON><PERSON><PERSON><PERSON> von", "sitePopularity": "Website-Popularität", "myMark": "<PERSON><PERSON>", "flagByRabby": "<PERSON>", "flagByMM": "<PERSON> markiert", "flagByScamSniffer": "<PERSON>", "verifiedByRabby": "<PERSON> verifiziert", "foundForbiddenRisk": "Verbotene Risiken gefunden. Die Verbindung ist blockiert.", "markAsTrustToast": "Als \"Vertrauenswürdig\" markieren", "markAsBlockToast": "Als \"Blockiert\" markieren", "markRemovedToast": "Markierung entfernt", "title": "<PERSON><PERSON> verbinden", "selectChainToConnect": "<PERSON><PERSON><PERSON>en Si<PERSON> eine Chain aus, um sich zu verbinden", "markRuleText": "<PERSON><PERSON>", "connectBtn": "Verbinden", "noWebsite": "<PERSON><PERSON>", "popularLevelHigh": "Hoch", "popularLevelMedium": "<PERSON><PERSON><PERSON>", "popularLevelLow": "<PERSON><PERSON><PERSON>", "popularLevelVeryLow": "<PERSON><PERSON>", "noMark": "<PERSON><PERSON>", "blocked": "<PERSON><PERSON><PERSON>", "trusted": "Vertrauenswürdig", "addedToWhitelist": "Zur Whitelist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addedToBlacklist": "Zur Blacklist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removedFromAll": "Aus allen Listen entfernt", "notOnAnyList": "<PERSON>cht auf einer Liste", "onYourBlacklist": "<PERSON><PERSON>", "onYourWhitelist": "<PERSON><PERSON>", "manageWhiteBlackList": "Whitelist/Blacklist verwalten", "SignTestnetPermission": {"title": "Berechtigung zum Signieren im Testnet"}, "ignoreAll": "Alle ignorieren", "SelectWallet": {"title": "<PERSON><PERSON><PERSON>en Si<PERSON> eine Wallet zum Verbinden aus", "desc": "<PERSON><PERSON><PERSON><PERSON> Sie aus den Wallets, die Si<PERSON> installiert haben"}, "otherWalletBtn": "Mit einer anderen Wallet verbinden", "connectAddress": "Adresse verbinden"}, "addressDetail": {"add-to-whitelist": "Zur Whitelist hinzufügen", "remove-from-whitelist": "Aus der Whitelist entfernen", "address-detail": "Adressdetail", "backup-private-key": "Private Key sichern", "backup-seed-phrase": "Seed Phrase sichern", "delete-address": "<PERSON><PERSON><PERSON>", "delete-desc": "<PERSON><PERSON>, <PERSON>ten Si<PERSON> bitte folgende Punkte, um zu verstehen, wie Sie Ihre Vermögenswerte schützen können.", "direct-delete-desc": "Diese Adresse ist eine {{renderBrand}}-<PERSON><PERSON><PERSON>. <PERSON><PERSON> s<PERSON> den Private Key oder die Seed Phrase für diese Adresse nicht. Sie können sie einfach löschen", "admins": "Admins", "tx-requires": "<PERSON><PERSON><PERSON> jede Transaktion sind <2>{{num}}</2> Bestätigungen erforderlich", "edit-memo-title": "<PERSON><PERSON><PERSON><PERSON>", "please-input-address-note": "<PERSON>te geben Si<PERSON> eine Adressnotiz ein", "address": "<PERSON><PERSON><PERSON>", "address-note": "<PERSON><PERSON><PERSON><PERSON>", "assets": "Vermögenswerte", "qr-code": "QR-Code", "source": "<PERSON><PERSON>", "hd-path": "HD-Pfad", "manage-seed-phrase": "Seed Phrase verwalten", "manage-addresses-under-this-seed-phrase": "<PERSON><PERSON><PERSON> unter dieser Seed Phrase verwalten", "safeModuleAddress": "<PERSON><PERSON><PERSON>", "coboSafeErrorModule": "Adresse ist abgelaufen. Bitte löschen und erneut importieren.", "importedDelegatedAddress": "Importierte Delegated-Adresse", "manage-addresses-under": "Verwalte Adressen unter diesem {{brand}}"}, "preferMetamaskDapps": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "Die folgenden Dapps bleiben unabhängig von der verwendeten Wallet über MetaMask verbunden", "howToAdd": "So fügen Sie hinzu", "howToAddDesc": "<PERSON>licken Sie mit der rechten Maustaste auf die Website und suchen Sie diese Option", "empty": "<PERSON><PERSON>"}, "customRpc": {"opened": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "closed": "Geschlossen", "empty": "<PERSON>ine benutzerdefinierte RPC-URL", "title": "RPC-URL ändern", "desc": "Nach der Änderung wird der benutzerdefinierte RPC den Node von Rabby er<PERSON>. Um den Node von Rabby weiter zu verwenden, löschen Sie den benutzerdefinierten RPC.", "add": "RPC-URL ändern", "EditRPCModal": {"invalidRPCUrl": "Ungültige RPC-URL", "invalidChainId": "Ungültige Chain-ID", "rpcAuthFailed": "Authentifizierung für RPC fehlgeschlagen", "title": "RPC-URL ändern", "rpcUrl": "RPC-URL", "rpcUrlPlaceholder": "<PERSON><PERSON><PERSON> Sie die RPC-URL ein"}, "EditCustomTestnetModal": {"quickAdd": "<PERSON><PERSON><PERSON> von Chain<PERSON> hinz<PERSON>ü<PERSON>", "title": "Benutzerdefiniertes Netzwerk hinzufügen"}}, "requestDebankTestnetGasToken": {"title": "DeBank Testnet Gas Token anfordern", "mintedTip": "<PERSON>haber des Rabby Badge können einmal täglich anfordern", "notMintedTip": "Anfrage nur für Inhaber des Rabby Badge verfügbar", "claimBadgeBtn": "<PERSON><PERSON>", "time": "Pro Tag", "requested": "<PERSON>e haben heute angefragt", "requestBtn": "Anfrage"}, "safeQueue": {"title": "Warteschlange", "sameNonceWarning": "Diese Transaktionen kollidieren, da sie dieselbe Nonce verwenden. Die Ausführung einer Transaktion ersetzt automatisch die anderen.", "loading": "Lade ausstehende Transaktionen", "noData": "<PERSON>ine ausstehenden Transaktionen", "loadingFaild": "Aufgrund der Instabilität des Safe-Servers stehen die Daten nicht zur Verfügung. Bitte überprüfen Sie dies nach 5 Minuten erneut.", "accountSelectTitle": "<PERSON>e können diese Transaktion mit jeder Adresse senden", "LowerNonceError": "Die Transaktion mit Nonce {{nonce}} muss zuerst ausgeführt werden", "submitBtn": "Transaktion senden", "unknownTx": "Unbekannte Transaktion", "cancelExplain": "{{token}} Freigabe für {{Protokoll}} abbrechen", "unknownProtocol": "Unbekanntes Protokoll", "approvalExplain": "{{count}} {{token}} für {{Protokoll}} freigeben", "unlimited": "unbegrenzt", "action": {"send": "Senden", "cancel": "Ausstehende Transaktion abbrechen"}, "viewBtn": "<PERSON><PERSON><PERSON>", "replaceBtn": "<PERSON><PERSON><PERSON><PERSON>", "ReplacePopup": {"options": {"send": "Token senden", "reject": "Transaktion ablehnen"}, "title": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> au<PERSON>, wie Sie diese Transaktion ersetzen möchten", "desc": "Eine signierte Transaktion kann nicht entfernt, aber durch eine neue Transaktion mit derselben Nonce ersetzt werden."}}, "importSuccess": {"title": "Erfolgreich importiert", "addressCount": "{{count}} <PERSON><PERSON><PERSON>", "gnosisChainDesc": "Diese <PERSON> wurde auf {{count}} Chains bereitgestellt"}, "backupSeedPhrase": {"title": "Backup Seed Phrase", "alert": "Diese Seed Phrase ist der Schlüssel zu Ihren Vermögenswerten. VERLIEREN oder enthüllen Sie sie NICHT gegenüber anderen, da Sie sonst Ihre Vermögenswerte dauerhaft verlieren könnten. Bitte sehen Sie sie in einer sicheren Umgebung ein und bewahren Sie sie sorgfältig auf.", "clickToShow": "<PERSON><PERSON><PERSON> hier, um die Seed Phrase anzuzeigen", "copySeedPhrase": "Seed Phrase kopieren", "qrCodePopupTitle": "QR Code", "qrCodePopupTips": "Teilen Sie niemals den QR-Code der Seed-Phrase mit anderen. Bitte sehen Si<PERSON> es in einer sicheren Umgebung an und bewahren Sie es sorgfältig auf.", "showQrCode": "QR-Code anzeigen"}, "backupPrivateKey": {"title": "Backup Private Key", "alert": "Dieser Private Key ist der Schlüssel zu Ihren Vermögenswerten. VERLIEREN oder enthüllen Sie ihn NICHT gegenüber anderen, da Sie sonst Ihre Vermögenswerte dauerhaft verlieren könnten. Bitte sehen Si<PERSON> ihn in einer sicheren Umgebung ein und bewahren Sie ihn sorgfältig auf.", "clickToShow": "<PERSON><PERSON><PERSON>, um den Private Key anzuzeigen", "clickToShowQr": "<PERSON><PERSON><PERSON>, um den QR-Code des Private Keys anzuzeigen"}, "ethSign": {"alert": "Das Signieren mit 'eth_sign' kann zu Vermögensverlust führen. Aus Sicherheitsgründen unterstützt Rabby diese Methode NICHT."}, "createPassword": {"title": "Passwort festlegen", "passwordRequired": "Bitte geben Si<PERSON> ein Passwort ein", "passwordMin": "Das Passwort muss mindestens 8 Zeichen lang sein", "passwordPlaceholder": "Das Passwort muss mindestens 8 Zeichen lang sein", "confirmRequired": "Bitte bestätigen Sie das Passwort", "confirmError": "Passwörter stimmen nicht überein", "confirmPlaceholder": "Passwort bestätigen", "agree": "Ich habe die <1/> <2>Nutzungsbedingungen</2> gelesen und stimme ihnen zu"}, "welcome": {"step1": {"title": "Zugriff auf alle Dapps", "desc": "<PERSON><PERSON> verbindet sich mit allen <PERSON>, die von MetaMask unterstützt werden"}, "step2": {"title": "Self-Custodial", "desc": "Private Keys werden lokal gespeichert und sind nur Ihnen zugänglich", "btnText": "Loslegen"}}, "importSafe": {"title": "Sichere Adresse hinzufügen", "placeholder": "<PERSON><PERSON> Adresse e<PERSON>ben", "error": {"invalid": "Ungültige Adresse", "required": "<PERSON><PERSON> Adresse e<PERSON>ben"}, "loading": "Suche nach der bereitgestellten Chain dieser Adresse", "gnosisChainDesc": "Diese <PERSON> wurde auf {{count}} Chains bereitgestellt"}, "importQrBase": {"desc": "Scannen Sie den QR-Code auf der {{brandName}} Hardware Wallet", "btnText": "<PERSON><PERSON><PERSON> versuchen"}, "bridge": {"showMore": {"title": "<PERSON><PERSON> anzeigen", "source": "Bridge Source"}, "settingModal": {"confirmModal": {"title": "Aktivieren Sie den Handel mit diesem Aggregator", "tip2": "2. <PERSON><PERSON> ist nicht haftbar für Risiken, die aus dem Vertrag dieses Aggregators entstehen.", "tip1": "<PERSON><PERSON><PERSON> akt<PERSON><PERSON><PERSON>, interagieren Sie direkt mit dem Vertrag von diesem Aggregator.", "i-understand-and-accept-it": "Ich verstehe und akzeptiere es"}, "SupportedBridge": "Unterstützte Bridge:", "title": "Bridge Aggregatoren zum Handeln aktivieren", "confirm": "Bestätigen"}, "tokenPairDrawer": {"tokenPair": "Token Pair", "title": "Wählen Sie aus unterstützten Token-Paar", "noData": "<PERSON><PERSON> un<PERSON>ütztes Token-Paar", "balance": "Balance Value"}, "bridgeTo": "<PERSON><PERSON><PERSON><PERSON> zu", "To": "Bis", "select-chain": "<PERSON><PERSON> au<PERSON>wählen", "Amount": "Betrag", "history": "Bridge history", "no-transaction-records": "<PERSON>ine Transaktionsaufzeichnungen", "tokenPairPlaceholder": "Token-<PERSON><PERSON> au<PERSON>", "insufficient-balance": "Unzureichendes G<PERSON>aben", "Select": "Auswählen", "title": "Bridge", "Pending": "<PERSON><PERSON><PERSON><PERSON>", "From": "<PERSON>", "detail-tx": "Detail", "Completed": "Abgeschlossen", "actual": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "no-quote": "<PERSON><PERSON>", "the-following-bridge-route-are-found": "Gefundene Route:", "BridgeTokenPair": "Bridge Token Pair", "getRoutes": "Routen abrufen", "unlimited-allowance": "Unbegrenzte Erlaubnis", "gas-fee": "GasFee: {{gasUsed}}", "gas-x-price": "Gas-Preis: {{price}} Gwei.", "Balance": "Kontostand:", "est-payment": "Geschätzte Zahlung:", "estimated-value": "≈ {{value}}", "best": "Beste", "bridge-cost": "Brückenkosten", "approve-and-bridge": "Genehmigen und Überbrücken", "estimate": "Schätzung:", "recommendFromToken": "<PERSON><PERSON><PERSON><PERSON> von <1></1> für ein verfügbares Angebot", "no-quote-found": "<PERSON><PERSON> gefunden. Bitte versuchen Sie es mit anderen Token-Paaren.", "completedTip": "Transaktion auf der Chain, <PERSON><PERSON>, um einen Eintrag zu erstellen", "need-to-approve-token-before-bridge": "Vor dem Bridge-Vorgang muss der Token genehmigt werden。", "loss-tips": "Sie verlieren {{usd}}. Versuchen Sie einen anderen Betrag.", "est-difference": "Geschätzter Unterschied:", "price-impact": "Preiseffekt", "duration": "{{duration}} min", "enable-it": "Aktivieren Sie es", "via-bridge": "über {{bridge}}", "approve-x-symbol": "Gene<PERSON><PERSON> {{symbol}}", "no-route-found": "<PERSON><PERSON> gefunden", "slippage-adjusted-refresh-quote": "Slippage angepasst. Route aktualisieren.", "bridge-via-x": "Bridge auf {{name}}", "price-expired-refresh-route": "Preis abgelaufen. Route aktualisieren.", "est-receiving": "Geschätzter Empfang:", "aggregator-not-enabled": "Dieser Aggregator ist von Ihnen nicht zum Handeln aktiviert.", "pendingTip": "Tx eingereicht. Wenn die Tx über mehrere Stunden aussteht, können <PERSON> ve<PERSON>uchen, das Ausstehende in den Einstellungen zu löschen.", "rabby-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max-tips": "Dieser Wert wird berechnet, indem die Gaskosten für die Brücke abgezogen werden."}, "pendingDetail": {"Header": {"predictTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> verpackt in"}, "TxStatus": {"pendingBroadcast": "Ausstehend: <PERSON><PERSON><PERSON> gese<PERSON>t", "reBroadcastBtn": "<PERSON><PERSON><PERSON> senden", "pendingBroadcasted": "Ausstehend: Gese<PERSON>t", "completed": "Abgeschlossen"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcastedCount_ordinal_two": "{{count}}-te <PERSON><PERSON>", "broadcastedCount_ordinal_one": "{{count}}. <PERSON><PERSON><PERSON><PERSON>", "broadcastedCount_ordinal_other": "{{count}}. Übertragung", "pending": "Status wird überprüft...", "broadcasted": "<PERSON><PERSON><PERSON><PERSON> gesendet", "created": "Transaktion erstellt", "broadcastedCount_ordinal_few": "{{count}}te Übertragung"}, "MempoolList": {"col": {"nodeName": "Node name", "txStatus": "Transaktionsstatus", "nodeOperator": "Knotenbetreiber"}, "txStatus": {"appeared": "Erschienen", "notFound": "Nicht gefunden", "appearedOnce": "<PERSON><PERSON><PERSON> e<PERSON>"}, "title": "<PERSON><PERSON><PERSON> in {{count}} RPC-Knoten"}, "PendingTxList": {"filterBaseFee": {"label": "Erfüllt nur die Basisgebühr-Anforderung", "tooltip": "Nur Transaktionen anzeigen, deren Gaspreis die Basisgebührenanforderungen des Blocks erfüllt"}, "col": {"actionType": "Aktionstyp", "action": "Transaktionsaktion", "gasPrice": "Gas Price", "balanceChange": "Kontostandänderung", "interact": "Interagieren mit"}, "titleSame": "GasPrice Ranks #{{rank}} wie Aktuell", "titleNotFound": "<PERSON><PERSON> in allen ausstehenden Txs", "title": "Gas<PERSON><PERSON> belegt Platz #{{rank}} bei allen ausstehenden Txs", "titleSameNotFound": "<PERSON><PERSON>ng im selben wie aktuell"}, "Empty": {"noData": "<PERSON><PERSON> Daten gefunden"}, "PrePackInfo": {"col": {"difference": "Ergebnisse überprüfen", "prePackResults": "Vorab verpackte Ergebnisse", "expectations": "Erwart<PERSON>", "prePackContent": "Pre-pack-Inhalt"}, "type": {"receive": "Empfangen", "pay": "<PERSON><PERSON><PERSON>"}, "error": "{{count}} <PERSON><PERSON> gefunden", "loss": "{{lossCount}} Verlust gefunden", "noLoss": "<PERSON><PERSON> festgestellt", "noError": "<PERSON><PERSON> gefunden", "desc": "Simulation im neuesten Block ausgeführt, aktualisiert {{time}}", "title": "Vorverpackungsprüfung"}, "Predict": {"completed": "Transaktion abgeschlossen", "predictFailed": "Verpackungszeitvorhersage fehlgeschlagen", "skipNonce": "Ihre Adresse hat auf der Ethereum-Chain eine Nonce übersprungen, wodurch die aktuelle Transaktion nicht abgeschlossen werden kann."}}, "dappSearch": {"searchResult": {"foundDapps": "Gefunden <2>{{count}}</2> <PERSON>pps", "totalDapps": "Insgesamt <2>{{count}}</2> Dapps"}, "selectChain": "Chain auswählen", "emptyFavorite": "<PERSON><PERSON>", "favorite": "<PERSON><PERSON>", "listBy": "<PERSON><PERSON> wurde auf<PERSON><PERSON> von", "emptySearch": "<PERSON><PERSON> gefunden", "expand": "<PERSON><PERSON><PERSON><PERSON>"}, "rabbyPoints": {"claimItem": {"go": "Los", "disabledTip": "Derzeit keine Punkte zu beanspruchen", "claim": "Beanspruchen", "claimed": "Beansprucht", "earnTip": "Einmal täglich Limit. Bitte verdienen Sie Punkte nach 00:00 UTC+0"}, "claimModal": {"rabbyValuedUserBadge": "<PERSON><PERSON> Valued User Badge", "addressBalance": "Wallet-<PERSON><PERSON><PERSON><PERSON>", "MetaMaskSwap": "MetaMask Swap", "rabbyUser": "<PERSON>bby Active User", "placeholder": "Geben Sie den Empfehlungscode für zusätzliche Punkte ein (optional)", "claim": "Beanspruchen", "snapshotTime": "Snapshot-Zeit: {{time}}", "title": "Beanspruchen Sie anfängliche Punkte", "walletBalance": "Wallet Balance", "activeStats": "Aktiver Status", "season2": "Saison 2", "invalid-code": "ungültiger Code", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "referral-code": "Empfehlungscode", "cantUseOwnCode": "Sie können Ihren eigenen Empfehlungscode nicht verwenden."}, "referralCode": {"verifyAddressModal": {"sign": "Unterschreiben", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "<PERSON>te unterschreiben Sie diese Textnachricht, um zu verifizieren, dass Sie der Besitzer dieser Adresse sind.", "cancel": "Abbrechen", "verify-address": "Adresse überprüfen"}, "set-my-referral-code": "Legen Sie meinen Empfehlungscode fest", "referral-code-already-exists": "Empfehlungscode existiert bereits", "confirm": "Bestätigen", "set-my-code": "Code festlegen", "my-referral-code": "Mein Em<PERSON>ehlungscode", "referral-code-available": "Empfehlungscode verfügbar", "referral-code-cannot-be-empty": "Empfehlungscode darf nicht leer sein", "once-set-this-referral-code-is-permanent-and-cannot-change": "<PERSON><PERSON><PERSON> dieser Empfehlungscode festgelegt ist, ist er dauerhaft und kann nicht mehr geändert werden.", "refer-a-new-user-to-get-50-points": "Empfehlen Sie einen neuen Benutzer, um 50 Punkte zu erhalten", "max-15-characters-use-numbers-and-letters-only": "Maximal 15 Zeichen, verwenden Sie nur Zahlen und Buchstaben.", "referral-code-cannot-exceed-15-characters": "Empfehlungscode darf 15 Zeichen nicht überschreiten"}, "top-100": "Top 100", "title": "<PERSON><PERSON>", "earn-points": "<PERSON><PERSON> ve<PERSON>", "share-on": "<PERSON><PERSON><PERSON> auf", "referral-code-copied": "Empfehlungscode kopiert", "out-of-x-current-total-points": "Von insgesamt {{total}} verteilten Punkten", "firstRoundEnded": "🎉 Die erste Runde der Rabby Points ist beendet", "initialPointsClaimEnded": "<PERSON><PERSON><PERSON> Punkte Beanspruchung beendet", "secondRoundEnded": "🎉 Die zweite Runde von Rabby Points ist beendet", "code-set-successfully": "Empfehlungscode erfolgreich festgelegt"}, "customTestnet": {"CustomTestnetForm": {"nativeTokenSymbol": "Währungssymbol", "name": "Netzwerkname", "rpcUrl": "RPC URL", "id": "Chain ID", "rpcUrlRequired": "Bitte RPC-URL eingeben", "idRequired": "<PERSON>te geben Sie die Chain-ID ein", "blockExplorerUrl": "Block explorer URL (Optional)", "nameRequired": "Bitte geben Sie den Netzwerknamen ein", "nativeTokenSymbolRequired": "Bitte geben Sie das Währungssymbol ein"}, "AddFromChainList": {"tips": {"added": "Sie haben diese Chain bereits hinzugefügt", "supported": "Von <PERSON>et bereits integrierte Chain"}, "search": "Nach benutzerdefiniertem Netzwerknamen oder ID suchen", "title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "empty": "Keine Chains gefunden"}, "signTx": {"title": "Transaktionsdaten"}, "ConfirmModifyRpcModal": {"desc": "Die Kette ist bereits von Rabby integriert. Müssen Sie ihre RPC-URL ändern?"}, "title": "Benutzerdefiniertes Netzwerk", "empty": "<PERSON><PERSON>erdefiniertes Netzwerk", "id": "ID", "add": "Benutzerdefiniertes Netzwerk hinzufügen", "currency": "Währung", "desc": "Rabby kann die Sicherheit benutzerdefinierter Netzwerke nicht überprüfen. Bitte fügen Sie nur vertrauenswürdige Netzwerke hinzu."}, "addChain": {"title": "Benutzerdefiniertes Netzwerk zu Rabby hinzufügen", "desc": "Rabby kann die Sicherheit benutzerdefinierter Netzwerke nicht überprüfen. Bitte fügen Sie nur vertrauenswürdige Netzwerke hinzu."}, "sign": {"transactionSpeed": "Transaktionsgeschwindigkeit"}, "ecology": {"sonic": {"home": {"earnTitle": "<PERSON><PERSON><PERSON>", "airdropDesc": "~200 Millionen S an Benutzer auf Opera und Sonic.", "migrateTitle": "<PERSON><PERSON><PERSON><PERSON>", "arcadeBtn": "Jetzt spielen", "airdrop": "Airdrop", "arcadeDesc": "Spiele kostenlose Spiele, um Punkte für den S Airdrop zu verdienen.", "migrateBtn": "Demnächst verfügbar", "migrateDesc": "请提供原文，以便我为您翻译成德语。", "airdropBtn": "<PERSON><PERSON> ve<PERSON>", "socialsTitle": "Beteiligen Si<PERSON> sich", "earnBtn": "Demnächst verfügbar", "earnDesc": "Setze dein $S"}, "points": {"sonicArcadeBtn": "Spiel starten", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shareOn": "<PERSON><PERSON><PERSON> auf", "sonicPoints": "Sonic Points", "pointsDashboardBtn": "Beginnen Sie Punkte zu sammeln", "pointsDashboard": "Punkte-Dashboard", "referralCode": "Empfehlungscode", "today": "<PERSON><PERSON>", "errorTitle": "Fehler beim Laden der Punkte", "referralCodeCopied": "Empfehlungscode kopiert", "getReferralCode": "Erhalten Sie den Empfehlungscode", "sonicArcade": "Sonic Arcade", "errorDesc": "<PERSON><PERSON>hrer Punkte ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."}}, "dbk": {"home": {"bridgeBtn": "Bridge", "mintNFTDesc": "Seien Sie ein Zeuge der DBK Chain", "mintNFT": "Präge DBK Genesis NFT", "mintNFTBtn": "Prägen", "bridge": "<PERSON><PERSON><PERSON><PERSON> zu DBK Chain", "bridgePoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von OP Superchain"}, "bridge": {"tabs": {"withdraw": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Einzahlung"}, "info": {"completeTime": "Fertigstellungszeit", "toAddress": "Um an", "gasFee": "Gas fee", "receiveOn": "Empfangen auf {{chainName}}"}, "error": {"notEnoughBalance": "Unzureichendes G<PERSON>aben"}, "ActivityPopup": {"status": {"readyToProve": "<PERSON><PERSON><PERSON> zu beweisen", "deposit": "Einzahlung", "challengePeriod": "Herausforderungszeitraum", "waitingToProve": "Status-<PERSON> ver<PERSON><PERSON>t", "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "proved": "Bewährt", "claimed": "Beansprucht", "readyToClaim": "Bereit zur Beanspruchung", "rootPublished": "Status-<PERSON> ver<PERSON><PERSON>t"}, "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "title": "Aktivitäten", "deposit": "Einzahlung", "claimBtn": "Beanspruchen", "proveBtn": "<PERSON><PERSON><PERSON>", "empty": "Noch keine Aktivitäten"}, "WithdrawConfirmPopup": {"step3": "Antrag auf Ethereum", "btn": "<PERSON><PERSON><PERSON><PERSON>", "question3": "<PERSON>ch verstehe, dass die Netzwerkgebühren ungefähr sind und sich ändern werden", "step2": "Auf Ethereum beweisen", "question2": "<PERSON><PERSON> versteh<PERSON>, dass eine Abhebung nicht beschleunigt oder storniert werden kann, sobald sie initiiert wurde.", "title": "DBK Chain-Abhebung dauert ~7 Tage", "step1": "Abhebung initiieren", "tips": "Der Abhebungsprozess umfasst drei Schritte und erfordert 1 DBK Chain-Transaktion und 2 Ethereum-Transaktionen", "question1": "<PERSON><PERSON> versteh<PERSON>, dass es etwa 7 Tage dauert, bis meine Mittel auf Ethereum beansprucht werden können, nachdem ich meinen Rückzug nachgewiesen habe."}, "labelTo": "<PERSON><PERSON>", "labelFrom": "<PERSON>"}, "minNFT": {"myBalance": "<PERSON><PERSON>", "minted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mintBtn": "Prägen", "title": "DBK Genesis"}}}, "miniSignFooterBar": {"status": {"txSending": "Signieranfrage wird gesendet", "txSendings": "Sende Signaturanfrage ({{current}}/{{total}})", "txCreated": "Transaktion erstellt", "txSigned": "Signiert. Transaktion wird erstellt"}, "signWithLedger": "<PERSON><PERSON> Led<PERSON> signieren"}, "gasAccount": {"history": {"noHistory": "<PERSON><PERSON>"}, "loginInTip": {"desc": "Auf allen Chains Gasgebühren zahlen", "title": "Einzahlung USDC / USDT", "login": "In GasAccount einloggen", "gotIt": "Verstanden"}, "loginConfirmModal": {"desc": "Nach der Bestätigung können Si<PERSON> es nur an dieser Adresse e<PERSON>zahlen。", "title": "Mit aktueller Adresse anmelden"}, "logoutConfirmModal": {"logout": "Abmelden", "title": "Abmelden aktuelles GasAccount", "desc": "Das Abmelden deaktiviert GasAccount. Sie können Ihr GasAccount wiederherstellen, indem Sie sich mit dieser Adresse anmelden."}, "depositPopup": {"invalidAmount": "Muss unter 500 sein", "amount": "Betrag", "title": "Einzahlung", "selectToken": "Token auswählen, um einzu<PERSON>hlen", "token": "Token", "desc": "Machen Sie eine Einzahlung auf das DeBank L2 Konto von Rabby ohne zusätzliche Gebühren – jederzeit abheben."}, "withdrawPopup": {"noEnoughGas": "<PERSON><PERSON> zu niedrig, um die Gasgebühren zu decken", "noEnoughValuetBalance": "Vault Balance nicht ausreichend. Wechseln Sie die Kette oder versuchen Si<PERSON> es später erneut.", "recipientAddress": "Empfängeradresse", "withdrawalLimit": "Abhebungslimit", "selectChain": "Wählen Sie Chain", "destinationChain": "Ziel-Blockchain", "deductGasFees": "Erhaltener Betrag wird Gasgebühren abziehen", "amount": "Betrag", "title": "<PERSON><PERSON><PERSON><PERSON>", "selectAddr": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "selectRecipientAddress": "Wählen Sie die Empfängeradresse", "selectDestinationChain": "Wählen Sie die Zielkette aus", "to": "Um", "noEligibleAddr": "<PERSON>ine berechtigte Adresse für Auszahlung", "desc": "Sie können Ihr GasAccount-Guthaben auf Ihr DeBank L2 Wallet abheben. Melden Sie sich in Ihrem DeBank L2 Wallet an, um die Gelder bei Bedarf auf eine unterstützte Blockchain zu übertragen.", "riskMessageFromChain": "Aufgrund der Risikokontrolle hängt das Auszahlungslimit von der insgesamt aus dieser Kette eingezahlten Menge ab.", "noEligibleChain": "<PERSON><PERSON> berechtigte Kette für Abhebungen", "riskMessageFromAddress": "Aufgrund der Risikokontrolle hängt das Auszahlungslimit von der Gesamtsumme ab, die diese Adresse e<PERSON>hlt hat."}, "withdrawConfirmModal": {"button": "<PERSON>f DeBank anzeigen", "title": "Zu Ihrem DeBank L2 Wallet übertragen"}, "GasAccountDepositTipPopup": {"title": "Öffnen Sie GasAccount und einzahlen", "gotIt": "Verstanden"}, "switchLoginAddressBeforeDeposit": {"desc": "Bitte wechseln Sie zu Ihrer Login-Adresse.", "title": "Wechseln Sie die Adresse vor der Einzahlung"}, "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "title": "GasAccount", "deposit": "Einzahlung", "noBalance": "<PERSON><PERSON>", "safeAddressDepositTips": "Multisig-Adressen werden für Einzahlungen nicht unterstützt.", "logout": "Aktuelles GasAccount abmelden", "risk": "Ihre aktuelle Adresse wurde als riskant erkannt, daher ist diese Funktion nicht verfügbar.", "gasExceed": "GasAccount-<PERSON><PERSON> $1000 nicht überschreiten", "gasAccountList": {"address": "<PERSON><PERSON><PERSON>", "gasAccountBalance": "Gas Balance"}, "withdrawDisabledIAP": "Abhebungen sind deaktiviert, da Ihr Guthaben Fiat-Mittel enthält, die nicht abgehoben werden können. Kontaktieren Sie den Support, um Ihr Token-Guthaben abzuheben.", "switchAccount": "Wechseln Sie GasAccount"}, "safeMessageQueue": {"noData": "<PERSON><PERSON>", "loading": "<PERSON><PERSON>"}, "newUserImport": {"guide": {"importAddress": "Ich habe bereits eine Adresse", "title": "<PERSON><PERSON>mmen bei <PERSON>", "createNewAddress": "<PERSON><PERSON><PERSON><PERSON> eine neue Adresse", "desc": "Die bahnbrechende Wallet für Ethereum und alle EVM Chains"}, "createNewAddress": {"showSeedPhrase": "Seed Phrase anzeigen", "title": "<PERSON><PERSON>nen", "desc": "Bitte lesen Sie die folgenden Sicherheitstipps und behalten Sie sie im Hinterkopf.", "tip1": "Wenn ich meinen Seed-Phrase verliere oder teile, verliere ich dauerhaft den Zugang zu meinen Vermögenswerten.", "tip2": "<PERSON><PERSON>-Phrase wird nur auf meinem Gerät gespeichert. <PERSON><PERSON> kann darauf nicht zugreifen.", "tip3": "Wenn ich <PERSON><PERSON> deinstalliere, ohne meine seed phrase zu sichern, kann sie von <PERSON> nicht wiederhergestellt werden."}, "importList": {"title": "Import-Methode auswählen"}, "importPrivateKey": {"title": "Privaten Schlüssel importieren", "pasteCleared": "Eingefügt und Zwischenablage geleert"}, "PasswordCard": {"form": {"password": {"min": "Das Passwort muss mindestens 8 <PERSON>eichen lang sein。", "label": "Passwort", "required": "Bitte Passwort eingeben", "placeholder": "Passwort (min. 8 Zeichen)"}, "confirmPassword": {"notMatch": "Passwörter stimmen nicht überein", "required": "Bitte Passwort bestätigen", "label": "Passwort bestätigen", "placeholder": "Passwort bestätigen"}}, "title": "Passwort festlegen", "agree": "Ich stimme den<1/> <2>Nutzungsbedingungen</2> und der <4>Datenschutzerklärung</4> zu.", "desc": "<PERSON>s wird verwendet, um die Wallet zu entsperren und Daten zu verschlüsseln"}, "successful": {"start": "<PERSON><PERSON><PERSON>", "create": "Erfolgreich erstellt", "import": "Erfolgreich importiert", "addMoreAddr": "<PERSON>ügen Sie weitere Adressen von dieser Seed Phrase hinzu", "addMoreFrom": "<PERSON><PERSON>gen Sie weitere Adressen von {{name}} hinzu"}, "readyToUse": {"pin": "<PERSON><PERSON> anheften", "guides": {"step2": "<PERSON><PERSON>", "step1": "Klicken Sie auf das Browsererweiterungssymbol"}, "extensionTip": "<PERSON><PERSON><PERSON> <1/> und dann <3/>", "title": "<PERSON><PERSON> ist bereit!", "desc": "<PERSON><PERSON> und pinne es."}, "importSeedPhrase": {"title": "Seed Phrase importieren"}, "importOneKey": {"title": "OneKey", "tip1": "1. Installiere <1>OneKey Bridge<1/>", "tip3": "3. Entsperren Sie Ihr Gerät", "connect": "Verbinden Sie OneKey", "tip2": "2. Schließen Sie Ihr OneKey-Gerät an"}, "importTrezor": {"title": "<PERSON><PERSON><PERSON>", "tip1": "1. Schließen Sie Ihr Trezor-Gerät an", "tip2": "2. Entsperren Sie Ihr Gerät", "connect": "<PERSON><PERSON><PERSON>"}, "ImportGridPlus": {"tip1": "1. Öffnen Sie Ihr GridPlus-Gerät", "connect": "GridPlus verbinden", "tip2": "2. <PERSON><PERSON> Lattice Connector verbinden", "title": "GridPlus"}, "importLedger": {"title": "Ledger", "connect": "Verbinden Sie Ledger", "tip3": "Öffnen Sie die Ethereum-App.", "tip1": "Schließen Sie Ihr Ledger-Gerät an.", "tip2": "<PERSON>eben Sie Ihre PIN ein, um zu entsperren."}, "importBitBox02": {"title": "BitBox02", "tip1": "1. Installiere die <1>BitBoxBridge<1/>", "connect": "Verbinden Sie BitBox02", "tip3": "3. Entsperren Sie Ihr Gerät", "tip2": "2. Schl<PERSON>ßen Sie Ihr BitBox02 an"}, "importKeystone": {"qrcode": {"desc": "Scannen Sie den QR-Code auf der Keystone-Hardware-Wallet"}, "usb": {"connect": "Verbinde Keystone", "desc": "<PERSON><PERSON><PERSON>, dass sich Ihr Keystone 3 Pro auf der Startseite befindet", "tip1": "Schließen Sie Ihr Keystone-Gerät an", "tip3": "Genehmigen Sie die Verbindung zu Ihrem Computer.", "tip2": "Geben Sie Ihr Passwort ein, um zu entsperren"}}, "importSafe": {"error": {"required": "<PERSON><PERSON> Adresse e<PERSON>ben", "invalid": "<PERSON><PERSON> gültige Adresse"}, "title": "Sichere Adresse hinzufügen", "placeholder": "Eingabe der sicheren Adresse", "loading": "Suche in der bereitgestellten Kette dieser Adresse"}}, "metamaskModeDapps": {"title": "Verwalte Erlau<PERSON>", "desc": "MetaMask-Modus für die folgenden Dapps aktiviert. Sie können Rabby verbinden, indem Sie die MetaMask-Option auswählen."}, "forgotPassword": {"home": {"buttonNoData": "Passwort festlegen", "title": "Passwort vergessen", "button": "Beginnen Sie mit dem Zurücksetzungsprozess", "descriptionNoData": "Rabby <PERSON>et speichert Ihr Passwort nicht und kann Ihnen nicht bei der Wiederherstellung helfen. Setzen Sie ein neues Passwort, wenn Sie es vergessen haben.", "description": "<PERSON><PERSON>et speichert Ihr Passwort nicht und kann Ihnen nicht dabei helfen, es wiederherzustellen. <PERSON>zen Sie Ihre Wallet zurück, um eine neue einzu<PERSON>ten."}, "reset": {"alert": {"privateKey": "Privater Key", "title": "Daten werden gelöscht und können nicht wiederhergestellt werden:", "seed": "Seed Phrase"}, "tip": {"title": "Daten werden gespeichert:", "hardware": "Importierte Hardware-Wallets", "records": "Signaturprotokolle", "whitelist": "Whitelist-Einstellungen", "watch": "Kontakte und Watch-Only-Adress<PERSON>", "safe": "Importierte Safe Wallets"}, "button": "Zurücksetzen bestätigen", "title": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON> <1>RESET</1> in das Feld ein, um zu bestätigen und fortzufahren"}, "tip": {"descriptionNoData": "Fügen Sie Ihre Adresse hinzu, um zu beginnen", "button": "Passwort festlegen", "title": "<PERSON><PERSON> Zurücksetzung abgeschlossen", "buttonNoData": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Passwort, um fortzufahren"}, "success": {"button": "<PERSON><PERSON><PERSON>", "description": "Du bist bereit, <PERSON><PERSON> zu verwenden", "title": "Passwort erfolgreich festgelegt"}}, "eip7702": {"alert": "EIP-7702 wird noch nicht unterstützt"}, "metamaskModeDappsGuide": {"toast": {"disabled": "Tarnung deaktiviert. Aktualisieren Sie die Dapp.", "enabled": "Tarnung aktiviert. Aktualisieren Sie die Dapp, um die Verbindung wiederherzustellen."}, "noDappFound": "<PERSON><PERSON> gefunden", "manage": "Verwalte zulässige Dapps", "step1": "Schritt 1", "step1Desc": "<PERSON><PERSON><PERSON><PERSON> Rabby, sich auf der aktuellen Dapp als MetaMask auszugeben.", "step2Desc": "Aktualisieren und über MetaMask verbinden", "step2": "Schritt 2", "alert": "Kann keine Verbindung zu einer <PERSON>pp herstellen, weil <PERSON><PERSON> nicht als Option angezeigt wird?", "title": "Verbinden Si<PERSON>, indem Si<PERSON> sich als MetaMask ausgeben"}, "syncToMobile": {"downloadAppleStore": "App Store", "clickToShowQr": "<PERSON><PERSON><PERSON> Si<PERSON> auf, um die Adresse auszuwählen und QR -Code anzeigen", "downloadGooglePlay": "Google Play", "description": "Ihre Adressdaten bleiben vollständig offline, versch<PERSON><PERSON><PERSON>t und werden sicher über einen QR-Code übertragen.", "title": "Synchronisieren Sie die Wallet-Adresse von der Rabby-Erweiterung mit dem Mobilgerät", "steps1": "1. <PERSON><PERSON> herunterladen", "steps2": "2. <PERSON><PERSON> scannen", "steps2Description": "Ihr QR-Code enthält sensible Daten. Bewahren Sie ihn privat auf und geben Sie ihn niemals an Dr<PERSON> weiter.", "disableSelectAddress": "Synchronisation nicht unterstützt für {{Typ}} Adresse", "disableSelectAddressWithPassphrase": "Synchronisation nicht unterstützt für {{Typ}} Ad<PERSON><PERSON> mit Passphrase", "disableSelectAddressWithSlip39": "Synchronisation nicht unterstützt für {{Typ}} Adresse mit Slip39", "selectedLenAddressesForSync_one": "Ausgewählte {{len}} Adresse für die Synchronisierung", "selectedLenAddressesForSync_other": "Ausgewählte {{len}} Adressen für die Synchronisierung", "selectAddress": {"title": "Adressen zum Synchronisieren auswählen"}}, "search": {"sectionHeader": {"Defi": "<PERSON><PERSON><PERSON>", "NFT": "NFT", "token": "Tokens", "AllChains": "Alle Chains"}, "header": {"placeHolder": "<PERSON><PERSON>", "searchPlaceHolder": "Token-Name / Adresse suchen"}, "tokenItem": {"listBy": "<PERSON><PERSON> von {{name}}", "FDV": "FDV", "scamWarningTips": "Das ist ein Token von geringer Qualität und könnte ein Betrug sein.", "gasToken": "Gas-Token", "verifyDangerTips": "Dies ist ein Betrugstoken", "Issuedby": "Ausgestellt von"}, "searchWeb": {"title": "Alle Ergebnisse", "searching": "Ergebnisse für", "noResults": "<PERSON><PERSON>", "noResult": "<PERSON><PERSON>", "searchTips": "Durchsuchen Sie das Web"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "<PERSON><PERSON> passende <PERSON>", "AddressItem": {"whitelistedAddressTip": "Whitelist-<PERSON><PERSON><PERSON>"}}, "AccountSelectDrawer": {"btn": {"cancel": "Abbrechen", "proceed": "Fortfahren"}}, "AddressList": {"AddressItem": {"addressTypeTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {{type}}"}}, "AuthenticationModal": {"passwordError": "Falsches Passwort", "passwordRequired": "Bitte Passwort eingeben", "passwordPlaceholder": "Passwort zur Bestätigung eingeben"}, "ConnectStatus": {"connecting": "Verbindung wird hergestellt...", "connect": "Verbinden", "gridPlusConnected": "GridPlus ist verbunden", "gridPlusNotConnected": "GridPlus ist nicht verbunden", "ledgerNotConnected": "Ledger ist nicht verbunden", "ledgerConnected": "Ledger ist verbunden", "keystoneConnected": "Keystone ist verbunden", "imKeyrNotConnected": "imKey ist nicht verbunden", "imKeyConnected": "imKey ist verbunden", "keystoneNotConnected": "Keystone ist nicht verbunden"}, "Contact": {"AddressItem": {"notWhitelisted": "<PERSON><PERSON> Adresse ist nicht auf der Whitelist", "whitelistedTip": "Whitelist-<PERSON><PERSON><PERSON>"}, "EditModal": {"title": "<PERSON><PERSON><PERSON> bear<PERSON>"}, "EditWhitelist": {"backModalTitle": "Änderungen verwerfen", "backModalContent": "Die von Ihnen vorgenommenen Änderungen werden nicht gespeichert", "title": "Whitelist bearbeiten", "tip": "<PERSON><PERSON>hlen Sie die Adresse aus, die Sie auf die Whitelist setzen möchten, und speichern Si<PERSON> sie.", "save": "In Whitelist speichern ({{count}})"}, "ListModal": {"title": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "whitelistEnabled": "Die Whitelist ist aktiviert. Sie können Assets nur an eine Adresse senden, die auf der Whitelist steht, oder Sie können die Whitelist in den Einstellungen deaktivieren", "whitelistDisabled": "Die Whitelist ist deaktiviert. Sie können Assets an jede Adresse senden", "editWhitelist": "Whitelist bearbeiten", "whitelistUpdated": "Whitelist a<PERSON><PERSON><PERSON><PERSON>", "authModal": {"title": "In Whitelist speichern"}}}, "LoadingOverlay": {"loadingData": "Daten werden geladen..."}, "MultiSelectAddressList": {"imported": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NFTNumberInput": {"erc1155Tips": "<PERSON><PERSON> G<PERSON>aben beträgt {{amount}}", "erc721Tips": "<PERSON>s kann jeweils nur ein NFT des Typs ERC-721 gesendet werden"}, "TiledSelect": {"errMsg": "Die Reihenfolge der Seed Phrase ist falsch. Bitte überprüfen Sie diese."}, "Uploader": {"placeholder": "Wählen Sie eine JSON-Datei aus"}, "WalletConnectBridgeModal": {"title": "Bridge-Server-URL", "requiredMsg": "<PERSON><PERSON> g<PERSON>en <PERSON>-Server-Host ein", "invalidMsg": "Bitte überprüfen Sie Ihren Host", "restore": "Ursprüngliche Einstellungen wiederherstellen"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "Integriertes Netzwerk", "testnet": "Benutzerdefiniertes Netzwerk"}}, "ChainSelectorModal": {"searchPlaceholder": "Chains durchsuchen", "noChains": "Keine Chains", "addTestnet": "Benutzerdefiniertes Netzwerk hinzufügen"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "ASSET / MENGE"}, "price": {"title": "PREIS"}, "usdValue": {"title": "USD-WERT"}}, "searchInput": {"placeholder": "Nach Name / Adresse suchen"}, "header": {"title": "Token auswählen"}, "noTokens": "<PERSON><PERSON>", "noMatch": "<PERSON><PERSON>", "noMatchSuggestion": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON>, die Smart Contract-Adresse auf {{ chainName }} zu suchen", "bridge": {"high": "Hoch", "value": "Wert", "low": "<PERSON><PERSON><PERSON>", "token": "Token", "liquidity": "Liquidität", "liquidityTips": "Je höher das historische Handelsvolumen, desto wahrscheinlicher ist es, dass die Brücke erfolgreich sein wird."}, "common": "Allgemein", "recent": "Neueste", "hot": "<PERSON><PERSON><PERSON>", "chainNotSupport": "<PERSON><PERSON> wird nicht unterstützt"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "<PERSON><PERSON><PERSON>", "Chain": "Chain", "PurschaseDate": "<PERSON><PERSON><PERSON><PERSON>", "LastPrice": "<PERSON><PERSON><PERSON>"}}, "signPermissionCheckModal": {"title": "<PERSON>e erlauben dieser Dapp nur das Signieren auf Testnets", "reconnect": "<PERSON><PERSON> erneut verbinden"}, "testnetCheckModal": {"title": "Bitte aktivieren Sie „Testnets aktivieren“ unter „Mehr“, bevor <PERSON> sich auf Testnets anmelden"}, "EcologyNavBar": {"providedBy": "Bereitgestellt von {{chainName}}"}, "EcologyNoticeModal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "notRemind": "<PERSON>cht mehr daran erinnern", "desc": "Die folgenden Dienstleistungen werden direkt von einem Drittanbieter-Ecosystem-Partner bereitgestellt. <PERSON><PERSON> übernimmt keine Verantwortung für die Sicherheit dieser Dienstleistungen."}, "ReserveGasPopup": {"instant": "Sofort", "title": "Reservieren Gas", "doNotReserve": "Reservieren Sie kein Gas", "fast": "<PERSON><PERSON><PERSON>", "normal": "Normal"}, "OpenExternalWebsiteModal": {"button": "Fortfahren", "title": "<PERSON><PERSON> verl<PERSON>en <PERSON>", "content": "<PERSON>e sind dabei, eine externe Website zu besuchen. <PERSON><PERSON> ist nicht verantwortlich für den Inhalt oder die Sicherheit dieser Seite."}, "TokenChart": {"price": "Pre<PERSON>", "holding": "Wert halten"}, "externalSwapBrideDappPopup": {"selectADapp": "<PERSON><PERSON><PERSON>en Si<PERSON> e<PERSON> Dapp", "chainNotSupported": "<PERSON>cht auf dieser <PERSON><PERSON> un<PERSON>tützt", "thirdPartyDappToProceed": "<PERSON>te verwenden Si<PERSON> eine Drittanbieter-<PERSON><PERSON>, um fortzufahren.", "viewDappOptions": "Dapp-Optionen anzeigen", "noDapp": "<PERSON><PERSON> ve<PERSON>", "noQuotesForChain": "<PERSON><PERSON><PERSON> diese Chain sind noch keine Preise verfügbar.", "help": "Bitte kontaktieren Sie das offizielle Team dieser Kette für Unterstützung.", "bridgeOnDapp": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "noDapps": "<PERSON><PERSON> auf dieser <PERSON><PERSON> ve<PERSON>", "swapOnDapp": "<PERSON><PERSON><PERSON><PERSON> auf <PERSON>\n"}, "AccountSelectorModal": {"title": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "searchPlaceholder": "<PERSON><PERSON><PERSON> suchen"}}, "global": {"appName": "<PERSON><PERSON>", "appDescription": "Die bahnbrechende Wallet für Ethereum und alle EVM-Blockchains", "copied": "<PERSON><PERSON><PERSON>", "confirm": "Bestätigen", "next": "<PERSON><PERSON>", "back": "Zurück", "ok": "OK", "refresh": "Aktualisieren", "failed": "Fehlgeschlagen", "scamTx": "Betrugstransaktion", "gas": "Gas", "unknownNFT": "Unbekanntes NFT", "copyAddress": "<PERSON><PERSON><PERSON> k<PERSON>", "watchModeAddress": "Überwachungsmodus-Adresse", "assets": "Vermögenswerte", "Confirm": "Bestätigen", "Cancel": "Abbrechen", "Clear": "Löschen", "Save": "Speichern", "confirmButton": "Bestätigen", "cancelButton": "Abbrechen", "backButton": "Zurück", "proceedButton": "Fortfahren", "editButton": "<PERSON><PERSON><PERSON>", "addButton": "Hinzufügen", "closeButton": "Schließen", "Deleted": "Gelöscht", "Loading": "Laden", "nonce": "<PERSON><PERSON>", "Balance": "<PERSON><PERSON><PERSON><PERSON>", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Nonce": "<PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "notSupportTesntnet": "<PERSON><PERSON>r benutzerdefiniertes Netzwerk nicht unterstützt"}, "background": {"error": {"noCurrentAccount": "<PERSON><PERSON> aktuel<PERSON> Account", "invalidChainId": "Ungültige Chain-ID", "notFindChain": "Kann die Chain {{chain}} nicht finden", "unknownAbi": "Unbekannte Smart Contract-ABI", "invalidAddress": "<PERSON><PERSON> gültige Adresse", "notFoundGnosisKeyring": "<PERSON><PERSON>-<PERSON>ring gefunden", "notFoundTxGnosisKeyring": "Keine Transaktion im Gnosis-Keyring gefunden", "addKeyring404": "Hinzufügen des Keyrings fehlgeschlagen, Keyring ist undefiniert", "emptyAccount": "Der aktuelle Account ist leer", "generateCacheAliasNames": "[GenerateCacheAliasNames]: mindestens eine Adresse erford<PERSON>lich", "invalidPrivateKey": "Der Private Key ist ungültig", "invalidJson": "Die Eingabedatei ist ungültig", "invalidMnemonic": "Die Seed Phrase ist ungültig. Bitte überprüfen Si<PERSON> sie!", "notFoundKeyringByAddress": "Keyring kann nicht nach Adresse gefunden werden", "txPushFailed": "Transaktionsübertragung fehlgeschlagen", "unlock": "<PERSON><PERSON> müssen zu<PERSON>t die Wallet entsperren", "duplicateAccount": "Der Account, den Si<PERSON> importieren möchten, ist ein Duplikat", "canNotUnlock": "Kann nicht entsperrt werden, ohne einen vorherigen Vault"}, "transactionWatcher": {"submitted": "Transaktion eingereicht", "more": "<PERSON><PERSON><PERSON> hier, um weitere Informationen anzuzeigen", "completed": "Transaktion abgeschlossen", "failed": "Transaktion fehlgeschlagen", "txFailedMoreContent": "{{chain}} #{{nonce}} fehlgeschlagen. Klicken Si<PERSON>, um mehr anzuzeigen.", "txCompleteMoreContent": "{{chain}} #{{nonce}} abgeschlossen. K<PERSON>en, um mehr zu sehen."}, "alias": {"HdKeyring": "Seed Phrase", "simpleKeyring": "Private Key", "watchAddressKeyring": "Kontakt"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "Erstellt durch Seed Phrase", "SimpleKeyring": "Importiert durch Private Key", "WatchAddressKeyring": "Kontakt"}, "IMPORTED_HD_KEYRING": "Importiert durch Seed Phrase", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Mainnet & Testnet", "TESTNET": "<PERSON>ur Testnets"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "Importiert durch Seed Phrase (Passphrase)"}}