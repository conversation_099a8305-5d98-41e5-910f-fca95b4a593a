/**
 * @jest-environment jsdom
 */
import customizedTokenMigration from '../../src/migrations/customizedTokenMigration';

const data = {
  preference: {
    addedToken: {
      '******************************************': [
        'bsc:******************************************',
        'cro:******************************************',
      ],
      '******************************************': [
        'eth:******************************************',
        'bsc:******************************************',
      ],
    },
  },
};

test('should migrate data', () => {
  return customizedTokenMigration.migrator(data).then((result) => {
    expect(result!.preference.customizedToken).toEqual([
      {
        chain: 'bsc',
        address: '******************************************',
      },
      {
        chain: 'cro',
        address: '******************************************',
      },
      {
        chain: 'eth',
        address: '******************************************',
      },
    ]);
  });
});

const data1 = {
  preference: {
    addedToken: {},
  },
};
test('do nothing if empty', () => {
  return customizedTokenMigration.migrator(data1).then((result) => {
    expect(result!.preference.customizedToken).toEqual([]);
  });
});
