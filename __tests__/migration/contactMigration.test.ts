/**
 * @jest-environment jsdom
 */
import contactMigration from '../../src/migrations/contactBookMigration';
import { PreferenceStore } from 'background/service/preference';

const data: { preference: PreferenceStore; contactBook } = {
  preference: {
    firstOpen: false,
    currentVersion: '',
    externalLinkAck: true,
    hiddenAddresses: [],
    isDefaultWallet: true,
    lastTimeSendToken: {
      '******************************************': {
        amount: 0.********,
        chain: 'eth',
        decimals: 18,
        display_symbol: null,
        id: 'eth',
        is_core: true,
        is_verified: true,
        is_wallet: true,
        logo_url:
          'https://static.debank.com/image/token/logo_url/eth/935ae4e4d1d12d59a99717a24f2540b5.png',
        name: 'ETH',
        optimized_symbol: 'ETH',
        price: 2955.33,
        raw_amount: '*****************',
        raw_amount_hex_str: '0x110f71fe39c0c00',
        symbol: 'ETH',
        time_at: **********,
      },
    },
    currentAccount: null,
    initAlianNames: true,
    gasCache: {
      '1': {
        gasLevel: 'slow',
        lastTimeSelect: 'gasLevel',
      },
    },
    addedToken: {},
    alianNames: {
      '******************************************': 'Main Account',
      '******************************************': 'Mnemonic 5',
    },
    balanceMap: {},
    curvePointsMap: {},
    locale: 'en',
    nftApprovalChain: {},
    pinnedChain: [],
    tokenApprovalChain: {},
    useLedgerLive: false,
    highligtedAddresses: [],
    walletSavedList: [],
    watchAddressPreference: {},
    testnetBalanceMap: {},
    addressSortStore: {
      search: '',
      sortType: 'usd',
    },
  },
  contactBook: {
    '******************************************': {
      address: '******************************************',
      name: 'hongbo',
    },
    '******************************************': {
      address: '******************************************',
      name: 'test name',
    },
  },
};

test('should migrate data', () => {
  return contactMigration.migrator(data).then((result) => {
    expect(result!.preference).toEqual({
      firstOpen: false,
      currentVersion: '',
      externalLinkAck: true,
      hiddenAddresses: [],
      isDefaultWallet: true,
      lastTimeSendToken: {
        '******************************************': {
          amount: 0.********,
          chain: 'eth',
          decimals: 18,
          display_symbol: null,
          id: 'eth',
          is_core: true,
          is_verified: true,
          is_wallet: true,
          logo_url:
            'https://static.debank.com/image/token/logo_url/eth/935ae4e4d1d12d59a99717a24f2540b5.png',
          name: 'ETH',
          optimized_symbol: 'ETH',
          price: 2955.33,
          raw_amount: '*****************',
          raw_amount_hex_str: '0x110f71fe39c0c00',
          symbol: 'ETH',
          time_at: **********,
        },
      },
      currentAccount: null,
      initAlianNames: true,
      gasCache: {
        '1': {
          gasLevel: 'slow',
          lastTimeSelect: 'gasLevel',
        },
      },
      addedToken: {},
      addressSortStore: {
        search: '',
        sortType: 'usd',
      },
      balanceMap: {},
      curvePointsMap: {},
      locale: 'en',
      nftApprovalChain: {},
      pinnedChain: [],
      testnetBalanceMap: {},
      tokenApprovalChain: {},
      useLedgerLive: false,
      highligtedAddresses: [],
      walletSavedList: [],
      watchAddressPreference: {},
    });
    expect(result.contactBook).toEqual({
      '******************************************': {
        address: '******************************************',
        name: 'hongbo',
        isAlias: false,
        isContact: true,
      },
      '******************************************': {
        address: '******************************************',
        name: 'test name',
        isAlias: true,
        isContact: true,
      },
      '******************************************': {
        address: '******************************************',
        name: 'Main Account',
        isAlias: true,
        isContact: false,
      },
    });
  });
});

test('should migrate when no alians', () => {
  const data: { preference: PreferenceStore; contactBook } = {
    preference: {
      firstOpen: false,
      currentVersion: '',
      externalLinkAck: true,
      hiddenAddresses: [],
      isDefaultWallet: true,
      lastTimeSendToken: {
        '******************************************': {
          amount: 0.********,
          chain: 'eth',
          decimals: 18,
          display_symbol: null,
          id: 'eth',
          is_core: true,
          is_verified: true,
          is_wallet: true,
          logo_url:
            'https://static.debank.com/image/token/logo_url/eth/935ae4e4d1d12d59a99717a24f2540b5.png',
          name: 'ETH',
          optimized_symbol: 'ETH',
          price: 2955.33,
          raw_amount: '*****************',
          raw_amount_hex_str: '0x110f71fe39c0c00',
          symbol: 'ETH',
          time_at: **********,
        },
      },
      currentAccount: null,
      initAlianNames: true,
      gasCache: {
        '1': {
          gasLevel: 'slow',
          lastTimeSelect: 'gasLevel',
        },
      },
      addedToken: {},
      alianNames: {},
      balanceMap: {},
      curvePointsMap: {},
      locale: 'en',
      nftApprovalChain: {},
      pinnedChain: [],
      tokenApprovalChain: {},
      useLedgerLive: false,
      highligtedAddresses: [],
      walletSavedList: [],
      watchAddressPreference: {},
      testnetBalanceMap: {},
      addressSortStore: {
        search: '',
        sortType: 'usd',
      },
    },
    contactBook: {
      '******************************************': {
        address: '******************************************',
        name: 'hongbo',
      },
      '******************************************': {
        address: '******************************************',
        name: 'test name',
      },
    },
  };
  return contactMigration.migrator(data).then((result) => {
    expect(result!.preference).toEqual({
      firstOpen: false,
      currentVersion: '',
      externalLinkAck: true,
      hiddenAddresses: [],
      isDefaultWallet: true,
      lastTimeSendToken: {
        '******************************************': {
          amount: 0.********,
          chain: 'eth',
          decimals: 18,
          display_symbol: null,
          id: 'eth',
          is_core: true,
          is_verified: true,
          is_wallet: true,
          logo_url:
            'https://static.debank.com/image/token/logo_url/eth/935ae4e4d1d12d59a99717a24f2540b5.png',
          name: 'ETH',
          optimized_symbol: 'ETH',
          price: 2955.33,
          raw_amount: '*****************',
          raw_amount_hex_str: '0x110f71fe39c0c00',
          symbol: 'ETH',
          time_at: **********,
        },
      },
      currentAccount: null,
      initAlianNames: true,
      gasCache: {
        '1': {
          gasLevel: 'slow',
          lastTimeSelect: 'gasLevel',
        },
      },
      addedToken: {},
      addressSortStore: {
        search: '',
        sortType: 'usd',
      },
      balanceMap: {},
      curvePointsMap: {},
      locale: 'en',
      nftApprovalChain: {},
      pinnedChain: [],
      testnetBalanceMap: {},
      tokenApprovalChain: {},
      useLedgerLive: false,
      highligtedAddresses: [],
      walletSavedList: [],
      watchAddressPreference: {},
    });
    expect(result.contactBook).toEqual({
      '******************************************': {
        address: '******************************************',
        name: 'hongbo',
        isAlias: false,
        isContact: true,
      },
      '******************************************': {
        address: '******************************************',
        name: 'test name',
        isAlias: false,
        isContact: true,
      },
    });
  });
});

test('should migrate when no contacts', () => {
  const data: { preference: PreferenceStore; contactBook } = {
    preference: {
      firstOpen: false,
      currentVersion: '',
      externalLinkAck: true,
      hiddenAddresses: [],
      isDefaultWallet: true,
      lastTimeSendToken: {
        '******************************************': {
          amount: 0.********,
          chain: 'eth',
          decimals: 18,
          display_symbol: null,
          id: 'eth',
          is_core: true,
          is_verified: true,
          is_wallet: true,
          logo_url:
            'https://static.debank.com/image/token/logo_url/eth/935ae4e4d1d12d59a99717a24f2540b5.png',
          name: 'ETH',
          optimized_symbol: 'ETH',
          price: 2955.33,
          raw_amount: '*****************',
          raw_amount_hex_str: '0x110f71fe39c0c00',
          symbol: 'ETH',
          time_at: **********,
        },
      },
      currentAccount: null,
      initAlianNames: true,
      gasCache: {
        '1': {
          gasLevel: 'slow',
          lastTimeSelect: 'gasLevel',
        },
      },
      addedToken: {},
      alianNames: {
        '******************************************': 'Main Account',
        '******************************************': 'Mnemonic 5',
      },
      balanceMap: {},
      curvePointsMap: {},
      locale: 'en',
      nftApprovalChain: {},
      pinnedChain: [],
      tokenApprovalChain: {},
      useLedgerLive: false,
      highligtedAddresses: [],
      walletSavedList: [],
      watchAddressPreference: {},
      testnetBalanceMap: {},
      addressSortStore: {
        search: '',
        sortType: 'usd',
      },
    },
    contactBook: {},
  };
  return contactMigration.migrator(data).then((result) => {
    expect(result!.preference).toEqual({
      firstOpen: false,
      currentVersion: '',
      externalLinkAck: true,
      hiddenAddresses: [],
      isDefaultWallet: true,
      lastTimeSendToken: {
        '******************************************': {
          amount: 0.********,
          chain: 'eth',
          decimals: 18,
          display_symbol: null,
          id: 'eth',
          is_core: true,
          is_verified: true,
          is_wallet: true,
          logo_url:
            'https://static.debank.com/image/token/logo_url/eth/935ae4e4d1d12d59a99717a24f2540b5.png',
          name: 'ETH',
          optimized_symbol: 'ETH',
          price: 2955.33,
          raw_amount: '*****************',
          raw_amount_hex_str: '0x110f71fe39c0c00',
          symbol: 'ETH',
          time_at: **********,
        },
      },
      currentAccount: null,
      initAlianNames: true,
      gasCache: {
        '1': {
          gasLevel: 'slow',
          lastTimeSelect: 'gasLevel',
        },
      },
      addedToken: {},
      addressSortStore: {
        search: '',
        sortType: 'usd',
      },
      balanceMap: {},
      curvePointsMap: {},
      locale: 'en',
      nftApprovalChain: {},
      pinnedChain: [],
      testnetBalanceMap: {},
      tokenApprovalChain: {},
      useLedgerLive: false,
      highligtedAddresses: [],
      walletSavedList: [],
      watchAddressPreference: {},
    });
    expect(result.contactBook).toEqual({
      '******************************************': {
        address: '******************************************',
        name: 'Main Account',
        isAlias: true,
        isContact: false,
      },
      '******************************************': {
        address: '******************************************',
        name: 'Mnemonic 5',
        isAlias: true,
        isContact: false,
      },
    });
  });
});

test('return undefined for new user', () => {
  contactMigration
    .migrator({ preference: undefined, contactBook: undefined })
    .then((result) => {
      expect(result.contactBook).toBeUndefined();
      expect(result.preference).toBeUndefined();
    });
});
