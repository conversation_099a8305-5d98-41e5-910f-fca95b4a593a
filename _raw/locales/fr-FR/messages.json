{"background": {"alias": {"HdKeyring": "Seed Phrase", "simpleKeyring": "Clé privée", "watchAddressKeyring": "Contact"}, "error": {"addKeyring404": "Échec de l'ajout du trousseau de clés, le trousseau n'est pas défini", "canNotUnlock": "Impossible de déverrouiller sans un coffre-fort antérieur", "duplicateAccount": "Le compte que vous essayez d'importer est en double", "emptyAccount": "le compte actuel est vide", "generateCacheAliasNames": "[GenerateCacheAliasNames] : nécessite au moins une adresse", "invalidAddress": "N'est pas une adresse valide", "invalidChainId": "Identifiant de chaîne invalide", "invalidJson": "le fichier d'entrée n'est pas valide", "invalidMnemonic": "La seed phrase n'est pas valide, veuillez vérifier !", "invalidPrivateKey": "la clé privée n'est pas valide", "noCurrentAccount": "Pas de compte actif", "notFindChain": "Impossible de trouver la chaîne {{chain}}", "notFoundGnosisKeyring": "Aucun trousseau de clés Gnosis trouvé", "notFoundKeyringByAddress": "Impossible de trouver le trousseau par adresse", "notFoundTxGnosisKeyring": "Aucune transaction trouvée dans le trousseau de clés Gnosis", "txPushFailed": "Échec de la transmission de la transaction", "unknownAbi": "abi inconnu pour ce contrat", "unlock": "vous devez d'abord d<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>"}, "transactionWatcher": {"completed": "Transaction finalisée", "failed": "La transaction a échoué", "more": "cliquer pour voir plus d'informations", "submitted": "Transaction envoyée", "txCompleteMoreContent": "{{chain}} #{{nonce}} finalisée. Cliquer pour en voir plus.", "txFailedMoreContent": "{{chain}} #{{nonce}} a échoué. Cliquer pour en voir plus."}}, "component": {"AccountSearchInput": {"AddressItem": {"whitelistedAddressTip": "<PERSON>ress<PERSON> sur liste blanche"}, "noMatchAddress": "<PERSON><PERSON>ne adresse correspondante"}, "AccountSelectDrawer": {"btn": {"cancel": "Annuler", "proceed": "<PERSON><PERSON><PERSON>"}}, "AddressList": {"AddressItem": {"addressTypeTip": "Importé par {{type}}"}}, "AuthenticationModal": {"passwordError": "mot de passe incorrect", "passwordPlaceholder": "Entrer le mot de passe pour confirmer", "passwordRequired": "Veuillez saisir le mot de passe"}, "ChainSelectorModal": {"noChains": "<PERSON><PERSON> <PERSON>", "searchPlaceholder": "Chercher une chaîne", "addTestnet": "Ajouter un réseau personnalis<PERSON>"}, "ConnectStatus": {"connect": "Connecter", "connecting": "Connexion...", "gridPlusConnected": "GridPlus connecté", "gridPlusNotConnected": "GridPlus non connecté", "ledgerConnected": "Clé Ledger connectée", "ledgerNotConnected": "Clé Ledger non connectée", "keystoneConnected": "Keystone connecté", "keystoneNotConnected": "Keystone non connecté", "imKeyConnected": "imKey est connecté", "imKeyrNotConnected": "imKey n'est pas connecté"}, "Contact": {"AddressItem": {"notWhitelisted": "Cette adresse n'est pas sur liste blanche", "whitelistedTip": "<PERSON>ress<PERSON> sur liste blanche"}, "EditModal": {"title": "Modifier la note d'adresse"}, "EditWhitelist": {"backModalContent": "Les modifications que vous avez apportées ne seront pas enregistrées", "backModalTitle": "Annuler les modifications", "save": "Enregistrer sur la liste blanche ({{count}})", "tip": "Sélectionnez l'adresse que vous souhaitez ajouter à la liste blanche et enregistrez-la.", "title": "Modifier liste blanche"}, "ListModal": {"authModal": {"title": "Enregistrer dans la liste blanche"}, "editWhitelist": "Modifier liste blanche", "title": "Sélectionner l'adresse", "whitelistDisabled": "La liste blanche est désactivée. <PERSON><PERSON> pou<PERSON> envoyer des actifs vers n'importe quelle adresse", "whitelistEnabled": "La liste blanche est activée. Vous ne pouvez envoyer des actifs que vers une adresse sur liste blanche ou vous pouvez la désactiver dans \"Paramètres\".", "whitelistUpdated": "Liste blanche mise à jour"}}, "LoadingOverlay": {"loadingData": "Chargement des données..."}, "ModalPreviewNFTItem": {"FieldLabel": {"Chain": "<PERSON><PERSON><PERSON>", "Collection": "Collection", "LastPrice": "Der<PERSON> prix", "PurschaseDate": "Date d'achat"}}, "MultiSelectAddressList": {"imported": "Importé"}, "NFTNumberInput": {"erc1155Tips": "Votre solde est de {{amount}}", "erc721Tips": "Un seul NFT de type ERC-721 ne peut être envoyé à la fois"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "Mainnets", "testnet": "Testnets"}}, "TiledSelect": {"errMsg": "L'ordre des mots de la seed phrase est erroné, veuillez vérifier"}, "TokenSelector": {"header": {"title": "Sélectionner un token"}, "listTableHead": {"assetAmount": {"title": "ACTIF / QUANTITÉ"}, "price": {"title": "PRIX"}, "usdValue": {"title": "VALEUR EN USD"}}, "noMatch": "Aucune concordance", "noMatchSuggestion": "Essayer de rechercher l'adresse du contrat sur {{ chainName }}", "noTokens": "Aucun token", "searchInput": {"placeholder": "Rechercher par nom / adresse"}, "bridge": {"value": "<PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "low": "Faible", "token": "Token", "liquidity": "Liquidité", "liquidityTips": "Plus le volume des transactions historiques est élevé, plus il est probable que le bridge réussira."}, "hot": "<PERSON><PERSON>", "common": "<PERSON><PERSON><PERSON>", "recent": "<PERSON><PERSON><PERSON>", "chainNotSupport": "Cette chaîne n'est pas prise en charge"}, "Uploader": {"placeholder": "Sélectionner un fichier JSON"}, "WalletConnectBridgeModal": {"invalidMsg": "Veuillez vérifier votre hôte", "requiredMsg": "Veuillez saisir l'hôte du serveur de bridge", "restore": "Restaurer le réglage initial", "title": "URL du serveur de bridge"}, "signPermissionCheckModal": {"reconnect": "Reconnecter DApp", "title": "Vous autorisez uniquement cette DApp à se connecter aux testnets"}, "testnetCheckModal": {"title": "Veuillez activer \" Activer les testnets \" dans \" Plus \" avant de vous connecter aux testnets."}, "EcologyNavBar": {"providedBy": "<PERSON><PERSON> par {{chain<PERSON><PERSON>}}"}, "EcologyNoticeModal": {"notRemind": "Ne plus me le rappeler", "title": "<PERSON><PERSON>", "desc": "Les services suivants seront fournis directement par un partenaire de l'écosystème tiers. Ra<PERSON>et n'assume pas la responsabilité de la sécurité de ces services."}, "ReserveGasPopup": {"title": "Réserve de Gas", "normal": "Normal", "doNotReserve": "Ne réservez pas de Gas", "instant": "Instant", "fast": "Rapide"}, "OpenExternalWebsiteModal": {"button": "<PERSON><PERSON><PERSON>", "title": "Vous quittez <PERSON>", "content": "Vous êtes sur le point de visiter un site web externe. <PERSON><PERSON> n'est pas responsable du contenu ou de la sécurité de ce site."}, "TokenChart": {"holding": "Valeur de dé<PERSON>tion", "price": "Prix"}, "externalSwapBrideDappPopup": {"viewDappOptions": "Voir les options Dapp", "noDapp": "<PERSON><PERSON><PERSON> disponible", "chainNotSupported": "Non pris en charge sur cette chaîne", "noQuotesForChain": "Aucune cotation disponible pour cette chaîne pour l'instant", "thirdPartyDappToProceed": "Veuillez utiliser une Dapp tierce pour continuer.", "selectADapp": "Sélectionnez un Dapp", "help": "Veuillez contacter l'équipe officielle de cette chaîne pour obtenir de l'aide.", "bridgeOnDapp": "Pont sur Dapp", "noDapps": "Aucune <PERSON>pp disponible sur cette chaîne", "swapOnDapp": "Échange sur Dapp\n"}, "AccountSelectorModal": {"searchPlaceholder": "Rechercher l'adresse", "title": "Sélectionner l'adresse"}}, "constant": {"IMPORTED_HD_KEYRING": "Importé par seed phrase", "KEYRING_TYPE_TEXT": {"HdKeyring": "C<PERSON>é par seed phrase", "SimpleKeyring": "Importé par clé privée", "WatchAddressKeyring": "Contact"}, "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Mainnet & Testnet", "TESTNET": "Uniquement les testnets"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "Importé par seed phrase (phrase secrète)"}, "global": {"Balance": "Solde", "Cancel": "Annuler", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "Confirm": "Confirmer", "Deleted": "Supprimé", "Done": "Fait", "Loading": "Chargement", "Save": "<PERSON><PERSON><PERSON><PERSON>", "addButton": "Ajouter", "appDescription": "Le wallet révolutionnaire pour Ethereum et toutes les chaînes EVM", "appName": "<PERSON><PERSON>", "assets": "actifs", "back": "Retour", "backButton": "Retour", "cancelButton": "Annuler", "closeButton": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "confirmButton": "Confirmer", "copied": "<PERSON><PERSON><PERSON>", "copyAddress": "Co<PERSON>r l'adresse", "editButton": "Modifier", "failed": "<PERSON><PERSON><PERSON>", "gas": "Gaz", "next": "Suivant", "nonce": "nonce", "ok": "OK", "proceedButton": "<PERSON><PERSON><PERSON>", "refresh": "Actualiser", "scamTx": "Tx frauduleuse", "unknownNFT": "NFT inconnu", "watchModeAddress": "<PERSON>resse en mode observation", "Nonce": "<PERSON><PERSON>", "tryAgain": "Essayer à nouveau", "notSupportTesntnet": "Non pris en charge pour le réseau personnalisé"}, "page": {"activities": {"signedText": {"empty": {"desc": "Tous les textes signés via <PERSON><PERSON> se<PERSON>t répertoriés ici.", "title": "Pas encore de texte signé"}, "label": "Texte"}, "signedTx": {"common": {"cancel": "Annuler", "pendingDetail": "Détails en attente", "speedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "Inconnu", "unknownProtocol": "Protocole inconnu", "unlimited": "illimité"}, "empty": {"desc": "Toutes les transactions signées via Rabby seront répertoriées ici.", "title": "Aucune transaction signée pour le moment"}, "explain": {"approve": "Approuver {{count}} {{token}} pour {{protocol}}", "cancel": "Annuler l'approbation de {{token}} pour {{protocol}}", "cancelNFTCollectionApproval": "Annuler l'approbation de la collection NFT pour {{protocol}}", "cancelSingleNFTApproval": "Annuler l'approbation NFT unique pour {{protocol}}", "nftCollectionApproval": "Approbation de la collection NFT pour {{protocol}}", "send": "Envoyer {{amount}} {{symbol}}", "singleNFTApproval": "Approbation NFT unique pour {{protocol}}", "unknown": "Transaction inconnue"}, "label": "Transactions", "status": {"canceled": "<PERSON><PERSON><PERSON>", "failed": "É<PERSON><PERSON>e", "pending": "En attente", "submitFailed": "Échec lors de l'envoi", "pendingBroadcast": "En attente : à diffuser", "pendingBroadcastFailed": "En attente : échec de la diffusion", "pendingBroadcasted": "En attente : diffusée", "withdrawed": "Annulation rapide"}, "tips": {"canNotCancel": "Impossible d'accélérer ou d'annuler : ce n'est pas la première transaction en attente", "pendingDetail": "Une seule transaction sera complétée, et c'est presque toujours celle avec le prix du gaz le plus élevé", "pendingBroadcast": "Mode économie de gaz : en attente de frais de réseau inférieurs. Max {{deadline}} heures d'attente.", "pendingBroadcastBtn": "Diffuser maintenant", "pendingBroadcastRetry": "La diffusion a échoué. Dernière tentative : {{pushAt}}", "pendingBroadcastRetryBtn": "Diffuser à nouveau"}, "txType": {"cancel": "Annuler tx", "initial": "Transaction initiale", "speedUp": "Accélérer la transaction"}, "CancelTxPopup": {"options": {"onChainCancel": {"desc": "Nouvelle transaction à annuler, nécessite du gaz", "title": "Annuler on-chain"}, "quickCancel": {"desc": "Annuler avant la diffusion, pas de frais de gaz", "tips": "Uniquement pris en charge pour les transactions qui n'ont pas été diffusées", "title": "Annulation rapide"}, "removeLocalPendingTx": {"title": "Effacer en attente localement", "desc": "Supprimer la transaction en attente de l'interface"}}, "title": "Annuler la transaction", "removeLocalPendingTx": {"title": "Supprimer la transaction localement", "desc": "Cette action supprimera localement la transaction en attente. \nLa transaction en attente peut toujours être soumise avec succès à l'avenir."}}, "MempoolList": {"empty": "Introuvable dans aucun nœud", "reBroadcastBtn": "Diffuser à nouveau", "title": "<PERSON><PERSON><PERSON> dans {{count}} nœuds RPC"}, "PredictTime": {"failed": "Échec de la prédiction du temps d'emballage", "noTime": "Le temps d'emballage est prévu", "time": "Prévu pour être emballé dans {{time}}"}, "SkipNonceAlert": {"alert": "Nonce #{{nonce}} ignoré sur la chaîne {{chainName}}. <PERSON>la peut entraîner de futures transactions en attente. <5></5> <6>Soumettre une tx</6> <7></7> on-chain pour régler le problème", "clearPendingAlert": "La transaction {{chainName}} ({{nonces}}) est en attente depuis plus de 3 minutes. Vous pouvez <5></5> <6>Effacer localement en attente</6> <7></7> et soumettre à nouveau la transaction."}, "message": {"broadcastSuccess": "<PERSON><PERSON><PERSON><PERSON>", "cancelSuccess": "<PERSON><PERSON><PERSON>", "reBroadcastSuccess": "Rediffusée", "deleteSuccess": "Supprimé avec succès"}, "gas": {"noCost": "Pas de frais de gaz"}, "CancelTxConfirmPopup": {"desc": "Cela supprimera la transaction en attente de votre interface. Vous pourrez ensuite initier une nouvelle transaction.", "title": "Effacer les actions en attente localement", "warning": "La transaction supprimée peut toujours être confirmée sur la chaîne à moins qu'elle ne soit remplacée."}}, "title": "Journal de signatures"}, "addToken": {"balance": "Solde", "noTokenFound": "Aucun token trouvé", "noTokenFoundOnThisChain": "Aucun token trouvé sur cette chaîne", "title": "Ajouter un token personnalisé sur Rabby", "tokenCustomized": "Le token actuel a déjà été ajouté en tant que token personnalisé", "tokenNotFound": "Token introuvable à partir de cette adresse de contrat", "tokenOnMultiChains": "Adresse du token présente sur plusieurs chaînes. Veuillez en choisir une", "tokenSupported": "Le token a été pris en charge sur Rabby", "hasAdded": "Ce jeton a été ajouté à votre keyring."}, "addressDetail": {"add-to-whitelist": "A<PERSON>ter à la liste blanche", "address": "<PERSON><PERSON><PERSON>", "address-detail": "<PERSON><PERSON><PERSON> de l'adresse", "address-note": "Note sur l'adresse", "admins": "Administrateurs", "assets": "Actifs", "backup-private-key": "Sauvegarder la clé privée", "backup-seed-phrase": "Sauvegarder la seed phrase", "coboSafeErrorModule": "L'adresse a expiré, veuillez supprimer et importer à nouveau celle-ci.", "delete-address": "Supp<PERSON>er l'adresse", "delete-desc": "Avant de supprimer, gardez les points suivants à l'esprit pour comprendre comment protéger vos actifs.", "direct-delete-desc": "<PERSON>tte adresse est une adresse {{renderBrand}}, <PERSON><PERSON> ne stocke pas la clé privée ou la seed phrase pour celle-ci, vous pouvez simplement la supprimer", "edit-memo-title": "Modifier la note d'adresse", "hd-path": "HD Path", "importedDelegatedAddress": "Adresse déléguée importée", "manage-addresses-under-this-seed-phrase": "<PERSON><PERSON><PERSON> les adresses liées à cette seed phrase", "manage-seed-phrase": "<PERSON><PERSON><PERSON> la seed phrase", "please-input-address-note": "<PERSON><PERSON><PERSON><PERSON> saisir une note d'adresse", "qr-code": "QR code", "remove-from-whitelist": "Supp<PERSON><PERSON> de la liste blanche", "safeModuleAddress": "Adresse du module sécurisé", "source": "Source", "tx-requires": "Toute transaction nécessite <2>{{num}}</2> confirmations", "manage-addresses-under": "<PERSON><PERSON><PERSON> les adresses sous ce {{brand}}"}, "approvals": {"RevokeApprovalModal": {"confirm": "Confirmer {{ selectedCount }}", "selectAll": "<PERSON><PERSON>", "subTitleContract": "Approuvé pour les contrats suivants", "subTitleTokenAndNFT": "Tokens et NFTs approuvés", "title": "Approbations", "unSelectAll": "<PERSON><PERSON>", "tooltipPermit2": "Cette approbation est approuvée via le contrat Permit2 :  \n{{ permit2Id }}"}, "component": {"ApprovalContractItem": {"ApprovalCount_one": "Approbation", "ApprovalCount_other": "Approbations"}, "RevokeButton": {"btnText_one": "Révoquer ({{count}})", "btnText_other": "Révoquer ({{count}})", "btnText_zero": "Révoquer", "permit2Batch": {"modalTitle_other": "Un total de <2>{{count}}</2> signatures est requis", "modalContent": "Les approbations du même contrat Permit2 seraient regroupées sous la même signature.", "modalTitle_one": "Un total de <2>{{count}}</2> signature est requis"}}, "ViewMore": {"text": "Voir plus"}, "table": {"bodyEmpty": {"loadingText": "Chargement...", "noMatchText": "Aucune concordance", "noDataText": "Aucune autorisation"}}}, "header": {"title": "Approbations sur {{ address }}"}, "search": {"placeholder": "Rechercher {{ type }} par nom/adresse"}, "tab-switch": {"assets": "Par actifs", "contract": "Par contrats"}, "tableConfig": {"byAssets": {"columnTitle": {"approvedAmount": "<PERSON><PERSON> approuv<PERSON>", "approvedSpender": "\"Spender\" approuvé", "asset": "Actif", "myApprovalTime": "Heure d'approbation", "type": "Type"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "<PERSON><PERSON> approuv<PERSON>", "tipMyBalance": "<PERSON> <PERSON>e"}}}, "byContracts": {"columnTip": {"contractTrustValue": "La valeur de confiance fait référence à la valeur totale de l'actif approuvé et exposé à ce contrat. Une valeur de confiance faible indique soit un risque, soit une inactivité pendant 180 jours.", "contractTrustValueDanger": "La valeur de confiance du contrat < 10 000 $", "contractTrustValueWarning": "La valeur de confiance du contrat < 100 000 $"}, "columnTitle": {"contract": "Contrat", "contractTrustValue": "Valeur de confiance du contrat", "myApprovalTime": "Heure d'approbation", "myApprovedAssets": "Mes actifs approuvés", "revokeTrends": "Tendances des révocations sur 24h"}}}, "revokeModal": {"confirmTitle": "Révocation en lot en un clic", "done": "<PERSON><PERSON><PERSON><PERSON>", "approvalCount_other": "{{count}} approbations", "resume": "<PERSON><PERSON><PERSON>", "pause": "Pause", "signAndStartRevoke": "Signer et commencer la révocation", "revoked": "Révoqué :", "confirmRevokePrivateKey": "En utilisant une phrase de récupération ou une adresse de clé privée, vous pouvez révoquer en masse {{count}} autorisations en un seul clic.", "batchRevoke": "Révocation en lot", "approvalCount_one": "{{count}} approval", "revokeOneByOne": "Révoquer un par un", "approvalCount_zero": "{{count}} approval", "totalRevoked": "Total :", "confirmRevokeLedger": "En utilisant une adresse Ledger, vous pouvez révoquer en masse {{count}} autorisations en 1 clic.", "paused": "En pause", "waitInQueue": "Attendez dans la file d'attente", "ledgerAlert": "Veuillez ouvrir l'application Ethereum sur votre appareil Ledger", "defaultFailed": "Échec de la transaction", "gasTooHigh": "Les frais de Gas sont élevés", "cancelTitle": "Annuler les révocations restantes", "submitTxFailed": "Échec de la soumission", "simulationFailed": "Simulation Failed", "ledgerSended": "Veuillez signer la demande sur Ledger ({{current}}/{{total}})", "connectLedger": "Connecter <PERSON>", "revokeWithLedger": "Commencez Revoke avec Ledger", "stillRevoke": "Encore Révoquer", "useGasAccount": "Votre solde de gaz est bas. Votre GasAccount couvrira les frais de gaz.", "cancelBody": "Si vous fermez cette page, les révocations restantes ne seront pas exécutées.", "gasNotEnough": "Gaz insuffisant pour soumettre", "ledgerSending": "Envoi de la demande de signature ({{current}}/{{total}})", "confirm": "Confirmer", "ledgerSigned": "Signé. Création de la transaction ({{current}}/{{total}})"}}, "backupPrivateKey": {"alert": "Cette clé privée permet l'accès à vos actifs. NE la perdez PAS et ne la communiquez à personne, sinon vous risquez de perdre vos actifs pour toujours. Veuillez la consulter dans un environnement sécurisé et la conserver soigneusement.", "clickToShow": "Cliquer pour afficher la clé privée", "clickToShowQr": "Cliquer pour afficher le QR code de la clé privée", "title": "Sauvegarder la clé privée"}, "backupSeedPhrase": {"alert": "Cette seed phrase permet l'accès à vos actifs. NE la perdez PAS et ne la communiquez à personne, sinon vous risquez de perdre vos actifs pour toujours. Veuillez la consulter dans un environnement sécurisé et la conserver soigneusement.", "clickToShow": "Cliquer pour afficher la seed phrase", "copySeedPhrase": "Copier la seed phrase", "title": "Sauvegarder la seed phrase", "showQrCode": "Afficher le code QR", "qrCodePopupTitle": "Code QR", "qrCodePopupTips": "Ne partagez jamais le code QR de la seed phrase avec qui que ce soit. Veuillez le consulter dans un environnement sécurisé et le conserver avec soin."}, "chainList": {"mainnet": "Mainnets", "testnet": "Testnets", "title": "{{count}} cha<PERSON><PERSON> prises en charge"}, "connect": {"SignTestnetPermission": {"title": "Autorisation de signature"}, "addedToBlacklist": "Ajouté à votre liste noire", "addedToWhitelist": "Ajouté à votre liste blanche", "blocked": "<PERSON><PERSON><PERSON><PERSON>", "connectBtn": "Connecter", "flagByMM": "Signalé par MetaMask", "flagByRabby": "<PERSON><PERSON> par <PERSON>", "flagByScamSniffer": "<PERSON>é par <PERSON>am<PERSON>niffer", "foundForbiddenRisk": "Risques détectés. La connexion est bloquée.", "ignoreAll": "<PERSON>gno<PERSON> tout", "listedBy": "Listé par", "manageWhiteBlackList": "<PERSON><PERSON><PERSON> la liste blanche/liste noire", "markAsBlockToast": "<PERSON><PERSON> comme \"bloqué\"", "markAsTrustToast": "<PERSON><PERSON> comme \"fiable\"", "markRemovedToast": "Marquage supprimé", "markRuleText": "Mon marquage", "myMark": "Mon marquage", "noMark": "<PERSON>cun marquage", "noWebsite": "Aucun", "notOnAnyList": "Sur aucune liste", "onYourBlacklist": "Sur votre liste noire", "onYourWhitelist": "Sur votre liste blanche", "popularLevelHigh": "<PERSON><PERSON>", "popularLevelLow": "Faible", "popularLevelMedium": "<PERSON><PERSON><PERSON>", "popularLevelVeryLow": "<PERSON><PERSON><PERSON> bas", "removedFromAll": "Supprimé de toutes les listes", "selectChainToConnect": "Sé<PERSON><PERSON>ner une chaîne à laquelle vous connecter", "sitePopularity": "Popularité du site", "title": "Se connecter à la DApp", "trusted": "Fiable", "verifiedByRabby": "Vérifié par <PERSON>", "SelectWallet": {"title": "Sélectionnez un portefeuille à connecter", "desc": "Choisissez parmi les portefeuilles que vous avez installés"}, "otherWalletBtn": "Connect with Another Wallet", "connectAddress": "Connect Address"}, "createPassword": {"agree": "J'ai lu et j'accepte les<1/> <2>Conditions d'utilisation</2>", "confirmError": "Les mots de passe ne correspondent pas", "confirmPlaceholder": "Confirmer le mot de passe", "confirmRequired": "Veuillez confirmer le mot de passe", "passwordMin": "Le mot de passe doit comporter au moins 8 caractères", "passwordPlaceholder": "Le mot de passe doit comporter au moins 8 caractères", "passwordRequired": "Veuillez saisir le mot de passe", "title": "Définir le mot de passe"}, "customRpc": {"EditRPCModal": {"invalidChainId": "ID de chaîne invalide", "invalidRPCUrl": "URL de RPC invalide", "rpcAuthFailed": "L'authentification RPC a échoué", "rpcUrl": "URL de RPC", "rpcUrlPlaceholder": "Entrer l'URL de RPC", "title": "Modifier le RPC"}, "add": "Ajouter un RPC", "closed": "<PERSON><PERSON><PERSON>", "desc": "L'activation du RPC personnalisé remplacera Rabby comme nœud par défaut. Pour continuer à utiliser Rabby, veuil<PERSON><PERSON> dés<PERSON>r ou supprimer le nœud RPC personnalisé.", "empty": "Aucun RPC personnalisé", "opened": "Ouvert", "title": "RPC personnalisé", "EditCustomTestnetModal": {"title": "Ajouter un réseau personnalis<PERSON>", "quickAdd": "<PERSON><PERSON>t rapide depuis Chainlist"}}, "dashboard": {"assets": {"amount": "QUANTITÉ", "blockDescription": "Le token que vous avez bloqué sera affiché ici", "blockLinkText": "Rechercher une adresse pour bloquer un token", "comingSoon": "À venir...", "customDescription": "Le token personnalisé que vous avez ajouté sera affiché ici", "customLinkText": "Rechercher une adresse pour ajouter un token personnalisé", "noAssets": "Aucun actif", "portfolio": {"fractionTips": "Calcul en fonction du prix du token ERC-20 lié.", "nftTips": "Calculé sur la base du floor price reconnu par ce protocole."}, "searchPlaceholder": "Tokens", "table": {"PL": "P&L", "assetAmount": "Actif / Quantité", "balanceValue": "Solde / Valeur", "claimable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dailyUnlock": "Déblocage quotidien", "debtRatio": "Ratio d'endettement", "endAt": "Fin à", "exerciseEnd": "Fin de l'exercice", "healthRate": "<PERSON><PERSON> de <PERSON>", "lentAgainst": "PRÊTÉ CONTRE", "leverage": "<PERSON><PERSON><PERSON>", "lowValueAssets": "{{count}} actifs de faible valeur", "lowValueDescription": "Les actifs de faible valeur seront affichés ici", "noMatch": "Aucune concordance", "percent": "Pour cent", "pool": "POOL", "price": "Prix", "side": "<PERSON><PERSON><PERSON>", "strikePrice": "Prix ​​d'exercice", "summaryDescription": "Tous les actifs des protocoles (par exemple les tokens LP) sont résolus en actifs sous-jacents pour les calculs statistiques", "summaryTips": "Valeur de l'actif divisée par la valeur nette totale", "token": "Token", "tradePair": "Paire d'échange", "type": "Type", "unlockAt": "Débloquer à", "unsupportedPoolType": "Type de pool non pris en charge", "useValue": "Valeur en USD", "lowValueAssets_0": "{{count}} jeton de faible valeur", "lowValueAssets_one": "{{count}} low value token", "lowValueAssets_other": "{{count}} low value tokens"}, "tokenButton": {"subTitle": "Le token de cette liste ne sera pas ajouté au solde total"}, "unfoldChain": "Déplier 1 chaîne", "unfoldChainPlural": "Déplier {{more<PERSON>en}} cha<PERSON>nes", "usdValue": "VALEUR EN USD", "AddMainnetToken": {"tokenAddress": "<PERSON><PERSON><PERSON>", "searching": "Recherche de token", "tokenAddressPlaceholder": "<PERSON><PERSON><PERSON>", "title": "Ajouter un jeton personnalisé", "isBuiltInToken": "Le token est déjà pris en charge", "selectChain": "Sélectionner la chaîne", "notFound": "<PERSON>on non trouvé"}, "AddTestnetToken": {"tokenAddressPlaceholder": "<PERSON><PERSON><PERSON>", "notFound": "<PERSON>on non trouvé", "searching": "Recherche de Token", "tokenAddress": "<PERSON><PERSON><PERSON>", "title": "Ajouter un token de réseau personnalisé", "selectChain": "Sélectionnez la chaîne"}, "TestnetAssetListContainer": {"add": "<PERSON><PERSON>", "addTestnet": "<PERSON><PERSON><PERSON>"}, "customButtonText": "Ajouter un token personnalisé", "noTestnetAssets": "Aucun actif de réseau personnalis<PERSON>", "addTokenEntryText": "<PERSON><PERSON>"}, "contacts": {"noData": "Pas de don<PERSON>", "noDataLabel": "pas de données", "oldContactList": "Ancienne liste de contacts", "oldContactListDescription": "En raison de la fusion des contacts et des adresses suivies, les anciens contacts seront sauvegardés ici puis supprimés après un certain temps. Veuillez ajouter du temps si vous souhaitez continuer à utiliser cette liste."}, "feedback": {"directMessage": {"content": "Message direct", "description": "Discuter avec Rabby <PERSON>et Official sur DeBank"}, "proposal": {"content": "Proposition", "description": "Soumettre une proposition pour <PERSON>bby Wallet sur DeBank"}, "title": "<PERSON><PERSON><PERSON>"}, "hd": {"howToConnectLedger": "Comment connecter une clé Ledger", "howToSwitch": "Comment changer", "ledger": {"doc1": "<PERSON>er une clé Ledger", "doc2": "Saisir le code pin pour déverrouiller", "doc3": "Ouvrir l'application Ethereum", "reconnect": "Si cela ne fonctionne pas, essayez de <1>vous reconnecter depuis le début.</1>", "connected": "Ledger connect<PERSON>"}, "userRejectedTheRequest": "L'utilisateur a rejeté la demande.", "howToConnectKeystone": "Comment connecter Keystone", "keystone": {"doc1": "Brancher un seul Keystone", "doc2": "Entrer le mot de passe pour déverrouiller", "doc3": "Approuver la connexion à l'ordinateur", "reconnect": "Si cela ne fonctionne pas, essayez de <1>vous reconnecter depuis le début.</1>", "title": "Assurez-vous que votre Keystone 3 Pro est sur la page d'accueil"}, "imkey": {"doc2": "Entrez le code PIN pour déverrouiller", "doc1": "Branchez un seul imKey"}, "howToConnectImKey": "Comment connecter imKey", "ledgerIsDisconnected": "Votre Ledger n'est pas connecté"}, "home": {"comingSoon": "À venir", "flip": "<PERSON><PERSON><PERSON>", "importType": "Importé par {{type}}", "metamaskIsInUseAndRabbyIsBanned": "MetaMask est utilisé et Rabby est banni", "offline": "Le réseau est déconnecté et aucune donnée n'est obtenue", "panel": {"approvals": "Approbations", "feedback": "<PERSON><PERSON><PERSON>", "gasTopUp": "Recharge de gaz", "manageAddress": "<PERSON><PERSON><PERSON> l'adresse", "more": "Plus", "nft": "NFT", "queue": "File d'attente", "receive": "Recevoir", "send": "Envoyer", "swap": "<PERSON><PERSON><PERSON>", "transactions": "Transactions", "ecology": "Écosystème", "bridge": "<PERSON><PERSON>", "rabbyPoints": "<PERSON><PERSON>", "mobile": "Mobile Sync"}, "pendingCount": "1 en attente", "pendingCountPlural": "{{countStr}} en attente", "queue": {"count": "{{count}} dans", "title": "File d'attente"}, "rabbyIsInUseAndMetamaskIsBanned": "Rabby est utilisé et Metamask est banni", "refreshTheWebPageToTakeEffect": "Actualiser la page Web pour prendre en compte les changements", "rejectAll": "<PERSON><PERSON> rejeter", "soon": "Bientôt", "transactionNeedsToSign": "la transaction doit être signée", "transactionsNeedToSign": "les transactions doivent être signées", "view": "Voir", "viewFirstOne": "Voir le premier", "whatsNew": "<PERSON><PERSON><PERSON> de <PERSON>uf", "chain": "<PERSON><PERSON><PERSON><PERSON>,", "chainEnd": "cha<PERSON><PERSON>", "missingDataTooltip": "Le solde peut ne pas être mis à jour en raison de problèmes de réseau actuels avec {{text}}."}, "nft": {"collectionList": {"all_nfts": {"label": "Tous les NFTs"}, "collections": {"label": "Collections"}}, "empty": "Aucun NFT trouvé dans les collections prises en charge", "listEmpty": "Vous n'avez pas encore reçu de NFT", "modal": {"chain": "<PERSON><PERSON><PERSON>", "collection": "Collection", "lastPrice": "Der<PERSON> prix", "purchaseDate": "Date d'achat", "send": "Envoyer", "sendTooltip": "Seuls les NFTs ERC-721 et ERC-1155 sont pris en charge pour le moment"}}, "rabbyBadge": {"claim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "claimSuccess": "<PERSON><PERSON><PERSON> réussie", "enterClaimCode": "Saisir le code de réclamation", "goToSwap": "Aller à l'échange", "imageLabel": "badge rabby", "learnMoreOnDebank": "En savoir plus sur DeBank", "noCode": "Vous n'avez pas activé le code de réclamation pour cette adresse", "rabbyValuedUserNo": "Utilisateur <PERSON><PERSON> estimé n°{{num}}", "swapTip": "V<PERSON> devez d'abord effectuer un échange avec un DEX notable dans <PERSON>bby Wallet.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> le badge Rabby pour", "viewOnDebank": "Voir sur DeBank", "viewYourClaimCode": "Afficher votre code de réclamation", "freeGasTitle": "Obtenez le Badge de Gas Gratuit pour", "freeGasTip": "Veuillez signer une transaction en utilisant Free Gas. Le bouton 'Free Gas' apparaîtra automatiquement lorsque votre gas n'est pas suffisant.", "learnMore": "En savoir plus", "rabbyFreeGasUserNo": "Utilisateur Rabby Free Gas n°{{num}}", "freeGasNoCode": "Veuillez cliquer sur le bouton ci-dessous pour visiter DeBank et obtenir le code de réclamation en utilisant d'abord votre adresse actuelle."}, "recentConnection": {"connected": "Connecté", "connectedDapp": "Rabby n'est pas connecté à la dApp actuelle. Pour vous connecter, recherchez et cliquez sur le bouton de connexion sur la page Web de la dApp.", "disconnectAll": "Tout déconnecter", "disconnectRecentlyUsed": {"description": "Les dApps épinglées resteront connectées", "title": "Déconnecter les <strong>{{count}}</strong> DApps récemment utilisées", "title_other": "Déconnecter <strong>{{count}}</strong> dApps connectées", "title_one": "Déconnecter <strong>{{count}}</strong> dApp connectée"}, "disconnected": "Déconnecté", "dragToSort": "Faire glisser pour trier", "metamaskTooltip": "Vous préférez utiliser MetaMask avec cette dApp. Mettez à jour ce paramètre à tout moment dans Paramètres > DApps préférées de MetaMask", "noDappFound": "Aucune dApp trouvée", "noPinnedDapps": "Aucune d<PERSON>pp <PERSON>", "noRecentlyConnectedDapps": "Aucune dApp récemment connectée", "notConnected": "Non connecté", "pinned": "<PERSON><PERSON><PERSON>", "recentlyConnected": "Récemment connecté", "rpcUnavailable": "Le RPC personnalisé n'est pas disponible", "title": "DApps connectées", "dapps": "DApps", "noConnectedDapps": "Aucune dApp connectée", "metamaskModeTooltip": "Impossible de connecter Rabby sur cette dApp ? Essayez d'activer le <1>Mode MetaMask</1>", "metamaskModeTooltipNew": "<PERSON><PERSON> Wallet se connectera lorsque vous sélectionnez \"MetaMask\" sur le Dapp. Vous pouvez gérer cela dans Plus > Connecter <PERSON><PERSON> en se déguisant en MetaMask"}, "security": {"comingSoon": "Plus de fonctionnalités à venir", "nftApproval": "Approbation de NFT", "title": "Sécurité", "tokenApproval": "Approbation de token"}, "settings": {"10Minutes": "10 minutes", "1Day": "1 jour", "1Hour": "1 heure", "4Hours": "4 heures", "7Days": "7 jours", "aboutUs": "À propos", "autoLockTime": "Temps avant verrouillage auto", "backendServiceUrl": "URL du service backend", "cancel": "Annuler", "claimRabbyBadge": "<PERSON><PERSON><PERSON><PERSON><PERSON> le badge Rabby !", "clearPending": "Effacer en attente localement", "clearPendingTip1": "Cette action supprime la transaction en attente de votre interface, aidant ainsi à résoudre les problèmes causés par des durées d'attente prolongées sur le réseau.", "clearPendingTip2": "Cela n'affecte pas les soldes de votre compte ni ne nécessite une nouvelle saisie de votre phrase de récupération. Tous les actifs et les détails du compte restent sécurisés.", "clearWatchAddressContent": "Êtes-vous certain de vouloir supprimer toutes les adresses du mode observation ?", "clearWatchMode": "Effacer le mode observation", "currentVersion": "Version actuelle", "disableWhitelist": "Désactiver la liste blanche", "disableWhitelistTip": "<PERSON><PERSON> pouvez envoyer des actifs vers n'importe quelle adresse une fois désactivé", "enableWhitelist": "<PERSON>r la liste blanche", "enableWhitelistTip": "Une fois activée, vous ne pouvez envoyer des actifs, via <PERSON><PERSON>, qu'aux adresses de la liste blanche.", "features": {"connectedDapp": "DApps connectées", "label": "Fonctionnalités", "lockWallet": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>", "manageAddress": "<PERSON><PERSON><PERSON> les adresses", "signatureRecord": "Journal des signatures", "rabbyPoints": "<PERSON><PERSON>", "searchDapps": "Rechercher des dApps", "gasTopUp": "Recharge de Gas"}, "followUs": "Suivez-nous", "host": "<PERSON><PERSON><PERSON>", "inputOpenapiHost": "Veuillez saisir l'hôte OpenAPI", "lock": {"never": "<PERSON><PERSON>"}, "pendingTransactionCleared": "Transaction(s) en attente effacée(s)", "pleaseCheckYourHost": "Veuillez vérifier votre hôte", "requestDeBankTestnetGasToken": "Demander un token de gaz pour le testnet DeBank", "reset": "Restaurer le réglage initial", "save": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"currentLanguage": "Langue actuelle", "customRpc": "RPC personnalisé", "enableTestnets": "<PERSON><PERSON> les test<PERSON>", "enableWhitelistForSendingAssets": "Activer la liste blanche pour l'envoi d'actifs", "label": "Paramètres", "metamaskPreferredDapps": "DApps préférées de MetaMask", "themeMode": "Thème", "toggleThemeMode": "Thème", "customTestnet": "Ajouter un réseau personnalis<PERSON>", "metamaskMode": "<PERSON><PERSON><PERSON> en se faisant passer pour MetaMask", "enableDappAccount": "Changer indépendamment l'adresse du DApp\n"}, "supportedChains": "Chaînes prises en charge", "testnetBackendServiceUrl": "URL du service backend testnet", "updateAvailable": "Mise à jour disponible", "updateVersion": {"content": "Une nouvelle mise à jour pour Rabby Wallet est disponible. Cliquer pour voir comment mettre à jour manuellement.", "okText": "Voir Tutoriel", "successTip": "Vous utilisez la dernière version", "title": "Mise à jour disponible"}, "warning": "Avertissement", "clearPendingWarningTip": "La transaction supprimée peut encore être confirmée sur la blockchain à moins qu'elle ne soit remplacée.", "DappAccount": {"title": "Changer l'adress<PERSON> indépendamment\n", "button": "Activer\n", "desc": "Une fois activé, vous pouvez choisir l'adresse à connecter à chaque <PERSON> indépendamment. Le changement de votre adresse principale n'affectera pas l'adresse connectée à chaque <PERSON>.\n"}}, "tokenDetail": {"blocked": "<PERSON><PERSON><PERSON><PERSON>", "blockedButton": "<PERSON><PERSON><PERSON><PERSON>", "blockedTip": "Le token bloqué ne sera pas affiché dans la liste des tokens", "customized": "<PERSON><PERSON><PERSON><PERSON>", "customizedButton": "<PERSON><PERSON><PERSON><PERSON>", "noTransactions": "Aucune transaction", "notSelectedCustom": "Le token n'est pas répertorié par Rabby. Il sera ajouté à la liste des tokens si vous activez.", "notSupported": "Le token sur cette chaîne n'est pas pris en charge", "receive": "Recevoir", "scamTx": "Transaction frauduleuse", "selectedCustom": "Le token n'est pas répertorié par Rabby. Vous l'avez ajouté à la liste des tokens personnalisés.", "send": "Envoyer", "swap": "<PERSON><PERSON><PERSON>", "txFailed": "<PERSON><PERSON><PERSON>", "blockedButtons": "jetons bloqués", "customizedButtons": "jetons personnalisés", "Chain": "<PERSON><PERSON><PERSON>", "SupportedExchanges": "Échanges pris en charge", "verifyScamTips": "C'est un token de scam", "customizedListTitle": "token personnalisé", "BridgeIssue": "Jeton bridge par un tiers", "NoSupportedExchanges": "Aucuns échanges pris en charge disponibles", "AddToMyTokenList": "Ajouter à ma liste de tokens", "blockedTips": "Le token bloqué ne sera pas affiché dans la liste des tokens.", "blockedListTitle": "jeton bloqué", "noIssuer": "Aucune information sur l'émetteur disponible", "customizedListTitles": "tokens personnalisés", "myBalance": "<PERSON> <PERSON>e", "OriginalToken": "Token original", "NoListedBy": "Aucune information de liste disponible", "OriginIssue": "Émis nativement sur cette blockchain", "ListedBy": "Répertorié par", "customizedHasAddedTips": "Le jeton n'est pas listé par Rabby. Vous l'avez ajouté à la liste des jetons manuellement.", "ContractAddress": "<PERSON>resse du contrat", "maybeScamTips": "Ceci est un token de faible qualité et peut être une arnaque.", "TokenName": "Nom du Token", "blockedListTitles": "tokens bloqués", "IssuerWebsite": "Site de l'émetteur", "BridgeProvider": "Fournisseur de pont", "fdvTips": "La capitalisation boursière si l'offre maximale était en circulation. Évaluation totalement diluée (FDV) = Prix x Offre maximale. Si l'offre maximale est nulle, FDV = Prix x Offre totale. Si ni l'offre maximale ni l'offre totale ne sont définies ou sont infinies, le FDV n'est pas affiché."}, "GnosisWrongChainAlertBar": {"warning": "L'adresse Safe ne prend pas en charge {{chain}}", "notDeployed": "Votre adresse Safe n'est pas déployée sur cette chaîne"}, "echologyPopup": {"title": "Écosystème"}, "MetamaskModePopup": {"title": "Mode MetaMask", "enableDesc": "Activez si dApp fonctionne uniquement avec MetaMask", "desc": "Si vous ne pouvez pas connecter Rabby sur une dApp, activez le Mode MetaMask et connectez-vous en sélectionnant l'option MetaMask.", "toastSuccess": "Activé. Actualisez la page pour vous reconnecter.", "footerText": "Ajoutez plus de dApps au Mode MetaMask dans Plus > Mode MetaMask"}, "offlineChain": {"chain": "{{chain}} sera bientôt non intégré.", "tips": "La chaîne {{chain}} ne sera pas intégrée le {{date}}. Vos actifs ne seront pas affectés mais ne seront pas inclus dans votre solde total. Pour y accéder, vous pouvez l'ajouter comme réseau personnalisé dans \"More\"."}, "recentConnectionGuide": {"title": "<PERSON><PERSON>, changez d'adresse pour la connexion Dapp.", "button": "<PERSON><PERSON><PERSON>"}}, "ethSign": {"alert": "Signer avec \"eth_sign\" peut entraîner une perte d'actifs. Pour votre sécurité, <PERSON><PERSON> ne prend pas en charge cette méthode."}, "gasTopUp": {"Amount": "Quantité", "Balance": "Solde", "Confirm": "Confirmer", "Continue": "<PERSON><PERSON><PERSON>", "Including-service-fee": "Y compris {{fee}} de frais de service", "InsufficientBalance": "Solde insuffisant dans le contrat Rabby lié à la chaîne actuelle. Veuillez réessayer plus tard.", "InsufficientBalanceTips": "Solde insuffisant", "Loading_Tokens": "Chargement des tokens...", "No_Tokens": "Aucun token", "Payment-Token": "Token de paiement", "Select-from-supported-tokens": "Sélectionner parmi les tokens pris en charge", "Select-payment-token": "Sélectionner le token de paiement", "Token": "Token", "Value": "<PERSON><PERSON>", "description": "Faites le plein de gaz en nous envoyant des tokens disponibles sur une autre chaîne. Virement instantané dès confirmation de votre paiement (sans attendre qu'il soit irréversible).", "hightGasFees": "Ce montant de recharge est trop faible car le réseau cible nécessite des frais de gaz élevés.", "payment": "Paiement de recharge de gaz", "service-fee-tip": "En fournissant le service de recharge de gaz, Rabby doit supporter la perte liée à la fluctuation des tokens et les frais de gaz pour la recharge. Des frais de service de 20% sont donc facturés.", "title": "Recharge de gaz instantanée", "topUpChain": "Chaîne pour la recharge"}, "importQrBase": {"btnText": "Essayer à nouveau", "desc": "Scanner le code QR sur le wallet matériel {{brandName}}"}, "importSafe": {"error": {"invalid": "N'est pas une adresse valide", "required": "Veuillez saisir l'adresse"}, "gnosisChainDesc": "Cette adresse a été déployée sur {{count}} chaînes", "loading": "Recherche de la chaîne sur laquelle cette adresse a été déployée", "placeholder": "Veuillez saisir l'adresse", "title": "Ajouter une adresse Safe"}, "importSuccess": {"addressCount": "{{count}} adresses", "gnosisChainDesc": "Cette adresse a été déployée sur {{count}} chaînes", "title": "Importé avec succès"}, "manageAddress": {"add-address": "Ajouter l'adresse", "address-management": "Gestion des adresses", "addressTypeTip": "Importé par {{type}}", "backup-seed-phrase": "Sauvegarder seed phrase", "cancel": "Annuler", "confirm": "Confirmer", "confirm-delete": "Confirmation de la suppression", "current-address": "<PERSON><PERSON><PERSON> actuelle", "delete-all-addresses-and-the-seed-phrase": "Supprimer toutes les adresses et la seed phrase", "delete-all-addresses-but-keep-the-seed-phrase": "Supprimer toutes les adresses, mais conserver la seed phrase", "delete-checklist-1": "Je comprends que si je supprime cette adresse, la clé privée et la seed phrase correspondantes seront supprimées et Rabby ne pourra PAS les récupérer.", "delete-checklist-2": "Je confirme que j'ai sauvegardé la clé privée ou la seed phrase et que je suis prêt pour la suppression.", "delete-desc": "Avant de supprimer, gardez les points suivants à l'esprit pour comprendre comment protéger vos actifs.", "delete-empty-seed-phrase": "Supprimer la seed phrase et son adresse 0", "delete-private-key-modal-title_one": "Supprimer {{count}} adresse de clé privée", "delete-private-key-modal-title_other": "Supprimer {{count}} adresses de clé privée", "delete-seed-phrase": "Supprimer la seed phrase", "delete-seed-phrase-title_one": "Supprimer la seed phrase et son adresse {{count}}", "delete-seed-phrase-title_other": "Supprimer la seed phrase et ses {{count}} adresses", "delete-title_one": "Supprimer l'adresse {{count}} {{brand}}", "delete-title_other": "Supprimer {{count}} adresses {{brand}}", "deleted": "Supprimé", "hd-path": "HD path:", "manage-address": "<PERSON><PERSON><PERSON> l'adresse", "no-address": "<PERSON><PERSON> d'adresse", "no-address-under-seed-phrase": "Vous n'avez importé aucune adresse via cette seed phrase.", "no-match": "Aucune concordance", "private-key": "Clé privée", "search": "<PERSON><PERSON><PERSON>", "seed-phrase": "Seed phrase", "seed-phrase-delete-title": "Supprimer la seed phrase ?", "update-balance-data": "Mettre à jour les données du solde", "watch-address": "Observer l'adresse", "whitelisted-address": "<PERSON>ress<PERSON> sur liste blanche", "enterPassphraseTitle": "Entrer la pass phrase pour signer", "enterThePassphrase": "Entrer la pass phrase", "passphraseError": "Pass phrase invalide", "sort-address": "<PERSON>er adresse", "sort-by-address-note": "Trier par note d'adresse", "sort-by-address-type": "Trier par type d'adresse", "sort-by-balance": "Trier par solde", "addNewAddress": "Ajouter une nouvelle adresse", "CurrentDappAddress": {"desc": "Changer <PERSON>'<PERSON><PERSON><PERSON>"}}, "newAddress": {"addContacts": {"addressEns": "Adresse / ENS", "cameraTitle": "Veuillez scanner le QR code avec votre appareil photo", "content": "Ajouter des contacts", "description": "Vous pouvez également l'utiliser comme adresse observée uniquement", "notAValidAddress": "N'est pas une adresse valide", "required": "Veuillez saisir l'adresse", "scanViaMobileWallet": "Scanner via un wallet mobile", "scanQRCode": "Scanner les QR codes avec des wallets compatibles WalletConnect", "scanViaPcCamera": "Scanner via une caméra sur PC", "walletConnect": "WalletConnect", "walletConnectVPN": "WalletConnect sera instable si vous utilisez un VPN."}, "coboSafe": {"addCoboArgusAddress": "Ajouter une adresse Cobo Argus", "findTheAssociatedSafeAddress": "Trouver l'adresse Safe associée", "import": "Importer", "inputSafeModuleAddress": "Saisir l'adresse du module Safe", "invalidAddress": "<PERSON><PERSON><PERSON> invalide", "whichChainIsYourCoboAddressOn": "Sur quelle chaîne se trouve votre adresse cobo"}, "connectHardwareWallets": "Connecter des wallets matériels", "connectInstitutionalWallets": "Connecter des wallets institutionnels", "connectMobileWalletApps": "Connecter des applications de wallet mobile", "createNewSeedPhrase": "<PERSON><PERSON>er une nouvelle seed phrase", "hd": {"addToRabby": "<PERSON><PERSON><PERSON> à <PERSON>", "addresses": "Adresses", "addressesIn": "Adresses dans {{0}}", "addressesInRabby": "Ad<PERSON> dans Rabby {{0}}", "advancedSettings": "Paramètres avancés", "balance": "Solde", "basicInformation": "Informations de base", "bitbox02": {"disconnected": "Impossible de se connecter à BitBox02. Veuillez actualiser la page pour vous reconnecter. Raison : {{0}}", "hdPathType": {"bip44": "BIP44 : HDpath défini par le protocole BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44 : HDpath défini par le protocole BIP44."}}, "clickToGetInfo": "Cliquer pour obtenir les informations on-chain", "connectedToLedger": "Connecté à une clé Ledger", "connectedToOnekey": "Connecté à une OneKey", "connectedToTrezor": "Con<PERSON><PERSON> à Trezor", "customAddressHdPath": "Aadresse HP path personnalisée", "done": "Fait", "firstTransactionTime": "Heure de la première transaction", "getOnChainInformation": "Obtenir des informations on-chain", "gridplus": {"hdPathType": {"bip44": "Norme BIP44 : HDpath défini par le protocole BIP44. Dans les 3 premières adresses, il y a des adresses utilisées on-chain.", "ledgerLive": "Ledger Live : HD path officiel de Ledger. Dans les 3 premières adresses, il y a des adresses utilisées on-chain.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto. Dans les 3 premières adresses, il y a des adresses utilisées on-chain."}, "hdPathTypeNochain": {"bip44": "Standard BIP44 : HD path défini par le protocole BIP44. Dans les 3 premières adresses, aucune adresse n'est utilisée on-chain.", "ledgerLive": "Ledger Live : HD path officiel de Ledger. Dans les 3 premières adresses, aucune adresse n'est utilisée on-chain.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto. Dans les 3 premières adresses, aucune adresse n'est utilisée on-chain."}, "switch": {"content": "L'importation de plusieurs appareils GridPlus n'est pas prise en charge. Si vous passez sur un nouvel appareil GridPlus, la liste d'adresses de l'appareil actuel sera supprimée avant de démarrer le processus d'importation.", "title": "Passer sur un nouvel appareil GridPlus"}, "switchToAnotherGridplus": "Passer sur un autre GridPlus"}, "hideOnChainInformation": "Masquer les informations on-chain", "keystone": {"hdPathType": {"bip44": "BIP44 : HDpath défini par le protocole BIP44.", "ledgerLive": "Ledger Live : HD path officiel de Ledger. Vous ne pouvez gérer que 10 adresses avec le path Ledger Live.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto."}, "hdPathTypeNochain": {"bip44": "BIP44 : HDpath défini par le protocole BIP44.", "ledgerLive": "Ledger Live : HD path officiel de Ledger. Vous ne pouvez gérer que 10 adresses avec le path Ledger Live.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto."}}, "ledger": {"hdPathType": {"bip44": "Standard BIP44 : HDpath défini par le protocole BIP44. Dans les 3 premières adresses, il y a des adresses utilisées on-chain.", "ledgerLive": "Ledger Live : HD path officiel de Ledger. Dans les 3 premières adresses, il y a des adresses utilisées on-chain.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto. Dans les 3 premières adresses, il y a des adresses utilisées on-chain."}, "hdPathTypeNoChain": {"bip44": "Standard BIP44 : HD path défini par le protocole BIP44. Dans les 3 premières adresses, aucune adresse n'est utilisée on-chain.", "ledgerLive": "Ledger Live : HD path officiel de Ledger. Dans les 3 premières adresses, aucune adresse n'est utilisée on-chain.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto. Dans les 3 premières adresses, aucune adresse n'est utilisée on-chain."}}, "loadingAddress": "Chargement de {{0}}/{{1}} adresses", "manageAddressFrom": "<PERSON><PERSON><PERSON> l'adresse de {{0}} à {{1}}", "manageAirgap": "<PERSON><PERSON><PERSON> AirGap", "manageBitbox02": "Gérer BitBox02", "manageNgraveZero": "Gérer NGRAVE ZERO", "manageCoolwallet": "<PERSON><PERSON><PERSON>", "manageGridplus": "<PERSON><PERSON><PERSON>", "manageKeystone": "<PERSON><PERSON><PERSON>", "manageSeedPhrase": "<PERSON><PERSON><PERSON> la seed phrase", "mnemonic": {"hdPathType": {"default": "Par défaut : Le HD path par défaut est utilisé pour l'importation d'une seed phrase.", "bip44": "Norme BIP44 : HDpath défini par le protocole BIP44.", "ledgerLive": "Ledger Live : HD path officiel de Ledger.", "legacy": "Legacy : HD path utilisé par MEW / Mycrypto."}, "hdPathTypeNoChain": {"default": "Par défaut : Le HD path par défaut est utilisé pour l'importation d'une seed phrase."}}, "notes": "Notes", "onekey": {"hdPathType": {"bip44": "BIP44 : HDpath défini par le protocole BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44 : HDpath défini par le protocole BIP44."}}, "qrCode": {"switch": {"content": "Il n'est pas possible d'importer plusieurs appareils {{0}}. Si vous passez sur un nouvel appareil {{0}}, la liste d'adresses de l'appareil actuel sera supprimée avant de démarrer le processus d'importation.", "title": "Passer sur un nouvel appareil {{0}}"}, "switchAnother": "Passer sur un autre {{0}}"}, "selectHdPath": "Sélectionner le HD path :", "selectIndexTip": "Sélectionner le numéro de série des adresses à partir desquelles commencer :", "tooltip": {"added": "L'adresse est ajoutée à Rabby", "connectError": "La connexion s'est arrêtée. Veuillez actualiser la page pour vous reconnecter.", "disconnected": "Impossible de se connecter au wallet matériel. Veuillez essayer de vous reconnecter.", "removed": "L'adresse est supprimée de Rabby"}, "trezor": {"hdPathType": {"bip44": "BIP44 : HDpath défini par le protocole BIP44.", "ledgerLive": "Ledger Live : chemin <PERSON> officiel de Ledger.", "legacy": "Legacy : chemin HD utilisé par MEW / Mycrypto."}, "hdPathTypeNoChain": {"bip44": "BIP44 : HDpath défini par le protocole BIP44.", "ledgerLive": "Ledger Live : chemin <PERSON> officiel de Ledger.", "legacy": "Legacy : chemin HD utilisé par MEW / Mycrypto."}, "message": {"disconnected": "{{0}}La connexion s'est arrêtée. Veuillez actualiser la page pour vous reconnecter."}}, "usedChains": "Chaînes utilisées", "waiting": "En attente", "manageImtokenOffline": "<PERSON><PERSON><PERSON> im<PERSON>oken", "importBtn": "Importer ({{count}})", "manageImKey": "<PERSON><PERSON><PERSON>"}, "importKeystore": "Importer un \"keystore\"", "importMyMetamaskAccount": "Importer mon compte MetaMask", "importPrivateKey": "Importer une clé privée", "importSeedPhrase": "Importer une seed phrase", "importYourKeystore": "Importer votre \"keystore\"", "importedSuccessfully": "Importé avec succès", "incorrectPassword": "mot de passe incorrect", "keystore": {"description": "Sélectionnez le fichier \"keystore\" que vous souhaitez importer et entrez le mot de passe correspondant", "password": {"placeholder": "Mot de passe", "required": "Veuillez saisir le mot de passe"}}, "ledger": {"allow": "Permettre", "allowRabbyPermissionsTitle": "Autorisez <PERSON> :", "cameraPermission1": "Autoriser Rabby à accéder à la caméra dans la fenêtre contextuelle du navigateur", "cameraPermissionTitle": "Autoriser <PERSON><PERSON> à accéder à la caméra", "ledgerPermission1": "Se connecter à un appareil HID", "ledgerPermissionTip": "Veuillez cliquer sur \\\"Autoriser\\\" ci-dessous et autoriser l'accès à votre Ledger dans la fenêtre contextuelle suivante.", "nowYouCanReInitiateYourTransaction": "Vous pouvez maintenant relancer votre transaction.", "permissionsAuthorized": "Permissions autorisées", "title": "Connecter une clé Ledger", "error": {"running_app_close_error": "Échec de la fermeture de l'application en cours d'exécution sur votre appareil Ledger.", "ethereum_app_open_error": "Veuillez installer/accepter l'application Ethereum sur votre appareil Ledger.", "ethereum_app_not_installed_error": "Veuillez installer l'application Ethereum sur votre appareil Ledger.", "ethereum_app_unconfirmed_error": "<PERSON><PERSON> avez refusé la demande d'ouverture de l'application Ethereum."}}, "metamask": {"how": "Comment importer mon compte MetaMask ?", "importSeedPhrase": "Importer la seed phrase ou la clé privée", "importSeedPhraseTips": "Ce sera uniquement stocké localement sur le navigateur. Rabby n'aura jamais accès à vos informations privées.", "step": "Étape", "step1": "Exporter la seed phrase ou la clé privée depuis MetaMask <br /> <1>Cliquer pour voir le tutoriel <1/></1>", "step2": "Importer la seed phrase ou la clé privée dans Rabby", "step3": "L'importation est terminée et l'ensemble de vos actifs <br /> apparaîtront automatiquement", "tips": "Conseils:", "tipsDesc": "Votre seed phrase/clé privée n'appartient pas à MetaMask ou à un autre wallet; cela n'appartient qu'à vous."}, "privateKey": {"isItPossibleToImportKeystore": {"answer": "<PERSON><PERSON>, vous pouvez <1> importer KeyStore </1> ici.", "question": "Est-il possible d'importer KeyStore ?"}, "isItSafeToImportItInRabby": {"answer": "<PERSON><PERSON>, elle sera stockée localement sur votre navigateur et uniquement accessible par vous.", "question": "Est-il sécurisé de l'importer dans Rabby ?"}, "notAValidPrivateKey": "N'est pas une clé privée valide", "placeholder": "Entrer votre clé privée", "required": "Veuillez saisir la clé privée", "whatIsAPrivateKey": {"answer": "Une chaîne de lettres et de chiffres utilisée pour contrôler vos actifs.", "question": "Qu'est-ce qu'une clé privée ?"}, "repeatImportTips": {"question": "Souhaitez-vous passer à cette adresse ?", "desc": "Cette adresse a été importée."}}, "seedPhrase": {"backup": "Sauvegarder la seed phrase", "backupTips": "Assurez-vous que personne d'autre ne regarde votre écran lorsque vous sauvegardez la seed phrase", "clearAll": "Tout effacer", "copy": "Copier la seed phrase", "createdSuccessfully": "<PERSON><PERSON><PERSON> avec succès", "fillInTheBackupSeedPhraseInOrder": "Remplir la seed phrase dans l'ordre", "importError": "[CreateMnemonics] étape inattendue {{0}}", "importQuestion4": "Si je désinstalle Rabby sans sauvegarder la seed phrase, <PERSON>bby ne pourra pas la récupérer pour moi.", "importTips": "V<PERSON> pouvez coller l'intégralité de votre seed phrase dans le 1er champ", "isItSafeToImportItInRabby": {"answer": "<PERSON><PERSON>, elle sera stockée localement sur votre navigateur et uniquement accessible par vous.", "question": "Est-il sécurisé de l'importer dans Rabby ?"}, "pleaseSelectWords": "Veuillez sélectionner les mots", "riskTips": "Avant de commencer, veuillez lire et garder à l'esprit les conseils de sécurité suivants.", "saved": "J'ai sauvegardé la phrase", "showSeedPhrase": "Afficher la seed phrase", "verificationFailed": "Échec de la vérification", "verifySeedPhrase": "Vérifier la seed phrase", "whatIsASeedPhrase": {"answer": "Une phrase de 12, 18 ou 24 mots utilisée pour contrôler vos actifs.", "question": "Qu'est-ce qu'une seed phrase ?"}, "wordPhrase": "J'ai une phrase de <1>{{count}}</1> mots", "inputInvalidCount_one": "1 entrée n'est pas conforme aux normes Seed Phrase, veuillez vérifier.", "inputInvalidCount_other": "{{count}} entrées ne sont pas conformes aux normes Seed Phrase, veuillez vérifier.", "invalidContent": "Contenu invalide", "passphrase": "Pass phrase", "pastedAndClear": "Collé et presse-papier effacé", "wordPhraseAndPassphrase": "J'ai pass phrase de <1>{{count}}</1> mots", "slip39SeedPhrasePlaceholder_few": "Entrez vos {{count}}e parts de phrase secrète ici", "slip39SeedPhrasePlaceholder_two": "Entrez vos {{count}}es parts de phrase secrète ici", "slip39SeedPhrasePlaceholder_other": "Entrez vos {{count}}ème parts de phrase de récupération ici", "slip39SeedPhrasePlaceholder_one": "Entrez vos {{count}}e fragments de phrase secrète ici", "slip39SeedPhrase": "J'ai une phrase de récupération <0>{{SLIP39}}</0>", "slip39SeedPhraseWithPassphrase": "J'ai une phrase secrète <0>{{SLIP39}}</0> avec une phrase de passe", "importQuestion1": "Si je perds ou partage ma phrase secrète, je perdrai l'accès à mes actifs de manière permanente.", "importQuestion2": "Ma phrase de récupération est uniquement stockée sur mon appareil. <PERSON><PERSON> ne peut pas y accéder.", "importQuestion3": "Si je désinstalle Rabby sans sauvegarder ma phrase de récupération, elle ne pourra pas être restaurée par <PERSON><PERSON>."}, "selectImportMethod": "Sélectionner la méthode d'importation", "theSeedPhraseIsInvalidPleaseCheck": "La seed phrase n'est pas valide, veuillez vérifier !", "title": "Ajouter une adresse", "unableToImport": {"description": "L'importation de plusieurs wallets matériels par QR codes n'est pas prise en charge. Veuillez supprimer toutes les adresses de {{0}} avant d'importer un autre appareil.", "title": "Impossible d'importer"}, "walletConnect": {"button": {"connect": "Connecter", "disconnect": "Déconnecter", "howToSwitch": "Comment changer"}, "changeBridgeServer": "Changer de serveur de bridge", "connectYour": "Connecter votre", "connectedSuccessfully": "Connecté avec succès", "disconnected": "Déconnecté", "qrCode": "QR code", "qrCodeError": "Veuillez vérifier votre réseau ou actualiser le QR code", "status": {"accountError": "L'adresse ne correspond pas.", "accountErrorDesc": "Veuillez changer d'adresse dans votre wallet mobile", "brandError": "Mauvaise application de wallet.", "brandErrorDesc": "Veuillez utiliser {{brandName}} pour vous connecter", "connected": "Connecté", "default": "Scanner avec votre {{brand}}", "duplicate": "L'adresse que vous essayez d'importer est en double", "received": "Scan réussi. En attente d'être confirmé", "rejected": "Connexion annulée. Veuillez scanner le QR code pour réessayer."}, "tip": {"accountError": {"tip1": "Con<PERSON><PERSON> mais impossible de signer.", "tip2": "Veuillez passer sur la bonne adresse dans le wallet mobile"}, "connected": {"tip": "Connecté à {{brandName}}"}, "disconnected": {"tip": "Non connecté à {{brandName}}"}}, "title": "Se connecter avec {{brandName}}", "url": "URL", "viaWalletConnect": "via Wallet Connect", "accountError": {}}, "keystone": {"allowRabbyPermissionsTitle": "Autoriser <PERSON><PERSON> :", "deviceIsBusy": "Le périphérique est occupé", "deviceIsLockedError": "<PERSON>sir le mot de passe pour déverrouiller", "deviceRejectedExportAddress": "Approuver la connexion à Rabby", "exportAddressJustAllowedOnHomePage": "Adresse d'exportation uniquement autorisée sur la page d'accueil", "keystonePermission1": "Se connecter à un périphérique USB", "keystonePermissionTip": "Veuillez cliquer sur « Autoriser » ci-dessous pour autoriser l'accès à votre Keystone dans la fenêtre pop-up suivante, et assurez-vous que votre Keystone 3 Pro est sur la page d'accueil.", "noDeviceFoundError": "Brancher un seul Keystone", "title": "Connecter <PERSON>", "unknowError": "<PERSON><PERSON><PERSON> inconnue, ve<PERSON><PERSON><PERSON> r<PERSON>er"}, "imkey": {"title": "Connecter <PERSON><PERSON><PERSON>", "imkeyPermissionTip": "Veuillez cliquer sur \"Autoriser\" ci-dessous et autoriser l'accès à votre imKey dans la fenêtre contextuelle suivante."}, "firefoxLedgerDisableTips": "Ledger n'est pas compatible avec Firefox", "addFromCurrentSeedPhrase": "Ajouter à partir de la phrase secrète actuelle"}, "nft": {"all": "Tous", "empty": {"description": "Vous pouvez sélectionner un NFT dans \"Tous\" et l'ajouter en \"Favoris\"", "title": "Aucun NFT favori"}, "floorPrice": "/ Floor price :", "noNft": "Pas de NFT", "starred": "Favoris ({{count}})", "title": "NFT"}, "preferMetamaskDapps": {"desc": "Les DApps suivantes resteront connectées via MetaMask, quel que soit le walelt vers lequel vous avez basculé", "empty": "<PERSON><PERSON>", "howToAdd": "Comment ajouter", "howToAddDesc": "Faites un clic droit sur le site Web et trouvez cette option", "title": "DApps préférées de MetaMask"}, "receive": {"title": "Recevoir {{token}} sur {{chain}}", "watchModeAlert1": "Il s'agit d'une adresse du mode observation.", "watchModeAlert2": "Êtes-vous sûr de vouloir l'utiliser pour recevoir des actifs ?"}, "requestDebankTestnetGasToken": {"claimBadgeBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>", "mintedTip": "Les détenteurs du badge Rabby peuvent en faire la demande une fois par jour", "notMintedTip": "Demande disponible uniquement pour les détenteurs du badge Rabby", "requestBtn": "<PERSON><PERSON><PERSON>", "requested": "Vous avez déjà demandé aujou<PERSON>'hui", "time": "Par jour", "title": "Demander un token de gaz pour le testnet DeBank"}, "safeQueue": {"LowerNonceError": "La transaction avec nonce {{nonce}} doit être exécutée en premier", "accountSelectTitle": "Vous pouvez soumettre cette transaction en utilisant n'importe quelle adresse", "action": {"send": "Envoyer", "cancel": "Annuler la transaction en attente"}, "approvalExplain": "Approuver {{count}} {{token}} pour {{protocol}}", "cancelExplain": "Annuler l'approbation de {{token}} pour {{protocol}}", "loading": "Chargement des transactions en attente", "loadingFaild": "En raison de l'instabilité du serveur Safe, les données ne sont pas disponibles, veuillez vérifier à nouveau dans 5 minutes", "noData": "Aucune transaction en attente", "sameNonceWarning": "Ces transactions sont en conflit car elles utilisent le même nonce. L'exécution de l'une remplacera automatiquement la ou les autre(s).", "submitBtn": "Soumettre la transaction", "title": "File d'attente", "unknownProtocol": "Protocole inconnu", "unknownTx": "Transaction inconnue", "unlimited": "illimité", "viewBtn": "Voir", "ReplacePopup": {"desc": "Une transaction signée ne peut pas être supprimée mais elle peut être remplacée par une nouvelle transaction avec le même nonce.", "options": {"reject": "Rejeter la transaction", "send": "Envoyer un token"}, "title": "Sélectionner comment remplacer cette transaction"}, "replaceBtn": "<PERSON><PERSON>lace<PERSON>"}, "securityEngine": {"alertTriggerReason": "Raison du déclenchement de l'alerte :", "currentValueIs": "La valeur actuelle est {{value}}", "enableRule": "<PERSON><PERSON> <PERSON> r<PERSON>", "forbiddenCantIgnore": "Détection d'un risque ne pouvant pas être ignoré.", "ignoreAlert": "Ignorer l'alerte", "no": "Non", "riskProcessed": "<PERSON><PERSON><PERSON>", "ruleDetailTitle": "<PERSON><PERSON><PERSON>", "ruleDisabled": "Les règles de sécurité ont été désactivées. Pour votre sécurité, vous pouvez les réactiver à tout moment.", "understandRisk": "Je comprends et accepte la responsabilité de toute perte", "undo": "Annuler", "unknownResult": "Résultat inconnu car la règle de sécurité n'est pas disponible", "viewRiskLevel": "<PERSON><PERSON><PERSON><PERSON> le niveau de risque", "viewRules": "Afficher les règles de sécurité", "whenTheValueIs": "lorsque la valeur est {{value}}", "yes": "O<PERSON>"}, "sendNFT": {"confirmModal": {"title": "Entrer le mot de passe pour confirmer"}, "header": {"title": "Envoyer"}, "nftInfoFieldLabel": {"Collection": "Collection", "Contract": "Contrat", "sendAmount": "Quantité à envoyer"}, "sectionChain": {"title": "<PERSON><PERSON><PERSON>"}, "sectionFrom": {"title": "<PERSON><PERSON><PERSON>"}, "sectionTo": {"addrValidator__empty": "Veuillez saisir l'adresse", "addrValidator__invalid": "Cette adresse n'est pas valide", "searchInputPlaceholder": "Entre<PERSON> l'adresse ou rechercher", "title": "Vers"}, "sendButton": "Envoyer", "tipAddToContacts": "Ajouter aux contacts", "tipNotOnAddressList": "N'est pas dans la liste d'adresses.", "whitelistAlert__disabled": "Liste blanche désactivée. <PERSON><PERSON> pouvez transférer vers n'importe quelle adresse.", "whitelistAlert__notWhitelisted": "L'adresse n'est pas sur liste blanche. <1 /> J'accepte d'accorder une autorisation temporaire de transfert.", "whitelistAlert__temporaryGranted": "Autorisation temporaire accordée", "whitelistAlert__whitelisted": "L'adresse est sur liste blanche"}, "sendToken": {"AddToContactsModal": {"addedAsContacts": "Ajouté dans les contacts", "editAddr": {"placeholder": "Saisir une note pour l'adresse", "validator__empty": "<PERSON><PERSON><PERSON><PERSON> saisir une note pour l'adresse"}, "editAddressNote": "Modifier la note de l'adresse", "error": "Échec de l'ajout aux contacts"}, "GasSelector": {"confirm": "Confirmer", "level": {"$unknown": "Inconnu", "custom": "<PERSON><PERSON><PERSON><PERSON>", "fast": "Instantané", "normal": "Rapide", "slow": "Standard"}, "popupDesc": "Le coût du gaz sera réservé sur le montant du transfert en fonction du prix du gaz que vous avez fixé", "popupTitle": "Fixer le prix du gaz (Gwei)"}, "addressNotInContract": "Pas sur la liste d'adresses. <1></1><2>Ajouter aux contacts</2>", "allowTransferModal": {"addWhitelist": "A<PERSON>ter à la liste blanche", "error": "mot de passe incorrect", "placeholder": "Entrer le mot de passe pour confirmer", "validator__empty": "Veuillez saisir le mot de passe"}, "balanceError": {"insufficientBalance": "Solde insuffisant"}, "balanceWarn": {"gasFeeReservation": "Réservation des frais de gaz requise"}, "header": {"title": "Envoyer"}, "max": "MAX", "modalConfirmAddToContacts": {"confirmText": "Confirmer", "title": "Ajouter aux contacts"}, "modalConfirmAllowTransferTo": {"cancelText": "Annuler", "confirmText": "Confirmer", "title": "Entrer le mot de passe pour confirmer"}, "sectionBalance": {"title": "Solde"}, "sectionChain": {"title": "<PERSON><PERSON><PERSON>"}, "sectionFrom": {"title": "<PERSON><PERSON><PERSON>"}, "sectionTo": {"addrValidator__empty": "Veuillez saisir l'adresse", "addrValidator__invalid": "Cette adresse n'est pas valide", "searchInputPlaceholder": "Entre<PERSON> l'adresse ou rechercher", "title": "Vers"}, "sendButton": "Envoyer", "tokenInfoFieldLabel": {"chain": "<PERSON><PERSON><PERSON>", "contract": "<PERSON>resse du contrat"}, "tokenInfoPrice": "Prix", "whitelistAlert__disabled": "Liste blanche désactivée. <PERSON><PERSON> pouvez transférer vers n'importe quelle adresse.", "whitelistAlert__notWhitelisted": "L'adresse n'est pas sur liste blanche. <1 /> J'accepte d'accorder une autorisation temporaire de transfert.", "whitelistAlert__temporaryGranted": "Autorisation temporaire accordée", "whitelistAlert__whitelisted": "L'adresse est sur liste blanche", "sectionMsgDataForContract": {"notHexData": "Uniquement les données hexadécimales prises en charge", "parseError": "Impossible de décoder l'appel du contrat", "placeholder": "Facultatif", "simulation": "Simulation d'appel de contrat :", "title": "<PERSON><PERSON> de contrat"}, "sectionMsgDataForEOA": {"currentIsOriginal": "Actuellement sur : donn<PERSON> originales. UTF-8 :", "currentIsUTF8": "Actuellement sur : UTF-8. <PERSON><PERSON><PERSON> originales : ", "placeholder": "Facultatif", "title": "Message"}, "blockedTransaction": "Transaction Bloquée", "blockedTransactionCancelText": "Je sais", "blockedTransactionContent": "Cette transaction interagit avec une adresse qui figure sur la liste des sanctions de l'OFAC."}, "sendTokenComponents": {"GasReserved": "<1>0</1> {{ tokenName }} réservé pour le coût du gaz", "SwitchReserveGas": "Réserve Gas <1 />"}, "signFooterBar": {"addressTip": {"airgap": "<PERSON><PERSON>e AirGap", "bitbox": "Adresse BitBox02", "coboSafe": "Adresse Cobo Argus", "coolwallet": "<PERSON><PERSON><PERSON>", "keystone": "<PERSON><PERSON><PERSON>", "onekey": "<PERSON><PERSON><PERSON>", "privateKey": "<PERSON><PERSON><PERSON> de clé privée", "safe": "<PERSON><PERSON><PERSON>", "seedPhrase": "Adresse de seed phrase", "trezor": "<PERSON><PERSON><PERSON>", "watchAddress": "Impossible de signer avec une adresse du mode observation", "seedPhraseWithPassphrase": "<PERSON>ress<PERSON> de la seed phrase (phrase secrète)"}, "beginSigning": "Commencer le processus de signature", "common": {"notSupport": "{{0}} n'est pas pris en charge"}, "connectButton": "Connecter", "connecting": "Connexion...", "gridPlusConnected": "GridPlus est connecté", "gridPlusNotConnected": "GridPlus n'est pas connecté", "ignoreAll": "Tout ignorer", "ledger": {"blindSigTutorial": "Tu<PERSON><PERSON> de signature aveugle de Ledger", "notConnected": "Votre wallet n'est pas connecté. Veuillez vous reconnecter.", "resent": "<PERSON><PERSON><PERSON>", "resubmited": "Envoyé à nouveau", "siging": "Envoi d'une demande de signature", "signError": "Erreur de signature de Ledger :", "submitting": "Signé. Soumission de la transaction", "txRejected": "Transaction rejetée", "txRejectedByLedger": "La transaction est rejetée sur votre Ledger", "unlockAlert": "Veuillez brancher et déverrouiller votre Led<PERSON>, puis ouvrir Ethereum dessus", "updateFirmwareAlert": "Veuillez mettre à jour le firmware et l'application Ethereum sur votre Ledger"}, "ledgerConnected": "Clé Ledger connectée", "ledgerNotConnected": "Clé Ledger non connectée", "mainnet": "Mainnet", "processRiskAlert": "<PERSON><PERSON><PERSON> de traiter l'alerte avant de signer", "qrcode": {"afterSignDesc": "Après avoir signé, placez le QR code sur {{brand}} devant la caméra de votre PC", "failedToGetExplain": "Impossible d'obtenir l'explication", "getSig": "Obtenir une signature", "misMatchSignId": "Données de transaction incongrues. Veuillez vérifier les détails de la transaction.", "qrcodeDesc": "Scannez avec votre {{brand}} pour signer<br></br><PERSON><PERSON>z ensuite sur le bouton ci-dessous pour recevoir la signature", "sigCompleted": "Transaction soumise", "sigReceived": "Signature reçue", "signWith": "Signer avec {{brand}}", "txFailed": "Échec de la soumission", "unknownQRCode": "Erreur : Nous n'avons pas pu identifier ce QR code"}, "requestFrom": "<PERSON><PERSON><PERSON> de", "resend": "Recommencer", "signAndSubmitButton": "Signer et soumettre", "submitTx": "Soumettre la transaction", "testnet": "Testnet", "walletConnect": {"chainSwitched": "Vous êtes passé sur une autre chaîne sur le wallet mobile. Veuillez passer sur {{0}} dans le wallet mobile", "connectBeforeSign": "{{0}} n'est pas connecté à <PERSON>, veuillez vous connecter avant de signer", "connected": "Connecté et prêt à signer", "connectedButCantSign": "Con<PERSON><PERSON> mais impossible de signer.", "howToSwitch": "Comment changer", "latency": "Latence", "notConnectToMobile": "Non connecté à {{brand}}", "requestFailedToSend": "La demande de signature n'a pas pu être envoyée", "requestSuccessToast": "<PERSON><PERSON><PERSON> envoy<PERSON> avec succès", "sendingRequest": "Envoi d'une demande de signature", "signOnYourMobileWallet": "Veuillez vous connecter sur votre wallet mobile.", "switchChainAlert": "Veuillez passer sur {{chain}} sur le wallet mobile", "switchToCorrectAddress": "Veuillez passer sur la bonne adresse dans le wallet mobile", "wrongAddressAlert": "Vous avez changé d'adresse sur le wallet mobile. Veuillez passer sur la bonne adresse dans le wallet mobile"}, "blockDappFromSendingRequests": "Empê<PERSON> la dApp d'envoyer des requêtes pendant 1 minute", "cancelAll": "Annuler les {{count}} demandes de la dApp", "cancelConnection": "Annuler la connexion", "cancelCurrentConnection": "Annuler la connexion actuelle", "cancelCurrentTransaction": "Annuler la transaction en cours", "cancelTransaction": "Annuler la transaction", "detectedMultipleRequestsFromThisDapp": "Détection de plusieurs requêtes de cette dApp", "keystone": {"qrcodeDesc": "Scannez pour signer. Après avoir signé, cliquez ci-dessous pour obtenir la signature. Pour USB, reconnectez et autorisez pour recommencer le processus de signature.", "hardwareRejectError": "La demande Keystone a été annulée. Pour continuer, veuillez vous réauthentifier.", "misMatchSignId": "Données de transaction incongrues. Veuillez vérifier les détails de la transaction.", "mismatchedWalletError": "Portefeuille invalide", "shouldOpenKeystoneHomePageError": "Assurez-vous que votre Keystone 3 Pro est sur la page d'accueil.", "shouldRetry": "Une erreur s'est produite. Veuillez réessayer.", "siging": "Envoi d'une demande de signature", "signWith": "Passer à {{method}} pour la signature", "txRejected": "Transaction rejetée", "unsupportedType": "Erreur : le type de transaction n'est pas pris en charge ou est inconnu.", "verifyPasswordError": "Échec de la signature, veuil<PERSON>z réessayer après le déverrouillage"}, "keystoneConnected": "Keystone connecté", "keystoneNotConnected": "Keystone non connecté", "gasless": {"notEnough": "Le solde de Gas n'est pas suffisant", "GetFreeGasToSign": "Obtenez du Gas gratuit", "watchUnavailableTip": "Les adresses en lecture seule ne sont pas prises en charge pour Free Gas", "customRpcUnavailableTip": "Les RPC personnalisés ne sont pas pris en charge pour Free Gas", "walletConnectUnavailableTip": "Le portefeuille mobile connecté via WalletConnect n'est pas pris en charge pour Free Gas.", "unavailable": "Votre solde de Gas n'est pas suffisant", "rabbyPayGas": "<PERSON><PERSON> paiera le gas nécessaire – il vous suffit de signer"}, "gasAccount": {"WalletConnectTips": "GasAccount ne prend pas en charge WalletConnect", "notEnough": "GasAccount n'est pas suffisant", "useGasAccount": "<PERSON><PERSON><PERSON><PERSON>", "gotIt": "<PERSON><PERSON><PERSON>", "loginFirst": "Veuillez d'abord vous connecter à GasAccount", "login": "Se connecter", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainNotSupported": "Cette chaîne n'est pas prise en charge par GasAccount.", "customRPC": "Non pris en charge lors de l'utilisation de custom RPC", "loginTips": "Pour terminer la connexion GasAccount, cette transaction sera annulée. Vous devrez la refaire après la connexion.", "depositTips": "Pour compléter le dépôt GasAccount, cette transaction sera annulée. Vous devrez la recréer après le dépôt."}, "imKeyConnected": "imKey est connecté", "imKeyNotConnected": "imKey n'est pas connecté"}, "signText": {"createKey": {"description": "Description", "interactDapp": "Interagir avec dApp"}, "message": "Message", "title": "Signer le texte", "sameSafeMessageAlert": "Le même message est confirmé ; aucune signature supplémentaire n'est requise."}, "signTx": {"addressNote": "Note d'adresse", "addressTypeTitle": "Type d'adresse", "balanceChange": {"errorTitle": "Impossible de récupérer le changement de solde", "failedTitle": "Échec de la simulation de transaction", "nftOut": "NFT sortant", "noBalanceChange": "Aucun changement de solde", "notSupport": "Simulation de transaction non prise en charge", "successTitle": "Résultats de simulation de transaction", "tokenIn": "Token entrant", "tokenOut": "Token sortant"}, "blocked": "<PERSON><PERSON><PERSON><PERSON>", "canOnlyUseImportedAddress": "Vous ne pouvez utiliser que des adresses importées pour signer", "cancelTx": {"gasPriceAlert": "Fixer le prix actuel du gaz à plus de {{value}} Gwei pour annuler la transaction en attente", "title": "Annuler la transaction en attente", "txToBeCanceled": "Transaction à annuler"}, "coboSafeNotPermission": "Cette adresse déléguée n'est pas autorisée à lancer cette transaction", "collectionTitle": "Collection", "contractAddress": "<PERSON>resse du contrat", "contractCall": {"operation": "Opération", "operationABIDesc": "L'opération est décodée à partir de l'ABI", "operationCantDecode": "L'opération n'est pas décodée", "payNativeToken": "Payer {{symbol}}", "title": "<PERSON><PERSON> de contrat", "suspectedReceiver": "<PERSON><PERSON><PERSON>'<PERSON>", "receiver": "<PERSON><PERSON><PERSON> <PERSON> destinataire"}, "contractPopularity": "N°{{0}} sur {{1}}", "crossChain": {"title": "Cross Chain"}, "deployContract": {"description": "Vous déployez un smart contract", "descriptionTitle": "Description", "title": "Déployer un contrat"}, "deployTimeTitle": "Heure du déploiement", "eip1559Desc1": "Sur les chaînes prenant en charge l'EIP-1559, les frais prioritaires sont le pourboire permettant aux mineurs de traiter votre transaction. Vous pouvez économiser votre coût final en gaz en réduisant les frais de priorité, ce qui peut rallonger la durée de traitement de la transaction.", "eip1559Desc2": "<PERSON><PERSON>, frais prioritaires (pourboire) = frais maximum - frais de base. Après avoir configuré les frais de priorité maximum, les frais de base en seront déduits et le reste sera reversé aux mineurs.", "enoughSafeSigCollected": "Assez de signatures collectées", "failToFetchGasCost": "Impossible de récupérer le coût du gaz", "fakeTokenAlert": "Il s'agit d'un token scam signalé par Rabby", "firstOnChain": "Première transaction on-chain", "floorPrice": "Floor price", "gasLimitEmptyAlert": "Veuillez saisir la limite de gaz", "gasLimitLessThanExpect": "La limite de gaz est faible. Il y a 1 % de probabilité que la transaction échoue.", "gasLimitLessThanGasUsed": "La limite de gaz est trop basse. Il y a 95% de chances que la transaction échoue.", "gasLimitMinValueAlert": "La limite de gaz doit être supérieure à 21 000", "gasLimitModifyOnlyNecessaryAlert": "Modifier seulement lorsque cela est nécessaire", "gasLimitNotEnough": "La limite de gaz est inférieure à 21 000. La transaction ne peut pas être soumise", "gasLimitTitle": "Limite de gaz", "gasMoreButton": "Plus", "gasNotRequireForSafeTransaction": "Les frais de gaz ne sont pas requis pour les transactions sécurisées", "gasPriceMedian": "Médiane des 100 dernières transactions on-chain :", "gasPriceTitle": "Prix ​​du gaz (Gwei)", "gasSelectorTitle": "Gaz", "hardwareSupport1559Alert": "Assurez-vous que le micrologiciel de votre wallet matériel a été mis à niveau vers la version prenant en charge l'EIP-1559.", "importedDelegatedAddress": "Adresse déléguée importée", "interactContract": "Interagir avec le contrat", "interacted": "A déjà interagi auparavant", "manuallySetGasLimitAlert": "Vous avez réglé manuellement la limite de gaz sur", "markAsBlock": "<PERSON><PERSON><PERSON> comme bloqué", "markAsTrust": "<PERSON>qué comme fiable", "markRemoved": "Marquage supprimé", "maxPriorityFee": "Frais de priorité maximum (Gwei)", "moreSafeSigNeeded": "{{0}} autre confirmation requise", "multiSigChainNotMatch": "Les adresses multi-signatures ne font pas partie de cette chaîne et ne peuvent pas initier de transactions", "myMark": "Mon marquage", "myMarkWithContract": "Mon marquage sur le contrat sur {{chainName}}", "myNativeTokenBalance": "Mon solde Gaz: ", "nativeTokenNotEngouthForGas": "Vous n'avez pas assez de gaz dans votre wallet", "neverInteracted": "Jamais interagi auparavant", "neverTransacted": "Jamais effectué de transaction auparavant", "nftApprove": {"approveNFT": "Approuver NFT", "nftContractTrustValueTip": "La valeur de confiance fait référence aux principaux NFTs approuvés et exposés à ce contrat. Une valeur de confiance faible indique soit un risque, soit une inactivité pendant 180 jours.", "title": "Approbation de NFT"}, "nftCollection": "Collection NFT", "nftCollectionApprove": {"approveCollection": "Approuver la collection", "title": "Approbation de la collection NFT"}, "nftIn": "NFT entrant", "noDelegatedAddress": "Aucune adresse déléguée importée", "noGasRequired": "Aucun gaz requis", "noMark": "<PERSON><PERSON> de marquage", "nonceLowerThanExpect": "Le nonce est trop faible, le minimum doit être {{0}}", "nonceTitle": "<PERSON><PERSON>", "popularity": "Popularité", "protocolTitle": "Protocole", "recommendGasLimitTip": "Est. {{est}}. Actuel {{current}}x, recommandé", "revokeNFTApprove": {"revokeNFT": "Révoquer le NFT", "title": "Révoquer l'approbation du NFT"}, "revokeNFTCollectionApprove": {"revokeCollection": "Révoquer la collection", "title": "Révoquer l'approbation de la collection NFT"}, "revokePermit2": {"title": "Révoquer l'approbation Permit2 du token"}, "revokeTokenApprove": {"revokeFrom": "Révoquer de", "revokeToken": "Révoquer le token", "title": "Révoquer l'approbation du token"}, "safeAddressNotSupportChain": "L'adresse sécurisée actuelle n'est pas prise en charge sur la chaîne {{0}}", "safeAdminSigned": "<PERSON><PERSON>", "scamTokenAlert": "Il s'agit potentiellement d'un token de mauvaise qualité et frauduleux, basé sur la détection de Rabby.", "send": {"addressBalanceTitle": "Solde d'adresse", "cexAddress": "Adresse CEX", "contractNotOnThisChain": "L'adresse du contrat ne figure pas sur cette chaîne", "notOnThisChain": "Pas sur cette chaîne", "notOnWhitelist": "Pas sur ma liste blanche", "notTopupAddress": "Il ne s'agit pas d'une adresse de recharge", "onMyWhitelist": "Sur ma liste blanche", "receiverIsTokenAddress": "<PERSON><PERSON><PERSON>", "sendTo": "Envoyer à", "sendToken": "Envoyer un token", "title": "Envoyer un token", "tokenNotSupport": "{{0}} non pris en charge", "whitelistTitle": "Liste blanche", "fromMyPrivateKey": "Depuis ma private key", "fromMySeedPhrase": "Depuis ma seed phrase", "scamAddress": "<PERSON><PERSON><PERSON> frauduleuse"}, "sendNFT": {"nftNotSupport": "NFT non pris en charge", "title": "Envoyer un NFT"}, "sigCantDecode": "Cette signature ne peut pas être décodée par Rabby", "signTransactionOnChain": "Signer la transaction sur {{chain}}", "speedUpTooltip": "Cette transaction est à la fois accélérée et initiale, une seule sera finalisée", "submitMultisig": {"multisigAddress": "<PERSON><PERSON><PERSON> multisig", "title": "Soumettre une transaction multisig"}, "swap": {"failLoadReceiveToken": "Échec du chargement", "minReceive": "Minimum à recevoir", "notPaymentAddress": "N'est pas l'adresse de paiement", "payToken": "Payer", "receiveToken": "Recevoir", "receiver": "<PERSON><PERSON><PERSON>", "simulationFailed": "La simulation de transaction a échoué", "simulationNotSupport": "La simulation de transaction n'est pas prise en charge sur cette chaîne", "slippageFailToLoad": "La tolérance de slippage ne parvient pas à se charger", "slippageTolerance": "Tolérance de slippage", "title": "Échanger un token", "valueDiff": "<PERSON><PERSON><PERSON><PERSON><PERSON> de valeur", "unknownAddress": "Adresse inconnue"}, "swapAndCross": {"title": "Échanger le token en cross chain"}, "tokenApprove": {"amountPopupTitle": "Quantité", "approveTo": "Approuver à", "approveToken": "Approuver le token", "contractTrustValueTip": "La valeur de confiance fait référence au total de tokens approuvés et exposés à ce contrat. Une valeur de confiance faible indique soit un risque, soit une inactivité pendant 180 jours.", "deployTimeLessThan": "Date de déploiement < {{value}} jours", "eoaAddress": "Adresse EOA", "flagByRabby": "<PERSON><PERSON> par <PERSON>", "myBalance": "<PERSON> <PERSON>e", "title": "Approbation de token", "trustValueLessThan": "Valeur de confiance ≤ {{value}}", "amount": "Approuver le Montant :", "exceed": "Dépasse votre solde actuel"}, "transacted": "Transaction avant", "trustValue": "Valeur de confiance", "trusted": "Fiable", "unknownAction": "Inconnu", "unknownActionType": "Type d'action inconnu", "unwrap": "\"Unwrapper\" le token", "viewRaw": "Voir au format brut", "wrapToken": "\"Wrapper\" le token", "BroadcastMode": {"instant": {"desc": "Les transactions seront immédiatement diffusées sur le réseau", "title": "Instantané"}, "lowGas": {"desc": "Les transactions seront diffusées lorsque le gaz du réseau sera faible", "title": "Économie de gaz"}, "lowGasDeadline": {"1h": "1h", "24h": "24h", "4h": "4h", "label": "<PERSON><PERSON><PERSON>'attente"}, "mev": {"desc": "Les transactions seront diffusées vers le nœud MEV désigné", "title": "MEV sécurisé"}, "tips": {"customRPC": "Non pris en charge lors de l'utilisation d'un RPC personnalisé", "notSupportChain": "Non pris en charge sur cette chaîne", "notSupported": "Non supporté", "walletConnect": "Non pris en charge par WalletConnect"}, "title": "Mode de diffusion"}, "SafeNonceSelector": {"error": {"pendingList": "Échec du chargement des transactions en attente, <1/><2><PERSON><PERSON><PERSON><PERSON></2>"}, "explain": {"contractCall": "<PERSON><PERSON> de contrat", "send": "Envoyer token", "unknown": "Transaction inconnue"}, "option": {"new": "Nouvelle transaction"}, "optionGroup": {"recommendTitle": "<PERSON><PERSON> recommand<PERSON>", "replaceTitle": "Remplacer la transaction dans la file d'attente"}}, "coboSafeCreate": {"descriptionTitle": "Description", "safeWalletTitle": "Safe{Wallet}", "title": "<PERSON><PERSON><PERSON>"}, "coboSafeModificationDelegatedAddress": {"descriptionTitle": "Description", "safeWalletTitle": "Safe{Wallet}", "title": "Soumettre une modification d'adresse déléguée"}, "coboSafeModificationRole": {"descriptionTitle": "Description", "safeWalletTitle": "Safe{Wallet}", "title": "Soumettre une modification de rôle sécurisé"}, "coboSafeModificationTokenApproval": {"descriptionTitle": "Description", "safeWalletTitle": "Safe{Wallet}", "title": "Soumettre la modification d'approbation du token"}, "decodedTooltip": "Cette signature est décodée par <PERSON><PERSON>", "importedAddress": "Adresse importée", "l2GasEstimateTooltip": "L'estimation du gaz pour la chaîne L2 n'inclut pas les frais de gaz L1. Les frais réels seront plus élevés que l'estimation actuelle.", "gasAccount": {"currentTxCost": "Montant de Gas envoyé à votre adresse :", "totalCost": "Coût total :", "gasCost": "Frais de gas pour transférer le gas à votre adresse :", "estimatedGas": "Gaz estimé :", "maxGas": "Gaz max :", "sendGas": "Le transfert de Gas pour votre transaction actuelle :"}, "customRPCErrorModal": {"button": "Désactiver Custom RPC", "content": "Votre RPC personnalisé est actuellement indisponible. Vous pouvez le désactiver et continuer à signer en utilisant le RPC officiel de Rabby.", "title": "Erreur RPC personnalisée"}, "transferOwner": {"transferTo": "Transf<PERSON>rer vers", "description": "Description", "title": "Transférer la propriété des actifs"}, "swapLimitPay": {"title": "Limite de paiement pour l'échange de tokens", "maxPay": "Paiement maximum"}, "batchRevokePermit2": {"title": "Batch Revoke Permit2 Approval\n\n翻译：Révoquer par lot l'approbation Permit2"}, "revokePermit": {"title": "Révoquer l'approbation du token de permit"}, "assetOrder": {"listAsset": "Lister l'actif", "receiveAsset": "Recevoir des actifs", "title": "<PERSON><PERSON>"}, "common": {"descTipWarningAssets": "La signature peut entraîner un changement d'actif", "descTipWarningPrivacy": "La signature peut vérifier la propriété de l'adresse", "interactContract": "Interagir avec le contrat", "descTipWarningBoth": "La signature peut entraîner un changement d'actifs et vérifier la propriété de l'adresse", "description": "Description", "descTipSafe": "La signature ne provoque pas de changement d'actif ni ne vérifie la propriété de l'adresse."}, "chain": "<PERSON><PERSON><PERSON>", "gasAccountForGas": "Utiliser les USD de mon GasAccount pour payer le gas", "nativeTokenForGas": "Utilisez le token {{tokenName}} sur {{chainName}} pour payer le gas", "contract": "Contrat intelligent", "address": "<PERSON><PERSON><PERSON>", "label": "Étiquette", "advancedSettings": "Paramètres avancés", "typedDataMessage": "Signer des données typées", "yes": "O<PERSON>", "protocol": "Protocole", "maxPriorityFeeDisabledAlert": "Veuillez d'abord définir le Gas Price", "addressSource": "Source d'adresse", "trustValueTitle": "Valeur de confiance", "amount": "<PERSON><PERSON>", "no": "Non", "hasInteraction": "Interagi auparavant", "primaryType": "Type primaire", "safeServiceNotAvailable": "Le service Safe n'est pas disponible pour le moment, veuillez réessayer plus tard.", "safeTx": {"selfHostConfirm": {"title": "Passer au service Safe de Rabby", "content": "L'API Safe est indisponible. Passez au service Safe déployé par Rabby pour que votre Safe reste fonctionnel. <strong>Tous les signataires Safe doivent utiliser Rabby Wallet pour autoriser les transactions.<strong>", "button": "OK"}}}, "signTypedData": {"buyNFT": {"expireTime": "Date d'expiration", "listOn": "Lister sur", "payToken": "Token de paiement", "receiveNFT": "Recevoir le NFT"}, "contractCall": {"operationDecoded": "L'opération est décodée à partir du message"}, "createKey": {"title": "<PERSON><PERSON><PERSON> une clé"}, "permit": {"title": "Autoriser l'approbation du token"}, "permit2": {"approvalExpiretime": "Délai d'expiration de l'approbation", "sigExpireTime": "<PERSON><PERSON>lai d'expiration de la signature", "sigExpireTimeTip": "La durée durant laquelle cette signature reste valide on-chain", "title": "Approbation Permit2 du token"}, "safeCantSignText": "Il s'agit d'une adresse Safe et elle ne peut pas être utilisée pour signer du texte.", "sellNFT": {"listNFT": "Lister le <PERSON>", "receiveToken": "Recevoir un token", "specificBuyer": "Acheteur spécifique", "title": "Commande de NFT"}, "signMultiSig": {"title": "Confirmer la transaction"}, "signTypeDataOnChain": "Signer des données typées sur {{chain}}", "swapTokenOrder": {"title": "<PERSON><PERSON><PERSON>"}, "verifyAddress": {"title": "Vérifier l'adresse"}, "safeCantSignTypedData": "Ceci est une adresse Safe, et elle ne prend en charge que la signature des données typées EIP-712 ou des chaînes."}, "swap": {"Completed": "Complété", "InSufficientTip": "Solde insuffisant pour effectuer une simulation de transaction et une estimation du gaz. Les cotations originales de l'agrégateur sont affichées", "Pending": "En attente", "QuoteLessWarning": "Le montant reçu est estimé à partir de la simulation de transaction Rabby. L'offre communiquée par le DEX est {{receive}}. Vous recevrez {{diff}} de moins que l'offre prévue.", "actual": "Réel:", "actual-slippage": "Slippage réel :", "amount-in": "Quantité en {{symbol}}", "approve-tips": "1.<PERSON><PERSON><PERSON><PERSON> → 2.<PERSON><PERSON><PERSON>", "approve-x-symbol": "Approuver {{symbol}}", "best": "Top", "by-transaction-simulation-the-quote-is-valid": "Par simulation de transaction, la cotation est valide", "cex": "CEX", "chain": "<PERSON><PERSON><PERSON>", "completedTip": "Transaction on-chain, décodage des données pour générer un enregistrement", "confirm": "Confirmer", "dex": "DEX", "directlySwap": "\"Wrapping\" des tokens {{symbol}} directement via le smart contract", "edit": "Modifier", "enable-exchanges": "<PERSON>r les échanges", "enable-it": "L'activer", "enable-trading": "Activer le trading", "est-difference": "Différence estimée :", "est-payment": "Paiement estimé :", "est-receiving": "Réception estimée :", "estimate": "Estimation :", "exchanges": "Exchanges", "fail-to-simulate-transaction": "Impossible de simuler la transaction", "gas-fee": "Frais de gaz : {{gasUsed}}", "gas-x-price": "Prix ​​du gaz : {{price}} Gwei.", "get-quotes": "Obtenir des cotations", "i-understand-and-accept-it": "Je comprends et je l'accepte", "insufficient-balance": "Solde insuffisant", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Un faible slippage peut entraîner l'échec des transactions en raison d'une forte volatilité", "minimum-received": "Minimum reçu", "need-to-approve-token-before-swap": "Besoin d'approuver le token avant l'échange", "no-transaction-records": "Aucun enregistrement de transaction", "not-supported": "Non supporté", "pendingTip": "Tx soumise. Si la transmission est en attente pendant de longues heures, vous pouvez essayer d'effacer les transactions en attente dans les paramètres.", "price-expired-refresh-quote": "Prix ​​expiré. Actualiser la cotation.", "rabby-fee": "<PERSON><PERSON>", "rate": "<PERSON><PERSON>", "rates-from-cex": "Taux depuis les CEX", "recommend-slippage": "Pour éviter le front-running, nous recommandons un slippage de <2>{{ slippage }}</2>%", "search-by-name-address": "Recherche par nom/adresse", "security-verification-failed": "La vérification de sécurité a échoué", "select-token": "Token", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "L'offre sélectionnée diffère considérablement du taux actuel et peut entraîner de grosses pertes.", "slippage-adjusted-refresh-quote": "Slippage ajusté. Actualiser la cotation.", "slippage-tolerance": "Tolérance de slippage", "slippage_tolerance": "Tolérance de slippage :", "swap-from": "Échange de", "swap-history": "Historique des échanges", "swap-via-x": "Échange via {{name}}", "testnet-is-not-supported": "Le réseau personnalisé n'est pas pris en charge", "the-following-swap-rates-are-found": "Les taux de swap suivants sont trouvés", "there-is-no-fee-and-slippage-for-this-trade": "Il n'y a pas de frais ni de slippage pour cette transaction", "this-exchange-is-not-enabled-to-trade-by-you": "Cet exchange n'est pas autorisé à négocier par vous.", "this-token-pair-is-not-supported": "La paire de tokens n'est pas prise en charge", "title": "<PERSON><PERSON><PERSON>", "to": "Vers", "trade": "<PERSON><PERSON><PERSON>", "tradingSettingTip1": "1. Une fois activé, vous interagirez directement avec le contrat de l'exchange", "tradingSettingTip2": "2. <PERSON>bby n'est pas responsable des risques découlant des contrats des exchanges", "tradingSettingTips": "{{viewCount}} exchanges proposent des cotations et {{tradeCount}} permettent le trading", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "La transaction peut être \"frontrun\" en raison d'une tolérance élevée au slippage", "unable-to-fetch-the-price": "Impossible de récupérer le prix", "unlimited-allowance": "Allocation illimitée", "view-quotes": "Voir les cotations", "wrap-contract": "Contrat de \"Wrap\"", "preferMEV": "Préférer MEV sécurisé", "preferMEVTip": "Activer la fonctionnalité « MEV Sécurisé » pour les swaps Ethereum afin de réduire les risques d'attaque sandwich. Remarque : cette fonctionnalité n'est pas prise en charge si vous utilisez une adresse de connexion RPC personnalisée ou Wallet Connect.", "sort-with-gas": "Tri au gaz", "rabbyFee": {"wallet": "Portefeuille", "rate": "<PERSON><PERSON> de frais", "title": "<PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "bridgeDesc": "Rabby Wallet trouvera toujours le meilleur taux possible parmi les meilleurs agrégateurs et vérifiera la fiabilité de leurs offres. Rabby facture des frais de 0,25 %, qui sont automatiquement inclus dans le devis.", "swapDesc": "Le portefeuille Rabby trouvera toujours le meilleur taux possible parmi les principaux agrégateurs et vérifiera la fiabilité de leurs offres. Rabby prélève des frais de 0,25 % (0 % pour l'emballage), qui sont automatiquement inclus dans le devis."}, "lowCreditModal": {"desc": "Une faible valeur de crédit indique souvent un risque élevé, comme un token piège ou une très faible liquidité.", "title": "Ce token a une faible valeur de crédit"}, "from": "De", "approve-swap": "Approuver et Échanger", "no-slippage-for-wrap": "Pas de slippage pour Wrap", "no-fee-for-wrap": "Pas de frais <PERSON> pour Wrap", "hidden-no-quote-rates_other": "{{count}} taux non disponibles", "approve-and-swap": "Approu<PERSON> et échanger via {{name}}", "two-step-approve-details": "Le jeton USDT nécessite 2 transactions pour modifier l'autorisation. <PERSON><PERSON> de<PERSON> d'abord réinitialiser l'autorisation à zéro, puis définir la nouvelle valeur de l'autorisation.", "process-with-two-step-approve": "Poursuivre avec une approbation en deux étapes", "Gas-fee-too-high": "Frais de gas trop élevés", "two-step-approve": "Signez 2 transactions pour changer l'allocation", "hidden-no-quote-rates_one": "{{count}} taux non disponible", "fetch-best-quote": "Récupération de la meilleure offre", "Auto": "Auto", "no-quote-found": "<PERSON><PERSON>n devis trouvé", "No-available-quote": "Aucune cotation disponible", "loss-tips": "Vous perdez {{usd}}. Essayez un montant plus petit sur un petit marché.", "usd-after-fees": "≈ {{usd}}", "no-fees-for-wrap": "Pas de frais <PERSON> pour Wrap", "price-impact": "Impact sur le prix", "source": "Source", "max": "MAX"}, "switchChain": {"chainNotSupport": "La chaîne demandée n'est pas encore prise en charge par Rabby", "testnetTip": "Veuillez activer « Activer les testnets » dans « Plus » avant de vous connecter aux testnets", "title": "Passer sur {{chain}}", "chainId": "ID chaîne :", "chainNotSupportYet": "La chaîne demandée n'est pas encore prise en charge par Rabby", "requestRabbyToSupport": "<PERSON><PERSON><PERSON> <PERSON> l'aide <PERSON>", "requestsReceivedPlural": "{{count}} demandes reçues", "unknownChain": "<PERSON><PERSON><PERSON> inconnue", "requestsReceived": "1 demande reçue", "addChain": "Ajouter Testnet", "chainNotSupportAddChain": "La chaîne demandée n'est pas encore intégrée par Rabby. Vous pouvez l'ajouter en tant que réseau de test personnalisé.", "desc": "Le réseau demandé n'est pas encore intégré par Rabby. Vous pouvez l'ajouter manuellement en tant que réseau personnalisé."}, "transactions": {"empty": {"desc": "Aucune transaction trouvée sur les <1>chaînes compatibles</1>", "title": "Aucune transaction"}, "explain": {"approve": "Approuver {{amount}} {{symbol}} pour {{project}}", "cancel": "A annulé une transaction en attente", "unknown": "Interaction avec un contrat"}, "title": "Transactions", "filterScam": {"btn": "Masquer les tx frauduleuses", "loading": "Le chargement peut prendre un moment et des retards de données sont possibles", "title": "Masquer les tx frauduleuses"}, "modalViewMessage": {"title": "Voir message"}, "txHistory": {"parseInputDataError": "Échec de l'analyse du message", "tipInputData": "La transaction comprend un message", "scamToolTip": "Cette transaction est initiée par des escrocs pour envoyer des tokens et NFTs frauduleux. Veuillez éviter d'interagir avec elle."}}, "unlock": {"btn": {"unlock": "Déverrouiller"}, "password": {"error": "mot de passe incorrect", "placeholder": "Entrer le mot de passe pour déverrouiller", "required": "Entrer le mot de passe pour déverrouiller"}, "title": "<PERSON><PERSON>", "description": "Le portefeuille révolutionnaire pour Ethereum et toutes les chaînes EVM", "btnForgotPassword": "Mot de passe oublié ?"}, "welcome": {"step1": {"desc": "Rabby se connecte à toutes les DApps prises en charge par MetaMask", "title": "Accéder à toutes les DApps"}, "step2": {"btnText": "Commencer", "desc": "Les clés privées sont stockées localement avec un accès exclusif pour vous", "title": "Self-custodial (à vous)"}}, "pendingDetail": {"Empty": {"noData": "<PERSON><PERSON><PERSON> donnée disponible"}, "Header": {"predictTime": "Prévu pour être emballé dans"}, "MempoolList": {"col": {"nodeName": "Nom du nœud", "nodeOperator": "Opérateur de nœud", "txStatus": "Statut de la transaction"}, "title": "<PERSON><PERSON><PERSON> dans {{count}} nœuds RPC", "txStatus": {"appeared": "<PERSON>ppa<PERSON>", "appearedOnce": "Apparu une fois", "notFound": "Non trouvé"}}, "PendingTxList": {"col": {"action": "Action de transaction", "actionType": "Type d'action", "balanceChange": "Changement de solde", "gasPrice": "Prix ​​du gaz", "interact": "Interagir avec"}, "filterBaseFee": {"label": "Répond uniquement aux exigences relatives aux frais de base", "tooltip": "Afficher uniquement les transactions dont le prix du gaz répond aux exigences des frais de base du bloc"}, "title": "Classements des prix du gaz", "titleNotFound": "Aucun classement dans toutes les transmissions en attente", "titleSame": "GasPrice se classe #{{rank}} dans la même catégorie que l'actuel", "titleSameNotFound": "Aucun classement identique à celui actuel"}, "PrePackInfo": {"col": {"difference": "Vérifier les résultats", "expectations": "Attentes", "prePackContent": "Contenu pré-emballé", "prePackResults": "Résultats pré-emballés"}, "desc": "Simulation exécutée dans le dernier bloc, mise à jour {{time}}", "error": "{{count}} erreur trouvée", "loss": "{{lossCount}} perte trouvée", "noError": "<PERSON><PERSON><PERSON> erreur trouvée", "noLoss": "Aucune perte constatée", "title": "Vérification du pré-emballage", "type": {"pay": "Payer", "receive": "Recevoir"}}, "Predict": {"completed": "Transaction terminée", "predictFailed": "Échec de la prédiction du temps d'emballage", "skipNonce": "Votre adresse a été ignorée (nonce sauté) sur la chaîne Ethereum, ce qui empêche la transaction en cours de se terminer"}, "TxHash": {"hash": "Hash de la tx"}, "TxStatus": {"completed": "Complété", "pendingBroadcast": "En attente : à diffuser", "pendingBroadcasted": "En attente : diffusé", "reBroadcastBtn": "Diffuser à nouveau"}, "TxTimeline": {"broadcasted": "Récemment diffusé", "broadcastedCount_ordinal_few": "{{count}}ème diffusion", "broadcastedCount_ordinal_one": "{{count}}ère diffusion", "broadcastedCount_ordinal_other": "{{count}}ième diffusion", "broadcastedCount_ordinal_two": "{{count}}ème diffusion", "created": "Transaction créée", "pending": "Vérification de l'état..."}}, "bridge": {"showMore": {"title": "Afficher plus", "source": "Source du Bridge"}, "settingModal": {"confirmModal": {"i-understand-and-accept-it": "Je comprends et j'accepte", "title": "Activez le trading avec cet agrégateur", "tip1": "Une fois activé, vous interagirez directement avec le contrat depuis cet agrégateur.", "tip2": "2. Rabby n'est pas responsable des risques découlant du contrat de cet agrégateur"}, "SupportedBridge": "Bridge pris en charge :", "confirm": "Confirmer", "title": "Activer Bridge Aggregators pour échanger"}, "tokenPairDrawer": {"balance": "<PERSON><PERSON> du Solde", "tokenPair": "<PERSON><PERSON>", "noData": "Aucune paire de token prise en charge", "title": "Sélectionnez à partir de la paire de jetons prise en charge"}, "no-quote": "Pas de devis", "no-transaction-records": "Aucun enregistrement de transaction", "Balance": "Solde :", "select-chain": "Sélectionner la chaîne", "title": "<PERSON><PERSON>", "pendingTip": "Tx soumise. Si la tx est en attente pendant de longues heures, vous pouvez essayer de supprimer les en attente dans les paramètres.", "To": "À", "From": "De", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "the-following-bridge-route-are-found": "Itinéraire suivant trouvé", "Pending": "En attente", "duration": "{{duration}} min", "no-route-found": "Aucun itinéraire trouvé", "enable-it": "Activez-le", "best": "Top", "unlimited-allowance": "Autorisation illimitée", "Completed": "<PERSON><PERSON><PERSON><PERSON>", "gas-fee": "GasFee : {{gasUsed}}", "approve-and-bridge": "Approuver et Bridge", "bridge-cost": "Coût du Bridge", "gas-x-price": "Prix du gas : {{price}} Gwei.", "Amount": "<PERSON><PERSON>", "tokenPairPlaceholder": "Sélectionner la paire de jetons", "actual": "Réel :", "detail-tx": "Détail", "BridgeTokenPair": "Bridger paire de tokens", "no-quote-found": "Aucune cotation trouvée. Veuillez essayer d'autres paires de jetons.", "via-bridge": "via {{bridge}}", "rabby-fee": "<PERSON><PERSON>", "slippage-adjusted-refresh-quote": "Glissement ajusté. Rafraîchir l'itinéraire.", "estimate": "Estimation :", "bridgeTo": "<PERSON>erelle vers", "getRoutes": "Obtenir des itinéraires", "estimated-value": "≈ {{value}}", "history": "Historique des bridges", "approve-x-symbol": "Approuver {{symbol}}", "bridge-via-x": "<PERSON><PERSON><PERSON> sur {{name}}", "completedTip": "Transaction on chain, décodage des données pour générer un enregistrement", "price-expired-refresh-route": "Le prix a expiré. Actualiser l'itinéraire.", "recommendFromToken": "Passerelle de <1></1> pour un devis disponible", "insufficient-balance": "Solde insuffisant", "price-impact": "Impact sur le prix", "est-payment": "Paiement Estimé :", "est-difference": "Différence estimée :", "est-receiving": "Est. Receiving:", "loss-tips": "Vous perdez {{usd}}. Essayez un montant différent.", "need-to-approve-token-before-bridge": "Besoin d'approuver le token avant le bridge", "aggregator-not-enabled": "Cet agrégateur n'est pas activé pour le trading par vous.", "max-tips": "Cette valeur est calculée en soustrayant le coût en gas pour le bridge."}, "dappSearch": {"searchResult": {"foundDapps": "Trouvé <2>{{count}}</2> dApps", "totalDapps": "Total <2>{{count}}</2> dApps"}, "selectChain": "Sélectionner la chaîne", "favorite": "<PERSON><PERSON><PERSON>", "expand": "<PERSON><PERSON><PERSON>", "emptyFavorite": "Aucune dApp Favorite", "emptySearch": "Aucune dApp trouvée", "listBy": "DApp listée par"}, "rabbyPoints": {"claimItem": {"claim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledTip": "Aucun point à réclamer pour le moment", "claimed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "go": "<PERSON><PERSON>", "earnTip": "Limite d'une fois par jour. Veuillez gagner des points après 00:00 UTC+0"}, "claimModal": {"walletBalance": "Solde du portefeuille", "title": "Réclamer des Points Initiaux", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "rabbyValuedUserBadge": "Badge Utilisateur Précieux Rabby", "addressBalance": "Solde du wallet", "season2": "Saison 2", "cantUseOwnCode": "Vous ne pouvez pas utiliser votre propre code de parrainage.", "rabbyUser": "Utilisateur Actif <PERSON>", "invalid-code": "code invalide", "referral-code": "Code de Parrainage", "claim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeStats": "Statut Actif", "MetaMaskSwap": "MetaMask Swap", "placeholder": "Entrez le code de parrainage pour des points supplémentaires (optionnel)", "snapshotTime": "Heure du snapshot : {{time}}"}, "referralCode": {"verifyAddressModal": {"sign": "Signer", "cancel": "Annuler", "verify-address": "Vérifier l'adresse", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "<PERSON><PERSON><PERSON><PERSON> signer ce message texte pour vérifier que vous êtes le propriétaire de cette adresse."}, "my-referral-code": "Mon code de parrainage", "referral-code-cannot-exceed-15-characters": "Le code de parrainage ne peut pas dépasser 15 caractères", "referral-code-cannot-be-empty": "Le code de parrainage ne peut pas être vide", "referral-code-already-exists": "Le code de parrainage existe déjà", "referral-code-available": "Code de parrainage disponible", "confirm": "Confirmer", "set-my-referral-code": "Définir mon code de parrainage", "refer-a-new-user-to-get-50-points": "Recommandez un nouvel utilisateur pour obtenir 50 points", "max-15-characters-use-numbers-and-letters-only": "Maximum 15 caractères, utilisez uniquement des chiffres et des lettres.", "once-set-this-referral-code-is-permanent-and-cannot-change": "Une fois défini, ce code de parrainage est permanent et ne peut pas être modifié.", "set-my-code": "Définir mon code"}, "title": "<PERSON><PERSON>", "share-on": "Partager sur", "top-100": "Top 100", "earn-points": "Gagner des points", "out-of-x-current-total-points": "Sur un total de {{total}} points distribués", "referral-code-copied": "Code de parrainage copié", "code-set-successfully": "Code de parrainage défini avec succès", "secondRoundEnded": "🎉 La deuxième ronde de Rabby Points est terminée", "initialPointsClaimEnded": "La période initiale de réclamation des Points est terminée", "firstRoundEnded": "🎉 La première ronde de Rabby Points est terminée"}, "customTestnet": {"CustomTestnetForm": {"name": "Nom du réseau", "id": "ID de chaîne", "idRequired": "Veuillez entrer l'identifiant de la chaîne", "nativeTokenSymbolRequired": "Veuillez saisir le symbole de la devise", "nativeTokenSymbol": "Symbole de la devise", "rpcUrl": "URL RPC", "rpcUrlRequired": "Veuillez entrer l'URL RPC", "blockExplorerUrl": "URL de l'explorateur de blocs (Optionnel)", "nameRequired": "Veuillez saisir le nom du réseau"}, "AddFromChainList": {"tips": {"supported": "La chaîne d<PERSON> intégrée par <PERSON><PERSON>et", "added": "Vous avez déjà a<PERSON>té cette chaîne."}, "title": "<PERSON><PERSON>t rapide depuis Chainlist", "search": "Rechercher le nom ou l'ID du réseau personnalisé", "empty": "<PERSON><PERSON><PERSON> chaîne trouvée"}, "signTx": {"title": "Données de transaction"}, "ConfirmModifyRpcModal": {"desc": "La chaîne est déjà intégrée par Rabby. Souhaitez-vous modifier son URL RPC ?"}, "id": "ID", "title": "<PERSON><PERSON><PERSON>", "currency": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>", "add": "Ajouter un réseau personnalis<PERSON>", "desc": "Rabby ne peut pas vérifier la sécurité des réseaux personnalisés. Veuillez ajouter uniquement des réseaux de confiance."}, "addChain": {"title": "Ajouter un réseau personnalisé à Rabby", "desc": "Rabby ne peut pas vérifier la sécurité des réseaux personnalisés. Veuillez ajouter uniquement des réseaux de confiance."}, "sign": {"transactionSpeed": "Vitesse de transaction"}, "ecology": {"sonic": {"home": {"arcadeBtn": "<PERSON>uer maintenant", "earnTitle": "<PERSON><PERSON><PERSON>", "airdropBtn": "Gagner des points", "migrateTitle": "<PERSON><PERSON><PERSON>", "migrateBtn": "Bientôt disponible", "earnBtn": "Bientôt disponible", "migrateDesc": "→", "arcadeDesc": "Jouez à des jeux gratuits pour gagner des points pour le largage S.", "socialsTitle": "Participez", "earnDesc": "Misez vos $S", "airdrop": "Airdrop", "airdropDesc": "~200 millions S aux utilisateurs sur Opera et Sonic."}, "points": {"sonicArcadeBtn": "Commencez à jouer", "sonicArcade": "Sonic Arcade", "shareOn": "Partager sur", "sonicPoints": "Sonic Points", "referralCodeCopied": "Code de parrainage copié", "today": "<PERSON><PERSON><PERSON>'hui", "referralCode": "Code de parrainage", "pointsDashboard": "Tableau de bord des Points", "pointsDashboardBtn": "Commencez à gagner des points", "errorTitle": "Impossible de charger les points", "getReferralCode": "Obtenez le code de parrainage", "errorDesc": "Une erreur s'est produite lors du chargement de vos points. Veuillez réessayer.", "retry": "<PERSON><PERSON><PERSON><PERSON>"}}, "dbk": {"home": {"bridgeBtn": "<PERSON><PERSON>", "mintNFTBtn": "Minter", "bridge": "Bridger vers DBK Chain", "mintNFT": "Minter NFT Genesis DBK", "mintNFTDesc": "Soyez témoin de DBK Chain", "bridgePoweredBy": "Propulsé par OP Superchain"}, "bridge": {"tabs": {"deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON><PERSON>"}, "info": {"gasFee": "Frais de Gas", "toAddress": "À l'adresse", "completeTime": "Temps d'achèvement", "receiveOn": "Recevoir sur {{chainName}}"}, "error": {"notEnoughBalance": "Solde insuffisant"}, "ActivityPopup": {"status": {"readyToProve": "<PERSON><PERSON><PERSON><PERSON> à prouver", "challengePeriod": "Période de challenge", "waitingToProve": "État racine publié", "withdraw": "<PERSON><PERSON><PERSON>", "rootPublished": "État root publié", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "readyToClaim": "<PERSON><PERSON><PERSON><PERSON>", "claimed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proved": "<PERSON><PERSON><PERSON><PERSON>"}, "empty": "Aucune activité pour le moment", "withdraw": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Activités", "proveBtn": "Prouver", "claimBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "WithdrawConfirmPopup": {"question3": "Je comprends que les frais de réseau sont approximatifs et vont changer", "question1": "Je comprends qu'il faudra environ 7 jours avant que mes fonds soient réclamables sur Ethereum après avoir prouvé mon retrait.", "tips": "Le retrait implique un processus en 3 étapes, nécessitant 1 transaction sur la chaîne DBK et 2 transactions Ethereum.", "btn": "<PERSON><PERSON><PERSON>", "step2": "Prouver sur Ethereum", "step1": "Initier le retrait", "question2": "Je comprends qu'une fois qu'un retrait est initié, il ne peut pas être accéléré ou annulé.", "step3": "Ré<PERSON>lamer sur Ethereum", "title": "Le retrait sur DBK Chain prend ~7 jours"}, "labelFrom": "<PERSON><PERSON><PERSON>", "labelTo": "À"}, "minNFT": {"mintBtn": "Minter", "myBalance": "<PERSON> <PERSON>e", "title": "DBK Genesis", "minted": "Minté"}}}, "miniSignFooterBar": {"status": {"txSendings": "Envoi de la demande de signature ({{current}}/{{total}})", "txCreated": "Transaction créée", "txSending": "Envoi de la demande de signature", "txSigned": "Signé. Création de la transaction"}, "signWithLedger": "Signer a<PERSON><PERSON>"}, "gasAccount": {"history": {"noHistory": "Aucun historique"}, "loginInTip": {"title": "Déposez USDC / USDT", "login": "Se connecter à GasAccount", "gotIt": "<PERSON><PERSON><PERSON>", "desc": "Payer les frais de Gas sur toutes les chaînes"}, "loginConfirmModal": {"title": "Se connecter avec l'adresse actuelle", "desc": "Une fois confirmé, vous ne pouvez le déposer qu'à cette adresse"}, "logoutConfirmModal": {"logout": "Se déconnecter", "desc": "La déconnexion désactive GasAccount. Vous pouvez restaurer votre GasAccount en vous connectant avec cette adresse.", "title": "Se déconnecter du GasAccount actuel"}, "depositPopup": {"amount": "<PERSON><PERSON>", "token": "<PERSON><PERSON>", "invalidAmount": "Doit être inférieur à 500", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectToken": "Sélectionnez le Token à Déposer", "desc": "Faites un dépôt sur le compte DeBank L2 de Rabby sans frais supplémentaires—retirez à tout moment."}, "withdrawPopup": {"to": "À", "recipientAddress": "<PERSON><PERSON><PERSON> <PERSON> destinataire", "amount": "<PERSON><PERSON>", "selectChain": "Sé<PERSON>ionner une chaîne", "withdrawalLimit": "Limite de retrait", "title": "<PERSON><PERSON><PERSON>", "selectDestinationChain": "Sélectionner la chaîne de destination", "selectAddr": "Sélectionner l'adresse", "noEnoughGas": "Montant insuffisant pour couvrir les frais de gas.", "selectRecipientAddress": "Sélectionner l'adresse du destinataire", "noEnoughValuetBalance": "Solde du Vault insuffisant. Changez de chaîne ou réessayez plus tard.", "desc": "Vous pouvez retirer le solde de votre GasAccount vers votre DeBank L2 Wallet. Connectez-vous à votre DeBank L2 Wallet pour transférer les fonds vers une blockchain prise en charge selon vos besoins.", "riskMessageFromAddress": "En raison du contrôle des risques, la limite de retrait dépend du montant total déposé par cette adresse.", "riskMessageFromChain": "En raison du contrôle des risques, la limite de retrait dépend du montant total déposé à partir de cette chaîne.", "noEligibleChain": "Aucune chaîne éligible pour le retrait", "destinationChain": "Chaîne <PERSON>", "deductGasFees": "Le montant reçu déduira les frais de gaz", "noEligibleAddr": "Aucune adresse éligible pour le retrait"}, "withdrawConfirmModal": {"title": "Transféré à votre portefeuille DeBank L2", "button": "Voir sur DeBank"}, "GasAccountDepositTipPopup": {"gotIt": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>r GasAccount et Déposer"}, "switchLoginAddressBeforeDeposit": {"title": "Changez d'adresse avant le dépôt", "desc": "Veuillez passer à votre adresse de connexion."}, "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noBalance": "Aucun solde", "withdraw": "<PERSON><PERSON><PERSON>", "gasExceed": "Le solde du GasAccount ne peut pas dépasser 1000 $.", "safeAddressDepositTips": "Les adresses Multisig ne sont pas prises en charge pour les dépôts.", "logout": "Déconnecter le GasAccount actuel", "risk": "Votre adresse actuelle a été détectée comme risquée, cette fonctionnalité est donc indisponible.", "title": "GasAccount", "gasAccountList": {"address": "<PERSON><PERSON><PERSON>", "gasAccountBalance": "Solde de Gas"}, "switchAccount": "Changer <PERSON>unt", "withdrawDisabledIAP": "Les retraits sont désactivés car votre solde inclut des fonds fiat, qui ne peuvent pas être retirés. Contactez le support pour retirer le solde de vos tokens."}, "safeMessageQueue": {"noData": "Aucun message", "loading": "Chargement des messages"}, "newUserImport": {"guide": {"title": "Bienvenue sur Rabby Wallet", "importAddress": "J'ai déjà une adresse", "createNewAddress": "<PERSON><PERSON><PERSON> une nouvelle adresse", "desc": "Le portefeuille révolutionnaire pour Ethereum et toutes les chaînes EVM"}, "createNewAddress": {"showSeedPhrase": "Afficher la phrase secrète", "title": "Avant de commencer", "desc": "Veuillez lire et garder à l'esprit les conseils de sécurité suivants.", "tip3": "Si je désinstalle Rabby sans sauvegarder ma phrase de récupération, elle ne peut pas être récupérée par <PERSON><PERSON>.", "tip2": "Ma phrase secrète est stockée uniquement sur mon appareil. <PERSON><PERSON> ne peut pas y accéder.", "tip1": "Si je perds ou partage ma seed phrase, je perds l'accès à mes actifs de manière permanente."}, "importList": {"title": "Sélectionner la méthode d'importation"}, "importPrivateKey": {"title": "Importer une clé privée", "pasteCleared": "Collé et presse-papiers effacé"}, "PasswordCard": {"form": {"password": {"label": "Mot de passe", "min": "Le mot de passe doit comporter au moins 8 caractères", "required": "Veuillez saisir le mot de passe", "placeholder": "Mot de passe (8 caractères minimum)"}, "confirmPassword": {"notMatch": "Les mots de passe ne correspondent pas", "label": "Confirmer le mot de passe", "required": "Veuillez confirmer le mot de passe", "placeholder": "Confirmer le mot de passe"}}, "title": "Définir le mot de passe", "agree": "J'accepte les<1/> <2>Conditions d'utilisation</2> et la <4>Politique de confidentialité</4>", "desc": "Il sera utilisé pour déverrouiller le portefeuille et chiffrer les données"}, "successful": {"start": "Commencer", "addMoreFrom": "<PERSON><PERSON><PERSON><PERSON> plus d'adresses de {{name}}", "create": "<PERSON><PERSON><PERSON> avec succès", "import": "Importé avec succès", "addMoreAddr": "Ajoutez plus d'adresses à partir de cette Seed Phrase"}, "readyToUse": {"pin": "<PERSON><PERSON><PERSON>", "guides": {"step2": "<PERSON><PERSON><PERSON>", "step1": "Cliquez sur l'icône de l'extension du navigateur"}, "desc": "<PERSON><PERSON><PERSON> et épinglez-le", "extensionTip": "Cliquez sur <1/> puis sur <3/>", "title": "Votre portefeuille Rabby est prêt !"}, "importSeedPhrase": {"title": "Importer Seed Phrase"}, "importOneKey": {"connect": "Connecter <PERSON>", "tip2": "2. <PERSON><PERSON> votre appareil <PERSON>ey", "title": "OneKey", "tip3": "3. Déverrou<PERSON>z votre device", "tip1": "1. Instal<PERSON>z <1>OneKey Bridge<1/>"}, "importTrezor": {"tip2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> votre appareil", "connect": "Connecter <PERSON>", "title": "<PERSON><PERSON><PERSON>", "tip1": "1. <PERSON><PERSON> votre appareil <PERSON>"}, "ImportGridPlus": {"connect": "Connecter GridPlus", "title": "GridPlus", "tip1": "1. <PERSON><PERSON><PERSON><PERSON> votre appareil GridPlus", "tip2": "2. Connecter via Lattice Connector"}, "importLedger": {"title": "Ledger", "tip2": "Entrez votre code PIN pour déverrouiller.", "connect": "Connecter <PERSON>", "tip1": "<PERSON><PERSON> votre appareil Led<PERSON>.", "tip3": "Ouvrez l'application Ethereum."}, "importBitBox02": {"title": "BitBox02", "tip1": "1. Installez le <1>BitBoxBridge<1/>", "tip2": "2. <PERSON>ez votre BitBox02", "tip3": "3. Déverrou<PERSON>z votre device", "connect": "Connecter BitBox02"}, "importKeystone": {"qrcode": {"desc": "Scannez le code QR sur le portefeuille matériel Keystone"}, "usb": {"connect": "Connecter <PERSON>", "tip1": "<PERSON><PERSON> votre appareil <PERSON><PERSON>", "tip3": "Approuvez la connexion à votre ordinateur", "desc": "Assurez-vous que votre Keystone 3 Pro est sur la page d'accueil", "tip2": "Entrez votre mot de passe pour déverrouiller"}}, "importSafe": {"error": {"required": "Veuillez saisir l'adresse", "invalid": "Adresse non valide"}, "title": "Ajouter l'adresse Safe", "placeholder": "Saisir l'adresse sécurisée", "loading": "Recherche de la chaîne déployée de cette adresse"}}, "metamaskModeDapps": {"title": "<PERSON><PERSON><PERSON> les Dapps autorisées", "desc": "Mode MetaMask activé pour les dApps suivantes. Vous pouvez connecter Rabby en sélectionnant l'option MetaMask."}, "forgotPassword": {"home": {"title": "Mot de passe oublié", "buttonNoData": "Définir le mot de passe", "button": "Commencer le processus de réinitialisation", "description": "Rabby Wallet ne stocke pas votre mot de passe et ne peut pas vous aider à le récupérer. Réinitialisez votre portefeuille pour en configurer un nouveau.", "descriptionNoData": "<PERSON><PERSON> ne stocke pas votre mot de passe et ne peut pas vous aider à le récupérer. Définissez un nouveau mot de passe si vous l'avez oublié."}, "reset": {"alert": {"title": "Les données seront supprimées et irrécupérables :", "seed": "Phrase de Récupération", "privateKey": "Clé Privée"}, "tip": {"safe": "Portefeuilles Safe importés", "records": "Historique des signatures", "whitelist": "Paramètres de la liste blanche", "title": "Les données seront conservées :", "watch": "Contacts et adresses en lecture seule", "hardware": "Portefeuilles Matériels Importés"}, "button": "Confirmer la réinitialisation", "title": "Réinitialiser le portefeuille Rabby", "confirm": "Tapez <1>RESET</1> dans la boîte pour confirmer et continuer"}, "tip": {"button": "Définir le mot de passe", "title": "Réinitialisation du Rabby Wallet terminée", "buttonNoData": "Ajouter une adresse", "description": "Créez un nouveau mot de passe pour continuer", "descriptionNoData": "Ajou<PERSON>z votre adresse pour commencer"}, "success": {"button": "<PERSON><PERSON><PERSON><PERSON>", "title": "Mot de passe défini avec succès", "description": "Vous êtes prêt à utiliser <PERSON><PERSON>."}}, "eip7702": {"alert": "EIP-7702 n'est pas encore pris en charge"}, "metamaskModeDappsGuide": {"toast": {"enabled": "Déguisement activé. Actualisez la Dapp pour vous reconnecter.", "disabled": "Déguisement désactivé. Actualisez le Dapp."}, "step2Desc": "Actualiser et se connecter via MetaMask", "step1": "Étape 1", "noDappFound": "<PERSON><PERSON><PERSON> trouvée", "alert": "Impossible de se connecter à un Dapp car il ne propose pas Rabby Wallet comme option ?", "step1Desc": "Permettre à Rabby de se déguiser en MetaMask sur le Dapp actuel", "title": "Connecter <PERSON> en se faisant passer pour MetaMask", "manage": "<PERSON><PERSON><PERSON> les Dapps autorisées", "step2": "Étape 2"}, "syncToMobile": {"steps1": "Téléchargez <PERSON>", "title": "Synchroniser l'adresse du portefeuille de l'extension Rabby vers le mobile", "clickToShowQr": "Cliquez pour sélectionner l'adresse et afficher le code QR", "steps2Description": "Votre code QR contient des données sensibles. Gardez-le privé et ne le partagez jamais avec quiconque.", "downloadGooglePlay": "Google Play", "downloadAppleStore": "App Store", "description": "Vos données d'adresse restent complètement hors ligne, cryptées et transférées en toute sécurité via un code QR.", "steps2": "2. Scanner avec Rabby Mobile", "disableSelectAddress": "Synchronisation non pris en charge pour {{type}} adresse", "disableSelectAddressWithPassphrase": "Synchronisation non pris en charge pour l'adresse {{type}} avec une phrase de passe", "disableSelectAddressWithSlip39": "Synchronisation non prise en charge pour l'adresse {{type}} avec SLIP39", "selectedLenAddressesForSync_one": "Adresse {{<PERSON>}} sélectionnée pour la synchronisation", "selectedLenAddressesForSync_other": "Adresses sélectionnées {{len}} pour la synchronisation", "selectAddress": {"title": "Sélectionner les adresses à synchroniser"}}, "search": {"sectionHeader": {"Defi": "<PERSON><PERSON><PERSON>", "NFT": "NFT", "token": "Jet<PERSON>", "AllChains": "Toutes les chaînes"}, "header": {"placeHolder": "Recherche", "searchPlaceHolder": "Rechercher le nom / l'adresse du Token"}, "tokenItem": {"Issuedby": "Émis par", "listBy": "Liste par {{name}}", "gasToken": "Jeton de Gaz", "FDV": "FDV", "verifyDangerTips": "Ceci est un token de scam", "scamWarningTips": "C'est un jeton de mauvaise qualité et cela pourrait être une arnaque."}, "searchWeb": {"searchTips": "Rechercher sur le web", "noResult": "Aucun résultat pour", "searching": "Résultats pour", "noResults": "Aucun résultat", "title": "Tous les résultats"}}}}