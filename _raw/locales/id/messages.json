{"page": {"transactions": {"empty": {"title": "Tidak ada trans<PERSON>i", "desc": "Tidak ditemukan transaksi di <1>supported chains</1>"}, "explain": {"unknown": "Contract Interaction", "cancel": "<PERSON><PERSON><PERSON>", "approve": "Setujui {{amount}} {{symbol}} untuk {{project}}"}, "txHistory": {"scamToolTip": "Transaksi ini dilakukan oleh penipu untuk mengirim token dan NFT penipuan. <PERSON><PERSON> hindari berinteraksi dengannya.", "tipInputData": "Transaksi ini termasuk pesan", "parseInputDataError": "Parse message gagal"}, "modalViewMessage": {"title": "<PERSON><PERSON>"}, "filterScam": {"loading": "Memuat mungkin membutuhkan beberapa saat, dan keterlambatan data mungkin terjadi", "btn": "Sembunyikan transaksi penipuan", "title": "Sembunyikan transaksi penipuan"}, "title": "Transaksi"}, "chainList": {"mainnet": "<PERSON><PERSON><PERSON>", "testnet": "<PERSON><PERSON><PERSON>", "title": "{{count}} <PERSON><PERSON>"}, "signTx": {"gasAccount": {"estimatedGas": "Estimasi Gas:", "currentTxCost": "Jumlah gas yang dikirim ke alamat Anda:", "totalCost": "Total cost: ", "maxGas": "Max Gas:", "gasCost": "Biaya gas untuk mentransfer gas ke alamat Anda:", "sendGas": "Gas transfer ke Anda untuk transaksi saat ini:"}, "balanceChange": {"tokenIn": "Token in", "tokenOut": "<PERSON><PERSON> keluar", "successTitle": "<PERSON><PERSON>", "failedTitle": "<PERSON><PERSON><PERSON><PERSON>", "noBalanceChange": "Tidak ada per<PERSON>han saldo", "notSupport": "Simulasi Tidak <PERSON>ng", "errorTitle": "<PERSON>l mengambil perubahan saldo", "nftOut": "NFT keluar"}, "customRPCErrorModal": {"title": "Kesalahan RPC Kustom", "button": "Nonaktifkan RPC Kustom", "content": "RPC kustom Anda tidak tersedia saat ini. Anda dapat menonaktifkannya dan melanjutkan tanda tangan menggunakan RPC resmi Rabby."}, "swap": {"minReceive": "Minimum Receive", "payToken": "Bayar", "title": "<PERSON><PERSON>", "receiveToken": "Terima", "simulationNotSupport": "Simulasi transaksi tidak didukung di chain ini", "slippageFailToLoad": "<PERSON><PERSON> memuat", "valueDiff": "Value diff", "simulationFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>i gagal", "failLoadReceiveToken": "<PERSON><PERSON> memuat", "notPaymentAddress": "<PERSON><PERSON><PERSON> al<PERSON>", "unknownAddress": "<PERSON><PERSON><PERSON> tidak dikenal", "slippageTolerance": "Toleransi slippage", "receiver": "<PERSON><PERSON><PERSON>"}, "crossChain": {"title": "<PERSON><PERSON><PERSON>"}, "swapAndCross": {"title": "Tukarkan <PERSON> dan <PERSON>"}, "transferOwner": {"transferTo": "Transfer ke", "title": "Transfer Kepemilikan Aset", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "swapLimitPay": {"maxPay": "<PERSON><PERSON><PERSON><PERSON> maksimum", "title": "<PERSON><PERSON> To<PERSON>em<PERSON>"}, "send": {"notOnThisChain": "Tidak di rantai ini", "contractNotOnThisChain": "Tidak di rantai ini", "sendToken": "<PERSON><PERSON> token", "onMyWhitelist": "Di daftar putih saya", "whitelistTitle": "<PERSON><PERSON><PERSON>", "notTopupAddress": "<PERSON><PERSON><PERSON> al<PERSON>", "cexAddress": "Alamat CEX", "addressBalanceTitle": "<PERSON><PERSON>", "receiverIsTokenAddress": "<PERSON><PERSON><PERSON> token", "fromMyPrivateKey": "<PERSON>i kunci pribadi saya", "scamAddress": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "notOnWhitelist": "Tidak di daftar putih saya", "fromMySeedPhrase": "Dari frase benih saya", "sendTo": "<PERSON><PERSON> ke", "tokenNotSupport": "{{0}} tidak didukung"}, "tokenApprove": {"eoaAddress": "EOA", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "approveTo": "Set<PERSON><PERSON><PERSON> untuk", "trustValueLessThan": "≤ {{value}}", "myBalance": "<PERSON><PERSON>a", "approveToken": "Setujui token", "flagByRabby": "Dibayangkan o<PERSON>", "amount": "<PERSON><PERSON><PERSON><PERSON>:", "amountPopupTitle": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "exceed": "Melebihi saldo Anda saat ini", "deployTimeLessThan": "< {{value}} hari", "contractTrustValueTip": "<PERSON><PERSON> kepercayaan mengacu pada total nilai aset yang dibel<PERSON>jakan oleh kontrak ini. <PERSON><PERSON> kepercayaan yang rendah menu<PERSON>n risiko atau ketidakak<PERSON> selama 180 hari."}, "revokeTokenApprove": {"title": "<PERSON><PERSON><PERSON>", "revokeToken": "Cabut token", "revokeFrom": "<PERSON><PERSON><PERSON> dari"}, "sendNFT": {"title": "Kirim NFT", "nftNotSupport": "NFT tidak didukung"}, "nftApprove": {"title": "Persetujuan NFT", "approveNFT": "Setujui NFT", "nftContractTrustValueTip": "<PERSON><PERSON> kepercayaan mengacu pada nilai NFT tertinggi yang dihabiskan oleh kontrak ini. <PERSON><PERSON> kepercayaan yang rendah menu<PERSON>kkan adanya risiko atau ketidakaktifan selama 180 hari."}, "revokeNFTApprove": {"revokeNFT": "Batalkan NFT", "title": "Mencabut Persetujuan NFT"}, "nftCollectionApprove": {"title": "Persetujuan Koleksi NFT", "approveCollection": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>i"}, "revokeNFTCollectionApprove": {"revokeCollection": "Batalkan koleksi", "title": "Cabut Koleksi NFT"}, "deployContract": {"description": "<PERSON>a sedang menerapkan kontrak cerdas", "descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "title": "Luncurkan Kontrak"}, "cancelTx": {"title": "Batalkan Transaksi <PERSON>", "txToBeCanceled": "<PERSON><PERSON><PERSON> yang akan di<PERSON>", "gasPriceAlert": "Atur harga gas saat ini lebih dari {{value}} Gwei untuk membatalkan transaksi yang tertunda"}, "submitMultisig": {"title": "<PERSON><PERSON> Transaksi Multisig", "multisigAddress": "<PERSON>amat multisig"}, "contractCall": {"title": "Panggilan Kontrak", "operation": "Operasi", "suspectedReceiver": "Exception <PERSON>", "receiver": "<PERSON><PERSON><PERSON>", "payNativeToken": "Bayar {{symbol}}", "operationCantDecode": "Operasi tidak terdecodifikasi", "operationABIDesc": "Operasi didekode dari ABI"}, "revokePermit2": {"title": "Cabut <PERSON>an Permit2"}, "batchRevokePermit2": {"title": "Batch Revoke Permit2 Approval"}, "revokePermit": {"title": "<PERSON><PERSON><PERSON>"}, "assetOrder": {"receiveAsset": "Te<PERSON> aset", "title": "<PERSON><PERSON><PERSON>", "listAsset": "<PERSON><PERSON><PERSON> aset"}, "BroadcastMode": {"instant": {"desc": "Transaksi akan segera disiarkan ke jaringan", "title": "Instan"}, "lowGas": {"title": "Hemat Gas", "desc": "Transaksi akan disiarkan saat gas jaringan rendah"}, "mev": {"desc": "Transaksi akan disiarkan ke node MEV yang ditentukan", "title": "MEV Guarded"}, "tips": {"walletConnect": "Tidak didukung oleh WalletConnect", "notSupported": "Tidak didukung", "customRPC": "Tidak didukung saat menggunakan RPC kustom", "notSupportChain": "Tidak didukung di rantai ini"}, "lowGasDeadline": {"1h": "1h", "label": "<PERSON><PERSON><PERSON>", "24h": "24j", "4h": "4h"}, "title": "Mode Siaran"}, "safeTx": {"selfHostConfirm": {"button": "OK", "title": "<PERSON><PERSON><PERSON> ke <PERSON>", "content": "API Safe tidak tersedia. <PERSON><PERSON><PERSON> ke layanan Safe yang dikerahkan oleh Rabby untuk menjaga Safe Anda tetap berfungsi. <strong>Se<PERSON>a penanda<PERSON>gan Safe harus menggunakan Rabby Wallet untuk mengotorisasi transaksi.</strong>"}}, "SafeNonceSelector": {"explain": {"unknown": "Transaksi Tidak Dikenal", "contractCall": "Panggilan Kontrak", "send": "<PERSON><PERSON>"}, "optionGroup": {"recommendTitle": "<PERSON><PERSON> yang <PERSON>n", "replaceTitle": "Ganti <PERSON><PERSON>"}, "option": {"new": "Transaks<PERSON>"}, "error": {"pendingList": "<PERSON>l memuat transaksi yang tertunda, <1/><2><PERSON><PERSON></2>"}}, "coboSafeCreate": {"descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "title": "Buat Cobo Safe", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationRole": {"descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationDelegatedAddress": {"descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "safeWalletTitle": "Safe{Wallet}", "title": "<PERSON><PERSON>"}, "coboSafeModificationTokenApproval": {"title": "<PERSON><PERSON>", "safeWalletTitle": "Safe{Wallet}", "descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "common": {"description": "<PERSON><PERSON><PERSON><PERSON>", "descTipWarningAssets": "Tanda tangan dapat menyebabkan perubahan aset", "descTipWarningPrivacy": "Tanda tangan dapat memverifikasi kepemilikan alamat", "descTipSafe": "Tanda tangan tidak menyebabkan perubahan aset atau memverifikasi kepemilikan alamat", "descTipWarningBoth": "Tanda tangan dapat menyebabkan perubahan aset dan memverifikasi kepemilikan alamat", "interactContract": "Interaksi kontrak"}, "manuallySetGasLimitAlert": "Anda telah menyetel Batas Gas secara manual ke", "nonceLowerThanExpect": "<PERSON><PERSON> terl<PERSON> rendah, nilai minimal harus {{0}}", "gasLimitLessThanExpect": "Gas limit rendah. Ada 1% kemungkinan transaksi ini gagal.", "canOnlyUseImportedAddress": "Anda tidak dapat menandatangani transaksi dengan alamat watch-only.", "gasMoreButton": "<PERSON><PERSON><PERSON>", "nativeTokenNotEngouthForGas": "Saldo Gas tidak cukup untuk transaksi", "gasSelectorTitle": "Gas", "safeAddressNotSupportChain": "Alamat Safe saat ini tidak didukung di rantai {{0}}", "nftIn": "NFT di", "gasLimitModifyOnlyNecessaryAlert": "Modifikasi hanya jika dip<PERSON>lukan", "gasAccountForGas": "Gunakan USD dari GasAccount saya untuk membayar gas", "nonceTitle": "<PERSON><PERSON>", "gasPriceTitle": "Harga Gas (Gwei)", "gasLimitTitle": "Batas gas", "gasLimitEmptyAlert": "<PERSON>lakan masukkan batas gas", "recommendGasLimitTip": "Est. {{est}}. Saat ini {{current}}x, reko<PERSON><PERSON><PERSON>n", "gasLimitMinValueAlert": "Batas gas harus lebih dari 21000", "gasPriceMedian": "Median dari 100 transaksi on-chain terakhir:", "multiSigChainNotMatch": "<PERSON><PERSON><PERSON> multi-tanda tangan tidak ada di rantai ini dan tidak dapat memulai transaksi.", "maxPriorityFee": "Max Priority Fee (Gwei)", "gasNotRequireForSafeTransaction": "Biaya gas tidak diperlukan untuk transaksi Safe", "nativeTokenForGas": "Gunakan token {{tokenName}} di {{chainName}} untuk membayar gas", "gasLimitLessThanGasUsed": "Batas gas terlalu rendah. Ada 95% kemungkinan transaksi ini mungkin gagal.", "noGasRequired": "Tidak ada gas yang diperlukan", "gasLimitNotEnough": "Batas gas kurang dari 21000. Transaksi tidak dapat diajukan.", "myNativeTokenBalance": "Saldo Gas saya:", "failToFetchGasCost": "Gagal memperkirakan gas", "hardwareSupport1559Alert": "Pastikan firmware dompet perangkat keras Anda telah diperbarui ke versi yang mendukung EIP 1559.", "safeAdminSigned": "Ditandatangani", "eip1559Desc1": "Di rantai yang mendukung EIP-1559, Biaya Prioritas adalah tip untuk penambang agar memproses transaksi Anda. <PERSON>a dapat menghemat biaya gas akhir Anda dengan menurunkan Biaya Prioritas, yang mungkin memerlukan lebih banyak waktu untuk transaksi diproses.", "enoughSafeSigCollected": "<PERSON><PERSON><PERSON> tanda tangan yang dikumpulkan", "moreSafeSigNeeded": "Butuh {{0}} tanda tangan lagi untuk mengonfirmasi", "eip1559Desc2": "<PERSON> di <PERSON>, <PERSON><PERSON><PERSON>itas (Tip) = <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON>a mengatur Biaya <PERSON>, <PERSON><PERSON><PERSON> akan dikurangi dari jumlah tersebut dan sisanya akan diberikan sebagai tip kepada penambang.", "unwrap": "Unwrap Token", "wrapToken": "<PERSON><PERSON>", "transacted": "Transacted sebelum", "noMark": "Tidak ada tanda", "neverInteracted": "Tidak pernah berinteraksi sebelumnya", "trusted": "Terpercaya", "interacted": "<PERSON><PERSON>", "markAsBlock": "Ditandai sebagai diblokir", "interactContract": "Interaksi kontrak", "markRemoved": "<PERSON>", "safeServiceNotAvailable": "<PERSON><PERSON><PERSON> aman tidak tersedia sekarang, silakan coba nanti.", "scamTokenAlert": "Ini kem<PERSON>an adalah token berkualitas rendah dan penipuan berdasar<PERSON> dete<PERSON><PERSON>.", "importedAddress": "<PERSON><PERSON><PERSON> yang diimpor", "fakeTokenAlert": "Ini adalah token scam yang ditandai oleh Rabby", "markAsTrust": "Ditandai sebagai tepercaya", "blocked": "Diblokir", "signTransactionOnChain": "Tandatangani Transaksi {{chain}}", "unknownAction": "<PERSON><PERSON><PERSON> Tanda Tangan Tidak Dikenal", "neverTransacted": "Belum pernah bertran<PERSON><PERSON>i sebelum<PERSON>", "speedUpTooltip": "Transaksi yang dipercepat ini dan transaksi asli, hanya salah satunya yang pada akhirnya akan diselesaikan.", "decodedTooltip": "Tanda tangan ini di-decode oleh <PERSON>", "myMark": "<PERSON>da saya", "deployTimeTitle": "<PERSON><PERSON><PERSON>", "addressNote": "Catatan alamat", "popularity": "Popularitas", "floorPrice": "<PERSON>rga lantai", "addressTypeTitle": "<PERSON><PERSON><PERSON> al<PERSON>", "importedDelegatedAddress": "<PERSON>amat delegasi yang diimpor", "contractAddress": "<PERSON><PERSON><PERSON> k<PERSON>", "collectionTitle": "<PERSON><PERSON><PERSON><PERSON>", "protocolTitle": "Protokol", "trustValue": "<PERSON><PERSON>", "nftCollection": "Koleksi NFT", "noDelegatedAddress": "Tidak ada alamat delegasi yang diimpor", "unknownActionType": "Tipe Aksi Tidak Dikenal", "viewRaw": "<PERSON><PERSON>", "sigCantDecode": "Tanda tangan ini tidak dapat didekode oleh <PERSON>", "firstOnChain": "Pertama di on-chain", "myMarkWithContract": "Tanda saya di kontrak {{chainName}}", "coboSafeNotPermission": "<PERSON><PERSON>t delegasi ini tidak memiliki izin untuk memulai transaksi ini.", "l2GasEstimateTooltip": "Perkiraan gas untuk rantai L2 tidak termasuk biaya gas L1. Biaya sebenarnya akan lebih tinggi daripada perkiraan saat ini.", "contractPopularity": "Tidak.{{0}} di {{1}}", "chain": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "addressSource": "<PERSON><PERSON>", "label": "Label", "typedDataMessage": "Tandatangani Data Tipe", "yes": "Ya", "address": "<PERSON><PERSON><PERSON>", "no": "Tidak", "hasInteraction": "<PERSON><PERSON>", "primaryType": "<PERSON><PERSON>e utama", "maxPriorityFeeDisabledAlert": "<PERSON><PERSON>an atur Gas Price terlebih dahulu", "protocol": "Protokol", "contract": "<PERSON><PERSON><PERSON> k<PERSON> pintar", "advancedSettings": "Pengaturan Lanju<PERSON>", "trustValueTitle": "<PERSON><PERSON>"}, "signFooterBar": {"gasless": {"unavailable": "Saldo Gas Anda tidak cukup", "notEnough": "Saldo gas tidak cukup", "watchUnavailableTip": "<PERSON><PERSON><PERSON> hanya untuk dilihat tidak didukung untuk Gas Gratis", "rabbyPayGas": "<PERSON><PERSON> akan membayar untuk gas yang diperlukan – cukup tandatangani.", "GetFreeGasToSign": "Dapatkan Gas Gratis", "walletConnectUnavailableTip": "Dompet seluler yang terhubung melalui WalletConnect tidak didukung untuk Free Gas", "customRpcUnavailableTip": "Custom RPC tidak didukung untuk Free Gas"}, "gasAccount": {"WalletConnectTips": "WalletConnect tidak didukung oleh GasAccount", "login": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON>", "gotIt": "Se<PERSON><PERSON>", "customRPC": "Tidak didukung saat menggunakan RPC kustom", "loginFirst": "<PERSON><PERSON><PERSON> masuk ke GasAccount terlebih dahulu", "useGasAccount": "<PERSON><PERSON><PERSON>unt", "loginTips": "Untuk menyelesaikan login GasAccount, transaksi ini akan dibuang. Anda perlu membuat ulang setelah login.", "chainNotSupported": "Rantai ini tidak didukung oleh GasAccount", "depositTips": "Untuk menyelesaikan setoran GasAccount, transaksi ini akan di<PERSON>. Anda perlu membuatnya kembali setelah setoran.", "notEnough": "GasAccount tidak cukup"}, "walletConnect": {"requestSuccessToast": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "connected": "Terhubung dan siap untuk menanda<PERSON>gani", "latency": "Ke<PERSON>lambatan", "howToSwitch": "Cara untuk beralih", "notConnectToMobile": "Tidak terhubung ke {{brand}}", "switchChainAlert": "<PERSON><PERSON>an beralih ke {{chain}} di dompet seluler", "switchToCorrectAddress": "<PERSON><PERSON><PERSON> beralih ke alamat yang benar di dompet seluler", "connectedButCantSign": "Terkoneksi tetapi tidak dapat menandatangani.", "chainSwitched": "<PERSON>a telah beralih ke rantai yang berbeda di dompet seluler. <PERSON><PERSON>an beralih ke {{0}} di dompet seluler.", "connectBeforeSign": "{{0}} tidak terhubung ke <PERSON>, silakan sambungkan sebelum menanda<PERSON>gani.", "wrongAddressAlert": "<PERSON>a telah beralih ke alamat yang berbeda di dompet seluler. <PERSON><PERSON><PERSON> beralih ke alamat yang benar di dompet seluler.", "sendingRequest": "<PERSON><PERSON><PERSON> permin<PERSON> pen<PERSON>", "requestFailedToSend": "Permin<PERSON>an tanda tangan gagal dikirim", "signOnYourMobileWallet": "<PERSON><PERSON>an tanda tangani di dompet seluler <PERSON>."}, "addressTip": {"safe": "<PERSON><PERSON><PERSON>an", "seedPhrase": "Seed Phrase alamat", "privateKey": "<PERSON><PERSON><PERSON>", "keystone": "Alamat <PERSON>", "seedPhraseWithPassphrase": "Seed Phrase address (Passphrase)", "trezor": "<PERSON><PERSON><PERSON>", "coboSafe": "Cobo Argus Alamat", "onekey": "OneKey address", "airgap": "AirGap address", "watchAddress": "Tidak dapat menandatangani dengan alamat lihat-saja", "coolwallet": "CoolWallet address", "bitbox": "BitBox02 alamat"}, "qrcode": {"sigReceived": "Tanda tangan diterima", "sigCompleted": "Transaksi dibuat", "failedToGetExplain": "Gagal untuk mendapatkan penjelasan", "signWith": "Tandatangani dengan {{brand}}", "misMatchSignId": "Data transaksi tidak sesuai. Silakan periksa rincian transaksi.", "afterSignDesc": "<PERSON><PERSON><PERSON>, letakkan kode QR di {{brand}} di depan kamera PC Anda", "unknownQRCode": "Kesalahan: <PERSON><PERSON> tidak dapat mengidentifikasi kode QR itu", "qrcodeDesc": "<PERSON><PERSON><PERSON> dengan {{brand}} Anda untuk menandatangani<br></br><PERSON><PERSON><PERSON> menanda<PERSON>gan<PERSON>, klik tombol di bawah untuk menerima tanda tangan", "txFailed": "<PERSON><PERSON> memb<PERSON>t", "getSig": "Dapatkan tanda tangan"}, "keystone": {"unsupportedType": "Kesalahan: <PERSON><PERSON> tidak didukung atau tidak dikenal.", "misMatchSignId": "Data transaksi tidak cocok. Silakan periksa rincian transaksi.", "signWith": "<PERSON><PERSON><PERSON> ke {{method}} untuk menanda<PERSON>gani", "qrcodeDesc": "Pindai untuk menandatangani. <PERSON><PERSON><PERSON> menanda<PERSON>, klik di bawah untuk mendapatkan tanda tangan. Untuk USB, sambungkan kembali dan otorisasi untuk memulai proses penandatanganan lagi.", "mismatchedWalletError": "Dompet tidak cocok", "shouldOpenKeystoneHomePageError": "Pastikan Keystone 3 Pro Anda ada di beranda", "txRejected": "<PERSON><PERSON><PERSON>", "shouldRetry": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON>an coba lagi.", "hardwareRejectError": "Permintaan Keystone dibatalkan. Untuk <PERSON>, silakan otor<PERSON> kembal<PERSON>.", "siging": "<PERSON><PERSON><PERSON> permin<PERSON> pen<PERSON>", "verifyPasswordError": "<PERSON><PERSON><PERSON><PERSON> tanda tangan, silakan coba lagi setelah membuka kunci"}, "ledger": {"siging": "Mengirim permintaan tanda tangan", "resent": "Resent", "resubmited": "<PERSON><PERSON><PERSON>", "blindSigTutorial": "Tutorial Tanda Tangan <PERSON>", "submitting": "Ditandatangani. Membuat transaksi", "signError": "<PERSON><PERSON><PERSON> tanda Ledger:", "txRejected": "<PERSON><PERSON><PERSON>", "txRejectedByLedger": "Transaksi ditolak di Ledger Anda", "updateFirmwareAlert": "<PERSON>lakan perbarui firmware dan aplikasi Ethereum di Ledger Anda.", "unlockAlert": "<PERSON><PERSON><PERSON> colokkan dan buka kunci Ledger Anda, buka Ethereum di dalamnya.", "notConnected": "Dompet Anda tidak terhubung. <PERSON>lakan sambungkan kembali."}, "common": {"notSupport": "{{0}} tidak didukung"}, "ledgerConnected": "Ledger terhu<PERSON>ng", "ignoreAll": "<PERSON><PERSON><PERSON><PERSON> semua", "requestFrom": "<PERSON><PERSON><PERSON><PERSON> dari", "connectButton": "Sambungkan", "gridPlusConnected": "GridPlus terhubung", "connecting": "Menghubungkan...", "ledgerNotConnected": "Ledger tidak terhubung", "keystoneConnected": "Keystone terhubung", "keystoneNotConnected": "Keystone tidak terhubung", "processRiskAlert": "<PERSON><PERSON><PERSON> proses peringatan sebelum menanda<PERSON>i", "gridPlusNotConnected": "GridPlus tidak terhubung", "signAndSubmitButton": "<PERSON>da tangan", "beginSigning": "<PERSON><PERSON> proses pen<PERSON>", "cancelCurrentConnection": "Batalkan koneksi saat ini", "cancelAll": "Batalkan semua {{count}} permin<PERSON><PERSON> dari <PERSON>", "cancelCurrentTransaction": "Batalkan transaksi saat ini", "testnet": "Testnet", "blockDappFromSendingRequests": "Blok Dapp dari mengirim permintaan selama 1 menit", "cancelTransaction": "Batalkan Transaksi", "submitTx": "<PERSON><PERSON>", "mainnet": "Mainnet", "detectedMultipleRequestsFromThisDapp": "Mendeteksi beberapa permintaan dari <PERSON> ini", "resend": "Coba lagi", "cancelConnection": "Batalkan koneksi", "imKeyConnected": "<PERSON><PERSON><PERSON> te<PERSON>", "imKeyNotConnected": "im<PERSON>ey tidak terhu<PERSON>ng"}, "signTypedData": {"permit": {"title": "<PERSON><PERSON>"}, "permit2": {"sigExpireTime": "<PERSON><PERSON><PERSON> ked<PERSON> tanda tangan", "sigExpireTimeTip": "<PERSON><PERSON>i tanda tangan ini berlaku di on-chain", "title": "Permit2 Persetujuan Token", "approvalExpiretime": "<PERSON><PERSON><PERSON>"}, "swapTokenOrder": {"title": "Token Order"}, "sellNFT": {"receiveToken": "Terima token", "specificBuyer": "P<PERSON>beli spesifik", "listNFT": "Daftar NFT", "title": "NFT Order"}, "signMultiSig": {"title": "Konfirmasi <PERSON>"}, "createKey": {"title": "Buat Key"}, "verifyAddress": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "buyNFT": {"payToken": "Bayar token", "expireTime": "<PERSON><PERSON><PERSON>", "listOn": "Daftar di", "receiveNFT": "Terima NFT"}, "contractCall": {"operationDecoded": "Operasi di-decode dari pesan"}, "signTypeDataOnChain": "<PERSON><PERSON> {{chain}} Data Terketik", "safeCantSignText": "<PERSON>i adalah al<PERSON>, dan tidak dapat digunakan untuk menandatangani teks.", "safeCantSignTypedData": "<PERSON>i ad<PERSON>h al<PERSON>, dan hanya mendukung tanda tangan data bertipe EIP-712 atau string."}, "activities": {"signedTx": {"empty": {"title": "Belum ada transaksi yang ditan<PERSON>ni", "desc": "<PERSON><PERSON><PERSON> trans<PERSON>i yang ditandatangani melalui Ra<PERSON> akan terdaftar di sini."}, "common": {"speedUp": "Percepat", "unknownProtocol": "Protokol tidak dikenal", "unlimited": "tidak terbatas", "cancel": "<PERSON><PERSON>", "pendingDetail": "Detail Tertunda", "unknown": "Tidak Dikenal"}, "tips": {"pendingBroadcastRetryBtn": "Re-broadcast", "pendingBroadcastBtn": "<PERSON><PERSON><PERSON>", "pendingBroadcastRetry": "<PERSON><PERSON> gagal. Upaya terakhir: {{pushAt}}", "pendingDetail": "<PERSON>ya satu trans<PERSON>i yang akan di<PERSON>, dan hampir selalu yang memiliki harga gas tertinggi.", "canNotCancel": "Tidak dapat mempercepat atau membatalkan: Bukan txn tertunda pertama", "pendingBroadcast": "Mode hemat gas: menunggu biaya jaringan yang lebih rendah. Maksimal {{deadline}} jam tunggu."}, "status": {"withdrawed": "Pembatalan cepat", "pendingBroadcasted": "Menunggu: disiarkan", "failed": "Gaga<PERSON>", "pendingBroadcast": "Pending: akan di<PERSON><PERSON>n", "submitFailed": "Gagal untuk mengirim", "pendingBroadcastFailed": "Tunggu: <PERSON><PERSON> gagal", "canceled": "Di<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>"}, "txType": {"initial": "<PERSON><PERSON><PERSON> awal", "cancel": "Batalkan tx", "speedUp": "Percepat tx"}, "explain": {"unknown": "Transaksi Tidak Dikenal", "send": "Kirim {{amount}} {{symbol}}", "cancel": "<PERSON><PERSON> {{token}} <PERSON><PERSON><PERSON><PERSON> untuk {{protocol}}", "approve": "Setujui {{count}} {{token}} untuk {{protocol}}", "nftCollectionApproval": "<PERSON><PERSON><PERSON>juan Koleksi NFT untuk {{protocol}}", "cancelSingleNFTApproval": "Batalkan Persetujuan NFT Tunggal untuk {{protocol}}", "singleNFTApproval": "Persetujuan NFT Tunggal untuk {{protocol}}", "cancelNFTCollectionApproval": "Batalkan Per<PERSON>ujuan Koleksi NFT untuk {{protocol}}"}, "CancelTxPopup": {"options": {"quickCancel": {"desc": "Batalkan sebelum disiarkan, tidak ada biaya gas", "title": "Pembatalan Cepat", "tips": "<PERSON><PERSON> didukung untuk transaksi yang belum disiarkan"}, "onChainCancel": {"title": "On-chain Cancel", "desc": "Transaksi baru untuk membatalkan, memerlukan gas"}, "removeLocalPendingTx": {"desc": "<PERSON><PERSON> transaksi yang tertunda dari antarmuka", "title": "Hapus Tertunda Secara Lokal"}}, "removeLocalPendingTx": {"title": "Hapus Transaksi Secara Lokal", "desc": "Tindakan ini akan menghapus transaksi yang tertunda secara lokal. Transaksi yang tertunda mungkin masih dapat dikirimkan dengan sukses di masa depan."}, "title": "Batalkan transaksi"}, "MempoolList": {"reBroadcastBtn": "Re-broadcast", "title": "Muncul di {{count}} node RPC", "empty": "Tidak ditemukan di node mana pun"}, "message": {"deleteSuccess": "<PERSON><PERSON><PERSON> den<PERSON> sukses", "cancelSuccess": "Di<PERSON><PERSON><PERSON>", "broadcastSuccess": "Disiarkan", "reBroadcastSuccess": "<PERSON><PERSON><PERSON><PERSON> ulang"}, "gas": {"noCost": "Tidak ada biaya Gas"}, "SkipNonceAlert": {"alert": "Nonce #{{nonce}} dilewat pada rantai {{chainName}}. Ini mungkin menyebabkan transaksi yang tertunda. <5></5> <6><PERSON><PERSON> tx</6> <7></7> di rantai untuk menyelesaikannya.", "clearPendingAlert": "{{chainName}} Transaksi ({{nonces}}) telah tertunda lebih dari 3 menit. <PERSON>a dapat <5></5> <6>Hapus Pending <PERSON><PERSON><PERSON></6> <7></7> dan mengirim ulang transaksi."}, "PredictTime": {"noTime": "W<PERSON>tu peng打包 sedang diprediksi", "time": "<PERSON><PERSON><PERSON><PERSON><PERSON> akan dikemas dalam {{time}}", "failed": "<PERSON>di<PERSON>i waktu pengemasan gagal"}, "CancelTxConfirmPopup": {"warning": "Transaksi yang dihapus mungkin masih dikonfirmasi di jaringan kecuali jika digantikan.", "title": "<PERSON><PERSON>", "desc": "<PERSON>i akan menghapus transaksi yang tertunda dari antarmuka <PERSON>. <PERSON><PERSON> kem<PERSON>an dapat memulai transaksi baru."}, "label": "Transaksi"}, "signedText": {"empty": {"title": "Belum ada teks yang ditan<PERSON>ngani", "desc": "<PERSON><PERSON><PERSON> teks yang ditandatangani melalui Rabby akan terdaftar di sini."}, "label": "Teks"}, "title": "<PERSON><PERSON><PERSON>"}, "receive": {"title": "Terima {{token}} di {{chain}}", "watchModeAlert1": "<PERSON><PERSON> adalah alamat Watch Mode.", "watchModeAlert2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menggunakannya untuk menerima aset?"}, "sendToken": {"AddToContactsModal": {"editAddr": {"validator__empty": "<PERSON>lakan masukkan catatan alamat", "placeholder": "Masukkan Catatan Alamat"}, "addedAsContacts": "Ditambahkan sebagai kontak", "editAddressNote": "Edit address note", "error": "Gagal menambah ke kontak"}, "allowTransferModal": {"validator__empty": "<PERSON><PERSON><PERSON> masukkan kata sandi", "addWhitelist": "Tambahkan ke daftar putih", "error": "kata sandi salah", "placeholder": "<PERSON><PERSON><PERSON><PERSON> Sand<PERSON> untuk Mengonfirmasi"}, "GasSelector": {"level": {"normal": "Cepat", "$unknown": "Tidak Dikenal", "custom": "Kustom", "slow": "Normal", "fast": "Instan"}, "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popupTitle": "Atur Harga Gas (Gwei)", "popupDesc": "Biaya gas akan dipotong dari jumlah transfer berdasarkan harga gas yang Anda atur."}, "header": {"title": "<PERSON><PERSON>"}, "modalConfirmAddToContacts": {"confirmText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Tambahkan ke kontak"}, "modalConfirmAllowTransferTo": {"confirmText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelText": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> Sand<PERSON> untuk Mengonfirmasi"}, "sectionBalance": {"title": "<PERSON><PERSON>"}, "sectionChain": {"title": "<PERSON><PERSON><PERSON>"}, "sectionFrom": {"title": "<PERSON><PERSON>"}, "sectionTo": {"title": "Untuk", "addrValidator__empty": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t", "addrValidator__invalid": "<PERSON><PERSON><PERSON> ini tidak valid", "searchInputPlaceholder": "<PERSON>i atau masukkan alamat"}, "tokenInfoFieldLabel": {"contract": "<PERSON><PERSON><PERSON>", "chain": "<PERSON><PERSON><PERSON>"}, "balanceWarn": {"gasFeeReservation": "Reservasi biaya Gas diperlukan"}, "balanceError": {"insufficientBalance": "Saldo tidak cukup"}, "sectionMsgDataForEOA": {"placeholder": "Opsional", "title": "<PERSON><PERSON>", "currentIsUTF8": "Input saat ini adalah UTF-8. Data Asli adalah:", "currentIsOriginal": "Input saat ini adalah Data Asli. UTF-8 adalah:"}, "sectionMsgDataForContract": {"simulation": "Simulasi panggilan kontrak:", "placeholder": "Opsional", "title": "Panggilan kontrak", "notHexData": "Hanya data hex yang didukung", "parseError": "Gagal mendekode panggilan kontrak"}, "addressNotInContract": "Tidak ada dalam daftar alamat. <1></1><2>Tambahkan ke kontak</2>", "sendButton": "<PERSON><PERSON>", "max": "MAKS", "blockedTransaction": "Transaksi Diblokir", "whitelistAlert__whitelisted": "<PERSON><PERSON><PERSON> telah dimasukkan dalam daftar putih", "blockedTransactionCancelText": "<PERSON><PERSON>", "whitelistAlert__notWhitelisted": "Alamat tidak tercantum dalam daftar putih. <1 /> <PERSON><PERSON> setuju untuk memberikan izin sementara untuk mentransfer.", "whitelistAlert__temporaryGranted": "Izin sementara diberikan", "tokenInfoPrice": "<PERSON><PERSON>", "blockedTransactionContent": "Transaksi ini berinteraksi dengan alamat yang ada di daftar sanksi OFAC.", "whitelistAlert__disabled": "Whitelist din<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> dapat mentransfer ke alamat mana pun."}, "sendTokenComponents": {"GasReserved": "Ditetapkan <1>0</1> {{ tokenName }} untuk biaya gas", "SwitchReserveGas": "Cadangkan Gas <1 />"}, "sendNFT": {"header": {"title": "<PERSON><PERSON>"}, "sectionChain": {"title": "<PERSON><PERSON><PERSON>"}, "sectionFrom": {"title": "<PERSON><PERSON>"}, "sectionTo": {"title": "<PERSON>", "addrValidator__empty": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t", "searchInputPlaceholder": "<PERSON>i atau masukkan alamat", "addrValidator__invalid": "<PERSON><PERSON><PERSON> ini tidak valid"}, "nftInfoFieldLabel": {"Contract": "Kontrak", "sendAmount": "<PERSON><PERSON>", "Collection": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmModal": {"title": "<PERSON><PERSON><PERSON><PERSON> Sand<PERSON> untuk Mengonfirmasi"}, "whitelistAlert__temporaryGranted": "Izin sementara diberikan", "sendButton": "<PERSON><PERSON>", "tipNotOnAddressList": "Tidak ada dalam daftar alamat.", "tipAddToContacts": "Tambahkan ke kontak", "whitelistAlert__notWhitelisted": "<PERSON><PERSON><PERSON> tidak ada dalam daftar putih. <1 /> <PERSON><PERSON> setu<PERSON> untuk memberikan izin sementara untuk mentransfer.", "whitelistAlert__disabled": "Whitelist din<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> dapat mentransfer ke alamat mana saja.", "whitelistAlert__whitelisted": "<PERSON><PERSON><PERSON> telah dimasukkan dalam daftar putih"}, "approvals": {"header": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> di {{ address }}"}, "tab-switch": {"assets": "<PERSON><PERSON>", "contract": "<PERSON><PERSON>"}, "component": {"table": {"bodyEmpty": {"loadingText": "Memuat...", "noDataText": "Tidak Ada Persetujuan", "noMatchText": "Tidak <PERSON>"}}, "ApprovalContractItem": {"ApprovalCount_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ApprovalCount_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "RevokeButton": {"permit2Batch": {"modalTitle_one": "Total <2>{{count}}</2> tanda tangan diperlukan", "modalTitle_other": "Total dari <2>{{count}}</2> tanda tangan yang diperlukan", "modalContent": "Persetujuan dari kontrak Permit2 yang sama akan dikemas bersama di bawah tanda tangan yang sama."}, "btnText_one": "Cabut ({{count}})", "btnText_zero": "Batalkan", "btnText_other": "Cabut ({{count}})"}, "ViewMore": {"text": "<PERSON><PERSON> lebih banyak"}}, "search": {"placeholder": "Cari {{ type }} berdasarkan nama/alamat"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "Kontrak", "revokeTrends": "24h Tren Pem<PERSON>alan", "myApprovalTime": "<PERSON><PERSON><PERSON>", "contractTrustValue": "<PERSON><PERSON>", "myApprovedAssets": "<PERSON><PERSON> ya<PERSON>"}, "columnTip": {"contractTrustValueDanger": "<PERSON><PERSON> k<PERSON> k<PERSON> < $10,000", "contractTrustValueWarning": "<PERSON><PERSON> k<PERSON> k<PERSON> < $100.000", "contractTrustValue": "<PERSON><PERSON> kepercayaan mengacu pada total nilai aset yang dibel<PERSON>jakan oleh kontrak ini. <PERSON><PERSON> kepercayaan yang rendah menun<PERSON>kkan risiko atau tidak ada aktivitas selama 180 hari."}}, "byAssets": {"columnTitle": {"myApprovalTime": "<PERSON><PERSON><PERSON>", "asset": "<PERSON><PERSON>", "type": "Tipe", "approvedAmount": "<PERSON><PERSON><PERSON>", "approvedSpender": "Pengguna yang Di<PERSON>u<PERSON>"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "<PERSON><PERSON><PERSON>", "tipMyBalance": "<PERSON><PERSON>"}}}}, "RevokeApprovalModal": {"confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ selectedCount }}", "unSelectAll": "<PERSON><PERSON><PERSON>", "subTitleContract": "Disetujui untuk Kontrak berikut", "selectAll": "<PERSON><PERSON><PERSON>", "subTitleTokenAndNFT": "Token dan NFT yang <PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltipPermit2": "Persetujuan ini disetujui melalui kontrak Permit2:  \n{{ permit2Id }}"}, "revokeModal": {"totalRevoked": "Total:", "approvalCount_one": "{{count}} per<PERSON><PERSON><PERSON><PERSON>", "batchRevoke": "<PERSON><PERSON>", "revokeOneByOne": "<PERSON><PERSON><PERSON> per <PERSON>tu", "approvalCount_other": "{{count}} per<PERSON><PERSON><PERSON><PERSON>", "approvalCount_zero": "{{count}} per<PERSON><PERSON><PERSON><PERSON>", "confirmRevokeLedger": "<PERSON>gan mengg<PERSON>kan al<PERSON>, <PERSON><PERSON> dapat membatalkan {{count}} persetujuan secara bersamaan dengan 1 klik.", "confirmRevokePrivateKey": "Dengan menggunakan seed phrase atau alamat private key, <PERSON>a dapat membatalkan {{count}} persetujuan dalam 1 klik.", "revoked": "Dicabut:", "submitTxFailed": "Gagal Mengirim", "resume": "Lanjutkan", "gasTooHigh": "Biaya gas tinggi", "stillRevoke": "<PERSON><PERSON><PERSON>", "gasNotEnough": "Gas tidak cukup untuk mengirim", "defaultFailed": "<PERSON><PERSON><PERSON> gagal", "confirmTitle": "Batalkan Batch dengan Satu Klik", "revokeWithLedger": "<PERSON><PERSON>", "connectLedger": "Sambungkan Ledger", "simulationFailed": "<PERSON><PERSON><PERSON><PERSON>", "ledgerSigned": "Ditandatangani. Membuat transaksi ({{current}}/{{total}})", "useGasAccount": "Saldo gas Anda rendah. GasAccount Anda akan menanggung biaya gas.", "waitInQueue": "<PERSON><PERSON><PERSON> dalam antrean", "paused": "Dihentikan", "pause": "<PERSON><PERSON>", "cancelTitle": "Batalkan Pencabut<PERSON> yang <PERSON>", "ledgerAlert": "<PERSON><PERSON>an buka Ethereum App di perangkat Ledger Anda", "signAndStartRevoke": "Tandatangani dan <PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledgerSended": "<PERSON><PERSON><PERSON> tandatanga<PERSON> permin<PERSON><PERSON> di <PERSON> ({{current}}/{{total}})", "ledgerSending": "Mengirim permintaan penanda<PERSON>an ({{current}}/{{total}})", "done": "Se<PERSON><PERSON>", "cancelBody": "<PERSON><PERSON>a <PERSON>p halaman ini, sisa pencabutan tidak akan di<PERSON>n."}}, "gasTopUp": {"Amount": "<PERSON><PERSON><PERSON>", "Continue": "Lanjutkan", "topUpChain": "Top Up Chain", "title": "Top Up Gas Instan", "InsufficientBalance": "Tidak ada cukup saldo di alamat kontrak Rabby untuk rantai saat ini. <PERSON>lakan coba lagi nanti.", "description": "Isi ulang gas dengan mengirimkan kami token yang tersedia di rantai lain. Transfer instan segera setelah pembayaran <PERSON> di<PERSON>, tanpa menunggu untuk menjadi tidak dapat dibatalkan.", "hightGasFees": "<PERSON><PERSON><PERSON> pengisian ulang ini terlalu kecil karena jaringan target memerlukan biaya gas yang tinggi.", "InsufficientBalanceTips": "Saldo tidak cukup", "Loading_Tokens": "Memuat <PERSON>...", "No_Tokens": "Tidak Ada Token", "Including-service-fee": "Termasuk biaya layanan {{fee}}", "Value": "<PERSON><PERSON>", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Token": "Token", "Select-from-supported-tokens": "Pilih dari token yang didukung", "Balance": "<PERSON><PERSON>", "payment": "Pembayaran Pengisian Gas", "Payment-Token": "<PERSON><PERSON>", "Select-payment-token": "Pilih token pembayaran", "service-fee-tip": "Dengan menyediakan layanan untuk Gas Top Up, <PERSON><PERSON> harus menanggung kerugian fluktuasi token dan biaya gas untuk top up. <PERSON><PERSON> karena itu, biaya layanan sebesar 20% dikenakan."}, "swap": {"rabbyFee": {"button": "Dapatkan", "wallet": "Dompet", "rate": "<PERSON><PERSON><PERSON>", "bridgeDesc": "<PERSON><PERSON>et akan selalu menemukan suku bunga terbaik dari agregator teratas dan memverifikasi keandalan tawaran mereka. <PERSON>bby mengenakan biaya 0,25%, yang secara otomatis termasuk dalam kutipan.", "title": "<PERSON><PERSON> biaya", "swapDesc": "<PERSON><PERSON>et akan selalu menemukan tarif terbaik dari agregator teratas dan memverifikasi keandalan tawaran mereka. <PERSON>bby mengenakan biaya 0,25% (0% untuk pembungkus), yang secara otomatis termasuk dalam kutipan."}, "lowCreditModal": {"title": "Token ini memiliki nilai kredit yang rendah", "desc": "<PERSON><PERSON> kredit yang rendah seringkali menandakan risiko tinggi, seperti token honeypot atau likuiditas yang sangat rendah."}, "Pending": "<PERSON><PERSON><PERSON>", "not-supported": "Tidak didukung", "slippage_tolerance": "Toleransi slippage:", "gas-x-price": "Harga gas: {{price}} Gwei.", "no-transaction-records": "Tidak ada catatan transaksi", "swap-history": "Riwayat swap", "approve-x-symbol": "Setujui {{symbol}}", "title": "<PERSON><PERSON>", "testnet-is-not-supported": "Jaringan kustom tidak didukung", "actual-slippage": "Actual Slippage:", "approve-and-swap": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON> me<PERSON> {{name}}", "InSufficientTip": "Saldo tidak cukup untuk melakukan simulasi transaksi dan estimasi gas. Kutipan aggregator asli ditampilkan.", "slippage-adjusted-refresh-quote": "Slippage disesuaikan. Segarkan kutipan.", "Completed": "Se<PERSON><PERSON>", "completedTip": "Transaksi di rantai, mendekode data untuk menghasilkan catatan", "pendingTip": "Tx dikirim. Jika tx tertunda selama berja<PERSON>-jam, <PERSON><PERSON> da<PERSON>t mencoba untuk menghapus yang tertunda di pengaturan.", "swap-via-x": "<PERSON><PERSON> {{name}}", "best": "Terbaik", "enable-it": "Aktifkan itu", "this-token-pair-is-not-supported": "Token pair tidak didukung", "amount-in": "<PERSON><PERSON><PERSON> {{symbol}}", "approve-swap": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "hidden-no-quote-rates_other": "{{count}} tarif tidak tersedia", "unable-to-fetch-the-price": "Tidak dapat mengambil harga", "no-fee-for-wrap": "Tidak ada biaya Rabby untuk Wrap", "rabby-fee": "<PERSON><PERSON> biaya", "hidden-no-quote-rates_one": "{{count}} tarif tidak tersedia", "security-verification-failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> keamanan gagal", "approve-tips": "1.<PERSON><PERSON><PERSON><PERSON> → 2.<PERSON><PERSON>", "preferMEV": "Suka MEV Guarded", "unlimited-allowance": "Kehendak tak terbatas", "there-is-no-fee-and-slippage-for-this-trade": "Tidak ada slippage untuk perdagangan ini.", "get-quotes": "Dapatkan kutipan", "from": "<PERSON><PERSON>", "search-by-name-address": "<PERSON><PERSON> / <PERSON><PERSON><PERSON>", "minimum-received": "Jumlah minimum diterima", "fail-to-simulate-transaction": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>i", "to": "Untuk", "this-exchange-is-not-enabled-to-trade-by-you": "Pertukaran ini tidak diaktifkan untuk diperdagangkan oleh Anda.", "need-to-approve-token-before-swap": "<PERSON><PERSON> menyetujui token sebelum melakukan swap", "swap-from": "<PERSON><PERSON> dari", "Gas-fee-too-high": "Gas fee terlalu tinggi", "price-expired-refresh-quote": "<PERSON><PERSON> telah <PERSON>. Segarkan kuti<PERSON>.", "chain": "<PERSON><PERSON><PERSON>", "no-slippage-for-wrap": "Tidak ada slippage untuk Wrap", "dex": "<PERSON>", "est-receiving": "<PERSON><PERSON><PERSON>:", "sort-with-gas": "Urutkan dengan gas", "est-difference": "<PERSON><PERSON><PERSON>:", "rates-from-cex": "<PERSON><PERSON><PERSON> da<PERSON>", "the-following-swap-rates-are-found": "<PERSON><PERSON><PERSON><PERSON> tarif be<PERSON>", "view-quotes": "<PERSON><PERSON>", "wrap-contract": "Wrap Contract", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select-token": "<PERSON><PERSON><PERSON>", "exchanges": "Bursa", "by-transaction-simulation-the-quote-is-valid": "Dengan simulasi transaksi, k<PERSON><PERSON> tersebut valid", "enable-trading": "Aktifkan Perdagangan", "trade": "Perdagangan", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Slippage rendah dapat menyebabkan transaksi gagal akibat volatilitas tinggi", "preferMEVTip": "Aktifkan fitur \"MEV Guarded\" untuk pertukaran Ethereum guna mengurangi risiko serangan sandwich. Catatan: fitur ini tidak didukung jika Anda menggunakan RPC kustom atau alamat wallet connect.", "est-payment": "<PERSON><PERSON><PERSON>:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "<PERSON><PERSON><PERSON> yang dipilih sangat berbeda dari tarif saat ini, dapat menyebabkan ker<PERSON>ian besar", "tradingSettingTip2": "2. <PERSON><PERSON> tidak bertanggung jawab atas risiko yang timbul dari kontrak pertukaran", "i-understand-and-accept-it": "<PERSON><PERSON> men<PERSON> dan men<PERSON>ya", "directlySwap": "Membungkus token {{symbol}} secara langsung dengan kontrak pintar", "enable-exchanges": "Aktifkan Pertukaran", "cex": "Cex", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "<PERSON><PERSON>i mungkin akan di-front-run karena toleransi slippage yang tinggi", "tradingSettingTip1": "1. <PERSON><PERSON><PERSON>, <PERSON><PERSON> akan be<PERSON>i dengan kontrak langsung dari bursa", "slippage-tolerance": "Toler<PERSON>i selip", "edit": "Sunting", "tradingSettingTips": "{{viewCount}} pertukaran menawarkan kutipan, dan {{tradeCount}} memungkinkan perdagangan", "rate": "<PERSON><PERSON><PERSON>", "recommend-slippage": "<PERSON><PERSON><PERSON> men<PERSON> front-running, kami <PERSON> slippage sebesar <2>{{ slippage }}</2>%", "QuoteLessWarning": "<PERSON><PERSON><PERSON> yang diterima diperkirakan dari simulasi trans<PERSON>i <PERSON>. Penawaran yang diberikan oleh dex adalah {{receive}}. <PERSON>a akan menerima {{diff}} kurang dari penawaran yang diharapkan.", "usd-after-fees": "≈ {{usd}}", "max": "MAKS", "actual": "Aktual:", "No-available-quote": "Tidak ada kutipan yang tersedia", "fetch-best-quote": "Mengambil kutipan terbaik", "process-with-two-step-approve": "Lanjutkan dengan persetujuan dua langkah", "no-fees-for-wrap": "Tidak ada biaya Rabby untuk Wrap", "Auto": "<PERSON><PERSON><PERSON><PERSON>", "source": "Sumber", "no-quote-found": "Tidak ada kutipan yang di<PERSON>ukan", "price-impact": "<PERSON><PERSON><PERSON>", "two-step-approve": "Tandatangani 2 transaksi untuk mengubah allowance", "insufficient-balance": "Saldo tidak cukup", "gas-fee": "GasFee: {{gasUsed}}", "loss-tips": "<PERSON>a kehilangan {{usd}}. <PERSON><PERSON><PERSON> jumlah yang lebih kecil di pasar kecil.", "two-step-approve-details": "Token USDT memerlukan 2 transaksi untuk mengubah allowance. <PERSON><PERSON><PERSON>, <PERSON><PERSON> perlu mengatur allowance ke nol, dan hanya kemudian menetapkan nilai allowance yang baru.", "estimate": "Perkiraan:"}, "bridge": {"showMore": {"title": "<PERSON><PERSON><PERSON><PERSON>", "source": "Jembatan Sumber"}, "settingModal": {"confirmModal": {"title": "Aktifkan Trading dengan Aggregator ini", "i-understand-and-accept-it": "<PERSON>a mengerti dan menerima ini", "tip1": "1. <PERSON><PERSON><PERSON>, <PERSON><PERSON> akan be<PERSON>i langsung dengan kontrak dari agregator ini.", "tip2": "2. <PERSON><PERSON> tidak bertanggung jawab atas risiko yang timbul dari kontrak agregator ini."}, "title": "Aktifkan Bridge Aggregators untuk berdagang", "SupportedBridge": "Bridge yang <PERSON>:", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tokenPairDrawer": {"balance": "<PERSON><PERSON>", "title": "<PERSON><PERSON>h dari pasangan token yang didukung", "noData": "Tidak Ada Pasangan <PERSON> yang <PERSON>", "tokenPair": "Token Pair"}, "To": "Untuk", "Balance": "Saldo:", "Select": "<PERSON><PERSON><PERSON>", "no-quote-found": "Tidak ada kutipan yang di<PERSON>ukan. Silakan coba pasangan token lain.", "From": "<PERSON><PERSON>", "select-chain": "<PERSON><PERSON><PERSON>", "Amount": "<PERSON><PERSON><PERSON>", "bridgeTo": "Bridge Ke", "estimate": "Perkiraan:", "insufficient-balance": "Saldo tidak cukup", "the-following-bridge-route-are-found": "Ditemukan rute berikut", "Pending": "<PERSON><PERSON><PERSON>", "actual": "Aktual:", "tokenPairPlaceholder": "<PERSON><PERSON><PERSON>", "getRoutes": "Dapatkan rute", "BridgeTokenPair": "Jembatan <PERSON>", "detail-tx": "Detail", "best": "Terbaik", "duration": "{{duration}} min", "no-transaction-records": "Tidak ada catatan transaksi", "Completed": "Se<PERSON><PERSON>", "title": "Jembatan", "no-quote": "Tidak Ada Kutipan", "via-bridge": "via {{bridge}}", "estimated-value": "≈ {{value}}", "need-to-approve-token-before-bridge": "<PERSON><PERSON> men<PERSON>tujui token sebelum jembatan", "slippage-adjusted-refresh-quote": "Slippage disesuaikan. Segarkan rute.", "gas-x-price": "Gas price: {{price}} Gwei.", "unlimited-allowance": "<PERSON>zin Tidak Terbatas", "gas-fee": "GasFee: {{gasUsed}}", "approve-x-symbol": "Setujui {{symbol}}", "history": "Riwayat Jembatan", "completedTip": "Transaksi di on chain, mendekode data untuk menghasilkan catatan", "approve-and-bridge": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "bridge-via-x": "Bridge di {{name}}", "rabby-fee": "<PERSON><PERSON> biaya", "enable-it": "Aktifkan itu", "price-impact": "<PERSON><PERSON><PERSON>", "est-difference": "<PERSON><PERSON><PERSON>:", "bridge-cost": "Biaya <PERSON>", "no-route-found": "Tidak ada rute ditemukan", "est-receiving": "<PERSON><PERSON><PERSON>:", "recommendFromToken": "<PERSON><PERSON><PERSON> dari <1></1> untuk kutipan yang tersedia", "loss-tips": "<PERSON>a kehilangan {{usd}}. <PERSON><PERSON><PERSON> jumlah yang berbeda.", "max-tips": "Nilai ini dihitung dengan mengurangi biaya gas untuk bridge.", "est-payment": "<PERSON><PERSON><PERSON>:", "aggregator-not-enabled": "Aggregator ini tidak diaktifkan untuk diperdagangkan oleh Anda.", "pendingTip": "Tx terkirim. Jika tx tertunda selama berjam-jam, <PERSON><PERSON> <PERSON><PERSON><PERSON> menco<PERSON> menghapus pending di pengaturan.", "price-expired-refresh-route": "<PERSON>rga telah ked<PERSON>. Segarkan rute."}, "manageAddress": {"address-management": "<PERSON><PERSON><PERSON><PERSON>", "no-match": "Tidak ada yang cocok", "no-address": "Tidak ada alamat", "current-address": "<PERSON><PERSON><PERSON>", "confirm-delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whitelisted-address": "<PERSON><PERSON><PERSON> yang te<PERSON>ar", "search": "<PERSON><PERSON>", "seed-phrase-delete-title": "Hapus frasa benih?", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete-seed-phrase": "Hapus seed phrase", "watch-address": "<PERSON><PERSON>", "addressTypeTip": "<PERSON><PERSON><PERSON> {{type}}", "deleted": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "private-key": "<PERSON><PERSON><PERSON>", "hd-path": "HD path:", "add-address": "<PERSON><PERSON><PERSON> al<PERSON>", "delete-private-key-modal-title_one": "Hapus {{count}} al<PERSON><PERSON> kunci privat", "delete-title_other": "Hapus {{count}} alamat {{brand}}", "delete-all-addresses-but-keep-the-seed-phrase": "<PERSON><PERSON> semua alamat, tetapi simpan seed phrase", "backup-seed-phrase": "Cadangan <PERSON>", "delete-title_one": "Hapus {{count}} alamat {{brand}}", "delete-private-key-modal-title_other": "Hapus {{count}} al<PERSON><PERSON> kunci privat", "seed-phrase": "<PERSON><PERSON>", "manage-address": "<PERSON><PERSON><PERSON>", "update-balance-data": "Perbarui data saldo", "delete-seed-phrase-title_one": "Hapus frase benih dan {{count}} alamatnya", "delete-all-addresses-and-the-seed-phrase": "<PERSON><PERSON> semua alamat dan frase benih", "delete-seed-phrase-title_other": "Hapus frase seed dan {{count}} alamatnya", "delete-empty-seed-phrase": "Hapus frasa benih dan alamat 0-nya", "delete-checklist-2": "<PERSON><PERSON> <PERSON> bahwa saya telah mencadangkan kunci privat atau Seed Phrase dan saya siap untuk menghap<PERSON>nya sekarang.", "no-address-under-seed-phrase": "<PERSON>a belum mengimpor alamat apapun di bawah frase seed ini.", "delete-checklist-1": "<PERSON>a men<PERSON> bahwa jika saya menghapus alamat ini, <PERSON>nci Privat & Frasa Benih yang sesuai dengan alamat ini akan dihapus dan Rabby TIDAK akan dapat memulihkannya.", "delete-desc": "Sebelum <PERSON>, ingat<PERSON> poin-poin berikut untuk memahami cara melindungi aset <PERSON>.", "sort-by-address-type": "<PERSON><PERSON><PERSON> be<PERSON> jeni<PERSON> al<PERSON>", "sort-by-address-note": "Urutkan berdasarkan alamat catatan", "addNewAddress": "Tambah<PERSON>", "enterPassphraseTitle": "<PERSON><PERSON><PERSON><PERSON> Passphrase untuk <PERSON>", "sort-by-balance": "<PERSON><PERSON><PERSON><PERSON> berda<PERSON>kan saldo", "enterThePassphrase": "Ma<PERSON>kkan Passphrase", "sort-address": "<PERSON><PERSON><PERSON>", "passphraseError": "Passphrase tidak valid", "CurrentDappAddress": {"desc": "Ganti <PERSON>"}}, "dashboard": {"home": {"panel": {"swap": "<PERSON><PERSON>", "nft": "NFT", "rabbyPoints": "<PERSON><PERSON>", "transactions": "Transaksi", "receive": "Terima", "gasTopUp": "Pengisian Gas", "more": "<PERSON><PERSON><PERSON> banyak", "ecology": "Ekosistem", "feedback": "<PERSON><PERSON>", "manageAddress": "<PERSON><PERSON><PERSON>", "queue": "<PERSON><PERSON><PERSON>", "bridge": "Jembatan", "send": "<PERSON><PERSON>", "approvals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "Mobile Sync"}, "queue": {"title": "<PERSON><PERSON><PERSON>", "count": "{{count}} di"}, "refreshTheWebPageToTakeEffect": "Segarkan halaman web untuk mengambil efek", "flip": "Balik", "comingSoon": "<PERSON><PERSON><PERSON>", "soon": "<PERSON><PERSON><PERSON>", "metamaskIsInUseAndRabbyIsBanned": "MetaMask sedang digunakan dan <PERSON> dilarang", "rabbyIsInUseAndMetamaskIsBanned": "<PERSON>bby sedang digunakan dan <PERSON> dilarang", "offline": "Jaringan terputus dan tidak ada data yang diperoleh", "whatsNew": "Apa yang baru", "chainEnd": "rantai", "chain": "rantai,", "viewFirstOne": "<PERSON><PERSON> yang pertama", "importType": "<PERSON><PERSON><PERSON> {{type}}", "transactionNeedsToSign": "transaksi perlu ditandatangani", "transactionsNeedToSign": "transaksi perlu ditandatangani", "pendingCount": "1 Menunggu", "view": "Lihat", "pendingCountPlural": "{{countStr}} Pendings", "missingDataTooltip": "<PERSON><PERSON> mungkin tidak diperbarui karena masalah jaringan saat ini dengan {{text}}.", "rejectAll": "<PERSON><PERSON>"}, "recentConnection": {"disconnectRecentlyUsed": {"title_one": "<PERSON><PERSON><PERSON> sambungan <strong>{{count}}</strong> <PERSON><PERSON> yang terhubung", "title_other": "<PERSON><PERSON><PERSON> sambungan <strong>{{count}}</strong> <PERSON><PERSON> yang terhubung", "title": "<PERSON><PERSON><PERSON> sambungan <strong>{{count}}</strong> DApps yang baru saja digunakan", "description": "DApps yang dipin akan tetap terhubung"}, "connected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinned": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>a", "dragToSort": "Seret ke urutan", "disconnected": "<PERSON><PERSON><PERSON><PERSON>", "disconnectAll": "<PERSON><PERSON><PERSON>", "notConnected": "Tidak terhubung", "rpcUnavailable": "RPC kustom tidak tersedia", "noDappFound": "Tidak ada <PERSON> yang di<PERSON>ukan", "noPinnedDapps": "Tidak ada dapps yang disematkan", "title": "<PERSON><PERSON> yang <PERSON>", "metamaskTooltip": "<PERSON>a lebih suka menggunakan MetaMask dengan dapp ini. <PERSON><PERSON><PERSON> pengaturan ini kapan saja di Pengaturan > Dapps Favorit MetaMask", "connectedDapp": "Rabby tidak terhubung ke Dapp saat ini. Untuk terhubung, temukan dan klik tombol sambung di halaman web Dapp.", "noConnectedDapps": "Tidak ada <PERSON> yang terhubung", "dapps": "<PERSON><PERSON>", "recentlyConnected": "Baru saja terhubung", "metamaskModeTooltipNew": "<PERSON>bby <PERSON>et akan terhubung ketika <PERSON><PERSON> memi<PERSON> \"MetaMask\" di Dapp. Anda dapat mengatur ini di More > Connect Rabby dengan Menyamar sebagai MetaMask", "metamaskModeTooltip": "Tidak bisa menghubungkan Rabby di Dapp ini? Coba aktifkan <1>MetaMask Mode</1>", "noRecentlyConnectedDapps": "Tidak ada <PERSON> yang baru terhubung"}, "feedback": {"directMessage": {"content": "<PERSON><PERSON>", "description": "<PERSON><PERSON> dengan Rabby Wallet Resmi di DeBank"}, "proposal": {"content": "<PERSON><PERSON><PERSON>", "description": "Ajukan proposal untuk Rabby Wallet di DeBank"}, "title": "<PERSON><PERSON>"}, "nft": {"collectionList": {"collections": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "all_nfts": {"label": "Semua NFT"}}, "modal": {"lastPrice": "<PERSON><PERSON>", "send": "<PERSON><PERSON>", "chain": "<PERSON><PERSON><PERSON>", "purchaseDate": "Tanggal Pembelian", "sendTooltip": "Saat ini hanya ERC 721 dan ERC 1155 NFT yang didukung", "collection": "<PERSON><PERSON><PERSON><PERSON>"}, "empty": "Tidak ada NFT yang ditemukan di Koleksi yang didukung", "listEmpty": "Anda belum mendapatkan NFT apapun."}, "rabbyBadge": {"learnMore": "<PERSON><PERSON><PERSON><PERSON>", "enterClaimCode": "<PERSON>su<PERSON>n kode klaim", "imageLabel": "rabby badge", "title": "<PERSON><PERSON><PERSON> untuk", "claim": "<PERSON><PERSON><PERSON>", "goToSwap": "<PERSON>gi ke Swap", "freeGasTitle": "<PERSON><PERSON><PERSON>cana Gas Gratis untuk", "swapTip": "Anda perlu menyelesaikan pertukaran dengan dex terkemuka dalam Rabby Wallet terlebih dahulu.", "freeGasTip": "<PERSON>lakan tanda tangani transaksi menggunakan Free Gas. Tombol 'Free Gas' akan muncul secara otomatis ketika gas Anda tidak cukup.", "rabbyFreeGasUserNo": "<PERSON>bby Free Gas Pengguna No.{{num}}", "rabbyValuedUserNo": "<PERSON><PERSON> Bernilai No.{{num}}", "noCode": "Anda belum mengaktifkan kode klaim untuk alamat ini.", "learnMoreOnDebank": "<PERSON><PERSON><PERSON><PERSON> lebih lanju<PERSON> di DeBank", "viewOnDebank": "<PERSON><PERSON>Bank", "viewYourClaimCode": "<PERSON><PERSON> kode klaim Anda di DeBank", "claimSuccess": "<PERSON><PERSON><PERSON>", "freeGasNoCode": "<PERSON>lakan klik tombol di bawah untuk mengunjungi DeBank dan dapatkan kode klaim menggunakan alamat Anda saat ini terlebih dahulu."}, "contacts": {"noData": "Tidak ada data", "oldContactList": "<PERSON><PERSON><PERSON>", "noDataLabel": "tidak ada data", "oldContactListDescription": "<PERSON><PERSON> pengg<PERSON>an kontak dan alamat model watch, kontak lama akan dicadangkan untuk Anda di sini dan setelah beberapa waktu kami akan menghapus daftar tersebut. <PERSON>lakan tambahkan tepat waktu jika Anda terus menggunakan."}, "security": {"title": "<PERSON><PERSON><PERSON>", "comingSoon": "<PERSON><PERSON>h banyak fitur segera hadir", "nftApproval": "Persetujuan NFT", "tokenApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"lock": {"never": "Tidak <PERSON>"}, "updateVersion": {"okText": "Lihat <PERSON>", "successTip": "<PERSON>a menggunakan versi terbaru", "title": "Pembaruan Tersedia", "content": "Pembaruan baru untuk Rabby Wallet tersedia. Klik untuk memeriksa cara memperbarui secara manual."}, "features": {"searchDapps": "<PERSON><PERSON>", "lockWallet": "<PERSON><PERSON><PERSON>", "manageAddress": "<PERSON><PERSON><PERSON>", "rabbyPoints": "<PERSON><PERSON>", "gasTopUp": "Pengisian Gas", "label": "<PERSON><PERSON>", "connectedDapp": "<PERSON>pps Terkoneksi", "signatureRecord": "<PERSON><PERSON><PERSON>"}, "settings": {"toggleThemeMode": "Mode Tema", "metamaskPreferredDapps": "MetaMask <PERSON>", "customRpc": "Ubah URL RPC", "enableWhitelistForSendingAssets": "Aktifkan Whitelist Untuk Mengirim Aset", "currentLanguage": "Bahasa Saat Ini", "label": "<PERSON><PERSON><PERSON><PERSON>", "enableTestnets": "Aktifkan Testnets", "themeMode": "Mode Tema", "customTestnet": "Tambahkan Jaring<PERSON>", "metamaskMode": "Sambungkan Ra<PERSON> dengan <PERSON>ai MetaMask", "enableDappAccount": "Ganti Alamat Dapp Secara Independen\n"}, "7Days": "7 hari", "4Hours": "4 jam", "10Minutes": "10 menit", "1Hour": "1 jam", "backendServiceUrl": "URL Layanan Backend", "pleaseCheckYourHost": "<PERSON><PERSON><PERSON> host <PERSON><PERSON>", "1Day": "1 hari", "host": "<PERSON><PERSON>", "pendingTransactionCleared": "Transaksi yang tertunda telah dibersihkan", "save": "Simpan", "clearPending": "<PERSON><PERSON>", "reset": "Men<PERSON><PERSON><PERSON><PERSON> pengaturan awal", "warning": "Peringatan", "autoLockTime": "<PERSON><PERSON><PERSON> kunci otomatis", "disableWhitelist": "Nonaktifkan Daftar Putih", "disableWhitelistTip": "Anda bisa mengirim aset ke alamat mana pun setelah dinonaktifkan.", "clearWatchAddressContent": "<PERSON><PERSON><PERSON><PERSON> Anda memastikan untuk menghapus semua alamat <PERSON>?", "cancel": "Batalkan", "enableWhitelist": "Aktifkan Daftar Putih", "claimRabbyBadge": "<PERSON><PERSON><PERSON>ge Rabby!", "clearPendingTip1": "Tindakan ini menghapus transaksi yang tertunda dari ant<PERSON>uka <PERSON>, membantu menyelesaikan masalah yang disebabkan oleh durasi tertunda yang lama di jaringan.", "enableWhitelistTip": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> hanya dapat mengirim aset ke alamat dalam whitelist men<PERSON><PERSON><PERSON>.", "clearPendingTip2": "Ini tidak mempengaruhi saldo akun Anda atau memerlukan Anda untuk memasukkan kembali frase benih <PERSON>a. <PERSON><PERSON><PERSON> aset dan detail akun tetap aman.", "testnetBackendServiceUrl": "URL Layanan Backend Testnet", "currentVersion": "Versi Saat Ini", "requestDeBankTestnetGasToken": "Minta Token Gas DeBank Testnet", "followUs": "<PERSON><PERSON><PERSON>", "supportedChains": "<PERSON><PERSON><PERSON>", "updateAvailable": "Pembaruan Tersedia", "clearWatchMode": "Hapus <PERSON>", "aboutUs": "<PERSON>tang kami", "clearPendingWarningTip": "Transaksi yang dihapus mungkin masih dikonfirmasi di on-chain kecuali jika digantikan.", "inputOpenapiHost": "<PERSON><PERSON><PERSON> ma<PERSON>kkan host <PERSON><PERSON><PERSON>", "DappAccount": {"title": "Ganti Alamat Dapp Secara Terpisah\n", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> dapat memilih alamat mana yang akan dihubungkan ke setiap Dapp secara terpisah. Mengganti alamat utama Anda tidak akan memengaruhi alamat yang terhubung ke setiap Dapp.\n", "button": "Aktifkan\n"}}, "tokenDetail": {"txFailed": "Gaga<PERSON>", "customized": "<PERSON><PERSON><PERSON><PERSON>", "swap": "<PERSON><PERSON><PERSON><PERSON>", "noTransactions": "Tidak Ada Transaksi", "blockedButtons": "token yang diblokir", "receive": "Terima", "blocked": "Diblokir", "send": "<PERSON><PERSON>", "blockedButton": "token yang diblokir", "selectedCustom": "Token tidak terdaftar oleh Ra<PERSON>. Anda telah menambahkannya ke dalam daftar token secara manual.", "blockedTip": "Token yang diblokir tidak akan ditampilkan dalam daftar token", "notSupported": "Token di jaringan ini tidak didukung", "customizedButton": "token kustom", "scamTx": "Scam tx", "customizedButtons": "token kustom", "notSelectedCustom": "Token tidak terdaftar oleh Rabby. Itu akan ditambahkan ke daftar token jika Anda mengh<PERSON>.", "blockedListTitle": "token yang diblokir", "ListedBy": "D daftar oleh", "noIssuer": "Tidak ada informasi penerbit yang tersedia", "TokenName": "<PERSON><PERSON>", "AddToMyTokenList": "Tambahkan ke daftar token saya", "BridgeProvider": "Penyedia <PERSON>", "verifyScamTips": "Ini adalah token penipuan", "customizedListTitle": "token kustom", "IssuerWebsite": "Website Penerbit", "OriginIssue": "Diterbitkan secara asli di blockchain ini", "customizedListTitles": "token kustom", "SupportedExchanges": "<PERSON><PERSON><PERSON> yang <PERSON>", "blockedListTitles": "token yang diblokir", "NoListedBy": "Tidak ada informasi daftar yang tersedia", "BridgeIssue": "Token yang dijembatani oleh pihak ketiga", "maybeScamTips": "Ini adalah token berkualitas rendah dan mungkin merupakan penipuan", "blockedTips": "Token yang diblokir tidak akan ditampilkan dalam daftar token", "NoSupportedExchanges": "Tidak ada bursa yang didukung tersedia", "customizedHasAddedTips": "Token tersebut tidak terdaftar di Rabby. Anda telah menambahkannya ke daftar token secara manual.", "OriginalToken": "<PERSON><PERSON>", "Chain": "<PERSON><PERSON><PERSON>", "ContractAddress": "<PERSON><PERSON><PERSON>", "myBalance": "<PERSON><PERSON>", "fdvTips": "Kapital pasar jika suplai maksimum beredar. <PERSON><PERSON><PERSON> (FDV) = Harga x Suplai Maksimum. Jika Suplai Maksimum null, FDV = Harga x Suplai Total. Jika baik Suplai Maksimum maupun Suplai Total tidak terdefinisi atau tidak terbatas, FDV tidak ditampilkan."}, "assets": {"portfolio": {"fractionTips": "Hitung berdasarkan harga token ERC20 yang terhubung.", "nftTips": "Dihitung berdasarkan harga dasar yang diakui oleh protokol ini."}, "tokenButton": {"subTitle": "Token dalam daftar ini tidak akan ditambahkan ke total saldo"}, "table": {"type": "Tipe", "side": "<PERSON><PERSON>", "unsupportedPoolType": "Tipe kolam tidak didukung", "lentAgainst": "DIPINJAM MELAWAN", "PL": "P&L", "percent": "Persentase", "healthRate": "<PERSON><PERSON><PERSON> kese<PERSON>an", "debtRatio": "<PERSON><PERSON>", "pool": "POOL", "balanceValue": "Saldo / Nilai", "endAt": "<PERSON><PERSON><PERSON> <PERSON>", "dailyUnlock": "<PERSON><PERSON><PERSON><PERSON><PERSON> harian", "unlockAt": "<PERSON><PERSON> <PERSON>", "exerciseEnd": "<PERSON><PERSON><PERSON>", "lowValueAssets_0": "{{count}} token nilai rendah", "assetAmount": "<PERSON><PERSON> / <PERSON>lah", "claimable": "<PERSON><PERSON><PERSON>", "strikePrice": "<PERSON><PERSON><PERSON><PERSON>", "useValue": "<PERSON>lai <PERSON>", "lowValueDescription": "<PERSON>et nilai rendah akan ditampilkan di sini", "leverage": "Leverage", "token": "Token", "noMatch": "Tidak Cocok", "tradePair": "<PERSON><PERSON><PERSON>", "summaryDescription": "<PERSON><PERSON><PERSON> aset dalam protokol (misalnya token LP) diselesaikan ke aset dasar untuk perhitungan statistik.", "lowValueAssets_one": "{{count}} token bern<PERSON><PERSON> rendah", "lowValueAssets_other": "{{count}} token nilai rendah", "summaryTips": "<PERSON><PERSON> aset dibagi dengan total kekayaan bersih", "price": "<PERSON><PERSON>"}, "AddMainnetToken": {"tokenAddress": "<PERSON><PERSON><PERSON>", "notFound": "Token tidak ditemukan", "selectChain": "<PERSON><PERSON><PERSON> rantai", "tokenAddressPlaceholder": "<PERSON><PERSON><PERSON>", "title": "Tambahkan Token Kustom", "searching": "<PERSON><PERSON><PERSON>", "isBuiltInToken": "Token sudah didukung"}, "AddTestnetToken": {"title": "Tambahkan Token Jaringan <PERSON>", "searching": "<PERSON><PERSON><PERSON>", "tokenAddressPlaceholder": "<PERSON><PERSON><PERSON>", "tokenAddress": "<PERSON><PERSON><PERSON>", "notFound": "Token tidak ditemukan", "selectChain": "<PERSON><PERSON><PERSON> rantai"}, "TestnetAssetListContainer": {"addTestnet": "<PERSON><PERSON><PERSON>", "add": "Token"}, "amount": "JUMLAH", "usdValue": "NILAI USD", "customButtonText": "Tambahkan token kustom", "comingSoon": "<PERSON><PERSON><PERSON>...", "addTokenEntryText": "Token", "noTestnetAssets": "Tidak ada <PERSON>", "noAssets": "Tidak ada aset", "unfoldChain": "Buka 1 chain", "unfoldChainPlural": "<PERSON><PERSON> {{<PERSON><PERSON><PERSON>}} rantai", "searchPlaceholder": "Tokens", "blockDescription": "Token yang diblokir oleh Anda akan ditampilkan di sini", "blockLinkText": "<PERSON>i alamat untuk memblokir token", "customDescription": "Token kustom yang ditambahkan oleh Anda akan ditampilkan di sini"}, "hd": {"ledger": {"connected": "Ledger terhu<PERSON>ng", "doc3": "Buka Aplikasi Ethereum", "doc2": "<PERSON><PERSON><PERSON><PERSON> pin untuk membuka kunci", "doc1": "Sambungkan satu Ledger", "reconnect": "<PERSON><PERSON> tidak be<PERSON>, coba <1>menghubungkan kembali dari awal.</1>"}, "keystone": {"doc1": "Colokkan satu Keystone", "doc2": "<PERSON><PERSON><PERSON>n kata sandi untuk membuka kunci", "title": "Pastikan Keystone 3 Pro Anda berada di beranda", "doc3": "Setujui koneksi ke komputer", "reconnect": "<PERSON><PERSON> tidak be<PERSON>, silakan coba <1>menyambung kembali dari awal.</1>"}, "imkey": {"doc2": "<PERSON><PERSON><PERSON><PERSON> pin untuk membuka kunci", "doc1": "Sambungkan satu imKey"}, "howToConnectLedger": "<PERSON>kan Ledger", "howToConnectKeystone": "Cara <PERSON>ubungkan Keystone", "userRejectedTheRequest": "Pengguna menolak permintaan tersebut.", "howToSwitch": "Cara untuk beralih", "ledgerIsDisconnected": "Ledger <PERSON><PERSON> tidak terhubung", "howToConnectImKey": "Cara <PERSON> imKey"}, "GnosisWrongChainAlertBar": {"notDeployed": "Alamat Safe Anda tidak diterapkan di rantai ini"}, "echologyPopup": {"title": "Ekosistem"}, "MetamaskModePopup": {"title": "Mode MetaMask", "enableDesc": "Aktifkan jika Dapp hanya bekerja dengan MetaMask", "footerText": "Tambahkan lebih banyak Dapps ke MetaMask Mode di More > MetaMask Mode", "desc": "Jika Anda tidak dapat menghubungkan Rabby di <PERSON>, aktifkan Mode MetaMask dan hubungkan dengan memilih opsi MetaMask.", "toastSuccess": "Diaktifkan. Segarkan halaman untuk menyambung kembali."}, "offlineChain": {"chain": "{{chain}} segera tidak akan terintegrasi.", "tips": "{{chain}} Chain tidak akan diintegrasikan pada {{date}}. Aset Anda tidak akan terpengaruh tetapi tidak akan termasuk dalam total saldo Anda. Untuk mengaksesnya, Anda dapat menambahkannya sebagai jaringan kustom di \"More\"."}, "recentConnectionGuide": {"button": "Dapatkan", "title": "Ganti alamat untuk koneksi Dapp di sini"}}, "nft": {"empty": {"title": "Tidak ada NFT yang Di<PERSON>dai", "description": "Anda dapat memilih NFT dari \"All\" dan menambahkan ke \"Starred\""}, "title": "NFT", "starred": "<PERSON><PERSON><PERSON> ({{count}})", "all": "<PERSON><PERSON><PERSON>", "floorPrice": "/ Floor Price:", "noNft": "Tidak ada NFT"}, "newAddress": {"addContacts": {"content": "Tambahkan Kontak", "walletConnectVPN": "WalletConnect akan tidak stabil jika Anda menggunakan VPN.", "scanViaPcCamera": "<PERSON>ndai melalui kamera PC", "cameraTitle": "<PERSON><PERSON>an pindai kode QR dengan kamera Anda", "scanViaMobileWallet": "Pindai melalui dompet seluler", "addressEns": "Alamat / ENS", "scanQRCode": "Pindai kode QR dengan dompet yang kompatibel dengan WalletConnect", "notAValidAddress": "<PERSON><PERSON><PERSON> tida<PERSON> valid", "walletConnect": "Wallet connect", "description": "Anda juga dapat menggunakannya sebagai alamat yang hanya dapat dilihat", "required": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t"}, "unableToImport": {"title": "Tidak dapat mengimpor", "description": "Mengimpor beberapa dompet keras berbasis QR tidak didukung. Harap hapus semua alamat dari {{0}} sebelum mengimpor perangkat lain."}, "seedPhrase": {"whatIsASeedPhrase": {"question": "Apa itu Seed Phrase?", "answer": "Frasa 12, 18, atau 24 kata yang digunakan untuk mengontrol aset <PERSON>."}, "isItSafeToImportItInRabby": {"question": "<PERSON><PERSON><PERSON><PERSON> aman untuk mengimpornya di Rabby?", "answer": "Ya, itu akan disimpan secara lokal di browser Anda dan hanya dapat diakses oleh <PERSON>."}, "importTips": "<PERSON>a dapat menempelkan seluruh frasa pemulihan rahasia Anda di kolom pertama", "showSeedPhrase": "<PERSON><PERSON><PERSON><PERSON> Seed Phrase", "saved": "<PERSON><PERSON> telah menyi<PERSON>an frasa tersebut", "backup": "Cadangan <PERSON>", "invalidContent": "Konten tidak valid", "slip39SeedPhrasePlaceholder_other": "<PERSON><PERSON><PERSON><PERSON> {{count}}th bagian frasa seed <PERSON><PERSON> di sini", "fillInTheBackupSeedPhraseInOrder": "<PERSON>i frasa biji cadangan dalam urutan yang benar", "backupTips": "Pastikan tidak ada orang lain yang melihat layar Anda saat Anda mencadangkan frase biji.", "importError": "[CreateMnemonics] langkah tak terduga {{0}}", "wordPhrase": "<PERSON><PERSON> memiliki frasa <1>{{count}}</1> kata", "verifySeedPhrase": "<PERSON><PERSON><PERSON><PERSON><PERSON> Seed Phrase", "inputInvalidCount_one": "1 input tidak sesuai dengan norma Seed Phrase, silakan periksa.", "slip39SeedPhrasePlaceholder_two": "<PERSON><PERSON><PERSON><PERSON> {{count}}nd seed phrase shares di sini", "slip39SeedPhrasePlaceholder_one": "<PERSON><PERSON><PERSON><PERSON> {{count}} saham frase benih <PERSON>a di sini", "slip39SeedPhrase": "<PERSON><PERSON> memiliki frase biji <0>{{SLIP39}}</0>", "pleaseSelectWords": "<PERSON><PERSON>an pilih kata-kata", "slip39SeedPhraseWithPassphrase": "<PERSON><PERSON> <0>{{SLIP39}}</0> Frasa Bijak dengan <PERSON>", "copy": "<PERSON><PERSON> frasa benih", "clearAll": "<PERSON><PERSON>", "wordPhraseAndPassphrase": "<PERSON>a memiliki frasa <1>{{count}}</1>-kata dengan Passphrase", "riskTips": "Sebelum <PERSON> mula<PERSON>, harap baca dan ingat tips keamanan berikut.", "createdSuccessfully": "Dibuat Berhasil", "slip39SeedPhrasePlaceholder_few": "<PERSON><PERSON><PERSON><PERSON> {{count}}rd seed phrase shares <PERSON><PERSON> di sini", "importQuestion4": "<PERSON><PERSON> saya menghapus instalasi Rabby tanpa mencadangkan frasa benih, <PERSON><PERSON> tidak dapat mengambilnya untuk saya.", "pastedAndClear": "Ditempelkan dan clipboard dibersihkan", "verificationFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "passphrase": "<PERSON><PERSON> sandi", "inputInvalidCount_other": "{{count}} input tidak sesuai dengan norma Seed Phrase, silakan periksa.", "importQuestion2": "Frasa seed saya hanya disimpan di perangkat saya. <PERSON><PERSON> tidak dapat mengaksesnya.", "importQuestion3": "<PERSON><PERSON> saya mencopot pemasangan Rabby tanpa mencadangkan frase benih saya, itu tidak dapat dipulihkan oleh <PERSON>.", "importQuestion1": "Jika saya kehilangan atau membagikan seed phrase saya, saya akan kehilangan akses ke aset saya secara permanen."}, "metamask": {"tips": "Tips:", "step": "Lang<PERSON><PERSON>", "how": "Bagaimana cara mengimpor <PERSON>a?", "importSeedPhrase": "Impor frasa seed atau kunci pribadi", "importSeedPhraseTips": "Itu hanya akan disimpan secara lokal di browser. <PERSON><PERSON> tidak akan pernah memiliki akses ke informasi pribadi Anda.", "step1": "Ekspor seed phrase atau private key dari <PERSON> <br /> <1>Klik untuk melihat tutorial <1/></1>", "tipsDesc": "Frasa kata sandi/kolom kunci Anda tidak dimiliki oleh <PERSON>aM<PERSON> atau dompet tertentu mana pun; itu hanya milik <PERSON>.", "step3": "<PERSON><PERSON>r telah se<PERSON>ai dan semua aset <PERSON> akan <br /> muncul secara otomatis", "step2": "Impor seed phrase atau kunci pribadi di Rabby"}, "privateKey": {"whatIsAPrivateKey": {"question": "Apa itu private key?", "answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> huruf dan angka yang digunakan untuk mengontrol aset <PERSON>."}, "repeatImportTips": {"desc": "<PERSON><PERSON><PERSON> ini telah diimpor.", "question": "<PERSON><PERSON><PERSON><PERSON> Anda ingin beralih ke alamat ini?"}, "isItSafeToImportItInRabby": {"question": "<PERSON><PERSON><PERSON><PERSON> aman untuk mengimpornya di Rabby?", "answer": "Ya, itu akan disimpan secara lokal di browser Anda dan hanya dapat diakses oleh <PERSON>."}, "isItPossibleToImportKeystore": {"question": "A<PERSON><PERSON><PERSON> mungkin untuk mengimpor KeyStore?", "answer": "<PERSON>, <PERSON><PERSON> <1> mengimpor KeyStore </1> di sini."}, "placeholder": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi Anda", "required": "<PERSON><PERSON><PERSON> ma<PERSON>kkan Private key", "notAValidPrivateKey": "Bukan kunci pribadi yang valid"}, "ledger": {"error": {"ethereum_app_open_error": "<PERSON><PERSON><PERSON> instal/terima aplikasi Ethereum di perangkat Ledger Anda.", "running_app_close_error": "Gagal menutup aplikasi yang sedang berjalan di perangkat Ledger Anda.", "ethereum_app_not_installed_error": "<PERSON><PERSON><PERSON> instal aplikasi Ethereum di perangkat Ledger Anda.", "ethereum_app_unconfirmed_error": "<PERSON>a telah menolak permintaan untuk membuka aplikasi Ethereum."}, "cameraPermissionTitle": "Biarkan Ra<PERSON> mengakses kamera", "title": "Sambungkan Ledger", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> untuk:", "ledgerPermission1": "Sambungkan ke perangkat HID", "permissionsAuthorized": "<PERSON><PERSON> yang <PERSON>", "cameraPermission1": "<PERSON>zinkan Ra<PERSON> mengakses kamera di pop-up browser", "ledgerPermissionTip": "<PERSON><PERSON><PERSON> k<PERSON> \"Allow\" di bawah dan berikan akses ke Ledger Anda di jendela pop-up berik<PERSON>nya.", "allow": "Izinkan", "nowYouCanReInitiateYourTransaction": "<PERSON><PERSON><PERSON>a dapat memulai kembali transaksi <PERSON>a."}, "imkey": {"title": "Sambungkan imKey", "imkeyPermissionTip": "<PERSON><PERSON><PERSON> klik \"Allow\" di bawah ini dan berikan otorisasi akses ke imKey Anda di jendela pop-up berik<PERSON>nya."}, "keystone": {"title": "Sambungkan Keystone", "unknowError": "<PERSON><PERSON><PERSON> tidak di<PERSON>, silakan coba lagi", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> untuk:", "exportAddressJustAllowedOnHomePage": "Ekspor alamat hanya diperbolehkan di halaman utama", "deviceRejectedExportAddress": "Set<PERSON><PERSON>i koneksi ke Rabby", "keystonePermission1": "Sambungkan ke perangkat USB", "deviceIsBusy": "Perangkat sedang sibuk", "deviceIsLockedError": "<PERSON><PERSON><PERSON>n kata sandi untuk membuka kunci", "keystonePermissionTip": "<PERSON><PERSON><PERSON> k<PERSON> \"Allow\" di bawah untuk memberikan izin akses ke Keystone Anda di jendela pop-up be<PERSON><PERSON>, dan pastikan Keystone 3 Pro Anda ada di halaman utama.", "noDeviceFoundError": "Colokkan satu Keystone"}, "walletConnect": {"status": {"brandError": "Aplikasi dompet salah.", "accountError": "<PERSON><PERSON>t tidak cocok.", "received": "Pemindaian berhasil. Menunggu untuk dikonfirmasi.", "brandErrorDesc": "<PERSON><PERSON><PERSON> gunakan {{brandName}} untuk terhubung", "rejected": "Koneksi <PERSON>. Silakan pindai kode QR untuk mencoba lagi.", "default": "Pindai dengan {{brand}}", "duplicate": "<PERSON><PERSON><PERSON> yang <PERSON>a coba impor adalah duplikat.", "accountErrorDesc": "<PERSON>lakan ganti alamat di dompet seluler Anda", "connected": "Terkoneksi"}, "accountError": {}, "tip": {"accountError": {"tip1": "Tersambung tetapi tidak dapat menandatangani.", "tip2": "<PERSON><PERSON>an beralih ke alamat yang benar di dompet seluler."}, "disconnected": {"tip": "Tidak terhubung ke {{brandName}}"}, "connected": {"tip": "Terhubung ke {{brandName}}"}}, "button": {"disconnect": "Putusk<PERSON> sambungan", "connect": "Sambungkan", "howToSwitch": "Bagaimana cara beralih"}, "connectedSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> dengan sukses", "url": "URL", "qrCode": "QR kode", "changeBridgeServer": "Ubah server jem<PERSON>an", "connectYour": "Sambungkan kunci Anda", "viaWalletConnect": "melalui Wallet Connect", "qrCodeError": "<PERSON><PERSON>an periksa jaringan Anda atau segarkan kode QR", "disconnected": "<PERSON><PERSON><PERSON><PERSON>", "title": "Hu<PERSON><PERSON><PERSON> dengan {{brandName}}"}, "hd": {"tooltip": {"removed": "<PERSON><PERSON><PERSON>", "disconnected": "Tidak dapat terhubung ke Hardware wallet. Silakan coba sambungkan kembali.", "connectError": "Koneksi telah dihentikan. <PERSON><PERSON>an segarkan halaman untuk terhubung kembali.", "added": "<PERSON><PERSON><PERSON> ke Rabby"}, "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> <PERSON> resmi Ledger. Pada 3 alamat pertama, terdapat alamat yang digunakan di on-chain.", "legacy": "Legacy: Jalur HD yang digunakan oleh MEW / Mycrypto. Dalam 3 alamat pertama, terdapat alamat yang digunakan di on-chain.", "bip44": "BIP44 Standar: HDpath yang ditentukan oleh protokol BIP44. Di 3 alamat pertama, ada alamat yang digunakan di chain."}, "hdPathTypeNoChain": {"legacy": "Legacy: HD path yang digunakan oleh MEW / Mycrypto. Pada 3 alamat pertama, tidak ada alamat yang digunakan di on-chain.", "bip44": "BIP44 Standar: Jalur HD yang ditentukan oleh protokol BIP44. Pada 3 alamat pertama, tidak ada alamat yang digunakan di on-chain.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> <PERSON> resmi Ledger. Pada 3 alamat pertama, tidak ada alamat yang digunakan di on-chain."}}, "trezor": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> <PERSON> resmi <PERSON>.", "legacy": "Legacy: HD path yang digunakan oleh MEW / Mycrypto.", "bip44": "BIP44: HDpath yang ditentukan oleh protokol BIP44."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> <PERSON> resmi <PERSON>.", "bip44": "BIP44: <PERSON><PERSON> didefini<PERSON>an oleh protokol BIP44.", "legacy": "Legacy: <PERSON><PERSON>ur <PERSON> yang digunakan oleh MEW / Mycrypto."}, "message": {"disconnected": "{{0}}Connect telah berhenti. <PERSON>lakan segarkan halaman untuk terhubung lagi."}}, "onekey": {"hdPathType": {"bip44": "BIP44: HDpath ditentukan oleh protokol BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDpath yang ditentukan oleh protokol BIP44."}}, "mnemonic": {"hdPathType": {"ledgerLive": "Ledger Live: Ledger official HD path.", "legacy": "Legacy: <PERSON><PERSON>ur <PERSON> yang digunakan oleh MEW / Mycrypto.", "default": "Default: <PERSON><PERSON><PERSON>ult untuk mengimpor frasa seed digunakan.", "bip44": "Standar BIP44: HDpath yang ditentukan oleh protokol BIP44."}, "hdPathTypeNoChain": {"default": "Default: <PERSON><PERSON><PERSON>ult untuk mengimpor frase seed digunakan."}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> <PERSON> resmi Ledger. Di 3 alamat pertama, terdapat alamat yang digunakan di on-chain.", "bip44": "BIP44 Standard: HDpath yang ditentukan oleh protokol BIP44. Di 3 alamat pertama, terdapat alamat yang digunakan di on-chain.", "legacy": "Legacy: Jalur HD yang digunakan oleh MEW / Mycrypto. Pada 3 alamat pertama, terdapat alamat yang digunakan di on-chain."}, "hdPathTypeNochain": {"legacy": "Legacy: Jalur HD yang digunakan oleh MEW / Mycrypto. Pada 3 alamat pertama, tidak ada alamat yang digunakan di on-chain.", "bip44": "Standar BIP44: Jalur HD yang ditentukan oleh protokol BIP44. Pada 3 alamat pertama, tidak ada alamat yang digunakan di on-chain.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> <PERSON> resmi Ledger. Pada 3 alamat pertama, tidak ada alamat yang digunakan di on-chain."}, "switch": {"title": "<PERSON><PERSON><PERSON> ke perangkat GridPlus yang baru", "content": "Tidak mendukung untuk mengimpor beberapa perangkat GridPlus. Jika Anda beralih ke perangkat GridPlus yang baru, daftar alamat perangkat saat ini akan dihapus sebelum memulai proses impor."}, "switchToAnotherGridplus": "Beralih ke GridPlus lain"}, "keystone": {"hdPathType": {"bip44": "BIP44: HDpath ditentukan oleh protokol BIP44.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> resmi Ledger. Anda hanya dapat mengelola 10 alamat dengan jalur Ledger Live.", "legacy": "Legacy: HD path yang digunakan oleh MEW / Mycrypto."}, "hdPathTypeNochain": {"bip44": "BIP44: HDpath ditentukan oleh protokol BIP44.", "legacy": "Legacy: <PERSON><PERSON>ur <PERSON> yang digunakan oleh MEW / Mycrypto.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> resmi Ledger. Anda hanya dapat mengelola 10 alamat dengan jalur Ledger Live."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: HDpath yang ditentukan oleh protokol BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDpath yang ditentukan oleh protokol BIP44."}, "disconnected": "Tidak dapat terhubung ke BitBox02. <PERSON><PERSON><PERSON> muat ulang halaman untuk mencoba terhubung lagi. Alasan: {{0}}"}, "qrCode": {"switch": {"title": "<PERSON><PERSON><PERSON> ke perangkat {{0}} yang baru", "content": "Tidak mendukung untuk mengimpor beberapa perangkat {{0}}. <PERSON>ka Anda beralih ke perangkat {{0}} yang baru, daftar alamat perangkat saat ini akan dihapus sebelum memulai proses impor."}, "switchAnother": "<PERSON><PERSON><PERSON> ke {{0}} lain"}, "basicInformation": "Informasi dasar", "loadingAddress": "Memuat {{0}}/{{1}} alamat", "notes": "Catatan", "getOnChainInformation": "Dapatkan informasi on-chain", "clickToGetInfo": "Klik untuk mendapatkan informasi di on-chain", "firstTransactionTime": "<PERSON><PERSON><PERSON> trans<PERSON>i pertama", "addToRabby": "Tambahkan ke Rabby", "balance": "<PERSON><PERSON>", "waiting": "<PERSON><PERSON><PERSON>", "addresses": "<PERSON><PERSON><PERSON>", "hideOnChainInformation": "Sembunyikan informasi on-chain", "usedChains": "Used chains", "manageKeystone": "<PERSON><PERSON>la <PERSON>tone", "addressesIn": "<PERSON><PERSON><PERSON> di {{0}}", "manageImKey": "<PERSON><PERSON><PERSON>", "manageBitbox02": "Kelola BitBox02", "manageAirgap": "Kelola AirGap", "connectedToLedger": "Terhubung ke Ledger", "manageNgraveZero": "Kelola NGRAVE ZERO", "connectedToOnekey": "Terhubung ke OneKey", "advancedSettings": "Pengaturan Lanju<PERSON>", "manageSeedPhrase": "<PERSON><PERSON><PERSON>", "manageGridplus": "<PERSON><PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "manageCoolwallet": "<PERSON><PERSON><PERSON>", "connectedToTrezor": "Terhubung ke Trezor", "customAddressHdPath": "<PERSON><PERSON>ur <PERSON>", "selectIndexTip": "Pilih nomor seri alamat untuk memulai dari:", "manageAddressFrom": "<PERSON><PERSON><PERSON> al<PERSON> da<PERSON> {{0}} ke {{1}}", "addressesInRabby": "<PERSON><PERSON><PERSON>{{0}}", "manageImtokenOffline": "<PERSON><PERSON><PERSON> im<PERSON>oken", "selectHdPath": "<PERSON><PERSON><PERSON> jalur HD:", "importBtn": "Impor ({{count}})"}, "keystore": {"password": {"required": "<PERSON><PERSON><PERSON> ma<PERSON>", "placeholder": "<PERSON><PERSON>"}, "description": "Pilih file keystore yang ingin Anda impor dan masukkan kata sandi yang sesuai."}, "coboSafe": {"addCoboArgusAddress": "Tambahkan alamat Cobo Argus", "whichChainIsYourCoboAddressOn": "Co<PERSON> address Anda ada di chain mana?", "invalidAddress": "<PERSON><PERSON><PERSON> tida<PERSON> valid", "inputSafeModuleAddress": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> Module", "findTheAssociatedSafeAddress": "<PERSON><PERSON><PERSON> alamat aman yang terkait", "import": "Impor"}, "importPrivateKey": "I<PERSON>r <PERSON>", "firefoxLedgerDisableTips": "Ledger tidak kompatibel dengan Firefox", "connectInstitutionalWallets": "Sambungkan Dompet Institusi", "createNewSeedPhrase": "Buat Frasa Benih Baru", "selectImportMethod": "<PERSON><PERSON><PERSON>", "connectHardwareWallets": "Sambungkan Dompet Perangkat Keras", "connectMobileWalletApps": "Sambungkan Aplikasi Dompet Seluler", "theSeedPhraseIsInvalidPleaseCheck": "Frasa biji tidak valid, silakan perik<PERSON>!", "importKeystore": "Impor KeyStore", "title": "Tambah<PERSON>", "importMyMetamaskAccount": "<PERSON><PERSON><PERSON>", "importSeedPhrase": "<PERSON><PERSON><PERSON> Seed Phrase", "importedSuccessfully": "Diimpor Berhasil", "incorrectPassword": "kata sandi salah", "addFromCurrentSeedPhrase": "Tambahkan dari Seed Phrase <PERSON>", "importYourKeystore": "Impor KeyStore Anda"}, "unlock": {"btn": {"unlock": "<PERSON><PERSON>"}, "password": {"required": "Ma<PERSON><PERSON><PERSON> Kat<PERSON> Sand<PERSON> untuk Membuka Kunc<PERSON>", "placeholder": "Ma<PERSON><PERSON><PERSON> Kat<PERSON> Sand<PERSON> untuk Membuka Kunc<PERSON>", "error": "kata sandi tidak benar"}, "btnForgotPassword": "Lupa Kata Sandi?", "description": "Dompet yang mengubah permainan untuk Ethereum dan semua rantai EVM", "title": "<PERSON><PERSON>"}, "addToken": {"balance": "<PERSON><PERSON>", "tokenNotFound": "Token tidak ditemukan dari alamat kontrak ini", "tokenSupported": "Token telah didukung di <PERSON>", "title": "Tambahkan token khusus ke Rabby", "tokenOnMultiChains": "<PERSON><PERSON><PERSON> token di berbagai rantai. <PERSON><PERSON>an pilih satu.", "tokenCustomized": "Token saat ini telah ditambahkan ke kustomisasi.", "noTokenFoundOnThisChain": "Tidak ada token yang ditemukan di rantai ini", "hasAdded": "Anda telah ditambahkan token ini.", "noTokenFound": "Tidak ada token yang di<PERSON>ukan"}, "switchChain": {"title": "Tambahkan Jaringan <PERSON> ke Rabby", "requestsReceived": "1 permintaan diterima", "requestRabbyToSupport": "Minta Rabby untuk <PERSON>", "chainId": "Chain ID:", "addChain": "Tambah Testnet", "chainNotSupport": "Rabby belum mendukung rantai yang diminta.", "requestsReceivedPlural": "{{count}} permin<PERSON>an diterima", "unknownChain": "Rantai tidak dikenal", "chainNotSupportYet": "Rabby belum mendukung rantai yang diminta.", "desc": "<PERSON><PERSON><PERSON> yang diminta belum terintegrasi o<PERSON>h <PERSON>. <PERSON>a dapat menambahkannya sebagai jaringan kustom secara manual.", "chainNotSupportAddChain": "<PERSON><PERSON>i yang diminta belum diintegrasikan o<PERSON>h <PERSON>. <PERSON>a dapat menambahkannya sebagai Testnet Kustom.", "testnetTip": "<PERSON><PERSON><PERSON> aktif<PERSON> \"Enable Testnets\" di bawah \"More\" sebelum terhubung ke testnets."}, "signText": {"createKey": {"interactDapp": "Interaksi <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "message": "Pesan Teks", "sameSafeMessageAlert": "<PERSON><PERSON> yang sama telah dikonfirmasi; tidak ada tanda tangan tambahan yang diperlukan.", "title": "Tanda Tangan Teks"}, "securityEngine": {"yes": "Ya", "unknownResult": "<PERSON><PERSON> tidak diketahui karena mesin keamanan tidak tersedia saat ini", "no": "Tidak", "viewRules": "<PERSON><PERSON> aturan k<PERSON>n", "undo": "Mundur", "ignoreAlert": "<PERSON><PERSON><PERSON><PERSON>", "riskProcessed": "<PERSON>inga<PERSON> risiko telah di<PERSON>n", "ruleDisabled": "<PERSON><PERSON>n keamanan telah dinonaktif<PERSON>. <PERSON><PERSON> k<PERSON>, <PERSON><PERSON> dapat mengaktif<PERSON> kapan saja.", "whenTheValueIs": "ketika nilainya adalah {{value}}", "currentValueIs": "<PERSON>lai saat ini adalah {{value}}", "alertTriggerReason": "Alasan pemicu peringatan:", "ruleDetailTitle": "Detail <PERSON>", "forbiddenCantIgnore": "Ditemukan risiko terlarang yang tidak dapat diabaikan.", "viewRiskLevel": "<PERSON><PERSON> tingkat risiko", "enableRule": "Aktifkan aturan", "understandRisk": "<PERSON><PERSON> men<PERSON>ti dan menerima tanggung jawab atas segala kerugian."}, "connect": {"SignTestnetPermission": {"title": "Menandatangani izin"}, "SelectWallet": {"title": "Pilih Dompet untuk Terhubung", "desc": "<PERSON><PERSON>h dari dompet yang telah Anda instal"}, "markAsBlockToast": "Tandai sebagai \"Blocked\"", "markRuleText": "<PERSON>da saya", "onYourBlacklist": "<PERSON><PERSON> daftar hitam <PERSON>a", "trusted": "Dipercaya", "listedBy": "<PERSON><PERSON><PERSON> oleh", "flagByRabby": "Ditan<PERSON>", "myMark": "<PERSON><PERSON>", "selectChainToConnect": "<PERSON><PERSON>h rantai untuk terhubung ke", "flagByMM": "Ditandai oleh MetaMask", "popularLevelHigh": "Tingg<PERSON>", "addedToBlacklist": "Ditambahkan ke daftar hitam Anda", "noWebsite": "Tidak ada", "flagByScamSniffer": "Flagged o<PERSON><PERSON>", "notOnAnyList": "Tidak ada dalam daftar mana pun", "blocked": "Diblokir", "title": "Sambungkan ke Dapp", "popularLevelMedium": "Medium", "sitePopularity": "Popularitas situs", "noMark": "<PERSON><PERSON> tanda", "popularLevelVeryLow": "Sangat Rendah", "popularLevelLow": "Rendah", "addedToWhitelist": "Ditambahkan ke daftar putih Anda", "verifiedByRabby": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "markRemovedToast": "<PERSON><PERSON>", "markAsTrustToast": "Tandai sebagai \"Trusted\"", "foundForbiddenRisk": "Ditemukan risiko terlarang. Koneksi diblokir.", "connectBtn": "Sambungkan", "removedFromAll": "<PERSON><PERSON><PERSON> dari semua daftar", "onYourWhitelist": "Di whitelist <PERSON><PERSON>", "ignoreAll": "<PERSON><PERSON><PERSON><PERSON> semua", "otherWalletBtn": "Sambungkan dengan Dompet Lain", "manageWhiteBlackList": "<PERSON><PERSON><PERSON> whitelist/blacklist", "connectAddress": "Sambung<PERSON>"}, "addressDetail": {"manage-seed-phrase": "<PERSON><PERSON><PERSON>", "backup-seed-phrase": "Cadangan Fr<PERSON>", "admins": "Admins", "edit-memo-title": "Edit catatan alamat", "source": "Sumber", "delete-address": "<PERSON><PERSON>", "assets": "<PERSON><PERSON>", "hd-path": "Jalur HD", "address-detail": "Detail <PERSON>", "safeModuleAddress": "<PERSON><PERSON><PERSON>", "add-to-whitelist": "Tambahkan ke Daftar Putih", "please-input-address-note": "<PERSON>lakan masukkan catatan alamat", "importedDelegatedAddress": "<PERSON>amat delegasi yang diimpor", "address-note": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "qr-code": "Kode QR", "tx-requires": "<PERSON><PERSON><PERSON><PERSON><PERSON> <2>{{num}}</2> kon<PERSON><PERSON><PERSON>", "backup-private-key": "Cadangkan Kunci Pribadi", "remove-from-whitelist": "<PERSON><PERSON>", "direct-delete-desc": "<PERSON><PERSON><PERSON> ini adalah alamat {{renderBrand}}, <PERSON><PERSON> tidak menyimpan kunci pribadi atau frasa seed untuk alamat ini, <PERSON><PERSON> dapat langsung mengh<PERSON>.", "coboSafeErrorModule": "<PERSON><PERSON><PERSON> telah <PERSON>, silakan hapus dan impor alamat lagi.", "manage-addresses-under-this-seed-phrase": "<PERSON><PERSON><PERSON> al<PERSON> di bawah Seed Phrase ini", "delete-desc": "Sebelum <PERSON>, ingat<PERSON> poin-poin berikut untuk memahami cara melindungi aset <PERSON>.", "manage-addresses-under": "<PERSON><PERSON><PERSON> al<PERSON> di bawah {{brand}}"}, "preferMetamaskDapps": {"empty": "Tidak ada dapps", "title": "MetaMask Dapps yang <PERSON>", "howToAdd": "<PERSON><PERSON><PERSON>", "howToAddDesc": "<PERSON><PERSON> kanan pada situs web dan temukan opsi ini", "desc": "Aplikasi dapps berikut akan tetap terhubung melalui <PERSON>, terlepas dari dompet yang <PERSON>a ganti."}, "customRpc": {"EditRPCModal": {"rpcUrl": "RPC URL", "rpcUrlPlaceholder": "Masukkan URL RPC", "invalidRPCUrl": "URL RPC tidak valid", "title": "Ubah URL RPC", "rpcAuthFailed": "RPC o<PERSON><PERSON><PERSON><PERSON> gagal", "invalidChainId": "ID Rantai Tidak Valid"}, "EditCustomTestnetModal": {"title": "Tambahkan Jaring<PERSON>", "quickAdd": "Tambahkan cepat dari Chainlist"}, "add": "Ubah URL RPC", "opened": "<PERSON><PERSON><PERSON>", "title": "Ubah URL RPC", "empty": "Tidak ada URL RPC kustom", "closed": "<PERSON><PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON>, RPC kustom akan menggantikan node <PERSON><PERSON>. Untuk terus menggunakan node <PERSON><PERSON>, hapus RPC kustom."}, "requestDebankTestnetGasToken": {"claimBadgeBtn": "<PERSON><PERSON><PERSON>", "requestBtn": "<PERSON><PERSON><PERSON><PERSON>", "title": "Minta Token Gas DeBank Testnet", "mintedTip": "<PERSON><PERSON><PERSON><PERSON> dapat melakukan permintaan sekali sehari", "notMintedTip": "Permintaan hanya tersedia untuk pemegang Ra<PERSON>", "requested": "Anda telah meminta hari ini", "time": "Per hari"}, "safeQueue": {"action": {"send": "<PERSON><PERSON>", "cancel": "Batalkan Transaksi Tertunda"}, "ReplacePopup": {"options": {"send": "<PERSON><PERSON>", "reject": "<PERSON><PERSON>"}, "title": "<PERSON>lih cara untuk mengganti transaksi ini", "desc": "Transaksi yang sudah ditandatangani tidak dapat dihapus tetapi dapat digantikan dengan transaksi baru dengan nonce yang sama."}, "title": "Antrian ({{total}})", "loading": "Memuat transaksi yang tertunda", "sameNonceWarning": "Transaksi ini bertentangan karena menggunakan nonce yang sama. Menjalankan salah satunya secara otomatis akan menggantikan yang lain.", "submitBtn": "<PERSON><PERSON>", "replaceBtn": "Ganti", "viewBtn": "Lihat", "unknownTx": "Transaksi Tidak Dikenal", "approvalExplain": "Persetujuan {{count}} {{token}} untuk {{protocol}}", "noData": "Tidak ada transaksi tertunda", "unknownProtocol": "Protokol tidak dikenal", "unlimited": "tidak terbatas", "accountSelectTitle": "Anda dapat mengirimkan transaksi ini menggunakan alamat apa pun", "cancelExplain": "Batalkan {{token}} Setujui untuk {{protocol}}", "LowerNonceError": "Transaksi dengan nonce {{nonce}} perlu dieksekusi terlebih dahulu.", "loadingFaild": "Karena ketidakstabilan server Safe, data tidak tersedia, silakan periksa kembali setelah 5 menit."}, "importSuccess": {"gnosisChainDesc": "<PERSON><PERSON><PERSON> ini di<PERSON>ukan dideploy di {{count}} rantai", "addressCount": "{{count}} alamat", "title": "Diimpor dengan Sukses"}, "backupSeedPhrase": {"copySeedPhrase": "Salin seed phrase", "clickToShow": "Klik untuk menampilkan Seed Phrase", "qrCodePopupTitle": "Kode QR", "qrCodePopupTips": "<PERSON>an pernah membagikan kode QR frasa benih kepada orang lain. Silakan lihat di lingkungan yang aman dan jaga dengan baik.", "showQrCode": "Tampilkan Kode QR", "title": "Cadangan Fr<PERSON>", "alert": "Seed Phrase ini adalah kredensial untuk aset Anda. JANGAN hilangkan atau ungkapkan kepada orang lain, jika tidak, <PERSON><PERSON> mungkin kehilangan aset <PERSON>a se<PERSON>. Harap lihat di lingkungan yang aman dan simpan dengan hati-hati."}, "backupPrivateKey": {"title": "Cadangkan Kunci Pribadi", "alert": "Kunci Privat ini adalah kredensial untuk aset Anda. JANGAN kehilangan atau mengungkapkannya kepada orang lain, jika tidak, <PERSON><PERSON> mungkin akan kehilangan aset Anda se<PERSON>. Silakan lihat di lingkungan yang aman dan simpan dengan hati-hati.", "clickToShow": "Klik untuk menampilkan kunci pribadi", "clickToShowQr": "Klik untuk menampilkan QR Code kunci pribadi"}, "ethSign": {"alert": "Menandatangani dengan 'eth_sign' dapat menyebabkan kehilangan aset. <PERSON><PERSON>, <PERSON><PERSON> tidak mendukung metode ini."}, "createPassword": {"passwordRequired": "<PERSON><PERSON><PERSON> ma<PERSON>", "confirmError": "Kata sandi tidak cocok", "passwordPlaceholder": "Kata sandi harus memiliki panjang setidaknya 8 karakter", "confirmRequired": "<PERSON><PERSON><PERSON>", "confirmPlaceholder": "Konfirmasi kata sandi", "passwordMin": "Kata sandi harus terdiri dari setidaknya 8 karakter", "title": "<PERSON><PERSON>", "agree": "<PERSON><PERSON> telah membaca dan setuju dengan <1/> <2><PERSON><PERSON><PERSON><PERSON></2> dan <4><PERSON><PERSON><PERSON><PERSON></4>"}, "welcome": {"step1": {"title": "<PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON> terhubung ke semua <PERSON> yang didukung oleh <PERSON>a<PERSON>"}, "step2": {"title": "<PERSON><PERSON><PERSON>", "btnText": "<PERSON><PERSON>", "desc": "Kunci privat disimpan secara lokal dengan akses eksklusif untuk Anda"}}, "importSafe": {"error": {"required": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t", "invalid": "<PERSON><PERSON><PERSON> tida<PERSON> valid"}, "title": "Tambahkan alamat Safe", "placeholder": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t", "loading": "<PERSON>cari rantai yang diterapkan dari alamat ini", "gnosisChainDesc": "<PERSON><PERSON><PERSON> ini ditemukan diterapkan di {{count}} rantai"}, "importQrBase": {"btnText": "<PERSON><PERSON>", "desc": "Pindai kode QR pada dompet perangkat keras {{brandName}}"}, "pendingDetail": {"Header": {"predictTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> akan dikemas dalam"}, "TxStatus": {"reBroadcastBtn": "Re-broadcast", "pendingBroadcast": "Pending: <PERSON><PERSON><PERSON>n", "pendingBroadcasted": "Pending: Broadcasted", "completed": "Se<PERSON><PERSON>"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcastedCount_ordinal_few": "{{count}}rd siaran", "broadcastedCount_ordinal_other": "{{count}} siaran th", "created": "Transaksi dibuat", "broadcastedCount_ordinal_one": "{{count}}st siaran", "broadcasted": "Baru-baru ini disiarkan", "pending": "Memeriksa status...", "broadcastedCount_ordinal_two": "{{count}}nd siaran"}, "MempoolList": {"col": {"nodeName": "Nama node", "txStatus": "Status Transaksi", "nodeOperator": "Node operator"}, "txStatus": {"appearedOnce": "Muncul sekali", "notFound": "Tidak ditem<PERSON>n", "appeared": "Muncul"}, "title": "Muncul di {{count}} node RPC"}, "PendingTxList": {"filterBaseFee": {"label": "<PERSON><PERSON> memenuhi persyaratan biaya dasar", "tooltip": "<PERSON><PERSON><PERSON><PERSON> hanya transaksi yang harga gasnya memenuhi persyaratan biaya dasar blok"}, "col": {"interact": "Berinteraks<PERSON> den<PERSON>", "balanceChange": "Perubahan saldo", "actionType": "<PERSON><PERSON>", "gasPrice": "<PERSON>rga <PERSON>", "action": "<PERSON><PERSON><PERSON>"}, "titleSameNotFound": "Tidak Ada Peringkat yang Sama dengan Saat Ini", "titleNotFound": "Tidak Ada Peringkat di Semua Tx Tertunda", "titleSame": "GasPrice berada di peringkat #{{rank}} sama dengan saat ini", "title": "GasPrice排在所有待处理交易的第#{{rank}}"}, "Empty": {"noData": "Tidak ada data yang ditemukan"}, "PrePackInfo": {"col": {"expectations": "<PERSON><PERSON>", "prePackContent": "Konten pra-kemas", "prePackResults": "Hasil pre-pack", "difference": "<PERSON><PERSON><PERSON> has<PERSON>"}, "type": {"receive": "Terima", "pay": "Bayar"}, "noError": "Tidak di<PERSON>n k<PERSON>han", "noLoss": "Tidak ada kehilangan yang ditemukan", "title": "Pre-pack Check", "error": "{{count}} k<PERSON><PERSON><PERSON> di<PERSON>n", "loss": "{{lossCount}} kehilangan ditemukan", "desc": "<PERSON><PERSON><PERSON><PERSON> di blok terbaru, diperbarui {{time}}"}, "Predict": {"skipNonce": "<PERSON><PERSON><PERSON> Anda memiliki Nonce yang dilewati di rantai Ethereum, menyebabkan transaksi saat ini tidak dapat diselesaikan.", "predictFailed": "Prediksi waktu pengepakan gagal", "completed": "Transaks<PERSON>"}}, "dappSearch": {"searchResult": {"totalDapps": "Total <2>{{count}}</2> Dapps", "foundDapps": "Di<PERSON><PERSON>n <2>{{count}}</2> <PERSON>pps"}, "selectChain": "<PERSON><PERSON><PERSON>", "expand": "<PERSON><PERSON><PERSON><PERSON>", "emptySearch": "Tidak Ada Dapp Ditemukan", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "emptyFavorite": "Tidak Ada Dapp Favorit", "listBy": "Dapp telah dicantumkan oleh"}, "rabbyPoints": {"claimItem": {"go": "<PERSON><PERSON>", "earnTip": "<PERSON><PERSON> sekali sehari. <PERSON><PERSON><PERSON> kump<PERSON>kan poin setelah 00:00 UTC+0", "disabledTip": "Tidak ada poin yang dapat diklaim sekarang", "claim": "<PERSON><PERSON><PERSON>", "claimed": "<PERSON>"}, "claimModal": {"activeStats": "Status Aktif", "walletBalance": "<PERSON><PERSON>", "rabbyUser": "<PERSON><PERSON>una Aktif", "addressBalance": "<PERSON><PERSON>", "rabbyValuedUserBadge": "<PERSON><PERSON>hor<PERSON>", "claim": "<PERSON><PERSON><PERSON>", "referral-code": "Kode Rujukan", "title": "<PERSON><PERSON><PERSON>", "season2": "Musim 2", "invalid-code": "kode tidak valid", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "placeholder": "<PERSON><PERSON><PERSON>n Kode Rujukan untuk poin tambahan (opsional)", "snapshotTime": "<PERSON><PERSON><PERSON> snapshot: {{time}}", "MetaMaskSwap": "MetaMask Swap", "cantUseOwnCode": "Anda tidak dapat menggunakan kode referral Anda sendiri."}, "referralCode": {"verifyAddressModal": {"sign": "<PERSON>da tangan", "cancel": "Batalkan", "verify-address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "<PERSON><PERSON><PERSON> tanda tangani pesan teks ini untuk memverifikasi bahwa Anda adalah pemilik alamat ini."}, "set-my-code": "At<PERSON> kode saya", "my-referral-code": "<PERSON><PERSON> rujukan saya", "set-my-referral-code": "Atur kode rujukan saya", "refer-a-new-user-to-get-50-points": "Ajak pengguna baru untuk mendapatkan 50 poin", "referral-code-cannot-be-empty": "Kode rujukan tidak boleh kosong", "referral-code-already-exists": "Kode rujukan sudah ada", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "once-set-this-referral-code-is-permanent-and-cannot-change": "<PERSON><PERSON><PERSON> di<PERSON>el, kode rujukan ini bersifat permanen dan tidak dapat diubah.", "max-15-characters-use-numbers-and-letters-only": "Maksimal 15 karakter, hanya gunakan angka dan huruf.", "referral-code-cannot-exceed-15-characters": "Kode referral tidak boleh melebihi 15 karakter", "referral-code-available": "Kode rujukan tersedia"}, "top-100": "Top 100", "referral-code-copied": "Kode referral telah disalin", "title": "<PERSON><PERSON>", "earn-points": "Dapatkan Poin", "share-on": "Bagikan di", "out-of-x-current-total-points": "Dari {{total}} Total Poin yang Diberikan", "initialPointsClaimEnded": "<PERSON><PERSON><PERSON> telah be<PERSON>", "code-set-successfully": "<PERSON>de rujukan ber<PERSON>il diatur", "secondRoundEnded": "🎉 Putaran kedua Rabby Points telah berakhir", "firstRoundEnded": "🎉 Putaran pertama Rabby Points telah berakhir"}, "customTestnet": {"CustomTestnetForm": {"rpcUrl": "RPC URL", "id": "Chain ID", "rpcUrlRequired": "<PERSON><PERSON>an masukkan URL RPC", "blockExplorerUrl": "URL penjelajah blok (Opsional)", "idRequired": "Silakan masukkan chain id", "nativeTokenSymbol": "Simbol mata uang", "name": "<PERSON><PERSON>", "nameRequired": "<PERSON><PERSON><PERSON> masukkan nama jaringan", "nativeTokenSymbolRequired": "<PERSON>lakan masukkan simbol mata uang"}, "AddFromChainList": {"tips": {"added": "Anda sudah menambahkan rantai ini", "supported": "Chain sudah diintegrasikan oleh Rabby Wallet"}, "title": "Tambahkan cepat dari Chainlist", "search": "Cari nama atau ID jaringan kustom", "empty": "Tidak ada rantai yang di<PERSON>ukan"}, "signTx": {"title": "Data Transaksi"}, "ConfirmModifyRpcModal": {"desc": "Rantai sudah terintegrasi o<PERSON>h <PERSON>. Apakah Anda perlu mengubah URL RPC-nya?"}, "id": "ID", "empty": "Tidak Ada Jaring<PERSON>", "title": "<PERSON><PERSON><PERSON>", "add": "Tambahkan Jaring<PERSON>", "currency": "<PERSON>", "desc": "<PERSON><PERSON> tidak dapat memverifikasi keamanan jaringan kustom. Harap tambahkan hanya jaringan yang dipercaya."}, "addChain": {"title": "Tambahkan Jaringan <PERSON> ke Rabby", "desc": "<PERSON><PERSON> tidak dapat memverifikasi keamanan jaringan kustom. Harap tambahkan hanya jaringan yang tepercaya."}, "sign": {"transactionSpeed": "Kecepatan Transaksi"}, "ecology": {"sonic": {"home": {"airdrop": "Airdrop", "socialsTitle": "Terlibat", "migrateBtn": "<PERSON><PERSON><PERSON> hadir", "earnTitle": "Dapatkan", "arcadeBtn": "Main sekarang", "airdropBtn": "Dapatkan poin", "airdropDesc": "~200 juta S kepada pengguna di Opera dan Sonic.", "earnBtn": "<PERSON><PERSON><PERSON> hadir", "arcadeDesc": "Mainkan permainan gratis untuk mendapatkan poin untuk airdrop S.", "migrateTitle": "<PERSON><PERSON><PERSON>", "migrateDesc": "请提供需要翻译的英文文案。", "earnDesc": "Taruh $S Anda"}, "points": {"pointsDashboardBtn": "<PERSON><PERSON> menda<PERSON> poin", "shareOn": "Bagikan di", "sonicPoints": "Sonic Points", "pointsDashboard": "<PERSON><PERSON>", "sonicArcadeBtn": "<PERSON><PERSON>", "errorTitle": "Tidak dapat memuat poin", "today": "<PERSON> ini", "getReferralCode": "Dapatkan kode referensi", "retry": "Coba lagi", "referralCodeCopied": "Kode rujukan disalin", "sonicArcade": "Sonic Arcade", "errorDesc": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memuat poin Anda. <PERSON>lakan coba lagi.", "referralCode": "Kode rujukan"}}, "dbk": {"home": {"mintNFTBtn": "Mint", "bridgeBtn": "Jembatan", "bridgePoweredBy": "Ditenagai oleh OP Superchain", "mintNFT": "Mint DBK Genesis NFT", "bridge": "Jembatan ke DBK Chain", "mintNFTDesc": "<PERSON><PERSON>lah saksi dari DBK Chain"}, "bridge": {"tabs": {"withdraw": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON>"}, "info": {"completeTime": "<PERSON><PERSON><PERSON>", "toAddress": "Untuk mengatasi", "gasFee": "Biaya Gas", "receiveOn": "Terima di {{chainName}}"}, "error": {"notEnoughBalance": "Saldo tidak cukup"}, "ActivityPopup": {"status": {"withdraw": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON>", "readyToProve": "Siap untuk membuktikan", "claimed": "D声。", "challengePeriod": "<PERSON>e tantangan", "readyToClaim": "Siap untuk mengklaim", "waitingToProve": "State root dipublikasikan", "rootPublished": "State root diterbitkan", "proved": "Dibuktikan"}, "claimBtn": "<PERSON><PERSON><PERSON>", "title": "Kegiatan", "withdraw": "<PERSON><PERSON>", "proveBtn": "Buktikan", "empty": "Belum ada aktivitas", "deposit": "<PERSON><PERSON>"}, "WithdrawConfirmPopup": {"question3": "<PERSON><PERSON> men<PERSON>ti bahwa biaya jaringan bersifat perkiraan dan akan berubah.", "tips": "Penarikan melibatkan proses 3 lang<PERSON><PERSON>, memerlukan 1 transaksi DBK Chain dan 2 transaksi Ethereum.", "step1": "Inisiasi penarikan", "question2": "<PERSON><PERSON> men<PERSON> bahwa sekali penarikan dimulai, itu tidak dapat dipercepat atau dibatalkan.", "question1": "<PERSON>a men<PERSON> bahwa akan memakan waktu sekitar ~7 hari hingga dana saya dapat diclaim di Ethereum setelah saya membuktikan penarikan saya.", "title": "Penarikan DBK Chain memakan waktu ~7 hari", "step2": "Buktikan di Ethereum", "btn": "<PERSON><PERSON>", "step3": "Klaim di Ethereum"}, "labelTo": "<PERSON>", "labelFrom": "<PERSON><PERSON>"}, "minNFT": {"mintBtn": "Mint", "myBalance": "<PERSON><PERSON>", "title": "DBK Genesis", "minted": "Dibuat"}}}, "miniSignFooterBar": {"status": {"txCreated": "Transaksi dibuat", "txSigned": "Ditandatangani. Membuat transaksi", "txSending": "Mengirim permintaan tanda tangan", "txSendings": "Mengirim permintaan tanda tangan ({{current}}/{{total}})"}, "signWithLedger": "Tandatangani dengan Ledger"}, "gasAccount": {"history": {"noHistory": "Tidak ada riwayat"}, "loginInTip": {"gotIt": "Dapatkan itu", "login": "<PERSON><PERSON><PERSON>unt", "desc": "Bayar Biaya Gas di Semua Rantai", "title": "Setor USDC / USDT"}, "loginConfirmModal": {"title": "<PERSON><PERSON><PERSON> alamat untuk masuk", "desc": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> hanya dapat menyetornya di alamat ini"}, "gasAccountList": {"address": "<PERSON><PERSON><PERSON>", "gasAccountBalance": "Gas Balance"}, "logoutConfirmModal": {"logout": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> dari GasAccount saat ini", "desc": "<PERSON><PERSON>ar dari akun akan menonaktifkan GasAccount. Anda dapat memulihkan GasAccount Anda dengan masuk menggunakan alamat ini."}, "depositPopup": {"title": "<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "invalidAmount": "Harus di bawah 500", "desc": "Setor dana ke Akun DeBank L2 Rabby tanpa biaya tambahan—tarik kapan saja.", "selectToken": "<PERSON><PERSON><PERSON> untuk Setoran", "token": "Token"}, "withdrawPopup": {"destinationChain": "<PERSON><PERSON><PERSON> tu<PERSON>", "amount": "<PERSON><PERSON><PERSON>", "selectChain": "<PERSON><PERSON><PERSON>", "noEnoughGas": "<PERSON><PERSON><PERSON> terlalu rendah untuk menutupi biaya gas", "selectRecipientAddress": "<PERSON><PERSON><PERSON>", "to": "Untuk", "recipientAddress": "<PERSON><PERSON><PERSON>", "withdrawalLimit": "<PERSON>as <PERSON>", "noEligibleAddr": "Tidak ada alamat yang memenuhi syarat untuk penarikan", "title": "<PERSON><PERSON>", "deductGasFees": "<PERSON><PERSON><PERSON> yang diterima akan dikurangi biaya gas", "riskMessageFromAddress": "<PERSON><PERSON> pengen<PERSON> r<PERSON><PERSON>, batas penarikan tergantung pada total jumlah yang telah disetor alamat ini.", "selectAddr": "<PERSON><PERSON><PERSON>", "noEligibleChain": "Tidak ada rantai yang memenuhi syarat untuk penarikan", "selectDestinationChain": "<PERSON><PERSON><PERSON> rantai tujuan", "noEnoughValuetBalance": "<PERSON><PERSON> tidak cukup. <PERSON><PERSON><PERSON> rantai atau coba lagi nanti.", "desc": "Anda dapat menarik saldo GasAccount Anda ke Dompet DeBank L2 Anda. Masuk ke Dompet DeBank L2 Anda untuk mentransfer dana ke blockchain yang didukung sesuai kebutuhan.", "riskMessageFromChain": "<PERSON><PERSON> pengen<PERSON> r<PERSON><PERSON>, batas penarikan tergantung pada total jumlah yang disetor dari rantai ini."}, "withdrawConfirmModal": {"title": "Dipindahkan ke Dompet DeBank L2 Anda", "button": "<PERSON><PERSON>Bank"}, "GasAccountDepositTipPopup": {"gotIt": "Dapatkan", "title": "<PERSON><PERSON> GasAccount dan <PERSON>"}, "switchLoginAddressBeforeDeposit": {"title": "Ganti alamat sebelum setoran", "desc": "<PERSON><PERSON>an beralih ke alamat login Anda."}, "withdraw": "<PERSON><PERSON>", "safeAddressDepositTips": "<PERSON>amat multisig tidak didukung untuk setoran.", "title": "GasAccount", "noBalance": "Tidak ada saldo", "logout": "<PERSON><PERSON><PERSON> dari GasAccount saat ini", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "gasExceed": "Saldo GasAccount tidak boleh melebihi $1000", "withdrawDisabledIAP": "Penarikan dinonaktifkan karena saldo Anda termasuk dana fiat, yang tidak dapat ditarik. Hubungi dukungan untuk menarik saldo token Anda.", "risk": "<PERSON><PERSON><PERSON>a saat ini terdeteksi be<PERSON>, jadi fitur ini tidak tersedia.", "switchAccount": "<PERSON><PERSON><PERSON>Account"}, "safeMessageQueue": {"loading": "<PERSON><PERSON><PERSON> pesan", "noData": "Tidak ada pesan"}, "newUserImport": {"guide": {"importAddress": "<PERSON>a sudah memiliki alamat", "title": "Selamat datang di <PERSON>", "createNewAddress": "<PERSON><PERSON>t alamat baru", "desc": "Dompet yang mengubah permainan untuk Ethereum dan semua rantai EVM"}, "createNewAddress": {"showSeedPhrase": "<PERSON><PERSON><PERSON><PERSON>", "title": "Sebelum <PERSON>", "tip2": "Frase benih saya hanya disimpan di perangkat saya. <PERSON><PERSON> tidak dapat mengaksesnya.", "desc": "<PERSON>lakan baca dan ingat tips keamanan berikut ini", "tip1": "<PERSON>ka saya kehilangan atau membagikan frase pemulihan saya, saya akan kehilangan akses ke aset saya secara permanen.", "tip3": "<PERSON><PERSON> saya mengh<PERSON>us Rabby tanpa mencadangkan frasa benih saya, itu tidak dapat dipulihkan oleh Rabby"}, "importList": {"title": "<PERSON><PERSON><PERSON>"}, "importPrivateKey": {"title": "I<PERSON>r <PERSON>", "pasteCleared": "Ditempelkan dan clipboard dibersihkan"}, "PasswordCard": {"form": {"password": {"label": "<PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> ma<PERSON>", "min": "Kata sandi harus terdiri dari setidaknya 8 karakter.", "placeholder": "<PERSON><PERSON> (min 8 karakter)"}, "confirmPassword": {"label": "<PERSON>n<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON>", "notMatch": "Kata sandi tidak cocok", "placeholder": "<PERSON>n<PERSON><PERSON><PERSON>"}}, "title": "<PERSON><PERSON>", "agree": "<PERSON><PERSON> <1/> <2><PERSON><PERSON><PERSON><PERSON></2> dan <4><PERSON><PERSON><PERSON><PERSON></4>", "desc": "Ini akan digunakan untuk membuka dompet dan mengenkripsi data."}, "successful": {"start": "<PERSON><PERSON>", "create": "Ber<PERSON>il <PERSON>", "addMoreFrom": "Tambah lebih banyak alamat dari {{name}}", "addMoreAddr": "Tambahkan lebih banyak alamat dari Seed Phrase ini", "import": "Diimpor Berhasil"}, "readyToUse": {"pin": "<PERSON><PERSON>", "guides": {"step1": "Klik ikon ekstensi browser", "step2": "<PERSON><PERSON>"}, "desc": "<PERSON><PERSON><PERSON> dan Pin itu", "title": "<PERSON><PERSON> Rabby Anda <PERSON>dah Siap!", "extensionTip": "<PERSON><PERSON> <1/> dan kem<PERSON><PERSON> <3/>"}, "importSeedPhrase": {"title": "<PERSON><PERSON><PERSON>"}, "importOneKey": {"title": "OneKey", "tip2": "2. <PERSON><PERSON><PERSON> per<PERSON> OneKey <PERSON>a", "connect": "Hubungkan OneKey", "tip3": "3. <PERSON><PERSON> kunci per<PERSON>", "tip1": "1. <PERSON><PERSON> <1>OneKey Bridge<1/>"}, "importTrezor": {"title": "<PERSON><PERSON><PERSON>", "connect": "Sambungkan Trezor", "tip1": "1. <PERSON><PERSON><PERSON>", "tip2": "2. <PERSON><PERSON> kunci per<PERSON>"}, "ImportGridPlus": {"title": "GridPlus", "tip2": "2. Sambungkan melalui Lattice Connector", "connect": "Sambungkan GridPlus", "tip1": "1. <PERSON><PERSON> perangkat GridPlus Anda"}, "importLedger": {"connect": "Sambungkan Ledger", "title": "Ledger", "tip1": "Colokkan perangkat Ledger Anda.", "tip3": "Buka aplikasi Ethereum.", "tip2": "Masukkan PIN Anda untuk membuka kunci."}, "importBitBox02": {"title": "BitBox02", "tip1": "1. <PERSON><PERSON> <1>BitBoxBridge<1/>", "tip3": "3. <PERSON><PERSON>", "connect": "Sambungkan BitBox02", "tip2": "2. Colokkan BitBox02 Anda"}, "importKeystone": {"qrcode": {"desc": "Pindai kode QR pada dompet keras {{brandName}}"}, "usb": {"tip1": "Sambungkan perangkat Keystone Anda", "tip3": "Setujui koneksi ke komputer Anda", "desc": "Pastikan Keystone 3 Pro Anda ada di halaman utama", "connect": "Sambungkan Keystone", "tip2": "<PERSON><PERSON><PERSON><PERSON> kata sandi Anda untuk membuka kunci"}}, "importSafe": {"error": {"invalid": "<PERSON><PERSON><PERSON> tida<PERSON> valid", "required": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t"}, "placeholder": "<PERSON><PERSON><PERSON><PERSON> alamat aman", "title": "<PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON>i rantai yang dideploy dari alamat ini"}}, "metamaskModeDapps": {"title": "<PERSON><PERSON><PERSON> ya<PERSON>", "desc": "Mode MetaMask diaktifkan untuk Dapps berikut. Anda dapat menghubungkan Rabby dengan memilih opsi MetaMask."}, "forgotPassword": {"home": {"buttonNoData": "<PERSON><PERSON>", "button": "<PERSON><PERSON>", "title": "Lupa Kata Sandi", "description": "Rabby Wallet tidak menyimpan kata sandi Anda dan tidak dapat membantu Anda untuk mengambilnya. Reset dompet Anda untuk mengatur yang baru.", "descriptionNoData": "<PERSON><PERSON>et tidak menyimpan kata sandi Anda dan tidak dapat membantu Anda men<PERSON>ann<PERSON>. Atur kata sandi baru jika Anda lupa."}, "reset": {"alert": {"title": "Data akan dihapus dan tidak dapat dipulihkan:", "privateKey": "<PERSON><PERSON><PERSON>", "seed": "Frasa Seed"}, "tip": {"whitelist": "<PERSON><PERSON><PERSON><PERSON>", "title": "Data akan disimpan:", "records": "<PERSON><PERSON><PERSON>", "hardware": "<PERSON><PERSON> Keras Impor", "safe": "Imported Safe Wallets", "watch": "Kontak dan <PERSON><PERSON><PERSON>"}, "button": "Konfirmasi Atur Ulang", "title": "Atur Ulang Dompet Rabby", "confirm": "Ketik <1>RESET</1> di kotak untuk mengonfirmasi dan melanjutkan"}, "tip": {"buttonNoData": "Tambah<PERSON>", "title": "Reset Dompet Ra<PERSON>", "button": "<PERSON><PERSON>", "description": "Buat kata sandi baru untuk melanjutkan", "descriptionNoData": "Tambah<PERSON> alamat <PERSON>a untuk memulai"}, "success": {"description": "Anda sudah siap untuk menggunakan Rabby Wallet", "button": "Se<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}}, "eip7702": {"alert": "EIP-7702 belum didukung."}, "metamaskModeDappsGuide": {"toast": {"enabled": "Disguise diaktifkan. Segarkan Dapp untuk menyambung kembali.", "disabled": "Disguise dinonaktifkan. Segarkan Dapp."}, "step2": "Langkah 2", "title": "Sambungkan Ra<PERSON> dengan <PERSON>ai MetaMask", "noDappFound": "Tidak ada <PERSON> yang di<PERSON>ukan", "step2Desc": "Menyegarkan dan terhubung melalui MetaMask", "manage": "<PERSON><PERSON><PERSON> ya<PERSON>", "alert": "Tidak dapat terhubung ke Dapp karena tidak menampilkan Rabby Wallet sebagai pilihan?", "step1Desc": "Izinkan Rabby menyamar sebagai MetaMask di Dapp saat ini", "step1": "Langkah 1"}, "syncToMobile": {"selectAddress": {"title": "<PERSON><PERSON><PERSON> untuk Disinkronkan"}, "downloadGooglePlay": "Google Play", "steps2": "2. <PERSON><PERSON><PERSON> dengan <PERSON>", "title": "Sinkron<PERSON><PERSON><PERSON> dari Rabby Extension ke Mobile", "clickToShowQr": "Klik untuk Me<PERSON> dan <PERSON> Kode QR", "steps1": "1. <PERSON><PERSON><PERSON>", "downloadAppleStore": "App Store", "selectedLenAddressesForSync_one": "Selected {{len}} address untuk sinkronisasi", "disableSelectAddress": "Sinkronisasi tidak didukung untuk alamat {{type}}", "description": "Data alamat Anda tetap sepen<PERSON> offline, teren<PERSON><PERSON><PERSON>, dan ditransfer dengan aman melalui kode QR.", "disableSelectAddressWithSlip39": "Sink tidak didukung untuk alamat {{type}} dengan slip39", "steps2Description": "*QR code Anda berisi data sensitif. Jaga kerahasiaannya dan jangan pernah membagikannya kepada siapa pun.", "selectedLenAddressesForSync_other": "Di<PERSON><PERSON><PERSON> {{len}} alamat untuk disinkronkan", "disableSelectAddressWithPassphrase": "Sinkronisasi tidak didukung untuk alamat {{type}} dengan frasa sandi"}, "search": {"sectionHeader": {"NFT": "NFT", "Defi": "<PERSON><PERSON><PERSON>", "token": "Token", "AllChains": "<PERSON><PERSON><PERSON>"}, "header": {"placeHolder": "<PERSON><PERSON>", "searchPlaceHolder": "<PERSON><PERSON> / Alamat"}, "tokenItem": {"gasToken": "Gas Token", "verifyDangerTips": "Ini adalah token penipuan", "Issuedby": "Diterbitkan oleh", "FDV": "FDV", "listBy": "Daftar oleh {{name}}", "scamWarningTips": "Ini adalah token berkualitas rendah dan mungkin merupakan penipuan"}, "searchWeb": {"searching": "<PERSON><PERSON>k", "searchTips": "<PERSON>i di web", "noResults": "Tidak Ada Hasil", "title": "<PERSON><PERSON><PERSON>", "noResult": "Tidak ada hasil untuk"}}}, "component": {"AccountSearchInput": {"AddressItem": {"whitelistedAddressTip": "<PERSON><PERSON><PERSON> yang memiliki daftar putih"}, "noMatchAddress": "Tidak ada alamat yang cocok"}, "AccountSelectDrawer": {"btn": {"cancel": "<PERSON><PERSON>", "proceed": "Lanjutkan"}}, "AddressList": {"AddressItem": {"addressTypeTip": "<PERSON><PERSON><PERSON> {{type}}"}}, "AuthenticationModal": {"passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> Sand<PERSON> untuk Mengonfirmasi", "passwordError": "kata sandi salah", "passwordRequired": "<PERSON><PERSON><PERSON> masukkan kata sandi"}, "ConnectStatus": {"ledgerConnected": "Ledger terhu<PERSON>ng", "ledgerNotConnected": "Ledger tidak terhubung", "connect": "Sambungkan", "gridPlusNotConnected": "GridPlus tidak terhubung", "keystoneNotConnected": "Keystone tidak terhubung", "imKeyConnected": "<PERSON><PERSON><PERSON> te<PERSON>", "imKeyrNotConnected": "im<PERSON>ey tidak terhu<PERSON>ng", "keystoneConnected": "Keystone terhubung", "gridPlusConnected": "GridPlus terhubung", "connecting": "Menghubungkan..."}, "Contact": {"AddressItem": {"whitelistedTip": "<PERSON><PERSON><PERSON> yang te<PERSON>ar", "notWhitelisted": "<PERSON><PERSON><PERSON> ini tidak terdaftar dalam daftar putih"}, "EditModal": {"title": "Edit address note"}, "EditWhitelist": {"title": "Edit Whitelist", "backModalTitle": "<PERSON><PERSON>", "backModalContent": "<PERSON><PERSON><PERSON> yang <PERSON>a buat tidak akan disimpan", "save": "Simpan ke Whitelist ({{count}})", "tip": "<PERSON><PERSON><PERSON> alamat yang ingin <PERSON>a whitelist dan simpan."}, "ListModal": {"authModal": {"title": "<PERSON><PERSON><PERSON> ke Whitelist"}, "whitelistUpdated": "Whitelist <PERSON><PERSON><PERSON><PERSON>", "editWhitelist": "Edit Whitelist", "title": "<PERSON><PERSON><PERSON>", "whitelistDisabled": "Whitelist din<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> dapat mengirim aset ke alamat mana pun.", "whitelistEnabled": "Whitelist te<PERSON> di<PERSON>. <PERSON>a hanya dapat mengirim aset ke alamat yang terdaftar atau Anda dapat menonaktifkannya di \"Pengaturan\"."}}, "LoadingOverlay": {"loadingData": "Memuat data..."}, "MultiSelectAddressList": {"imported": "Diimpor"}, "NFTNumberInput": {"erc1155Tips": "<PERSON><PERSON> adala<PERSON> {{amount}}", "erc721Tips": "Hanya satu NFT dari ERC 721 yang dapat dikirim sekaligus."}, "TiledSelect": {"errMsg": "<PERSON><PERSON><PERSON> frasa benih salah, silakan periksa"}, "Uploader": {"placeholder": "Pilih file JSON"}, "WalletConnectBridgeModal": {"restore": "<PERSON><PERSON><PERSON><PERSON> pengaturan awal", "title": "URL server Jembatan", "requiredMsg": "<PERSON>lakan masukkan host server jem<PERSON><PERSON>", "invalidMsg": "<PERSON><PERSON><PERSON> host <PERSON><PERSON>"}, "PillsSwitch": {"NetSwitchTabs": {"testnet": "<PERSON><PERSON><PERSON>", "mainnet": "<PERSON><PERSON><PERSON>"}}, "ChainSelectorModal": {"searchPlaceholder": "<PERSON><PERSON> rantai", "addTestnet": "Tambah<PERSON>", "noChains": "Tidak ada rantai"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "ASET / JUMLAH"}, "price": {"title": "HARGA"}, "usdValue": {"title": "USD VALUE"}}, "bridge": {"liquidity": "Likuiditas", "token": "Token", "liquidityTips": "Semakin tinggi volume perdagangan historis, semakin besar kemungkinan jembatan akan ber<PERSON>il.", "value": "<PERSON><PERSON>", "low": "Rendah", "high": "Tingg<PERSON>"}, "searchInput": {"placeholder": "<PERSON><PERSON> / <PERSON><PERSON><PERSON>"}, "header": {"title": "<PERSON><PERSON><PERSON>"}, "recent": "Terbaru", "noMatch": "Tidak Cocok", "hot": "Panas", "noTokens": "Tidak Ada Token", "noMatchSuggestion": "Coba untuk mencari alamat kontrak di {{ chainName }}", "common": "<PERSON><PERSON>", "chainNotSupport": "Rantai ini tidak didukung"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "<PERSON><PERSON><PERSON><PERSON>", "Chain": "<PERSON><PERSON><PERSON>", "LastPrice": "<PERSON><PERSON>", "PurschaseDate": "Tanggal Pembelian"}}, "signPermissionCheckModal": {"reconnect": "Sambungkan Dapp", "title": "Anda hanya mengizinkan Dapp ini untuk menandatangani di testnets."}, "testnetCheckModal": {"title": "<PERSON><PERSON><PERSON> akt<PERSON> \"Enable Testnets\" di bawah \"More\" sebelum masuk ke testnets."}, "EcologyNavBar": {"providedBy": "Disediakan oleh {{chainName}}"}, "EcologyNoticeModal": {"title": "Pemberitahuan", "notRemind": "<PERSON><PERSON> ingatkan saya lagi", "desc": "Layanan berikut akan disediakan secara langsung oleh Mitra Ekosistem pihak ketiga. <PERSON><PERSON> tidak bertanggung jawab atas keamanan layanan ini."}, "ReserveGasPopup": {"fast": "Cepat", "title": "Cadangan Gas", "normal": "Normal", "doNotReserve": "Jangan cadangkan Gas", "instant": "Instan"}, "OpenExternalWebsiteModal": {"button": "Lanjutkan", "title": "<PERSON><PERSON> Meninggalkan Rabby <PERSON>et", "content": "<PERSON>a akan mengunjungi situs web eksternal. <PERSON><PERSON> tidak bertanggung jawab atas konten atau keamanan situs ini."}, "TokenChart": {"price": "<PERSON><PERSON>", "holding": "<PERSON><PERSON><PERSON><PERSON>"}, "externalSwapBrideDappPopup": {"noDapp": "Tidak ada <PERSON> yang tersedia", "viewDappOptions": "<PERSON><PERSON>", "help": "<PERSON><PERSON>an hubungi tim resmi dari rantai ini untuk dukungan.", "noQuotesForChain": "Belum ada kutipan tersedia untuk rantai ini.", "thirdPartyDappToProceed": "<PERSON><PERSON><PERSON> gunakan <PERSON> pihak ketiga untuk melanjutkan", "chainNotSupported": "Tidak didukung di rantai ini", "selectADapp": "<PERSON><PERSON><PERSON>", "bridgeOnDapp": "<PERSON> di <PERSON>", "noDapps": "Tidak ada Dapp yang tersedia di rantai ini", "swapOnDapp": "<PERSON><PERSON>\n"}, "AccountSelectorModal": {"title": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON>"}}, "global": {"appName": "<PERSON><PERSON>", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editButton": "Edit", "Save": "Simpan", "confirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "ok": "Baik", "refresh": "<PERSON><PERSON>", "scamTx": "Scam tx", "next": "Selanjutnya", "proceedButton": "Lanjutkan", "Done": "Se<PERSON><PERSON>", "assets": "aset", "cancelButton": "<PERSON><PERSON>", "unknownNFT": "Tidak Dikenal NFT", "copied": "Di<PERSON>in", "Loading": "Memuat", "Deleted": "<PERSON><PERSON><PERSON>", "appDescription": "Dompet yang mengubah permainan untuk Ethereum dan semua rantai EVM", "addButton": "Tambahkan", "watchModeAddress": "Watch Mode alamat", "gas": "Gas", "Cancel": "Batalkan", "copyAddress": "<PERSON><PERSON>", "closeButton": "<PERSON><PERSON><PERSON>", "Balance": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "back": "Kembali", "nonce": "nonce", "failed": "Gaga<PERSON>", "notSupportTesntnet": "Tidak didukung untuk jaringan kustom", "tryAgain": "<PERSON><PERSON>", "Nonce": "<PERSON><PERSON>", "backButton": "Kembali"}, "background": {"error": {"invalidPrivateKey": "Kunci pribadi tidak valid", "notFoundKeyringByAddress": "Tidak dapat menemukan keyring berda<PERSON><PERSON> alamat", "noCurrentAccount": "Tidak ada akun saat ini", "notFindChain": "Tidak dapat menemukan rantai {{chain}}", "txPushFailed": "Transaksi dorong gagal", "notFoundTxGnosisKeyring": "Tidak ada transaksi yang ditemukan di Gnosis keyring", "invalidChainId": "Invalid chain id", "generateCacheAliasNames": "[GenerateCacheAliasNames]: perlu setidaknya satu alamat", "notFoundGnosisKeyring": "Tidak ada keyring Gnosis ditemukan", "unlock": "anda perlu membuka kunci wallet terlebih dahulu", "addKeyring404": "gagal men<PERSON><PERSON><PERSON> keyring, keyring tidak terdefinisi", "invalidMnemonic": "Frasa benih tidak valid, silakan perik<PERSON>!", "duplicateAccount": "<PERSON><PERSON><PERSON> yang Anda coba impor adalah duplikat", "canNotUnlock": "Tidak dapat membuka kunci tanpa brankas sebelumnya", "invalidJson": "file input tidak valid", "emptyAccount": "akun saat ini kosong", "unknownAbi": "kontrak abi tidak di<PERSON>i", "invalidAddress": "<PERSON><PERSON><PERSON> tida<PERSON> valid"}, "transactionWatcher": {"completed": "Transaksi <PERSON>", "submitted": "Transaksi terkirim", "more": "klik untuk melihat informasi lebih lanjut", "txFailedMoreContent": "{{chain}} #{{nonce}} gagal. <PERSON>lik untuk melihat lebih lanjut.", "failed": "<PERSON><PERSON><PERSON> gagal", "txCompleteMoreContent": "{{chain}} #{{nonce}} se<PERSON>ai. Klik untuk melihat lebih banyak."}, "alias": {"watchAddressKeyring": "Kontak", "simpleKeyring": "<PERSON><PERSON><PERSON>", "HdKeyring": "<PERSON><PERSON>"}}, "constant": {"KEYRING_TYPE_TEXT": {"WatchAddressKeyring": "Kontak", "HdKeyring": "Dibuat oleh Seed Phrase", "SimpleKeyring": "Diimpor oleh Kunci Pribadi"}, "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Mainnet & Testnet", "TESTNET": "<PERSON><PERSON>"}, "IMPORTED_HD_KEYRING": "Diim<PERSON> oleh Seed Phrase", "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "<PERSON><PERSON><PERSON> o<PERSON>h Seed Phrase (Passphrase)"}}