{"global": {"copied": "已复制", "confirm": "确认", "next": "下一步", "back": "后退", "ok": "好的", "refresh": "刷新", "Cancel": "取消", "Clear": "清除", "Confirm": "确认", "Save": "保存", "addButton": "添加", "assets": "资产", "backButton": "后退", "cancelButton": "取消", "closeButton": "关闭", "confirmButton": "确认", "copyAddress": "复制地址", "editButton": "编辑", "failed": "失败", "gas": "矿工费", "proceedButton": "继续", "scamTx": "诈骗交易", "unknownNFT": "未知 NFT", "watchModeAddress": "观察模式地址", "appDescription": "The game-changing wallet for Ethereum and all EVM chains", "appName": "<PERSON><PERSON>", "Deleted": "已删除", "Loading": "加载中", "Balance": "余额", "Done": "完成", "nonce": "nonce", "Nonce": "<PERSON><PERSON>", "tryAgain": "再试一次", "notSupportTesntnet": "不支持自定义网络"}, "page": {"transactions": {"title": "交易记录", "empty": {"desc": "在<1>支持的链</1>上找不到交易", "title": "没有交易"}, "explain": {"approve": "授权 {{amount}} {{symbol}} 给  {{project}} ", "cancel": "取消了待完成的交易", "unknown": "合约交互"}, "modalViewMessage": {"title": "查看留言"}, "txHistory": {"tipInputData": "这笔交易有留言", "parseInputDataError": "解析留言失败", "scamToolTip": "这笔交易是由骗子发起的，用于发送欺诈代币和NFT。请避免与其交互。"}, "filterScam": {"title": "隐藏欺诈交易", "loading": "数据加载需要一定时间，且可能会有延迟", "btn": "隐藏欺诈交易"}}, "dashboard": {"feedback": {"directMessage": {"content": "发送私信", "description": "在 DeBank 上与 Rabby Wallet 官方1v1联系"}, "proposal": {"content": "发起提案", "description": "在 DeBank 上发布对 Rabby Wallet 的提案"}, "title": "反馈"}, "nft": {"empty": "在支持的系列中找不到 NFT", "collectionList": {"collections": {"label": "系列"}, "all_nfts": {"label": "所有 NFT"}}, "listEmpty": "你还没有获得任何 NFT", "modal": {"collection": "系列", "chain": "链", "purchaseDate": "购买日期", "lastPrice": "最后价格", "sendTooltip": "目前仅支持 ERC 721 和 ERC 1155 NFT", "send": "发送"}}, "home": {"offline": "网络已断开，无法获取数据", "panel": {"swap": "兑换", "send": "发送", "receive": "接收", "gasTopUp": "充值 Gas", "queue": "Queue", "transactions": "交易记录", "approvals": "授权", "feedback": "反馈", "more": "更多", "manageAddress": "管理地址", "nft": "NFT", "rabbyPoints": "<PERSON><PERSON>", "ecology": "生态系统", "bridge": "Bridge", "mobile": "Mobile Sync"}, "comingSoon": "即将推出", "soon": "很快", "flip": "切换", "metamaskIsInUseAndRabbyIsBanned": "正在使用 MetaMask，禁用 Rabby", "rabbyIsInUseAndMetamaskIsBanned": "正在使用 Ra<PERSON>，禁用 Metamask", "refreshTheWebPageToTakeEffect": "刷新网页即可生效", "transactionNeedsToSign": "笔交易需要签名", "transactionsNeedToSign": "笔交易需要签名", "view": "查看", "viewFirstOne": "查看第一个", "rejectAll": "全部拒绝", "pendingCount": "1 待完成", "pendingCountPlural": "{{countStr}} 待完成", "queue": {"title": "Queue", "count": "{{count}} in"}, "whatsNew": "更新内容", "importType": "由 {{type}} 导入", "missingDataTooltip": "由于 {{text}}的当前网络问题，地址余额可能无法更新。", "chain": " 链、", "chainEnd": " 链"}, "recentConnection": {"disconnected": "已断开连接", "rpcUnavailable": "自定义RPC不可用", "metamaskTooltip": "你选择在该 Dapp 优先使用 MetaMask。\n你可在 “更多” > “优先使用MetaMask连接的Dapp” 中更新此设置", "notConnected": "未连接", "connected": "已连接", "connectedDapp": "Rabby 未连接到当前 Dapp。请在 Dapp 网页上单击连接按钮", "noDappFound": "未找到 Dapp", "disconnectAll": "全部断开", "disconnectRecentlyUsed": {"title": "断开最近使用的 <strong>{{count}}</strong> 个Dapp", "description": "置顶的 DApp 将保持连接", "title_one": "断开<strong>{{count}}</strong>个已连接的 Dapp", "title_other": "断开<strong>{{count}}</strong>个已连接的 Dapps"}, "title": "已连接的 Dapp", "pinned": "已置顶", "noPinnedDapps": "没有置顶的 dapp", "dragToSort": "拖动排序", "noRecentlyConnectedDapps": "没有最近连接的 Dapp", "recentlyConnected": "最近连接", "noConnectedDapps": "没有已连接的 Dapps", "dapps": "<PERSON><PERSON>", "metamaskModeTooltip": "无法在此 Dapp 上连接 Rabby？尝试启用 <1>MetaMask 模式</1>。", "metamaskModeTooltipNew": "Rabby Wallet将在您在Dapp中选择“MetaMask”时连接。您可以在更多 > 通过伪装为MetaMask连接Rabby中进行管理。"}, "contacts": {"noData": "没有数据", "noDataLabel": "没有数据", "oldContactList": "旧联系人列表", "oldContactListDescription": "由于联系人功能和观察地址合并，旧的联系人数据备份于此。后续我们会删除该列表。如果你需要继续使用，请及时添加为联系人"}, "rabbyBadge": {"claim": "领取", "claimSuccess": "领取成功", "enterClaimCode": "输入邀请码", "goToSwap": "完成兑换", "imageLabel": "<PERSON><PERSON>", "learnMoreOnDebank": "跳转至 DeBank 领取", "noCode": "你尚未激活该地址的邀请码", "rabbyValuedUserNo": "<PERSON><PERSON> Valued User No.{{num}}", "swapTip": "你需要先在 Rabby Wallet 中完成一笔与知名dex的兑换", "title": "领取 <PERSON><PERSON>", "viewOnDebank": "在 DeBank 上查看", "viewYourClaimCode": "查看你的邀请码", "freeGasTitle": "领取免费 Gas 徽章", "freeGasTip": "请使用免费 Gas 签署交易。当您的 Gas 不足时，“免费 Gas”按钮将自动出现。", "learnMore": "了解更多", "freeGasNoCode": "请点击下方按钮访问 DeBank，并首先使用您当前的地址获取领取代码。", "rabbyFreeGasUserNo": "<PERSON>bby Free Gas 用户 No.{{num}}"}, "security": {"comingSoon": "更多功能即将推出", "nftApproval": "NFT 授权", "title": "安全", "tokenApproval": "代币授权"}, "settings": {"10Minutes": "10分钟", "1Day": "1天", "1Hour": "1小时", "4Hours": "4小时", "7Days": "7天", "aboutUs": "关于我们", "autoLockTime": "自动锁定时间", "backendServiceUrl": "后端服务网址", "cancel": "取消", "claimRabbyBadge": "领取 <PERSON><PERSON>", "clearPending": "清除本地挂起", "clearPendingTip1": "此操作将从您的界面中移除待处理交易，帮助解决网络上长时间待处理引起的问题。", "clearPendingTip2": "它不会影响您的账户余额，也不需要重新输入您的助记词。所有资产和账户信息仍然安全。", "clearWatchAddressContent": "你确定删除所有观察地址吗？", "clearWatchMode": "清除观察地址", "currentVersion": "当前版本", "disableWhitelist": "关闭转账白名单", "disableWhitelistTip": "关闭后你可以将资产发送到任何地址", "enableWhitelist": "启用转账白名单", "enableWhitelistTip": "启用后，你只能使用 Rabby 向白名单中的地址发送资产", "features": {"connectedDapp": "已连接的 Dapp", "label": "功能", "lockWallet": "锁定钱包", "manageAddress": "管理地址", "signatureRecord": "签名记录", "searchDapps": "搜索 Dapp", "gasTopUp": "Gas Top Up", "rabbyPoints": "<PERSON><PERSON>"}, "followUs": "关注我们", "host": "host", "inputOpenapiHost": "请输入openapi host", "lock": {"never": "永不"}, "pendingTransactionCleared": "待完成交易已清除", "pleaseCheckYourHost": "请检查你的 Host", "reset": "恢复初始设置", "save": "保存", "settings": {"customRpc": "修改 RPC URL", "enableWhitelistForSendingAssets": "启用转账白名单", "label": "设置", "metamaskPreferredDapps": "优先使用MetaMask连接的Dapp", "enableTestnets": "启用测试网", "currentLanguage": "当前语言", "toggleThemeMode": "主题模式", "themeMode": "主题模式", "customTestnet": "添加自定义网络", "metamaskMode": "通过伪装成 MetaMask 连接 Rabby", "enableDappAccount": "独立切换 Dapp 地址\n"}, "testnetBackendServiceUrl": "测试网后端服务 URL", "updateAvailable": "更新版本", "updateVersion": {"content": "检测到 Rabby Wallet 更新版本\n点击查看如何手动更新", "okText": "查看教程", "successTip": "你正在使用最新版本", "title": "有更新可用"}, "warning": "警告", "requestDeBankTestnetGasToken": "获取 DeBank Testnet Gas Token", "claimFreeGasBadge": "领取 Rabby Free Gas 徽章！", "clearPendingWarningTip": "被移除的交易可能仍然会在链上确认，除非它被替换。", "supportedChains": "集成链", "DappAccount": {"title": "独立切换 Dapp 地址\n", "button": "启用\n", "desc": "启用后，您可以独立选择连接到每个 Dapp 的地址。切换您的主地址不会影响连接到每个 Dapp 的地址。\n"}}, "tokenDetail": {"blockedTip": "已隐藏的代币将不会显示在代币列表中", "blocked": "隐藏", "selectedCustom": "Rabby 未支持该代币。\n你已手动添加该代币", "notSelectedCustom": "Rabby 未支持该代币。\n你可以手动添加该代币", "customized": "添加", "scamTx": "诈骗交易", "txFailed": "失败", "notSupported": "不支持该链上的代币", "swap": "兑换", "send": "发送", "receive": "接收", "noTransactions": "没有交易记录", "customizedButton": "个已添加", "blockedButton": "个已隐藏", "customizedButtons": "自定义代币", "blockedButtons": "被阻止的代币", "NoListedBy": "没有可用的列表信息", "OriginalToken": "原始代币", "myBalance": "我的余额", "Chain": "链", "customizedListTitle": "自定义代币", "SupportedExchanges": "支持的交易所", "OriginIssue": "原生发行在这个区块链上", "BridgeProvider": "桥接服务提供商", "customizedListTitles": "自定义代币", "AddToMyTokenList": "添加到我的代币列表", "blockedListTitle": "被阻止的 token", "TokenName": "代币名称", "maybeScamTips": "这是一个低质量的代币，可能是一个诈骗。", "blockedListTitles": "被阻止的代币", "customizedHasAddedTips": "该代币未在 Rabby 上列出。您已手动将其添加到代币列表中。", "ContractAddress": "合约地址", "blockedTips": "被阻止的令牌将不会显示在令牌列表中", "IssuerWebsite": "发行者的网站", "verifyScamTips": "这是一个诈骗代币", "NoSupportedExchanges": "没有可用的支持交易所", "noIssuer": "没有可用的发行方信息", "BridgeIssue": "第三方桥接的 token", "ListedBy": "列出 by", "fdvTips": "如果最大供应量在流通中，市场总值请参考以下公式。完全稀释估值（FDV）= 价格 x 最大供应量。如果最大供应量为零，则 FDV = 价格 x 总供应量。如果最大供应量和总供应量都没有定义或为无限，则不显示 FDV。"}, "assets": {"usdValue": "美元价值", "amount": "数量", "portfolio": {"nftTips": "根据本协议认可的地板价计算", "fractionTips": "根据链接的 ERC20 代币的价格进行计算"}, "tokenButton": {"subTitle": "该列表中的代币不会计算到总余额中"}, "table": {"assetAmount": "资产 / 数量", "price": "价格", "useValue": "美元价值", "healthRate": "健康率", "debtRatio": "债务比率", "unlockAt": "解锁于", "lentAgainst": "借出抵押", "type": "类型", "strikePrice": "行使价格", "exerciseEnd": "行使结束", "tradePair": "交易对", "side": "方向", "leverage": "杠杆", "PL": "P&L", "unsupportedPoolType": "不支持的池类型", "claimable": "可认领的", "endAt": "结束于", "dailyUnlock": "每日解锁", "pool": "POOL", "token": "代币", "balanceValue": "余额 / 价值", "percent": "百分比", "summaryTips": "资产价值除以总净值", "summaryDescription": "协议中的所有资产（例如 LP 代币）都将解析为统计计算所需的基础资产", "noMatch": "没有匹配", "lowValueDescription": "低价值资产将显示在这里", "lowValueAssets": "{{count}} 个低价值资产", "lowValueAssets_one": "{{count}} 个低价值代币", "lowValueAssets_other": "{{count}} 个低价值代币", "lowValueAssets_0": "{{count}} 个低价值代币"}, "noAssets": "没有资产", "blockLinkText": "搜索代币地址，隐藏代币", "blockDescription": "被你隐藏的代币将显示在此处", "unfoldChain": "展开 1 条链条", "unfoldChainPlural": "展开 {{moreLen}} 条链", "customLinkText": "搜索代币地址，添加代币", "customDescription": "你手动添加的代币将显示在此处", "comingSoon": "即将推出...", "searchPlaceholder": "代币", "customButtonText": "添加自定义代币", "AddMainnetToken": {"title": "添加自定义代币", "selectChain": "选择链", "searching": "搜索代币", "tokenAddress": "代币地址", "tokenAddressPlaceholder": "代币地址", "notFound": "未找到代币", "isBuiltInToken": "已支持的代币"}, "AddTestnetToken": {"title": "添加自定义网络代币", "selectChain": "选择链", "searching": "搜索代币", "tokenAddress": "代币地址", "tokenAddressPlaceholder": "代币地址", "notFound": "未找到代币"}, "TestnetAssetListContainer": {"add": "代币", "addTestnet": "网络"}, "noTestnetAssets": "没有自定义网络资产", "addTokenEntryText": "代币"}, "hd": {"howToConnectLedger": "如何连接 Ledger", "howToConnectKeystone": "如何连接 Keystone", "userRejectedTheRequest": "用户拒绝了请求", "ledger": {"doc1": "插入 Ledger", "doc2": "输入 PIN 码解锁 Ledger", "doc3": "打开 Ethereum 应用程序", "reconnect": "如果无法连接，请尝试<1>从头开始重新连接</1>", "connected": "Ledger 已连接"}, "keystone": {"title": "确保您的Keystone 3 Pro位于首页", "doc1": "插入一个Keystone", "doc2": "输入密码以解锁", "doc3": "批准与电脑的连接", "reconnect": "如果无法使用，请尝试<1>从头开始重新连接。</1>"}, "howToSwitch": "如何切换", "imkey": {"doc1": "插入 imKey", "doc2": "输入 PIN 码解锁 imKey"}, "howToConnectImKey": "如何连接 imKey", "ledgerIsDisconnected": "您的 Ledger 已断开连接"}, "GnosisWrongChainAlertBar": {"warning": "Safe 地址不支持 {{chain}}", "notDeployed": "您的 Safe 地址未在此链上部署。"}, "echologyPopup": {"title": "生态系统"}, "MetamaskModePopup": {"title": "MetaMask 模式", "toastSuccess": "已启用。刷新页面以重新连接。", "enableDesc": "如果 Dapp 仅与 MetaMask 一起使用，请启用", "desc": "如果无法在 Dapp 上连接 Rabby，请启用 MetaMask 模式并通过选择 MetaMask 选项进行连接。", "footerText": "在“更多 > MetaMask 模式”中的 MetaMask 模式中添加更多 Dapps"}, "offlineChain": {"chain": "{{chain}} 将很快不再被集成。", "tips": "{{chain}}链将不会在{{date}}集成。您的资产不会受到影响，但不会包含在您的总余额中。如需访问，您可以在“更多”中将其添加为自定义网络。"}, "recentConnectionGuide": {"button": "明白了\n", "title": "此处切换用于 Dapp 连接的地址\n"}}, "chainList": {"title": "{{count}} chains 集成", "mainnet": "主网", "testnet": "测试网"}, "nft": {"floorPrice": "/ 地板价:", "title": "NFT", "all": "全部", "starred": "已收藏 ({{count}})", "empty": {"title": "没有已收藏的 NFT", "description": "你可以从“全部”中选择NFT添加到“已收藏”"}, "noNft": "无 NFT"}, "newAddress": {"title": "添加地址", "importSeedPhrase": "导入助记词", "importPrivateKey": "导入私钥", "importMyMetamaskAccount": "导入我的 MetaMask 帐户", "addContacts": {"content": "添加联系人", "description": "你也可以将其用作观察地址", "required": "请输入地址", "notAValidAddress": "无效地址", "scanViaMobileWallet": "通过手机钱包扫描", "scanViaPcCamera": "通过电脑摄像头扫描", "scanQRCode": "使用兼容 WalletConnect 的钱包扫描二维码", "walletConnect": "WalleConnect", "walletConnectVPN": "如果你使用 VPN，WalletConnect 将会不稳定", "cameraTitle": "请用相机扫描二维码", "addressEns": "输入地址/ENS"}, "unableToImport": {"title": "无法导入", "description": "不支持导入多个基于二维码的硬件钱包。\n请先删除 {{0}} 中的所有地址，然后再导入新的设备"}, "connectHardwareWallets": "连接硬件钱包", "firefoxLedgerDisableTips": "Ledger和Firefox不兼容", "connectMobileWalletApps": "连接手机钱包", "connectInstitutionalWallets": "连接机构钱包", "createNewSeedPhrase": "创建新的助记词", "importKeystore": "导入 KeyStore", "selectImportMethod": "选择导入方式", "theSeedPhraseIsInvalidPleaseCheck": "助记词无效，请检查！", "seedPhrase": {"importTips": "你可以将完整助记词粘贴到第一个字段中", "whatIsASeedPhrase": {"question": "什么是助记词？", "answer": "用于控制你的资产的 12、18 或 24 个单词的短语。"}, "isItSafeToImportItInRabby": {"question": "在 Rabby 中导入它安全吗？", "answer": "是的，它将存储在你本地的浏览器上，并且只有你可以访问。"}, "importError": "[CreateMnemonics] 意外步骤 {{0}}", "importQuestion4": "如果我在未备份助记词的情况下卸载 Rabby，Rabby 无法为我找回助记词。", "riskTips": "在开始之前，请阅读并牢记以下安全提示", "showSeedPhrase": "创建助记词", "backup": "备份助记词", "backupTips": "备份助记词时，确保没有其他人查看你的屏幕", "copy": "复制助记词", "saved": "我已备份", "pleaseSelectWords": "请选择字词", "verificationFailed": "验证失败", "createdSuccessfully": "创建成功", "verifySeedPhrase": "验证助记词", "fillInTheBackupSeedPhraseInOrder": "按顺序填写备份助记词", "clearAll": "全部清除", "wordPhrase": "我有一个 <1>{{count}}</1> 词的助记词", "pastedAndClear": "已粘贴并清空剪切板", "inputInvalidCount": "有{{count}}处输入内容不符合助记词规范，请检查", "wordPhraseAndPassphrase": "我有一个包含 <1>{{count}}</1> 个词的短语和密码", "slip39SeedPhrase": "我有一个 <0>{{SLIP39}}</0> 种子短语", "slip39SeedPhraseWithPassphrase": "我有一个带密码的 <0>{{SLIP39}}</0> 种子短语", "slip39SeedPhrasePlaceholder_one": "在这里输入你的第 {{count}} 个种子短语份额", "slip39SeedPhrasePlaceholder_two": "在这里输入你的第 {{count}} 个种子短语份额", "slip39SeedPhrasePlaceholder_few": "在这里输入你的第 {{count}} 个种子短语份额", "slip39SeedPhrasePlaceholder_other": "在这里输入你的第 {{count}} 个种子短语份额", "invalidContent": "内容无效", "inputInvalidCount_one": "1 个输入不符合种子短语规范，请检查。", "inputInvalidCount_other": "{{count}} 个输入不符合种子短语规范，请检查。", "passphrase": "密码", "importQuestion1": "如果我丢失或分享我的种子短语，我将永久失去对我的资产的访问权限。", "importQuestion3": "如果我在没有备份我的种子短语的情况下卸载 Rabby，将无法通过 Rabby 恢复。", "importQuestion2": "我的助记词仅存储在我的设备上。Rabby 无法访问它。"}, "metamask": {"step1": "从 MetaMask 导出助记词或私钥  <br/><1>查看操作指南<1/></1>", "step2": "在 Rabby 中导入助记词或私钥", "step3": "导入完成，你的所有资产将自动展示<br />", "how": "如何导入我的 MetaMask 帐户？", "step": "步骤", "importSeedPhrase": "导入助记词或私钥", "importSeedPhraseTips": "它只会存储在本地浏览器上。 \nRabby 永远不会访问你的私人信息。", "tipsDesc": "你的私钥和助记词不属于MetaMask或者某个具体钱包，它只属于你自己。", "tips": "Tips:"}, "privateKey": {"required": "请输入私钥", "placeholder": "输入你的私钥", "whatIsAPrivateKey": {"question": "什么是私钥？", "answer": "用于控制你的资产的一串字符。"}, "isItSafeToImportItInRabby": {"question": "在 Rabby 中导入它安全吗？", "answer": "是的，它将存储在你本地的浏览器上，并且只有你可以访问。"}, "isItPossibleToImportKeystore": {"question": "可以导入 KeyStore 吗？", "answer": "是的，你可以在此处<1>导入 KeyStore</1>。"}, "notAValidPrivateKey": "无效私钥", "repeatImportTips": {"desc": "此地址已导入。", "question": "是否要切换到此地址？"}}, "importedSuccessfully": "导入成功", "ledger": {"title": "连接Ledger", "cameraPermissionTitle": "允许Rabby访问相机", "cameraPermission1": "在浏览器弹出窗口中允许Rabby访问摄像头", "allowRabbyPermissionsTitle": "允许 Rabby 获取以下权限：", "ledgerPermission1": "连接到 HID 设备", "ledgerPermissionTip": "请点击下面的“允许”，并在弹出窗口中授权访问你的 Ledger", "permissionsAuthorized": "权限已授权", "nowYouCanReInitiateYourTransaction": "现在你可以重新发起你的交易", "allow": "允许", "error": {"ethereum_app_not_installed_error": "请在您的 Ledger 设备上安装以太坊应用程序。", "ethereum_app_unconfirmed_error": "您已拒绝打开以太坊应用程序的请求。", "ethereum_app_open_error": "请在您的 Ledger 设备上安装/接受以太坊应用程序。", "running_app_close_error": "无法关闭 Ledger 设备上正在运行的应用程序。"}}, "keystone": {"title": "连接Keystone", "allowRabbyPermissionsTitle": "允许Rabby访问以下权限：", "keystonePermission1": "连接至USB设备", "keystonePermissionTip": "请在下方点击“允许”，以在弹出窗口中授权访问您的Keystone，并确保您的Keystone 3 Pro位于首页。", "noDeviceFoundError": "请插入一个Keystone", "deviceIsLockedError": "输入密码以解锁", "deviceRejectedExportAddress": "请批准与 Rabby 的连接", "deviceIsBusy": "设备正在忙碌中", "exportAddressJustAllowedOnHomePage": "仅在 Keystone 主页上允许导出地址", "unknowError": "未知错误，请再试一次"}, "walletConnect": {"connectYour": "连接你的", "viaWalletConnect": "通过 Wallet Connect", "connectedSuccessfully": "连接成功", "qrCodeError": "请检查你的网络或刷新二维码", "qrCode": "二维码", "url": "网址", "changeBridgeServer": "更改桥接服务器", "status": {"received": "扫描成功\n等待确认", "rejected": "连接已取消\n请扫描二维码重试", "brandError": "检测到错误的钱包应用程序", "brandErrorDesc": "请使用 {{brandName}} 进行连接", "accountError": "地址不匹配", "accountErrorDesc": "请切换手机钱包地址", "connected": "已连接", "duplicate": "该地址已导入", "default": "用你的{{brand}}扫描"}, "title": "与 {{brandName}} 连接", "disconnected": "已断开连接", "accountError": {}, "tip": {"accountError": {"tip1": "已连接但无法签名", "tip2": "请切换至手机钱包中的正确地址"}, "disconnected": {"tip": "未连接到 {{brandName}}"}, "connected": {"tip": "已连接至 {{brandName}}"}}, "button": {"disconnect": "断开", "connect": "连接", "howToSwitch": "如何切换"}}, "hd": {"tooltip": {"removed": "该地址已从 Rabby 中删除", "added": "地址已添加至Rabby", "connectError": "连接已停止\n请刷新页面以重新连接", "disconnected": "无法连接到硬件钱包\n请尝试重新连接"}, "waiting": "加载中", "clickToGetInfo": "点击获取链上信息", "addToRabby": "添加到 Rabby", "basicInformation": "基本信息", "addresses": "地址", "loadingAddress": "正在加载 {{0}}/{{1}} 地址", "notes": "备注", "getOnChainInformation": "获取链上信息", "hideOnChainInformation": "隐藏链上信息", "usedChains": "使用过的链", "firstTransactionTime": "首次交易时间", "balance": "余额", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live：Ledger 官方 HDpath\n前3个地址中，有链上使用的地址", "bip44": "BIP44标准：BIP44 协议定义的 HDpath\n前3个地址中，有链上使用的地址", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath\n前3个地址中，有链上使用的地址"}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live：Ledger 官方 HDpath\n前3个地址中，没有链上使用的地址", "bip44": "BIP44：BIP44 协议定义的 HDpath\n前3个地址中，没有链上使用的地址", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath\n前3个地址中，没有链上使用的地址"}}, "trezor": {"hdPathType": {"bip44": "BIP44：BIP44 协议定义的 HDpath", "ledgerLive": "Ledger Live: Ledger 官方 HD 路径。", "legacy": "传统：MEW / Mycrypto 使用的 HD 路径。"}, "hdPathTypeNoChain": {"bip44": "BIP44：BIP44 协议定义的 HDpath", "ledgerLive": "Ledger Live：Ledger 官方 HD 路径。", "legacy": "传统：HD 路径由 MEW / Mycrypto 使用。"}, "message": {"disconnected": "{{0}}连接已停止\n请刷新页面以重新连接"}}, "onekey": {"hdPathType": {"bip44": "BIP44：BIP44 协议定义的 HDpath"}, "hdPathTypeNoChain": {"bip44": "BIP44：BIP44 协议定义的 HDpath"}}, "mnemonic": {"hdPathType": {"default": "默认：使用导入助记词的默认 HDpath", "ledgerLive": "Ledger Live：Ledger 官方 HDpath", "bip44": "BIP44标准：BIP44 协议定义的 HDpath", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath"}, "hdPathTypeNoChain": {"default": "默认：使用导入助记词的默认 HDpath"}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live：Ledger 官方 HDpath\n前3个地址中，有链上使用的地址", "bip44": "BIP44：BIP44 协议定义的 HDpath\n前3个地址中，有链上使用的地址", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath\n前3个地址中，有链上使用的地址"}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live：Ledger 官方 HDpath\n前3个地址中，没有链上使用的地址", "bip44": "BIP44：BIP44 协议定义的 HDpath\n前3个地址中，没有链上使用的地址", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath\n前3个地址中，没有链上使用的地址"}, "switch": {"title": "切换到新的 GridPlus 设备", "content": "不支持导入多个 GridPlus 设备。如果切换到新的 GridPlus 设备，需要在开始导入过程之前，删除当前设备的所有地址"}, "switchToAnotherGridplus": "切换到另一个 GridPlus"}, "keystone": {"hdPathType": {"bip44": "BIP44：BIP44协议定义的 HDpath", "ledgerLive": "Ledger Live：Ledger 官方 HDpath。只能通过Ledger Live路径管理10个地址。", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath"}, "hdPathTypeNochain": {"bip44": "BIP44：BIP44协议定义的 HDpath", "ledgerLive": "Ledger Live：Ledger 官方 HDpath。只能通过Ledger Live路径管理10个地址。", "legacy": "旧版：MEW / Mycrypto 使用的 HDpath"}}, "bitbox02": {"hdPathType": {"bip44": "BIP44：BIP44协议定义的 HDpath"}, "hdPathTypeNoChain": {"bip44": "BIP44：BIP44协议定义的 HDpath"}, "disconnected": "无法连接到 BitBox02\n请刷新页面以重新连接\n原因：{{0}}"}, "selectHdPath": "选择 HDpath：", "selectIndexTip": "选择起始地址的序列号：", "manageAddressFrom": "管理从 {{0}} 到 {{1}} 的地址", "advancedSettings": "高级设置", "customAddressHdPath": "自定义地址 HDpath", "connectedToLedger": "连接 Ledger", "connectedToTrezor": "连接 Trezor", "connectedToOnekey": "连接 Onekey", "manageSeedPhrase": "管理助记词", "manageGridplus": "管理 Gridplus", "manageImtokenOffline": "管理 imToken", "manageKeystone": "管理 Keystone", "manageAirgap": "管理 Airgap", "manageCoolwallet": "管理 CoolWallet", "manageBitbox02": "管理 BitBox02", "manageNgraveZero": "管理 NGRAVE ZERO", "done": "完成", "addressesIn": "{{0}} 中的地址", "addressesInRabby": "<PERSON><PERSON> 中的地址{{0}}", "qrCode": {"switch": {"title": "切换到新的 {{0}} 设备", "content": "不支持导入多个 {{0}} 设备。如果你切换到新的 {{0}} 设备，需要在开始导入过程之前，删除当前设备的所有地址。"}, "switchAnother": "切换到另一个{{0}}"}, "manageImKey": "管理 imKey", "importBtn": "导入 ({{count}})"}, "importYourKeystore": "导入你的 KeyStore", "incorrectPassword": "密码错误", "keystore": {"description": "选择需要导入的 keystore 文件并输入对应的密码", "password": {"required": "请输入密码", "placeholder": "密码"}}, "coboSafe": {"addCoboArgusAddress": "添加 Cobo Argus 地址", "findTheAssociatedSafeAddress": "查找关联的安全地址", "import": "导入", "inputSafeModuleAddress": "输入安全模块地址", "invalidAddress": "地址无效", "whichChainIsYourCoboAddressOn": "您的 Cobo 地址位于哪条链上"}, "imkey": {"title": "连接 imKey", "imkeyPermissionTip": "请点击下方的“允许”并在接下来的弹出窗口中授权访问您的 imKey。"}, "addFromCurrentSeedPhrase": "从当前助记词添加"}, "activities": {"signedText": {"empty": {"desc": "所有通过 Rabby 签署的文本都将在此展示", "title": "尚未签署文本"}, "label": "签署文本"}, "signedTx": {"common": {"cancel": "取消", "pendingDetail": "待完成", "speedUp": "加速", "unknown": "未知", "unknownProtocol": "未知协议", "unlimited": "无限"}, "empty": {"desc": "所有通过 Rabby 签署的交易都将在此展示", "title": "尚未签署交易"}, "explain": {"approve": "授权 {{protocol}} 的 {{count}} {{token}}", "cancel": "取消 {{token}} 授权 {{protocol}}", "cancelNFTCollectionApproval": "取消 {{protocol}} 的 NFT collection 授权", "cancelSingleNFTApproval": "取消 {{protocol}} 的单一 NFT 授权", "nftCollectionApproval": "{{protocol}} 的 NFT collection 授权", "send": "发送 {{amount}} {{symbol}}", "singleNFTApproval": "{{protocol}} 的单一 NFT 授权", "unknown": "未知交易"}, "label": "签署交易", "status": {"canceled": "已取消", "failed": "交易失败", "pending": "待完成", "submitFailed": "提交失败", "pendingBroadcast": "Pending: 等待广播", "pendingBroadcasted": "Pending: 已广播", "withdrawed": "已快捷取消", "pendingBroadcastFailed": "Pending: 广播失败"}, "tips": {"canNotCancel": "无法加速或取消：不是第一个待完成的交易", "pendingDetail": "只会有一笔交易成功完成，而且一般是 Gas Price 最高的一笔交易将成功", "pendingBroadcastRetry": "广播失败，上次广播时间：{{pushAt}}", "pendingBroadcastRetryBtn": "重新广播", "pendingBroadcast": "已设置为省Gas广播模式，正在等链上Gas较低的时广播。最晚将在创建后{{deadline}}小时后广播。", "pendingBroadcastBtn": "立即广播"}, "txType": {"cancel": "取消交易", "initial": "发起交易", "speedUp": "加速交易"}, "MempoolList": {"empty": "未发现包含这笔交易的节点", "reBroadcastBtn": "重新广播", "title": "Appeared in {{count}} RPC nodes"}, "message": {"broadcastSuccess": "已广播", "cancelSuccess": "已取消", "reBroadcastSuccess": "已重新广播", "deleteSuccess": "删除成功"}, "CancelTxPopup": {"title": "取消交易", "options": {"quickCancel": {"title": "快捷取消", "desc": "不需要发起链上取消，不需要Gasfee", "tips": "不是等待广播的交易，不支持快捷取消"}, "onChainCancel": {"title": "链上取消", "desc": "发起新的链上交易来取消，需要Gasfee"}, "removeLocalPendingTx": {"title": "清除本地待处理", "desc": "从界面中移除待处理交易"}}, "removeLocalPendingTx": {"title": "本地删除交易", "desc": "此操作将在本地删除待处理的事务。\n待处理的交易将来仍有可能成功提交。"}}, "SkipNonceAlert": {"alert": "当前 {{chainName}} 链因快捷取消导致 Nonce#{{nonce}} 被跳过, 导致后续 Pending 交易无法完成, 可 <5></5><6>发起一笔链上交易</6><7></7>来填充", "clearPendingAlert": "{{chainName}} 交易（{{nonces}}）已挂起超过 3 分钟。您可以<5></5><6>在本地清除挂起</6><7></7>并重新提交交易。"}, "gas": {"noCost": "无 Gas 费用"}, "PredictTime": {"time": "预计将在 {{time}} 内打包", "noTime": "正在预测打包时间", "failed": "打包时间预测失败"}, "CancelTxConfirmPopup": {"desc": "这将从您的界面中移除待处理的交易。然后，您可以发起新交易。", "warning": "已移除的交易可能仍会在链上确认，除非它被替换。", "title": "清除本地挂起"}}, "title": "签名记录"}, "addToken": {"balance": "余额", "noTokenFound": "未找到代币", "noTokenFoundOnThisChain": "该链上未找到代币", "title": "添加代币至 Rabby", "tokenCustomized": "当前代币已手动添加", "tokenNotFound": "该合约地址上未找到代币", "tokenOnMultiChains": "多链上的代币地址，\n请选择一个", "tokenSupported": "Ra<PERSON> 已支持该代币", "hasAdded": "你已添加此代币。"}, "approvals": {"RevokeApprovalModal": {"confirm": "确认 {{ selectedCount }}", "selectAll": "全选", "subTitleContract": "授权以下合约", "subTitleTokenAndNFT": "授权的代币和 NFT", "title": "授权", "tooltipPermit2": "此批准通过 Permit2 合约批准： {{ permit2Id }}", "unSelectAll": "取消全选"}, "component": {"ApprovalContractItem": {"ApprovalCount_one": "个授权", "ApprovalCount_other": "个授权"}, "table": {"bodyEmpty": {"loadingText": "加载中...", "noMatchText": "没有匹配", "noDataText": "无授权"}}, "RevokeButton": {"btnText_zero": "取消授权", "btnText_one": "取消授权 ({{count}})", "btnText_other": "取消授权 ({{count}})", "permit2Batch": {"modalTitle_other": "总共需要<2>{{count}}</2>个签名", "modalTitle_one": "共需 <2>{{count}}</2> 个签名", "modalContent": "来自相同 Permit2 合约的授权将会在相同的签名下打包在一起。"}}, "ViewMore": {"text": "查看更多"}}, "header": {"title": "{{ address }} 的授权"}, "search": {"placeholder": "按名称/地址搜索 {{ type }}"}, "tab-switch": {"assets": "按资产分类", "contract": "按合约分类"}, "tableConfig": {"byAssets": {"columnTitle": {"approvedAmount": "授权额度", "approvedSpender": "授权对象地址", "asset": "资产", "myApprovalTime": "我的授权时间", "type": "类型"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "授权额度", "tipMyBalance": "我的余额"}}}, "byContracts": {"columnTip": {"contractTrustValue": "信任值是指授权并暴露给该合约的资产总价值，\n信任值低表示该合约存在风险或 180 天内不活跃", "contractTrustValueDanger": "合约信任值 < 10,000 美元", "contractTrustValueWarning": "合约信任值 < 100,000 美元"}, "columnTitle": {"contract": "合约", "contractTrustValue": "合约信任值", "myApprovedAssets": "我的授权资产", "revokeTrends": "24小时取消授权趋势", "myApprovalTime": "我的授权时间"}}}, "revokeModal": {"approvalCount_one": "{{count}} 批准", "approvalCount_other": "{{count}} 个批准", "batchRevoke": "批量撤销", "cancelBody": "如果关闭此页面，剩余的撤销将不会执行。", "cancelTitle": "取消剩余撤销", "confirm": "确认", "confirmTitle": "1签名批量撤销", "defaultFailed": "交易失败", "done": "完毕", "stillRevoke": "仍撤销", "signAndStartRevoke": "签名并开始撤销", "revoked": "撤销：", "revokeOneByOne": "一一撤销", "resume": "继续", "pause": "暂停", "useGasAccount": "Gas余额不足，你的GasAccount账户将支付这笔Gas费", "gasNotEnough": "Gas 不足，无法提交", "submitTxFailed": "提交失败", "gasTooHigh": "煤气费高", "totalRevoked": "全部的：", "approvalCount_zero": "{{count}} 批准", "ledgerAlert": "请在您的 Ledger 设备上打开以太坊应用程序", "connectLedger": "连接账本", "revokeWithLedger": "使用 Ledger 开始撤销", "ledgerSended": "请在 Ledger 上签署请求（{{current}}/{{total}}）", "ledgerSigned": "签署。创建交易（{{current}}/{{total}}）", "ledgerSending": "正在发送签名请求（{{current}}/{{total}}）", "confirmRevokeLedger": "使用账本地址，您可以一键批量撤销 {{count}} 个批准。", "confirmRevokePrivateKey": "使用助记词或私钥地址，您可以一键批量撤销 {{count}} 个批准。", "simulationFailed": "交易模拟失败", "paused": "暂停", "waitInQueue": "排队等待"}}, "connect": {"addedToBlacklist": "添加到你的黑名单", "addedToWhitelist": "添加到你的白名单", "blocked": "黑名单", "connectBtn": "连接", "flagByMM": "被 MetaMask 标记为风险网址", "flagByRabby": "被 Rabby 标记为风险网址", "flagByScamSniffer": "被 ScamSniffer 标记为风险网址", "foundForbiddenRisk": "检测到高危风险，\n无法连接", "listedBy": "收录于", "manageWhiteBlackList": "管理白名单/黑名单", "markAsBlockToast": "已加入黑名单", "markAsTrustToast": "已加入白名单", "markRemovedToast": "已删除标记", "markRuleText": "我的标记", "myMark": "我的标记", "noMark": "无标记", "noWebsite": "未收录于任何平台", "notOnAnyList": "不在任何名单上", "onYourBlacklist": "已加入黑名单", "onYourWhitelist": "已加入白名单", "popularLevelHigh": "高", "popularLevelLow": "低", "popularLevelMedium": "中等", "popularLevelVeryLow": "非常低", "removedFromAll": "从所有列表中删除", "selectChainToConnect": "选择要连接的链", "sitePopularity": "网站热度", "title": "连接 Dapp", "trusted": "白名单", "verifiedByRabby": "经 Ra<PERSON> 验证", "SignTestnetPermission": {"title": "签名权限"}, "ignoreAll": "忽略全部", "SelectWallet": {"title": "选择一个钱包进行连接", "desc": "从您已安装的钱包中选择"}, "otherWalletBtn": "连接其他钱包", "connectAddress": "连接地址\n"}, "gasTopUp": {"Amount": "数量", "Balance": "余额", "Confirm": "确认", "Continue": "继续", "Including-service-fee": "包含 {{fee}} 服务费", "InsufficientBalance": "当前链的 Rabby 合约地址余额不足，\n请稍后再试", "InsufficientBalanceTips": "余额不足", "Loading_Tokens": "正在加载代币...", "No_Tokens": "没有代币", "Payment-Token": "支付代币", "Select-from-supported-tokens": "从支持的代币中选择", "Select-payment-token": "选择支付代币", "Token": "代币", "Value": "价值", "description": "你可以向任意链上充值 Gas， 只需要支付已有链上的可用代币。\n付款一经确认立即到账，无需等待，不可撤销", "hightGasFees": "该充值金额太小，目标网络需要较高的 Gas 费", "payment": "Gas 充值付款", "service-fee-tip": "Rabby 通过提供 Gas 充值服务，需要承担代币波动的损失以及充值的 Gas 费用。\n因此要收取20%的服务费", "title": "极速充值 Gas", "topUpChain": "充值链"}, "manageAddress": {"add-address": "添加地址", "address-management": "地址管理", "addressTypeTip": "由 {{type}} 导入", "backup-seed-phrase": "备份助记词", "cancel": "取消", "confirm": "确认", "confirm-delete": "确认删除", "current-address": "当前地址", "delete-all-addresses-and-the-seed-phrase": "删除所有地址和助记词", "delete-all-addresses-but-keep-the-seed-phrase": "删除所有地址，但保留助记词", "delete-checklist-1": "我明白如果我删除这个地址，对应的私钥及助记词都将被删除，Rabby 无法为我找回", "delete-checklist-2": "我确认我已备份私钥或助记词，现在已准备好进行删除", "delete-desc": "删除前，请记住以下几点，以了解如何保护你的资产", "delete-empty-seed-phrase": "删除助记词及其 0 个地址", "delete-private-key-modal-title_one": "删除 {{count}} 个私钥地址", "delete-private-key-modal-title_other": "删除 {{count}} 个私钥地址", "delete-seed-phrase": "删除助记词", "delete-seed-phrase-title_one": "删除助记词及其 {{count}} 个地址", "delete-seed-phrase-title_other": "删除助记词及其 {{count}} 个地址", "delete-title_one": "删除 {{count}} {{brand}} 地址", "delete-title_other": "删除 {{count}} 个 {{brand}} 地址", "deleted": "已删除", "hd-path": "HDpath：", "manage-address": "管理地址", "no-address": "无地址", "no-address-under-seed-phrase": "你尚未在此助记词下导入任何地址", "no-match": "没有匹配", "private-key": "私钥", "search": "搜索", "seed-phrase": "助记词", "seed-phrase-delete-title": "删除助记词？", "update-balance-data": "刷新所有余额", "watch-address": "观察地址", "whitelisted-address": "白名单地址", "sort-by-balance": "按地址金额排序", "sort-by-address-type": "按地址类型排序", "sort-by-address-note": "按地址备注排序", "sort-address": "地址排序", "enterThePassphrase": "输入密码", "enterPassphraseTitle": "输入密码以签名", "passphraseError": "密码无效", "addNewAddress": "添加新地址", "CurrentDappAddress": {"desc": "切换 Dapp 地址\n"}}, "receive": {"title": "在 {{chain}} 上接收{{token}}", "watchModeAlert1": "这是一个观察地址", "watchModeAlert2": "你确定用它来接收资产吗？"}, "securityEngine": {"alertTriggerReason": "警报触发原因：", "currentValueIs": "当前值为 {{value}}", "enableRule": "启用规则", "forbiddenCantIgnore": "发现了不可忽视的高危风险", "ignoreAlert": "忽略警报", "no": "不", "riskProcessed": "风险提示被忽视", "ruleDetailTitle": "规则详情", "ruleDisabled": "安全规则已被禁用。\n为了你的安全，你可以随时将其打开", "understandRisk": "我理解并自行承担任何损失", "undo": "撤消", "unknownResult": "结果未知，因为安全引擎目前不可用", "viewRiskLevel": "查看风险级别", "viewRules": "查看安全规则", "whenTheValueIs": "当值为 {{value}} 时", "yes": "是的"}, "sendNFT": {"header": {"title": "发送"}, "nftInfoFieldLabel": {"Collection": "系列", "Contract": "合约", "sendAmount": "数量"}, "sectionChain": {"title": "链"}, "sectionFrom": {"title": "从"}, "sectionTo": {"addrValidator__empty": "请输入地址", "addrValidator__invalid": "该地址无效", "searchInputPlaceholder": "输入地址或搜索", "title": "至"}, "sendButton": "发送", "tipAddToContacts": "添加为联系人", "tipNotOnAddressList": "不在地址列表中", "whitelistAlert__temporaryGranted": "已授权此次转账", "whitelistAlert__disabled": "转账白名单已关闭，你可以转账给任何地址", "whitelistAlert__notWhitelisted": "该地址不属于转账白名单，授权此次转账权限", "whitelistAlert__whitelisted": "白名单地址", "confirmModal": {"title": "输入密码进行确认"}}, "sendToken": {"addressNotInContract": "不在地址列表中, <1></1><2>添加为联系人</2>", "AddToContactsModal": {"addedAsContacts": "添加为联系人", "editAddr": {"placeholder": "输入地址备注", "validator__empty": "请输入地址备注"}, "editAddressNote": "编辑地址备注", "error": "添加联系人失败"}, "allowTransferModal": {"error": "密码错误", "placeholder": "输入密码进行确认", "validator__empty": "请输入密码", "addWhitelist": "添加到白名单"}, "GasSelector": {"confirm": "确认", "level": {"$unknown": "未知", "custom": "自定义", "fast": "立即", "normal": "快速", "slow": "标准"}, "popupDesc": "矿工费将根据你设置的 Gas Price 从转账金额中预留", "popupTitle": "设置 Gas Price (Gwei)"}, "header": {"title": "发送"}, "modalConfirmAddToContacts": {"confirmText": "确认", "title": "添加为联系人"}, "modalConfirmAllowTransferTo": {"cancelText": "取消", "confirmText": "确认", "title": "输入密码进行确认"}, "sectionBalance": {"title": "余额"}, "sectionChain": {"title": "链"}, "sectionFrom": {"title": "从"}, "sectionTo": {"addrValidator__empty": "请输入地址", "addrValidator__invalid": "该地址无效", "searchInputPlaceholder": "输入地址或搜索", "title": "接收地址"}, "sendButton": "发送", "tokenInfoFieldLabel": {"chain": "链", "contract": "合约地址"}, "tokenInfoPrice": "价格", "whitelistAlert__disabled": "转账白名单已关闭，你可以转账给任何地址", "whitelistAlert__notWhitelisted": "该地址不属于转账白名单，授权此次转账权限", "whitelistAlert__temporaryGranted": "已授权此次转账", "whitelistAlert__whitelisted": "白名单地址", "balanceError": {"insufficientBalance": "余额不足"}, "balanceWarn": {"gasFeeReservation": "需要预留 Gas"}, "max": "全部", "sectionMsgDataForEOA": {"title": "留言", "currentIsOriginal": "当前输入内容为原始数据, UTF8 格式为:", "currentIsUTF8": "当前输入内容编码为 UTF-8, 原始数据是:", "placeholder": "可选"}, "sectionMsgDataForContract": {"simulation": "合约调用模拟", "notHexData": "只支持 16 进制格式数据", "title": "合约调用", "placeholder": "可选", "parseError": "合约调用模拟失败"}, "blockedTransactionContent": "该交易涉及的地址在OFAC制裁名单上。", "blockedTransaction": "已阻止的交易", "blockedTransactionCancelText": "我知道"}, "sendTokenComponents": {"GasReserved": "预留 <1>0</1> {{ tokenName }} 用于 矿工费", "SwitchReserveGas": "预留 Gas <1 />"}, "signFooterBar": {"addressTip": {"airgap": "AirGap 地址", "bitbox": "BitBox02 地址", "coolwallet": "CoolWallet 地址", "keystone": "KeyStone 地址", "onekey": "Onekey 地址", "privateKey": "私钥地址", "safe": "Safe 地址", "seedPhrase": "助记词地址", "trezor": "Trezor 地址", "watchAddress": "无法使用观察地址进行签名", "coboSafe": "Cobo Argus 地址", "seedPhraseWithPassphrase": "种子短语地址（密码）"}, "beginSigning": "开始签名流程", "connectButton": "连接", "connecting": "正在连接...", "gridPlusConnected": "GridPlus 已连接", "gridPlusNotConnected": "GridPlus 未连接", "ignoreAll": "忽略所有", "ledgerConnected": "Ledger 已连接", "ledgerNotConnected": "Ledger 未连接", "keystoneNotConnected": "Keystone 未连接", "keystoneConnected": "Keystone 已连接", "processRiskAlert": "请先处理所有警报", "requestFrom": "请求来源", "signAndSubmitButton": "签名并提交", "walletConnect": {"chainSwitched": "你已在移动钱包上切换到不同的链\n请在手机钱包中切换到{{0}}", "connectBeforeSign": "{{0}}未连接到 Rabby，请在签名前连接", "connected": "已连接并准备好签名", "connectedButCantSign": "已连接但无法签名", "howToSwitch": "如何切换", "notConnectToMobile": "未连接到 {{brand}}", "switchChainAlert": "请在手机钱包中切换到{{chain}}", "switchToCorrectAddress": "请在手机钱包中切换到正确地址", "wrongAddressAlert": "你已在手机钱包中切换到其他地址\n请在手机钱包中切换至正确地址", "latency": "延迟", "requestFailedToSend": "签名请求发送失败", "requestSuccessToast": "请求已成功发送", "sendingRequest": "签名请求发送中", "signOnYourMobileWallet": "请用你的手机钱包签名"}, "common": {"notSupport": "暂不支持 {{0}}"}, "ledger": {"blindSigTutorial": "Ledger 的盲签名教程", "notConnected": "你的钱包未连接，请重新连接", "resent": "重试", "siging": "签名请求发送中", "signError": "Ledger 签名错误:", "txRejected": "交易已拒绝", "txRejectedByLedger": "Ledger 上已拒绝该交易", "unlockAlert": "请插入并解锁你的 Ledger 设备，打开 Ethereum 应用程序", "updateFirmwareAlert": "请更新你的 Ledger 固件版本及 Ethereum 应用程序", "submitting": "已签名，交易正在提交", "resubmited": "重新提交"}, "qrcode": {"afterSignDesc": "签名确认后，请将 {{brand}} 的二维码放置于你的电脑摄像头前", "failedToGetExplain": "获取说明失败", "getSig": "获取签名", "misMatchSignId": "交易数据不一致。请检查交易详情。", "qrcodeDesc": "请使用你的 {{brand}} 扫码签名。<br></br>签名后，点击下方按钮获取签名。", "sigCompleted": "交易已提交", "sigReceived": "签名已获取", "signWith": "使用 {{brand}} 签名", "txFailed": "交易失败", "unknownQRCode": "错误：无法识别二维码"}, "keystone": {"signWith": "切换到{{method}}进行签名", "qrcodeDesc": "扫描以签名。签名后，点击下面获取签名。对于USB，重新连接并授权以重新开始签名过程", "misMatchSignId": "交易数据不一致。请检查交易详情。", "unsupportedType": "错误：交易类型不支持或未知。", "siging": "正在发送签名请求", "txRejected": "交易被拒绝", "shouldRetry": "发生了某些错误。请重试。", "hardwareRejectError": "Keystone 请求已取消。要继续，请重新授权。", "mismatchedWalletError": "钱包不匹配", "verifyPasswordError": "签名失败，请解锁后重试", "shouldOpenKeystoneHomePageError": "确保您的Keystone 3 Pro位于首页"}, "resend": "重试", "submitTx": "提交", "testnet": "测试网", "mainnet": "主网", "imKeyNotConnected": "imKey 未连接", "imKeyConnected": "imKey 已连接", "gasless": {"unavailable": "Gas不足且不符合免费Gas条件", "notEnough": "Gas余额不足", "GetFreeGasToSign": "获取免费Gas进行签名", "rabbyPayGas": "Rabby将支付所需Gas费用 - 只需签名即可", "customRpcUnavailableTip": "不支持免费Gas的自定义RPC", "walletConnectUnavailableTip": "通过WalletConnect连接的移动钱包不支持免费Gas", "watchUnavailableTip": "观察钱包地址不支持免费Gas"}, "cancelTransaction": "取消交易", "detectedMultipleRequestsFromThisDapp": "检测到来自此Dapp的多个请求", "cancelCurrentTransaction": "取消当前交易", "cancelAll": "取消此Dapp的所有{{count}}个请求", "blockDappFromSendingRequests": "阻止Dapp在1分钟内发送请求", "cancelConnection": "取消连接", "cancelCurrentConnection": "取消当前连接", "gasAccount": {"login": "登录", "deposit": "存入", "notEnough": "GasAccount 不足够", "customRPC": "使用自定义 RPC 时不支持", "loginFirst": "请先登录GasAccount", "WalletConnectTips": "GasAccount 不支持 WalletConnect", "useGasAccount": "使用 GasAccount", "gotIt": "知道了", "chainNotSupported": "此链不被GasAccount支持", "loginTips": "要完成 GasAccount 登录，此交易将被丢弃。您需要在登录后重新进行。", "depositTips": "要完成 GasAccount 存款，此交易将被丢弃。存款完成后，您需要重新发起交易。"}}, "signText": {"createKey": {"description": "描述", "interactDapp": "交互 Dapp"}, "message": "信息", "title": "签署文本", "sameSafeMessageAlert": "同样的消息已确认；不需要额外的签名。"}, "signTx": {"addressNote": "地址备注", "addressTypeTitle": "地址类型", "balanceChange": {"errorTitle": "获取余额变化失败", "failedTitle": "交易模拟失败", "nftOut": "减少的 NFT", "noBalanceChange": "未检测到余额变化", "notSupport": "该链暂不支持交易模拟", "successTitle": "交易模拟结果", "tokenIn": "增加的代币", "tokenOut": "减少的代币"}, "blocked": "拉黑", "canOnlyUseImportedAddress": "你只能使用导入的地址进行签名", "cancelTx": {"gasPriceAlert": "将当前 Gas Price 设置为大于 {{value}} Gwei 以取消待处理的交易", "title": "取消待处理交易", "txToBeCanceled": "交易被取消"}, "collectionTitle": "系列", "contractAddress": "合约地址", "contractCall": {"operation": "操作", "operationABIDesc": "操作从 ABI 解码", "operationCantDecode": "操作未解码", "payNativeToken": "支付{{symbol}}", "title": "合约调用", "suspectedReceiver": "异常地址", "receiver": "接收者地址"}, "crossChain": {"title": "跨链"}, "deployContract": {"description": "你正在部署智能合约", "descriptionTitle": "描述", "title": "部署合约"}, "deployTimeTitle": "部署时间", "eip1559Desc1": "在支持 EIP-1559 的链上，Priority Fee 是给处理你的交易的矿工的小费。\n你可以通过降低 Max Priority Fee 来节省最终的矿工费，这可能会导致完成交易需要耗费更长时间", "eip1559Desc2": "在 Rabby 中，Priority Fee（小费）= Max Fee - Base Fee。\n设置 Max Priority Fee 后，将从中扣除 Base Fee，剩下的部分将作为小费给予矿工", "enoughSafeSigCollected": "已收集到足够的签名", "failToFetchGasCost": "无法估计矿工费", "importedAddress": "已导入地址", "fakeTokenAlert": "这是由 Rabby 标记的诈骗代币", "firstOnChain": "首次链上交易", "floorPrice": "地板价", "gasLimitEmptyAlert": "请输入 Gas limit", "gasLimitLessThanExpect": "Gas limit 较低，\n交易失败的可能性为 1%", "gasLimitLessThanGasUsed": "Gas limit 太低，\n交易失败的可能性为 95%", "gasLimitMinValueAlert": "Gas limit 应大于21000", "gasLimitModifyOnlyNecessaryAlert": "仅在必要时修改", "gasLimitNotEnough": "Gas limit 小于21000.交易无法提交", "gasLimitTitle": "Gas limit", "gasMoreButton": "更多", "gasNotRequireForSafeTransaction": "Safe 交易无需矿工费", "gasPriceMedian": "最近 100 笔链上交易的中位数：", "gasPriceTitle": "Gas Price (Gwei)", "gasSelectorTitle": "矿工费", "hardwareSupport1559Alert": "确保你的硬件钱包固件已升级到支持 EIP 1559 的版本", "interactContract": "交互合约", "interacted": "之前交互过", "manuallySetGasLimitAlert": "你已手动将 Gas 限制设置为", "markAsBlock": "已标记为拉黑", "markAsTrust": "已标记为信任", "markRemoved": "标记已删除", "maxPriorityFee": "Max Priority Fee (Gwei)", "moreSafeSigNeeded": "还需要 {{0}} 次确认", "multiSigChainNotMatch": "多签地址不在本链上，无法发起交易", "myMark": "我的标记", "myNativeTokenBalance": "我的矿工费余额：", "nativeTokenNotEngouthForGas": "你的钱包里没有足够的 Gas", "neverInteracted": "之前没有交互过", "neverTransacted": "之前没有交易过", "nftApprove": {"approveNFT": "授权 NFT", "nftContractTrustValueTip": "信任值是指授权并暴露给该合约的资产总价值，\n信任值低表示该合约存在风险或 180 天内不活跃", "title": "授权 NFT"}, "nftCollection": "NFT collection", "nftCollectionApprove": {"approveCollection": "授权 NFT collection", "title": "授权 NFT Collection"}, "nftIn": "增加的NFT", "noGasRequired": "无需 Gas", "noMark": "无标记", "nonceLowerThanExpect": "Nonce 太低，最小值应为 {{0}}", "nonceTitle": "<PERSON><PERSON>", "popularity": "人气", "protocolTitle": "协议", "recommendGasLimitTip": "预估\n{{est}}。\n当前{{current}}x，推荐", "revokeNFTApprove": {"revokeNFT": "取消 NFT 授权", "title": "取消 NFT 授权"}, "revokeNFTCollectionApprove": {"revokeCollection": "取消的 NFT collection", "title": "取消 NFT Collection 授权 "}, "revokePermit2": {"title": "取消 Permit2 代币授权"}, "revokeTokenApprove": {"revokeFrom": "取消的合约", "revokeToken": "取消的 Token", "title": "取消代币授权"}, "safeAddressNotSupportChain": "{{0}} 链不支持当前 Safe 地址", "safeAdminSigned": "已签", "scamTokenAlert": "根据 Rabby 的检测，这可能是一个低质量的诈骗代币", "send": {"addressBalanceTitle": "地址余额", "cexAddress": "中心化交易所地址", "contractNotOnThisChain": "不在这个链上", "notOnThisChain": "不在本链上", "notOnWhitelist": "不在我的白名单中", "notTopupAddress": "非充值地址", "onMyWhitelist": "在我的白名单中", "receiverIsTokenAddress": "代币地址", "sendTo": "收款地址", "sendToken": "发送代币", "title": "发送代币", "tokenNotSupport": "不支持 {{0}}", "whitelistTitle": "白名单", "scamAddress": "诈骗地址", "fromMySeedPhrase": "使用我的种子短语", "fromMyPrivateKey": "使用我的私钥"}, "sendNFT": {"nftNotSupport": "不支持 NFT", "title": "发送 NFT"}, "sigCantDecode": "该签名无法被 Rabby 解码", "signTransactionOnChain": "签署 {{chain}} 交易", "speedUpTooltip": "本次加速交易和原交易，最终只会完成其中一项", "decodedTooltip": "该签名已被 Rabby Wallet 解析", "submitMultisig": {"multisigAddress": "多签地址", "title": "提交多签交易"}, "swap": {"failLoadReceiveToken": "加载失败", "minReceive": "最少收到", "notPaymentAddress": "非当前付款地址", "unknownAddress": "未知地址", "payToken": "卖出", "receiveToken": "买入", "receiver": "接收地址", "simulationFailed": "交易模拟失败", "simulationNotSupport": "该链暂不支持交易模拟", "slippageFailToLoad": "滑点加载失败", "slippageTolerance": "滑点", "title": "代币兑换", "valueDiff": "价差"}, "swapAndCross": {"title": "兑换 & 跨链"}, "tokenApprove": {"amountPopupTitle": "批准金额", "approveTo": "授权给", "approveToken": "授权代币", "contractTrustValueTip": "信任值是指授权并暴露给该合约的资产总价值，\n信任值低表示该合约存在风险或 180 天内不活跃", "deployTimeLessThan": "< {{value}} 天", "eoaAddress": "EOA", "flagByRabby": "被 Rabby 标记为风险", "myBalance": "我的余额", "title": "代币授权", "trustValueLessThan": "≤ {{value}}", "amount": "授权金额：", "exceed": "超过您当前的余额"}, "transacted": "之前交易过", "trustValue": "信任值", "trusted": "信任", "unknownAction": "未知签名类型", "unknownActionType": "未知的动作类型", "unwrap": "Unwrap", "viewRaw": "查看原始数据", "wrapToken": "Wrap 代币", "contractPopularity": "No.{{0}} on {{1}}", "myMarkWithContract": "我在 {{chainName}} 合约上的标记", "coboSafeNotPermission": "此委托地址无权发起此交易", "l2GasEstimateTooltip": "该L2链的交易Gas预估值不包括L1的验证费用，实际的Gas消耗比当前预估值要多", "BroadcastMode": {"instant": {"title": "立即广播", "desc": "交易创建后将被立即广播到链上节点"}, "lowGas": {"title": "省 Gas 广播", "desc": "交易创建后会等链上Gas较低的时广播从而节省Gas"}, "mev": {"title": "MEV 广播", "desc": "交易创建后会被广播到指定MEV节点"}, "tips": {"notSupportChain": "当前链不支持该模式", "customRPC": "使用自定义RPC时不支持该模式", "walletConnect": "Wallet Connect 签名不支持该模式", "notSupported": "不支持该模式"}, "lowGasDeadline": {"label": "最长等待时间", "1h": "1h", "4h": "4h", "24h": "24h"}, "title": "广播模式"}, "SafeNonceSelector": {"optionGroup": {"recommendTitle": "推荐的Nonce", "replaceTitle": "替换Queue中的交易"}, "error": {"pendingList": "Pending列表获取失败, <1/><2>重试</2>"}, "explain": {"contractCall": "合约调用", "send": "发送代币", "unknown": "未知交易"}, "option": {"new": "新交易"}}, "common": {"description": "描述", "descTipSafe": "该签名不涉及资产变动和身份验证权限", "descTipWarningPrivacy": "签名可能涉及身份验证权限", "descTipWarningAssets": "签名可能涉及资产变动", "descTipWarningBoth": "该签名不涉及资产变动和身份验证权限", "interactContract": "交互合约"}, "chain": "链", "protocol": "协议", "hasInteraction": "之前互动过", "no": "否", "yes": "是", "address": "地址", "advancedSettings": "高级设置", "amount": "数量", "trustValueTitle": "信任值", "addressSource": "地址来源", "maxPriorityFeeDisabledAlert": "请先设置 Gas Price", "customRPCErrorModal": {"title": "自定义RPC错误", "content": "您的自定义RPC当前不可用。您可以禁用它，并继续使用Rabby的官方RPC进行签名。", "button": "禁用自定义RPC"}, "revokePermit": {"title": "撤销许可令牌批准"}, "assetOrder": {"title": "资产顺序", "listAsset": "列出资产", "receiveAsset": "接收资产"}, "importedDelegatedAddress": "导入的委托地址", "noDelegatedAddress": "没有导入的委托地址", "coboSafeCreate": {"safeWalletTitle": "安全{钱包}", "descriptionTitle": "描述", "title": "创建 Cobo Safe"}, "coboSafeModificationRole": {"title": "提交安全角色修改", "safeWalletTitle": "安全{钱包}", "descriptionTitle": "描述"}, "coboSafeModificationDelegatedAddress": {"title": "提交委托地址修改", "safeWalletTitle": "安全{钱包}", "descriptionTitle": "描述"}, "coboSafeModificationTokenApproval": {"title": "提交代币批准修改", "safeWalletTitle": "安全{钱包}", "descriptionTitle": "描述"}, "contract": "智能合约", "typedDataMessage": "签署类型数据", "label": "标签", "gasAccount": {"currentTxCost": "发送到您地址的 Gas 数量：", "gasCost": "将 gas 转移到您地址的 Gas 成本：", "totalCost": "总费用：", "maxGas": "最大 Gas:", "sendGas": "当前交易向您转移的Gas：", "estimatedGas": "预估 Gas:"}, "transferOwner": {"transferTo": "转移到", "title": "资产所有权转移", "description": "描述"}, "swapLimitPay": {"maxPay": "最大支付", "title": "交换代币限额支付"}, "batchRevokePermit2": {"title": "批量撤销 Permit2 许可"}, "gasAccountForGas": "使用我 GasAccount 中的 USD 支付 gas 费", "nativeTokenForGas": "使用 {{tokenName}} 代币在 {{chainName}} 支付 gas 费用", "primaryType": "主要类型", "safeServiceNotAvailable": "当前无法提供安全服务，请稍后再试。", "safeTx": {"selfHostConfirm": {"title": "切换到 Rabby 的 Safe Service", "button": "OK", "content": "Safe API 暂不可用。切换到由 Rabby 部署的 Safe 服务以保持您的 Safe 功能正常。<strong>所有 Safe 签署者必须使用 Rabby Wallet 授权交易。</strong>"}}}, "signTypedData": {"buyNFT": {"expireTime": "过期时间", "listOn": "挂单于", "payToken": "卖出", "receiveNFT": "接收 NFT"}, "contractCall": {"operationDecoded": "操作解码于"}, "createKey": {"title": "创建密钥"}, "permit": {"title": "代币授权 Permit"}, "permit2": {"approvalExpiretime": "授权有效期", "sigExpireTime": "签名过期时间", "sigExpireTimeTip": "该签名在链上有效的时间", "title": "代币授权 Permit2"}, "safeCantSignText": "Safe 地址不支持用于签署文本", "sellNFT": {"listNFT": "挂出 NFT", "receiveToken": "接收代币", "specificBuyer": "指定买家", "title": "NFT 挂单"}, "signMultiSig": {"title": "确认交易"}, "signTypeDataOnChain": "在 {{chain}} 上签署类型数据", "swapTokenOrder": {"title": "代币订单"}, "verifyAddress": {"title": "验证地址"}, "safeCantSignTypedData": "这是一个 Safe 地址，只支持签名 EIP-712 类型数据或字符串。"}, "swap": {"Completed": "已完成", "InSufficientTip": "余额不足，无法进行交易模拟和 Gas 估算。\n将显示原始报价", "Pending": "待完成", "QuoteLessWarning": "通过Rabby交易模拟估算， \ndex 提供的报价是 {{receive}} ，\n你收到的报价将比预期少 {{diff}}", "actual-slippage": "实际滑点：", "amount-in": "卖出{{symbol}}金额", "approve-tips": "1.授权 → 2.兑换", "approve-x-symbol": "授权 {{symbol}}", "best": "最佳", "by-transaction-simulation-the-quote-is-valid": "通过模拟交易，已验证报价有效", "cex": "CEX", "chain": "链", "completedTip": "链上交易，解码数据生成记录", "confirm": "确认", "dex": "DEX", "directlySwap": "直接使用智能合约 Wrap {{symbol}} 代币", "edit": "编辑", "enable-exchanges": "启用交易所", "enable-it": "启用", "enable-trading": "允许直接交易", "est-difference": "预计差值：", "est-payment": "预计支付：", "est-receiving": "预计接收：", "exchanges": "交易所", "fail-to-simulate-transaction": "模拟交易失败", "gas-x-price": "Gas Price：{{price}} Gwei", "get-quotes": "获取报价", "i-understand-and-accept-it": "我理解并接受", "insufficient-balance": "余额不足", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "滑点过低，可能会因高波动性而导致交易失败", "minimum-received": "最少收到", "need-to-approve-token-before-swap": "兑换前需要授权代币", "no-transaction-records": "无交易记录", "not-supported": "不支持", "pendingTip": "交易已提交。\n如果交易长时间处于待完成状态，你可以尝试点击“更多”>“清除待完成”", "price-expired-refresh-quote": "价格已过期，\n刷新报价", "rabby-fee": "<PERSON><PERSON>手续费", "preferMEV": "优先 MEV 模式", "preferMEVTip": "开启后你在 Ethereum 链上的 Swap 交易将优先使用 Mev Guard 模式，这有助于减少你的交易被夹的风险。当你使用自定义 RPC 或者手机钱包地址时，则不支持该模式。", "rate": "汇率", "rates-from-cex": "来自 CEX 的汇率", "recommend-slippage": "为了防止被抢先交易，我们建议滑点为 <2>{{ slippage }}</2>%", "search-by-name-address": "搜索名称/地址", "security-verification-failed": "安全验证失败", "select-token": "选择代币", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "所选报价与当前价格差异较大，可能造成重大损失", "slippage-adjusted-refresh-quote": "滑点调整，\n刷新报价", "slippage-tolerance": "滑点", "slippage_tolerance": "滑点：", "swap-from": "卖出", "swap-history": "兑换历史", "swap-via-x": "通过 {{name}} 兑换", "testnet-is-not-supported": "不支持自定义网络", "the-following-swap-rates-are-found": "发现以下兑换报价", "sort-with-gas": "排序考虑gas", "there-is-no-fee-and-slippage-for-this-trade": "此交易不收取任何费用和滑点", "this-exchange-is-not-enabled-to-trade-by-you": "你未启用该交易所进行交易", "this-token-pair-is-not-supported": "不支持该代币对", "title": "兑换", "to": "买入", "trade": "交易", "tradingSettingTip1": "1. 开启后，你将直接与交易所合约进行交互", "tradingSettingTip2": "2. <PERSON><PERSON> 不承担因交易所合约产生的任何风险", "tradingSettingTips": "共 {{viewCount}} 个交易所提供报价，{{tradeCount}} 个允许直接交易", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "由于滑点较高，交易可能会被抢先交易", "unable-to-fetch-the-price": "无法获取价格", "unlimited-allowance": "无限额度", "view-quotes": "查看报价", "wrap-contract": "Wrap 合约", "actual": "实际：", "gas-fee": "矿工费：{{gasUsed}}", "estimate": "预估：", "no-slippage-for-wrap": "Wrap 无滑点", "rabbyFee": {"wallet": "钱包", "rate": "费率", "title": "Rabby fee", "button": "知道了", "bridgeDesc": "Rabby Wallet将始终从顶级聚合器中找到最佳汇率，并验证其报价的可靠性。Rabby收取0.25%的费用，该费用会自动包含在报价中。", "swapDesc": "Rabby Wallet将始终从顶级聚合器中找到最佳汇率，并验证其报价的可靠性。Rabby收取0.25%的费用（包装不收取费用），该费用已自动包含在报价中。"}, "lowCreditModal": {"title": "该代币的信用价值较低", "desc": "低信用值通常意味着高风险，例如蜜罐代币或非常低的流动性。"}, "usd-after-fees": "≈ {{usd}}", "approve-swap": "批准并交换", "Auto": "自动", "price-impact": "价格影响", "Gas-fee-too-high": "Gas fee 太高", "source": "来源", "from": "从", "no-fee-for-wrap": "Wrap 操作不收取 Rabby 费用", "no-quote-found": "未找到报价", "process-with-two-step-approve": "进行两步批准", "approve-and-swap": "通过 {{name}} 批准并兑换", "hidden-no-quote-rates_one": "{{count}} rate unavailable", "two-step-approve": "签署 2 笔交易以更改授权额度", "loss-tips": "您正在亏损 {{usd}}。尝试在小市场中使用较小的金额吧。", "no-fees-for-wrap": "Wrap 不收取 Rabby 费用", "max": "最大值", "hidden-no-quote-rates_other": "{{count}} 费率不可用", "No-available-quote": "暂无报价", "fetch-best-quote": "正在获取最佳报价", "two-step-approve-details": "Token USDT 需要 2 笔交易来更改 allowance。首先，您需要将 allowance 重置为零，然后才能设置新的 allowance 值。"}, "switchChain": {"chainNotSupport": "<PERSON><PERSON>尚不支持请求的链", "title": "向 Rabby 添加自定义网络", "testnetTip": "请在\"更多\"中打开“开启测试网”后再发起测试网连接", "chainNotSupportYet": "<PERSON><PERSON>尚未支持请求的链", "chainId": "链 ID：", "unknownChain": "未知链", "requestsReceived": "收到1个请求", "requestsReceivedPlural": "收到{{count}}个请求", "requestRabbyToSupport": "请求Rabby提供支持", "chainNotSupportAddChain": "Rabby尚未集成请求的链。您可以将其添加为自定义测试网络", "addChain": "添加测试网络", "desc": "请求的网络尚未被Rabby集成。您可以手动将其添加为自定义网络"}, "unlock": {"btn": {"unlock": "解锁"}, "password": {"error": "密码错误", "placeholder": "输入密码解锁", "required": "输入密码解锁"}, "btnForgotPassword": "忘记密码？", "description": "革命性改变的Ethereum及所有EVM链的钱包", "title": "<PERSON><PERSON>"}, "addressDetail": {"add-to-whitelist": "添加到白名单", "remove-from-whitelist": "移除白名单", "address-detail": "地址详情", "backup-private-key": "备份私钥", "backup-seed-phrase": "备份助记词", "tx-requires": "任何交易都需要 <2>{{num}}</2> 次确认", "address": "地址", "address-note": "地址备注", "admins": "管理员", "assets": "余额", "delete-address": "删除地址", "delete-desc": "在删除之前，请记住以下几点，以了解如何保护你的资产", "direct-delete-desc": "该地址是一个{{renderBrand}}地址，Rabby不存储该地址的私钥或助记词，你可以将其删除", "edit-memo-title": "编辑地址备注", "hd-path": "HDpath", "manage-addresses-under-this-seed-phrase": "管理此助记词下的地址", "manage-seed-phrase": "管理助记词", "please-input-address-note": "请输入地址备注", "qr-code": "二维码", "source": "来源", "coboSafeErrorModule": "地址已过期，请删除并重新导入地址。", "importedDelegatedAddress": "导入的委托地址", "safeModuleAddress": "Safe Module Address", "manage-addresses-under": "在此 {{brand}} 下管理地址"}, "preferMetamaskDapps": {"title": "优先使用MetaMask连接的Dapp", "howToAddDesc": "在网页空白处点击右键，找到该选项", "desc": "无论当前启用Rabby或者MetaMask，以下Dapp将保持连接至MetaMask", "howToAdd": "如何添加", "empty": "No dapps"}, "customRpc": {"title": "修改 RPC URL", "desc": "一旦修改，自定义 RPC 将替换 Rabby 的节点。要继续使用 Rabby 的节点，请删除自定义 RPC。", "add": "修改 RPC URL", "empty": "没有自定义 RPC URL", "opened": "已打开", "closed": "已关闭", "EditRPCModal": {"invalidRPCUrl": " RPC URL 无效", "invalidChainId": "Chain ID 无效", "rpcAuthFailed": "RPC 验证失败", "title": "修改 RPC URL", "rpcUrl": "RPC URL", "rpcUrlPlaceholder": "输入 RPC URL"}, "EditCustomTestnetModal": {"title": "添加自定义网络", "quickAdd": "从 Chainlist 快速添加"}}, "requestDebankTestnetGasToken": {"title": "获取 DeBank Testnet Gas Token", "mintedTip": "Rabby Badge持有者可以每天获取一次", "notMintedTip": "仅 <PERSON>bby Badge持有者可以获取", "claimBadgeBtn": "领取 <PERSON><PERSON>", "time": "每日额度", "requested": "你今日已获取过", "requestBtn": "获取"}, "safeQueue": {"title": "Queue", "sameNonceWarning": "这些交易nonce相同，无法同时完成。                   一笔交易完成后，其他交易将被替代", "loading": "加载待完成交易", "noData": "暂无待完成交易", "loadingFaild": "由于 Safe 服务器不稳定，暂时无法获取数据，请5分钟后重试", "accountSelectTitle": "你可以用任意地址提交交易", "LowerNonceError": "Nonce {{nonce}} 交易需要优先完成", "submitBtn": "提交交易", "unknownTx": "未知交易", "cancelExplain": "取消授权 {{token}} 给 {{protocol}}", "unknownProtocol": "未知协议", "approvalExplain": "授权 {{count}} {{token}} 给 {{protocol}}", "unlimited": "无限额度", "action": {"send": "发送", "cancel": "取消挂起交易"}, "viewBtn": "查看", "replaceBtn": "替换", "ReplacePopup": {"title": "请选择替换交易的方式", "desc": "已经签名的交易无法被清除，但是可以通过发起一笔相同 Nonce的交易来替换。", "options": {"send": "发送代币", "reject": "拒绝交易"}}}, "importSuccess": {"title": "成功导入", "addressCount": "{{count}} 个地址", "gnosisChainDesc": "该地址部署于  {{count}} 条链上"}, "backupPrivateKey": {"alert": "此私钥是您资产的凭证。请勿丢失或泄露给他人，否则可能永远失去您的资产。请在安全环境中查看并小心保管。", "clickToShowQr": "点击展示私钥二维码", "clickToShow": "点击展示私钥", "title": "备份私钥"}, "backupSeedPhrase": {"alert": "此种子短语是您资产的凭证。请勿丢失或泄露给他人，否则可能永远失去您的资产。请在安全环境中查看并小心保管。", "clickToShow": "点击展示助记词", "copySeedPhrase": "复制助记词", "title": "备份助记词", "showQrCode": "显示二维码", "qrCodePopupTitle": "二维码", "qrCodePopupTips": "永远不要与任何其他人分享种子短语的二维码。请在安全的环境中查看并小心保管。"}, "ethSign": {"alert": " 'eth_sign' 类型签名会导致资产损失。为了你的安全， Rabby 不支持此类签名"}, "createPassword": {"agree": "我已阅读并同意 <1/> <2>Terms of Use</2>", "confirmPlaceholder": "确认密码", "passwordMin": "密码长度至少需要8个字符", "confirmError": "密码不匹配", "confirmRequired": "请确认密码", "title": "设置密码", "passwordRequired": "请输入密码", "passwordPlaceholder": "密码长度至少需要8个字符"}, "welcome": {"step2": {"title": "自托管", "desc": "私钥存储在本地，只有您能访问", "btnText": "开始使用"}, "step1": {"title": "访问所有 Dapp", "desc": "Rabby 可连接到 MetaMask 支持的所有 Dapp"}}, "importQrBase": {"btnText": "重试", "desc": "请扫描 {{brandName}} 硬件钱包的二维码"}, "importSafe": {"error": {"invalid": "无效地址", "required": "请输入地址"}, "gnosisChainDesc": "该地址部署于  {{count}} 条链上", "loading": "正在查询地址部署链", "placeholder": "请输入地址", "title": "添加 Safe 地址"}, "pendingDetail": {"Header": {"predictTime": "预计打包时间"}, "TxStatus": {"completed": "已完成", "pendingBroadcasted": "待处理：已广播", "pendingBroadcast": "待处理：待广播", "reBroadcastBtn": "重新广播"}, "TxHash": {"hash": "交易哈希"}, "TxTimeline": {"pending": "正在检查状态...", "created": "交易已创建", "broadcasted": "最近已广播", "broadcastedCount_ordinal_one": "{{count}}次广播", "broadcastedCount_ordinal_two": "{{count}}次广播", "broadcastedCount_ordinal_few": "{{count}}次广播", "broadcastedCount_ordinal_other": "{{count}}次广播"}, "MempoolList": {"col": {"nodeName": "节点名称", "nodeOperator": "节点操作者", "txStatus": "交易状态"}, "txStatus": {"appeared": "已出现", "appearedOnce": "仅出现一次", "notFound": "未找到"}, "title": "出现在 {{count}} 个RPC节点中"}, "PendingTxList": {"title": "所有待处理交易中的Gas价格排名 #{{rank}}", "titleNotFound": "所有待处理交易中无排名", "filterBaseFee": {"label": "仅满足基础费用要求", "tooltip": "仅显示Gas价格符合区块基础费用要求的交易"}, "col": {"gasPrice": "Gas 价格", "action": "交易操作", "balanceChange": "余额变动", "actionType": "操作类型", "interact": "与之交互"}, "titleSame": "与当前相同Gas价格排名 #{{rank}}", "titleSameNotFound": "当前Gas价格无排名"}, "Empty": {"noData": "未找到数据"}, "PrePackInfo": {"col": {"prePackContent": "预打包内容", "expectations": "期望", "prePackResults": "预打包结果", "difference": "检查结果"}, "type": {"pay": "支付", "receive": "接收"}, "noLoss": "未发现损失", "noError": "未发现错误", "title": "预打包检查", "error": "{{count}} 个错误发现", "loss": "{{lossCount}} 个损失发现", "desc": "在最新区块中执行的模拟，更新于 {{time}}"}, "Predict": {"completed": "交易已完成", "predictFailed": "打包时间预测失败", "skipNonce": "您的地址在以太坊链上的Nonce已跳过，导致当前交易无法完成"}}, "dappSearch": {"selectChain": "选择链", "searchResult": {"foundDapps": "找到 <2>{{count}}</2> 个Dapp", "totalDapps": "总共 <2>{{count}}</2> 个Dapp"}, "expand": "展开", "emptyFavorite": "没有收藏的Dapp", "favorite": "收藏", "emptySearch": "未找到Dapp", "listBy": "Dapp已由以下列表"}, "rabbyPoints": {"title": "<PERSON><PERSON> 积分", "out-of-x-current-total-points": "在{{total}}总分发积分中", "share-on": "分享到", "referral-code-copied": "已复制推荐码", "earn-points": "赚取积分", "top-100": "前100名", "claimItem": {"claim": "认领", "disabledTip": "当前无法认领积分", "go": "前往", "earnTip": "每天限制一次。请在UTC+0时区00:00后再赚取积分", "claimed": "已认领"}, "claimModal": {"title": "认领初始积分", "snapshotTime": "快照时间：{{time}}", "placeholder": "输入推荐码获取额外积分（可选）", "claim": "认领", "addressBalance": "钱包余额", "MetaMaskSwap": "MetaMask 交换", "rabbyUser": "<PERSON><PERSON> 活跃用户", "rabbyValuedUserBadge": "Ra<PERSON> 重要用户徽章", "rabbyDesktopGenesisNft": "Rabby 桌面起源NFT", "walletBalance": "钱包余额", "activeStats": "活跃状态", "referral-code": "推荐码", "invalid-code": "无效的推荐码", "cantUseOwnCode": "您不能使用自己的推荐码。", "season2": "第二季"}, "referralCode": {"referral-code-cannot-be-empty": "推荐码不能为空", "referral-code-cannot-exceed-15-characters": "推荐码不能超过15个字符", "referral-code-already-exists": "推荐码已存在", "referral-code-available": "推荐码可用", "my-referral-code": "我的推荐码", "refer-a-new-user-to-get-50-points": "推荐新用户获取50积分", "set-my-code": "设置我的推荐码", "set-my-referral-code": "设置我的推荐码", "once-set-this-referral-code-is-permanent-and-cannot-change": "一旦设置，此推荐码将永久不变。", "max-15-characters-use-numbers-and-letters-only": "最多15个字符，只能使用数字和字母。", "confirm": "确认", "verifyAddressModal": {"verify-address": "验证地址", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "请签署此文本消息以验证您是此地址的所有者", "cancel": "取消", "sign": "签名"}}, "code-set-successfully": "推荐码设置成功", "initialPointsClaimEnded": "初始积分认领已结束", "firstRoundEnded": "🎉 第一轮Rabby积分已结束", "secondRoundEnded": "🎉 第二轮Rabby积分已结束"}, "customTestnet": {"title": "自定义网络", "desc": "Rabby 无法验证自定义网络的安全性，请仅添加信任的网络。", "add": "添加自定义网络", "empty": "没有自定义网络", "currency": "货币", "id": "ID", "CustomTestnetForm": {"id": "链ID", "name": "网络名称", "rpcUrl": "RPC URL", "idRequired": "请输入链ID", "nameRequired": "请输入网络名称", "rpcUrlRequired": "请输入RPC URL", "nativeTokenSymbol": "货币符号", "nativeTokenSymbolRequired": "请输入货币符号", "blockExplorerUrl": "区块浏览器URL（可选）"}, "AddFromChainList": {"title": "从Chainlist快速添加", "search": "搜索自定义网络名称或ID", "empty": "未找到链", "tips": {"added": "您已经添加了此链", "supported": "该链已被Rabby钱包集成"}}, "signTx": {"title": "交易数据"}, "ConfirmModifyRpcModal": {"desc": "该链已被Rabby集成。您是否需要修改其RPC URL？"}}, "addChain": {"title": "将自定义网络添加到Rabby", "desc": "Rabby 无法验证自定义网络的安全性，请仅添加信任的网络。"}, "sign": {"transactionSpeed": "交易速度"}, "forgotPassword": {"home": {"buttonNoData": "设置新密码", "button": "开始重置流程", "title": "忘记密码", "description": "Rabby不会保存你的密码,因此丢失密码无法找回 如果忘记密码只能重置钱包并重新设置密码", "descriptionNoData": "Rabby不会保存你的密码,因此丢失密码无法找回 如果忘记密码只能重新设置密码"}, "reset": {"title": "重置 <PERSON><PERSON>", "alert": {"title": "重置后会清除以下数据,且无法帮你找回", "seed": "助记词", "privateKey": "私钥"}, "tip": {"title": "会保留以下数据", "hardware": "已导入的硬件钱包地址", "safe": "已导入的 Safe 地址", "watch": "已导入的观察地址", "whitelist": "转账白名单", "records": "签名记录"}, "confirm": "如确认重置钱包, 请输入: <1>RESET</1>", "button": "确认重置"}, "success": {"title": "密码创建成功", "description": "请继续使用 Rabby", "button": "完成"}, "tip": {"title": "Rabby Wallet 已经重置", "button": "设置新密码", "buttonNoData": "重新导入地址", "description": "请设定一个新的密码继续使用钱包", "descriptionNoData": "请重新导入地址后使用钱包"}}, "bridge": {"showMore": {"source": "Bridge Source", "title": "显示更多"}, "settingModal": {"confirmModal": {"i-understand-and-accept-it": "我理解并接受", "tip1": "一旦启用，您将直接从此聚合器与合约交互。", "title": "使用此 Aggregator 启用交易", "tip2": "2. <PERSON><PERSON> 不对该聚合器的合约相关风险承担任何责任。"}, "title": "启用 Bridge Aggregators 进行交易", "SupportedBridge": "支持的桥：", "confirm": "确认"}, "tokenPairDrawer": {"balance": "余额价值", "title": "从支持的token对中选择", "tokenPair": "Token Pair", "noData": "不支持的代币对"}, "From": "从", "To": "至", "select-chain": "选择链", "no-quote-found": "未找到报价。请尝试其他代币对。", "Select": "选择", "Balance": "余额：", "Pending": "待处理", "gas-fee": "GasFee: {{gasUsed}}", "approve-and-bridge": "批准并跨链", "getRoutes": "获取路线", "no-quote": "No Quote", "actual": "实际：", "pendingTip": "交易已提交。如果交易长时间处于待处理状态，您可以尝试在设置中清除待处理。", "tokenPairPlaceholder": "选择 Token 对", "gas-x-price": "Gas price: {{price}} Gwei。", "detail-tx": "详情", "title": "Bridge", "history": "跨链桥历史记录", "Amount": "金额", "insufficient-balance": "余额不足", "completedTip": "链上交易，解码数据生成记录", "unlimited-allowance": "无限额度", "BridgeTokenPair": "桥接 Token 对", "Completed": "已完成", "price-expired-refresh-route": "价格已过期。刷新路线。", "slippage-adjusted-refresh-quote": "滑点已调整。刷新路线。", "no-transaction-records": "暂无交易记录", "bridgeTo": "桥接到", "approve-x-symbol": "批准 {{symbol}}", "the-following-bridge-route-are-found": "找到以下路线", "estimate": "估算：", "rabby-fee": "Rabby fee", "best": "最佳", "via-bridge": "通过 {{bridge}}", "bridge-cost": "Bridge cost", "need-to-approve-token-before-bridge": "在桥接之前需要批准 token", "price-impact": "价格影响", "est-difference": "预估差异：", "aggregator-not-enabled": "此聚合器未被您启用交易功能。", "bridge-via-x": "在 {{name}} 上桥接", "enable-it": "启用它", "estimated-value": "≈ {{value}}", "no-route-found": "未找到路由", "est-payment": "预计付款：", "loss-tips": "您正在损失{{usd}}。请尝试不同的金额。", "max-tips": "此值通过减去桥接的 gas 成本来计算。", "duration": "{{duration}} 分钟", "recommendFromToken": "从 <1></1> 桥接以获取可用报价", "est-receiving": "预计接收："}, "ecology": {"sonic": {"home": {"socialsTitle": "参与其中", "arcadeBtn": "立即开始", "earnTitle": "赚取", "airdropBtn": "赚取积分", "airdrop": "空投", "earnDesc": "质押你的 $S", "migrateBtn": "即将推出", "migrateTitle": "迁移", "migrateDesc": "→", "airdropDesc": "~2亿美元 S 给 Opera 和 Sonic 的用户。", "arcadeDesc": "玩免费游戏以赚取 S 空投积分。", "earnBtn": "即将推出"}, "points": {"referralCodeCopied": "推荐码已复制", "pointsDashboardBtn": "开始赚取积分", "getReferralCode": "获取 referral code", "errorTitle": "无法加载积分", "sonicPoints": "Sonic Points", "today": "今天", "sonicArcadeBtn": "开始使用", "shareOn": "分享至", "pointsDashboard": "积分仪表板", "sonicArcade": "Sonic Arcade", "referralCode": "推荐码", "retry": "重试", "errorDesc": "加载您的积分时出错。请重试。"}}, "dbk": {"home": {"bridge": "桥接至 DBK 链", "mintNFTBtn": "Mint", "bridgeBtn": "Bridge", "mintNFTDesc": "成为 DBK Chain 的见证者", "mintNFT": "铸造 DBK Genesis NFT", "bridgePoweredBy": "由 OP Superchain 提供支持"}, "bridge": {"tabs": {"deposit": "存款", "withdraw": "提现"}, "info": {"gasFee": "Gas fee", "receiveOn": "在 {{chainName}} 上接收", "completeTime": "完成时间", "toAddress": "收件地址"}, "error": {"notEnoughBalance": "余额不足"}, "ActivityPopup": {"status": {"readyToProve": "准备好证明", "proved": "已验证", "withdraw": "提款", "deposit": "存款", "challengePeriod": "挑战期", "claimed": "已领取", "readyToClaim": "准备领取", "waitingToProve": "状态根已发布", "rootPublished": "状态根已发布"}, "proveBtn": "证明", "withdraw": "提现", "empty": "暂无活动", "deposit": "存入", "claimBtn": "领取", "title": "活动"}, "WithdrawConfirmPopup": {"question1": "我明白在我证明提款后，大约需要 7 天我的资金才能在 Ethereum 上可申领", "step1": "发起提款", "step2": "在 Ethereum 上证明", "tips": "提取涉及一个三步流程，需要1次DBK Chain交易和2次Ethereum交易。", "step3": "在 Ethereum 上领取", "question3": "我理解网络费用是大致的，并且会有所变化。", "question2": "我理解，一旦提款开始，就不能加速或取消。", "btn": "提现", "title": "DBK Chain Withdrawal 需要大约7天"}, "labelFrom": "从", "labelTo": "至"}, "minNFT": {"minted": "已铸造", "mintBtn": "铸造", "myBalance": "我的余额", "title": "DBK Genesis"}}}, "miniSignFooterBar": {"status": {"txSendings": "发送签名请求({{current}}/{{total}})", "txSending": "发送签名请求", "txCreated": "交易已创建", "txSigned": "已签名。创建交易中"}, "signWithLedger": "使用 Ledger 签名"}, "gasAccount": {"history": {"noHistory": "无历史记录"}, "loginInTip": {"gotIt": "知道了", "title": "存入 USDC / USDT", "desc": "在所有链上支付 Gas Fees", "login": "登录 GasAccount"}, "loginConfirmModal": {"title": "使用当前地址登录", "desc": "一旦确认，您只能将其存入此地址"}, "logoutConfirmModal": {"logout": "注销", "title": "注销当前GasAccount", "desc": "注销会禁用GasAccount。您可以通过使用此地址登录来恢复您的GasAccount。"}, "depositPopup": {"title": "存款", "selectToken": "选择代币进行存款", "invalidAmount": "必须在 500 以下", "token": "Token", "desc": "无需额外费用即可向 Rabby 的 DeBank L2 账户存款—随时提款。", "amount": "金额"}, "withdrawPopup": {"title": "提取", "selectRecipientAddress": "选择收件人地址", "selectAddr": "选择地址", "noEligibleAddr": "没有可提取的地址", "selectChain": "选择链条", "withdrawalLimit": "提取限额", "recipientAddress": "收件人地址", "noEnoughGas": "金额过低，无法支付 gas 费用", "noEnoughValuetBalance": "Vault Balance 不足。切换链或稍后重试。", "to": "至", "deductGasFees": "接收到的金额将扣除 gas 费用", "noEligibleChain": "没有符合条件的链可以提取", "riskMessageFromAddress": "由于风险控制，提款上限取决于该地址的总存款金额。", "riskMessageFromChain": "由于风险控制，提款限额取决于从该链存入的总金额。", "amount": "金额", "destinationChain": "目标链", "selectDestinationChain": "选择目标链", "desc": "您可以将您的 GasAccount 余额提取到您的 DeBank L2 Wallet。请登录到您的 DeBank L2 Wallet，根据需要将资金转移到支持的区块链。"}, "withdrawConfirmModal": {"title": "已转移至您的 DeBank L2 钱包", "button": "在 DeBank 查看"}, "GasAccountDepositTipPopup": {"gotIt": "明白了", "title": "打开 GasAccount 并存入"}, "switchLoginAddressBeforeDeposit": {"title": "存款前切换地址", "desc": "请切换到您的登录地址。"}, "title": "GasAccount", "noBalance": "无余额", "deposit": "存款", "gasExceed": "GasAccount余额不能超过$1000", "withdraw": "提取", "safeAddressDepositTips": "多签地址不支持存款。", "logout": "退出当前 GasAccount", "risk": "您的当前地址已被检测为风险地址，因此该功能不可用。", "gasAccountList": {"gasAccountBalance": "Gas Balance", "address": "地址"}, "switchAccount": "切换 GasAccount", "withdrawDisabledIAP": "提款功能已禁用，因为您的余额中包含无法提取的法定资金。请联系支持以提取您的代币余额。"}, "safeMessageQueue": {"loading": "加载消息", "noData": "没有消息"}, "newUserImport": {"guide": {"importAddress": "我已经有一个地址", "title": "欢迎使用 <PERSON><PERSON> Wallet", "createNewAddress": "创建一个新地址", "desc": "颠覆性的以太坊及所有 EVM 链的钱包"}, "createNewAddress": {"showSeedPhrase": "显示 Seed Phrase", "title": "在开始之前", "desc": "请阅读并牢记以下安全提示", "tip2": "我的种子短语仅存储在我的设备上。Rabby 无法访问它。", "tip1": "如果我丢失或分享我的种子短语，我将永久失去对我的资产的访问权限。", "tip3": "如果我在没有备份我的种子短语的情况下卸载 Rabby，则无法通过 Rabby 恢复。"}, "importList": {"title": "选择导入方式"}, "importPrivateKey": {"title": "导入私钥", "pasteCleared": "已粘贴并清除剪贴板"}, "PasswordCard": {"form": {"password": {"label": "密码", "required": "请输入密码", "min": "密码长度必须至少为 8 个字符", "placeholder": "密码（最少8个字符）"}, "confirmPassword": {"label": "确认密码", "required": "请确认密码", "notMatch": "密码不匹配", "placeholder": "确认密码"}}, "title": "设置密码", "agree": "我同意<1/><2>使用条款</2>和<4>隐私政策</4>", "desc": "它将用于解锁钱包和加密数据"}, "successful": {"import": "导入成功", "start": "开始使用", "addMoreAddr": "从这个 Seed Phrase 添加更多地址", "create": "创建成功", "addMoreFrom": "从 {{name}} 添加更多地址"}, "readyToUse": {"pin": "固定 Ra<PERSON> 钱包", "guides": {"step2": "固定 <PERSON><PERSON>", "step1": "点击浏览器扩展图标"}, "desc": "找到 Rabby Wallet 并将其固定", "extensionTip": "点击 <1/> 然后 <3/>", "title": "您的 Rabby Wallet 已准备就绪！"}, "importSeedPhrase": {"title": "导入助记词"}, "importOneKey": {"connect": "连接 OneKey", "title": "OneKey", "tip2": "2. 插入你的 OneKey 设备", "tip3": "3. 解锁您的设备", "tip1": "1. 安装 <1>OneKey Bridge<1/>"}, "importTrezor": {"title": "<PERSON><PERSON><PERSON>", "tip2": "2. 解锁你的设备", "connect": "连接 Trezor", "tip1": "1. 插入你的 Trezor 设备"}, "ImportGridPlus": {"connect": "连接 GridPlus", "title": "GridPlus", "tip1": "1. 打开您的 GridPlus 设备", "tip2": "2. 通过 Lattice Connector 连接"}, "importLedger": {"title": "Ledger", "connect": "连接 Ledger", "tip2": "输入您的 PIN 码以解锁。", "tip1": "连接您的Ledger设备。", "tip3": "打开 Ethereum 应用。"}, "importBitBox02": {"title": "BitBox02", "connect": "连接 BitBox02", "tip3": "3. 解锁您的设备", "tip2": "2. 插入你的 BitBox02", "tip1": "1. 安装 <1>BitBoxBridge<1/>"}, "importKeystone": {"qrcode": {"desc": "扫描 Keystone 硬件钱包上的二维码"}, "usb": {"tip2": "请输入密码以解锁", "tip1": "请插入您的 Keystone 设备", "tip3": "批准与您的计算机连接", "connect": "连接 Keystone", "desc": "确保您的 Keystone 3 Pro 位于主页上"}}, "importSafe": {"error": {"invalid": "不是有效的地址", "required": "请输入地址"}, "title": "添加 Safe 地址", "placeholder": "输入安全地址", "loading": "正在搜索该地址的已部署链"}}, "metamaskModeDapps": {"title": "管理已授权的 Dapps", "desc": "MetaMask 模式已为以下 Dapps 启用。您可以通过选择 MetaMask 选项连接 Rabby。"}, "eip7702": {"alert": "EIP-7702 尚未支持"}, "metamaskModeDappsGuide": {"toast": {"enabled": "伪装已启用。刷新 Dapp 以重新连接。", "disabled": "伪装已禁用。刷新 Dapp。"}, "step1": "第 1 步", "noDappFound": "未找到 Dapp", "step1Desc": "允许 Rabby 在当前 Dapp 上伪装成 MetaMask", "alert": "无法连接到 Dapp，因为它未显示 Rabby Wallet 作为选项？", "step2": "步骤 2", "title": "通过伪装成 MetaMask 来连接 Rabby", "step2Desc": "刷新并通过 MetaMask 连接", "manage": "管理 Allowed Dapps"}, "syncToMobile": {"steps2": "2. 使用 Rabby Mobile 扫描", "clickToShowQr": "单击选择地址并显示QR代码", "description": "您的地址数据始终完全离线、加密，并通过二维码安全传输。", "downloadGooglePlay": "Google Play", "steps2Description": "您的二维码包含敏感数据。请保管好，绝不要与他人分享。", "steps1": "下载 Rabby Mobile", "downloadAppleStore": "App Store", "title": "从 Rabby 扩展同步钱包地址到移动端", "disableSelectAddress": "不支持{{type}}地址的同步", "disableSelectAddressWithPassphrase": "与密码不支持{{type}}地址的同步", "disableSelectAddressWithSlip39": "不支持{{type}}地址的同步39", "selectedLenAddressesForSync_one": "选择{{len}}同步地址", "selectedLenAddressesForSync_other": "选择{{len}}地址同步", "selectAddress": {"title": "选择要同步的地址"}}, "search": {"sectionHeader": {"NFT": "NFT", "token": "代币", "AllChains": "所有链", "Defi": "<PERSON><PERSON><PERSON>"}, "header": {"placeHolder": "搜索", "searchPlaceHolder": "搜索 Token 名称 / 地址"}, "tokenItem": {"Issuedby": "发行方", "listBy": "由 {{name}} 列出", "FDV": "FDV", "verifyDangerTips": "这是一个骗局代币", "scamWarningTips": "这是一个低质量的代币，可能是一个骗局。", "gasToken": "Gas Token"}, "searchWeb": {"noResults": "没有结果", "searchTips": "在网页上搜索", "searching": "结果为", "noResult": "没有结果สำหรับ", "title": "所有结果"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "没有匹配地址", "AddressItem": {"whitelistedAddressTip": "白名单地址"}}, "AccountSelectDrawer": {"btn": {"cancel": "取消", "proceed": "继续"}}, "AddressList": {"AddressItem": {"addressTypeTip": "由 {{type}} 导入"}}, "AuthenticationModal": {"passwordError": "密码错误", "passwordRequired": "请输入密码", "passwordPlaceholder": "输入密码进行确认"}, "ConnectStatus": {"connecting": "连接中...", "connect": "连接", "gridPlusConnected": "GridPlus 已连接", "gridPlusNotConnected": "GridPlus 未连接", "ledgerNotConnected": "Ledger 未连接", "ledgerConnected": "Ledger 已连接", "keystoneConnected": "Keystone 已连接", "keystoneNotConnected": "Keystone 未连接", "imKeyrNotConnected": "imKey 未连接", "imKeyConnected": "imKey 已连接"}, "Contact": {"AddressItem": {"notWhitelisted": "该地址未加入白名单", "whitelistedTip": "白名单地址"}, "EditModal": {"title": "编辑地址备注"}, "EditWhitelist": {"backModalTitle": "放弃修改", "backModalContent": "你所做的改动不会被保存", "title": "编辑白名单", "tip": "选择地址加入白名单，点击保存", "save": "保存至白名单 ({{count}})"}, "ListModal": {"title": "选择地址", "whitelistEnabled": "白名单已开启。你只能转账给白名单中的地址。你也可以在 “更多” 中关闭白名单功能", "whitelistDisabled": "白名单已关闭。你可以转账给所有地址", "editWhitelist": "编辑白名单", "whitelistUpdated": "白名单已更新", "authModal": {"title": "保存至白名单"}}}, "LoadingOverlay": {"loadingData": "加载数据..."}, "MultiSelectAddressList": {"imported": "已导入"}, "NFTNumberInput": {"erc1155Tips": "你持有数量为 {{amount}}", "erc721Tips": "ERC 721 NFT 只支持单个发送"}, "TiledSelect": {"errMsg": "助记词无效，请检查！"}, "Uploader": {"placeholder": "选择 JSON 问姐"}, "WalletConnectBridgeModal": {"title": "Bridge server URL", "requiredMsg": "请输入 bridge server host", "invalidMsg": "请检查你的 host", "restore": "恢复初始设置"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "集成网络", "testnet": "自定义网络"}}, "ChainSelectorModal": {"searchPlaceholder": "搜索链", "noChains": "无匹配", "addTestnet": "添加自定义网络"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "资产/金额"}, "price": {"title": "价格"}, "usdValue": {"title": "美元价值"}}, "searchInput": {"placeholder": "搜索名称/地址"}, "header": {"title": "选择代币"}, "noTokens": "无代币", "noMatch": "无匹配", "noMatchSuggestion": "尝试搜索 {{ chainName }} 上的合约地址", "bridge": {"high": "高", "low": "低", "value": "价值", "liquidityTips": "历史交易量越高，桥接成功的可能性就越大。", "liquidity": "流动性", "token": "Token"}, "recent": "最近", "hot": "热门", "common": "常用", "chainNotSupport": "此链不受支持"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "系列", "Chain": "链", "PurschaseDate": "购买日期", "LastPrice": "最后价格"}}, "signPermissionCheckModal": {"title": "你已经设置该Dapp仅签名测试网", "reconnect": "重新连接"}, "testnetCheckModal": {"title": "请在\"更多\"中打开“开启测试网”后再发起测试网签名"}, "EcologyNavBar": {"providedBy": "由 {{chainName}} 提供"}, "EcologyNoticeModal": {"notRemind": "不再提醒我", "desc": "以下服务将由第三方生态合作伙伴直接提供。Rabby Wallet 不对这些服务的安全性承担责任。", "title": "注意事项"}, "ReserveGasPopup": {"doNotReserve": "不要保留Gas", "normal": "正常", "instant": "即时", "fast": "快速", "title": "Reserve Gas"}, "OpenExternalWebsiteModal": {"button": "继续", "content": "您即将访问一个外部网站。Rabby Wallet 不对该网站的内容或安全性负责。", "title": "您将离开 Rabby 钱包"}, "TokenChart": {"price": "价格", "holding": "持有价值"}, "externalSwapBrideDappPopup": {"viewDappOptions": "查看 Dapp 选项", "help": "请联系该链的官方团队以获取支持", "noDapp": "没有可用的 Dapps", "noQuotesForChain": "当前该链尚无可用报价", "selectADapp": "选择一个 Dapp", "chainNotSupported": "在此链上不支持", "thirdPartyDappToProceed": "请使用第三方 Dapp 继续操作", "bridgeOnDapp": "桥接 Dapp\n", "noDapps": "该链上无可用 Dapp\n", "swapOnDapp": "在 Dapp 上兑换\n"}, "AccountSelectorModal": {"title": "选择地址\n", "searchPlaceholder": "搜索地址\n"}}, "background": {"error": {"noCurrentAccount": "当前账户不存在", "invalidChainId": "无效的 chain id", "notFindChain": "无法找到 {{chain}} 链", "unknownAbi": "未知的合约 abi", "invalidAddress": "无效地址", "notFoundGnosisKeyring": "未找到 Gnosis keyring", "notFoundTxGnosisKeyring": "未找到 Gnosis keyring 的交易", "addKeyring404": "添加 Keyring 失败，未定义 keyring", "emptyAccount": "当前帐户为空", "generateCacheAliasNames": "[GenerateCacheAliasNames]: 至少需要一个地址", "invalidPrivateKey": "私钥无效", "invalidJson": "输入的文件无效", "invalidMnemonic": "助记词无效，请检查！", "notFoundKeyringByAddress": "无法找到与地址对应的密钥环", "txPushFailed": "交易推送失败", "unlock": "你需要先解锁钱包", "canNotUnlock": "无法在没有先前保险库的情况下解锁", "duplicateAccount": "地址已导入"}, "transactionWatcher": {"submitted": "交易已提交", "more": "点击查看更多信息", "failed": "交易失败", "completed": "交易已完成", "txCompleteMoreContent": "{{chain}} #{{nonce}} 交易已完成，点击查看更多", "txFailedMoreContent": "{{chain}} #{{nonce}} 交易失败，点击查看更多"}, "alias": {"HdKeyring": "助记词", "simpleKeyring": "私钥", "watchAddressKeyring": "联系人"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "通过助记词创建", "SimpleKeyring": "通过私钥导入", "WatchAddressKeyring": "联系人"}, "IMPORTED_HD_KEYRING": "通过助记词导入", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "主网和测试网", "TESTNET": "仅限测试网"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "通过助记词（密码）导入"}}