{"page": {"transactions": {"title": "Transações", "empty": {"title": "Sem transações", "desc": " Sem transações encontradas na <1>rede suportada</1>"}, "explain": {"approve": "Aprovar {{amount}} {{symbol}} para {{project}}", "unknown": "Interação com Contrato", "cancel": "Transações pendentes canceladas"}, "txHistory": {"tipInputData": "A transação inclui uma mensagem", "parseInputDataError": "Falha ao analisar mensagem", "scamToolTip": "Esta transação é iniciada por golpistas para enviar tokens e NFTs fraudulentos. Por favor, evite interagir com ela."}, "modalViewMessage": {"title": "<PERSON><PERSON><PERSON>"}, "filterScam": {"title": "Ocultar transações fraudulentas", "loading": "O carregamento pode levar um momento, e atrasos nos dados são possíveis", "btn": "Ocultar transações fraudulentas"}}, "chainList": {"title": "{{count}} rede suportada", "mainnet": "Redes Principais", "testnet": "Redes de Testes"}, "signTx": {"nftIn": "NFT Recebidos", "gasLimitNotEnough": "Limite de Gas é menor que 21000. A Transação não será efetuada", "gasLimitLessThanExpect": "Limite de Gas é baixo. Existe 1% de chance da sua transação falhar.", "gasLimitLessThanGasUsed": "Limite de Gas é baixo. Existe 95% de chance da sua transação falhar.", "nativeTokenNotEngouthForGas": "Você não tem Gas suficiente em sua carteira ", "nonceLowerThanExpect": "O Nonce é baixo, o nonce minimo precisa ser {{0}}", "canOnlyUseImportedAddress": "Você só pode usar Endereços importados para transacionar", "multiSigChainNotMatch": "Endereços Multi-assinatura não estão nessa rede, não é possivel iniciar transação", "safeAddressNotSupportChain": "Endereços Safe atual não sem suporte na rede {{0}}", "noGasRequired": "Gas não é necessário", "gasSelectorTitle": "Gas", "failToFetchGasCost": "Falha ao buscar custo do gas", "gasMoreButton": "<PERSON><PERSON>", "manuallySetGasLimitAlert": "Você alterou manualmente o limite do Gas para", "gasNotRequireForSafeTransaction": "Taxas de Gas não são necessárias para transações Safe", "gasPriceTitle": "Preço do Gas (Gwei)", "maxPriorityFee": "Taxa Maxima para Prioridade (Gwei)", "eip1559Desc1": "Redes que suportarem EIP-1559, A Taxa de Prioridade é dada como gorjeta para os Validadores processarem sua transação. Você pode economizar Gas baixando a Taxa de Prioridade, isso afeta o tempo que sua transação será processada.", "eip1559Desc2": "<PERSON>, A Taxa de Prioridade (Gorjeta) = Taxa Maxima - Taxa Base. Depois que você seleciona a Taxa Maxima para Prioridade, a Taxa Base é deduzida e o restante é dado como gorjeta aos validadores.", "hardwareSupport1559Alert": "Tenha certeza que o firmware de sua hardwallet esteja atualizado para uma versão que suporte EIP-1559", "gasLimitTitle": "Gas limite", "recommendGasLimitTip": "Estimado {{est}}. Atual {{current}}x, Recomendado ", "nonceTitle": "<PERSON><PERSON>", "gasLimitModifyOnlyNecessaryAlert": "Altere apenas se for necessário", "gasPriceMedian": "Media entre 100 ultimas transações verificadas: ", "myNativeTokenBalance": "<PERSON>u <PERSON>ldo: ", "gasLimitEmptyAlert": "Por favor digite o Gas limite", "gasLimitMinValueAlert": "O limite do Gas precisa ser maior que 21000", "balanceChange": {"successTitle": "Resultado Simulado da Transação", "failedTitle": "Simulação da Transação Falhou", "noBalanceChange": "Sem alteração no saldo", "tokenOut": "Token Saindo", "tokenIn": "Token Entrando", "errorTitle": "Falha ao buscar alteração no saldo", "notSupport": "Simulação de Transação Não Suportada", "nftOut": "NFT Saindo"}, "enoughSafeSigCollected": "Coleta de Assinatura foi suficiente", "moreSafeSigNeeded": "{{0}} Confirmações Necessárias", "safeAdminSigned": "<PERSON><PERSON><PERSON>", "swap": {"title": "Trocar Token", "payToken": "<PERSON><PERSON>", "receiveToken": "<PERSON><PERSON><PERSON>", "failLoadReceiveToken": "Falha ao carregar", "valueDiff": "Valor di<PERSON>ente", "simulationFailed": "Simulação da Transação Falhou", "simulationNotSupport": "Simulação da Transação não suportada nessa rede", "minReceive": "Recebimento Minimo", "slippageFailToLoad": "Tolerancia de Slippage falhou ao carregar", "slippageTolerance": "Slippage Tolerado", "receiver": "<PERSON><PERSON><PERSON>", "notPaymentAddress": "Não é endereço de pagamento", "unknownAddress": "Endereço desconhecido"}, "crossChain": {"title": "Interoperabilidade de Redes"}, "swapAndCross": {"title": "Trocar Token entre Redes"}, "wrapToken": "<PERSON><PERSON>", "unwrap": "Unwrap Token", "send": {"title": "<PERSON><PERSON><PERSON>", "sendToken": "Enviar token", "sendTo": "Enviar para", "receiverIsTokenAddress": "Contrato do Token", "contractNotOnThisChain": "Endereço de Contrato não está nessa Rede", "notTopupAddress": "Não é o endereço de saldo", "tokenNotSupport": "{{0}} n<PERSON> suportado", "onMyWhitelist": "Na minha agenda de endereços", "notOnThisChain": "Não está nessa Rede", "cexAddress": "Endereço de Corretora Centralizada", "addressBalanceTitle": "Address balance", "whitelistTitle": "Agenda de Endereços", "notOnWhitelist": "Não esta na Agenda de Endereços", "fromMySeedPhrase": "A partir da minha seed phrase", "fromMyPrivateKey": "Da minha chave privada", "scamAddress": "Endereço de scam"}, "tokenApprove": {"title": "<PERSON><PERSON><PERSON>", "approveToken": "Aprovar token", "myBalance": "<PERSON><PERSON> saldo", "approveTo": "<PERSON><PERSON><PERSON> para", "eoaAddress": "Endereço EOA", "trustValueLessThan": "Confiar valores ≤ {{value}}", "deployTimeLessThan": "Tempo de aprovação < {{value}} dias", "amountPopupTitle": "Amount", "flagByRabby": "<PERSON><PERSON> por <PERSON>", "contractTrustValueTip": "Valor Confiado refere-se ao total de tokens aprovados e expostos a esse contrato. Um valor baixo de confiança indica risco ou inatividade por 180 dias.", "amount": "<PERSON><PERSON><PERSON>:", "exceed": "Excede seu saldo atual"}, "revokeTokenApprove": {"title": "Revogar Aprovação ao Token", "revokeFrom": "<PERSON><PERSON><PERSON>", "revokeToken": "Revogar tokens"}, "sendNFT": {"title": "Enviar NFT", "nftNotSupport": "Sem suporte ao NFT"}, "nftApprove": {"title": "Aprovação de NFT", "approveNFT": "Aprovar NFT", "nftContractTrustValueTip": "Valor Confiado refere-se ao NFT mais valioso aprovado e exposto a esse contrato. Um valor baixo de confiança indica risco ou inatividade por 180 dias."}, "revokeNFTApprove": {"title": "Revogar Aprovação a NFT", "revokeNFT": "Revogar NFT"}, "nftCollectionApprove": {"title": "Aprovação a Coleção NFT", "approveCollection": "<PERSON><PERSON><PERSON>"}, "revokeNFTCollectionApprove": {"title": "Revogar Aprovação a Coleção NFT", "revokeCollection": "<PERSON><PERSON><PERSON>"}, "deployContract": {"title": "Implantar um Contrato", "descriptionTitle": "Descrição", "description": "Você está implantando um Contrato Inteligente"}, "cancelTx": {"title": "Cancelar Transações Pendentes", "txToBeCanceled": "Transações para serem canceladas", "gasPriceAlert": "Indique um valor atual de Gas maior que {{value}} Gwei para cancelar transações pendentes"}, "submitMultisig": {"title": "Enviar Transação Multi-assinatura", "multisigAddress": "Endereço Multi-assinatura"}, "contractCall": {"title": "Interação com Contrato", "operation": "Operação", "operationABIDesc": "Operação decodificada da ABI", "operationCantDecode": "Operação não decodificada", "payNativeToken": "Pagar {{symbol}}", "suspectedReceiver": "Endereço de Exceção", "receiver": "Endereço do Recebedor"}, "revokePermit2": {"title": "Revogar Aprovação ao Token Permit2"}, "unknownAction": "Deconhecido", "interactContract": "Interação com Contrato", "markAsTrust": "Marcado como Confiavel", "markAsBlock": "Marcado como Bloqueado", "interacted": "Interagiu Antes", "neverInteracted": "Nunca interagiu antes", "transacted": "Transacionou antes", "neverTransacted": "Nunca transacionou antes", "fakeTokenAlert": "<PERSON><PERSON> marcou esse token como <PERSON>am", "scamTokenAlert": "<PERSON><PERSON> detectou baixa qualidade ou suposto scam no token", "trusted": "<PERSON><PERSON><PERSON>", "blocked": "Bloqueado", "noMark": "<PERSON><PERSON>", "markRemoved": "<PERSON><PERSON>", "speedUpTooltip": "Esta transação acelerada e a transação original, apenas uma será eventualmente concluída", "signTransactionOnChain": "Assinar Transação {{chain}}", "viewRaw": "Ver Raw", "unknownActionType": "Tipo de ação desconhecido", "sigCantDecode": "Esta assinatura não pode ser decodificada pela Rabby", "nftCollection": "Coleção de NFT", "floorPrice": "Preço mínimo", "contractAddress": "Endereço do Contrato", "protocolTitle": "Protocolo", "deployTimeTitle": "Tempo de Implantação", "popularity": "Popularidade", "contractPopularity": "No.{{0}} em {{1}}", "addressNote": "Nota de Endereço", "myMarkWithContract": "Minha marca em {{chainName}} Contrato", "myMark": "Minha marca", "collectionTitle": "Coleção", "addressTypeTitle": "Tipo de Endereço", "firstOnChain": "Primeiro na cadeia", "trustValue": "Valor de Confiança", "importedDelegatedAddress": "Endereço Delegado Importado", "noDelegatedAddress": "Sem endereço delegado importado", "coboSafeNotPermission": "Este endereço delegado não tem permissão para iniciar esta transação", "gasAccount": {"gasCost": "Custo de Gas para transferir gas para o seu endereço:", "estimatedGas": "Gás Estimado:", "totalCost": "Custo total: ", "maxGas": "Máximo Gas:", "sendGas": "A transferência de Gas para você na transação atual:", "currentTxCost": "Quantidade de Gas enviada para o seu endereço:"}, "customRPCErrorModal": {"title": "Erro RPC Personalizado", "content": "Seu RPC personalizado está indisponível no momento. Você pode desativá-lo e continuar assinando usando o RPC oficial do Rabby.", "button": "Desativar Custom RPC"}, "transferOwner": {"description": "Descrição", "transferTo": "Transferir para", "title": "Transferir a Propriedade dos Ativos"}, "swapLimitPay": {"title": "Troca Limite de Pagamento de Token", "maxPay": "Pagamento máximo"}, "batchRevokePermit2": {"title": "Revogar em Lote a Aprovação de Permit2"}, "revokePermit": {"title": "Revogar Aprovação de Token de Permissão"}, "assetOrder": {"receiveAsset": "Receber ativo", "listAsset": "Listar ativo", "title": "Ordem de Ativos"}, "BroadcastMode": {"instant": {"title": "Instant", "desc": "As transações serão imediatamente transmitidas para a rede"}, "lowGas": {"title": "Economia de Gas", "desc": "As transações serão transmitidas quando o gas da rede estiver baixo"}, "mev": {"title": "Protegido por MEV", "desc": "As transações serão transmitidas para o nó MEV designado"}, "tips": {"notSupported": "Não suportado", "notSupportChain": "Não é suportado nesta rede", "walletConnect": "Não suportado pelo WalletConnect", "customRPC": "Não suportado ao usar RPC personalizado"}, "lowGasDeadline": {"1h": "1h", "label": "Tempo esgotado", "4h": "4h", "24h": "24h"}, "title": "Modo Broadcast"}, "SafeNonceSelector": {"explain": {"unknown": "Transação Desconhecida", "send": "<PERSON><PERSON><PERSON>", "contractCall": "Chamada de Contrato"}, "optionGroup": {"recommendTitle": "<PERSON><PERSON> recomendado", "replaceTitle": "Substituir a transação na Fila"}, "option": {"new": "Nova Transação"}, "error": {"pendingList": "Falha ao carregar transações pendentes, <1/><2>Tentar novamente</2>"}}, "coboSafeCreate": {"safeWalletTitle": "Safe{Wallet}", "descriptionTitle": "Descrição", "title": "Criar Cobo Safe"}, "coboSafeModificationRole": {"safeWalletTitle": "Safe{Wallet}", "title": "Enviar Modificação de Função Segura", "descriptionTitle": "Descrição"}, "coboSafeModificationDelegatedAddress": {"descriptionTitle": "Descrição", "title": "Enviar Modificação de Endereço Delegado", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationTokenApproval": {"title": "Enviar Modificação de Aprovação de Token", "descriptionTitle": "Descrição", "safeWalletTitle": "<PERSON><PERSON><PERSON>{Wallet}"}, "common": {"description": "Descrição", "descTipWarningPrivacy": "A assinatura pode verificar a propriedade do endereço", "interactContract": "Interagir com o contrato", "descTipWarningBoth": "A assinatura pode causar alteração de ativos e verificar a propriedade do endereço", "descTipWarningAssets": "A assinatura pode causar alteração de ativos", "descTipSafe": "A assinatura não causa alteração de ativos nem verifica a propriedade do endereço"}, "nativeTokenForGas": "Use {{tokenName}} token em {{chainName}} para pagar pelo gas", "gasAccountForGas": "Usar USD da minha GasAccount para pagar o gás", "chain": "Cadeia", "decodedTooltip": "Esta assinatura é decodificada pelo Rabby <PERSON>", "trustValueTitle": "Confiança no valor", "contract": "Contrato inteligente", "no": "Não", "l2GasEstimateTooltip": "A estimativa de gas para a cadeia L2 não inclui a taxa de gas da L1. A taxa real será maior que a estimativa atual.", "typedDataMessage": "<PERSON><PERSON><PERSON>", "address": "Endereço", "protocol": "Protocolo", "amount": "Quantidade", "yes": "<PERSON>m", "hasInteraction": "Interagido anteriormente", "advancedSettings": "Configurações Avançadas", "importedAddress": "Endereço importado", "addressSource": "Fonte do Endereço", "maxPriorityFeeDisabledAlert": "Por favor, defina o <PERSON> primeiro", "label": "<PERSON><PERSON><PERSON><PERSON>", "primaryType": "Tipo principal", "safeServiceNotAvailable": "O serviço Safe não está disponível no momento, por favor, tente mais tarde.", "safeTx": {"selfHostConfirm": {"button": "OK", "title": "Mude para o Serviço Seguro do Rabby", "content": "A API do Safe está indisponível. Altere para o serviço Safe implantado pelo Rabby para manter seu Safe funcional. <strong>Todos os signatários do Safe devem usar o Rabby Wallet para autorizar transações.<strong>"}}}, "signFooterBar": {"requestFrom": "Solicitação de", "processRiskAlert": "Por favor, processe o alerta antes de assinar", "ignoreAll": "<PERSON><PERSON><PERSON> tudo", "gridPlusConnected": "GridPlus está conectado", "gridPlusNotConnected": "GridPlus não está conectado", "connectButton": "Conectar", "connecting": "Conectando...", "ledgerNotConnected": "Ledger não está conectado", "ledgerConnected": "Ledger está conectado", "signAndSubmitButton": "Assinar e Enviar", "walletConnect": {"connectedButCantSign": "<PERSON><PERSON><PERSON><PERSON>, mas incapaz de assinar.", "switchToCorrectAddress": "Por favor, mude para o endereço correto na carteira móvel", "switchChainAlert": "Por favor, mude para {{chain}} na carteira móvel", "notConnectToMobile": "Não conectado a {{brand}}", "connected": "Conectado e pronto para assinar", "howToSwitch": "Como mudar", "wrongAddressAlert": "Você mudou para um endereço diferente na carteira móvel. Por favor, mude para o endereço correto na carteira móvel", "connectBeforeSign": "{{0}} não está conectado a<PERSON>, por favor conecte antes de assinar", "chainSwitched": "Você mudou para uma cadeia diferente na carteira móvel. Por favor, mude para {{0}} na carteira móvel", "latency": "Latência", "requestSuccessToast": "Solicitação enviada com sucesso.", "sendingRequest": "Enviando solicitação de assinatura", "signOnYourMobileWallet": "Por favor, assine na sua carteira móvel.", "requestFailedToSend": "Falha ao enviar solicitação de assinatura"}, "beginSigning": "Iniciar processo de assinatura", "addressTip": {"onekey": "Endereço OneKey", "trezor": "Endereço Trezor", "bitbox": "Endereço BitBox02", "keystone": "Endereço Keystone", "airgap": "Endereço AirGap", "coolwallet": "Endereço CoolWallet", "privateKey": "Endereço de Chave Privada", "seedPhrase": "Endereço de Frase de Recuperação", "watchAddress": "Não é possível assinar com endereço de somente leitura", "safe": "Endereço Safe", "coboSafe": "Endereço Cobo Argus", "seedPhraseWithPassphrase": "<PERSON><PERSON><PERSON><PERSON> Seed Phrase (Passphrase)"}, "qrcode": {"signWith": "Assinar com {{brand}}", "failedToGetExplain": "Falha ao obter explicação", "txFailed": "Transação falhou", "sigReceived": "Assinatura recebida", "sigCompleted": "Assinatura concluída", "getSig": "Obter assinatura", "qrcodeDesc": "Escaneie com o seu {{brand}} para assinar<br></br>Após a assinatura, clique no botão abaixo para receber a assinatura", "misMatchSignId": "Dados de transação incongruentes. Por favor, verifique os detalhes da transação.", "unknownQRCode": "Erro: Não conseguimos identificar esse código QR", "afterSignDesc": "Após a assinatura, coloque o código QR no {{brand}} na frente da câmera do seu PC"}, "ledger": {"resent": "Reenviar", "signError": "Erro de assinatura Ledger:", "notConnected": "Sua carteira não está conectada. Por favor, reconecte.", "siging": "Enviando solicitação de assinatura...", "txRejected": "Transação rejeitada", "unlockAlert": "<PERSON>r <PERSON>, conecte e desbloqueie o seu Ledger, abra o Ethereum nele", "updateFirmwareAlert": "Por favor, atualize o firmware e o aplicativo Ethereum no seu Ledger", "txRejectedByLedger": "A transação foi rejeitada no seu Ledger", "blindSigTutorial": "Tutorial de Assinatura Cega do Ledger", "resubmited": "Reenviado", "submitting": "Assinado. Criando transação"}, "common": {"notSupport": "{{0}} não é suportado"}, "resend": "Reenviar", "submitTx": "Enviar Transação", "testnet": "Rede de Teste", "mainnet": "<PERSON>e Principal", "gasless": {"watchUnavailableTip": "Endereço de visualização não é compatível com Free Gas", "unavailable": "Seu saldo de Gas não é suficiente", "walletConnectUnavailableTip": "Carteira móvel conectada via WalletConnect não é suportada para Free Gas", "GetFreeGasToSign": "Obtenha <PERSON>", "rabbyPayGas": "Rabby cu<PERSON> do gás necessário – é só assinar", "notEnough": "O saldo de Gas não é suficiente", "customRpcUnavailableTip": "Os RPCs personalizados não são suportados para Free Gas"}, "gasAccount": {"gotIt": "<PERSON><PERSON><PERSON>", "WalletConnectTips": "WalletConnect não é suportado pelo GasAccount", "notEnough": "GasAccount não é suficiente", "customRPC": "Não é suportado ao usar RPC personalizado", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "chainNotSupported": "Esta cadeia não é suportada pelo GasAccount", "useGasAccount": "Use GasAccount", "login": "Entrar", "loginFirst": "Por favor, faça login no GasAccount primeiro", "loginTips": "Para concluir o login no GasAccount, esta transação será descartada. Você precisará refazê-la após o login.", "depositTips": "Para concluir o depósito do GasAccount, esta transação será descartada. Você precisará refazê-la após o depósito."}, "keystone": {"siging": "Enviando solicitação de assinatura", "txRejected": "Transação rejeitada", "hardwareRejectError": "A solicitação do Keystone foi cancelada. Para prosseguir, por favor, reautorize.", "signWith": "<PERSON><PERSON> para {{method}} para assinar", "misMatchSignId": "Dados de transação incongruentes. Por favor, verifique os detalhes da transação.", "qrcodeDesc": "Escanear para assinar. Após assinar, clique abaixo para obter a assinatura. Para USB, reconecte e autorize para iniciar o processo de assinatura novamente.", "unsupportedType": "Erro: O tipo de transação não é suportado ou é desconhecido.", "shouldRetry": "Ocorreu um erro. Por favor, tente novamente.", "verifyPasswordError": "Falha na assinatura, por favor, tente novamente após desbloquear", "shouldOpenKeystoneHomePageError": "Certifique-se de que seu Keystone 3 Pro esteja na página inicial", "mismatchedWalletError": "Carteira incompatível"}, "keystoneConnected": "Keystone está conectado", "keystoneNotConnected": "Keystone não está conectado", "imKeyNotConnected": "imKey não está conectado", "cancelCurrentTransaction": "Cancelar transação atual", "cancelConnection": "<PERSON><PERSON><PERSON>", "detectedMultipleRequestsFromThisDapp": "Detectado múltiplas solicitações deste Dapp", "imKeyConnected": "imKey está conectado", "cancelTransaction": "Cancelar Transação", "blockDappFromSendingRequests": "Bloquear Dapp de enviar solicitações por 1 min", "cancelCurrentConnection": "<PERSON><PERSON><PERSON> atual", "cancelAll": "<PERSON><PERSON>ar to<PERSON> as {{count}} solicitações do Dapp"}, "signTypedData": {"signTypeDataOnChain": "<PERSON><PERSON><PERSON> Dad<PERSON> Digitados na {{chain}}", "safeCantSignText": "Este é um endereço Safe e não pode ser usado para assinar texto.", "permit": {"title": "Aprovação de Token Permit"}, "permit2": {"title": "Aprovação de Token Permit2", "sigExpireTimeTip": "A duração para esta assinatura ser válida na cadeia", "sigExpireTime": "Tempo de expiração da assinatura", "approvalExpiretime": "Tempo de expiração da aprovação"}, "swapTokenOrder": {"title": "Or<PERSON><PERSON> de <PERSON>"}, "sellNFT": {"title": "Ordem de NFT", "receiveToken": "Receber token", "listNFT": "Listar NFT", "specificBuyer": "Comprador específico"}, "signMultiSig": {"title": "Confirmar <PERSON>"}, "createKey": {"title": "<PERSON><PERSON><PERSON>"}, "verifyAddress": {"title": "Verificar Endereço"}, "buyNFT": {"payToken": "Pagar token", "receiveNFT": "Receber NFT", "expireTime": "Tempo de expiração", "listOn": "Listado em"}, "contractCall": {"operationDecoded": "Operação decodificada da mensagem"}, "safeCantSignTypedData": "Este é um endereço Safe e ele suporta apenas assinar dados tipados EIP-712 ou strings."}, "activities": {"title": "Registro de Assinatura", "signedTx": {"label": "Transações", "empty": {"title": "Ainda não há transações assinadas", "desc": "<PERSON><PERSON> as transaç<PERSON><PERSON> assinadas via <PERSON><PERSON> serão listadas aqui."}, "common": {"unlimited": "ilimitado", "unknownProtocol": "Protocolo desconhecido", "unknown": "Desconhecido", "speedUp": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "pendingDetail": "<PERSON><PERSON><PERSON>"}, "tips": {"pendingDetail": "Apenas uma transação será concluída, e quase sempre é aquela com o maior preço de gás", "canNotCancel": "Não é possível acelerar ou cancelar: Não é a primeira transação pendente", "pendingBroadcastBtn": "Transmitir agora", "pendingBroadcastRetryBtn": "Re-broadcast", "pendingBroadcastRetry": "Transmissão falhou. Última tentativa: {{pushAt}}", "pendingBroadcast": "Modo de economia de Gas: aguardando taxas de rede mais baixas. Máximo de {{deadline}}h de espera."}, "status": {"canceled": "Cancelada", "failed": "Fal<PERSON>", "submitFailed": "Falha ao enviar", "pending": "Pendente", "pendingBroadcastFailed": "Pendente: Falha na transmissão", "pendingBroadcasted": "Pendente: broadcasted", "pendingBroadcast": "Pendente: a ser transmitido", "withdrawed": "Cancelamento rápido"}, "txType": {"initial": "Transação Inicial", "cancel": "Transação de Cancelamento", "speedUp": "Transação de Aceleração"}, "explain": {"unknown": "Transação Desconhecida", "send": "Enviar {{amount}} {{symbol}}", "cancel": "Cancelar a Aprovação de {{token}} para {{protocol}}", "approve": "Aprovar {{count}} {{token}} para {{protocol}}", "cancelNFTCollectionApproval": "Cancelar a Aprovação de Coleção de NFT para {{protocol}}", "cancelSingleNFTApproval": "Cancelar a Aprovação de NFT Individual para {{protocol}}", "singleNFTApproval": "Aprovação de NFT Individual para {{protocol}}", "nftCollectionApproval": "Aprovação de Coleção de NFT para {{protocol}}"}, "CancelTxPopup": {"options": {"removeLocalPendingTx": {"title": "Limpar Pendências Localmente", "desc": "Remova a transação pendente da interface"}, "quickCancel": {"title": "<PERSON><PERSON><PERSON>", "tips": "Apenas compatível para transações que não foram transmitidas", "desc": "Cancelar antes de transmitir, sem taxa de gas"}, "onChainCancel": {"title": "Cancelamento On-chain", "desc": "Nova transação para cancelar, requer gas"}}, "removeLocalPendingTx": {"title": "Excluir transação localmente", "desc": "Esta ação excluirá a transação pendente localmente. \nA transação pendente ainda poderá ser enviada com sucesso no futuro."}, "title": "Cancelar transação"}, "MempoolList": {"reBroadcastBtn": "Re-broadcast", "empty": "Não encontrado em nenhum nó", "title": "Apar<PERSON>eu em {{count}} nós RPC"}, "message": {"reBroadcastSuccess": "Re-broadcasted", "cancelSuccess": "Cancelado", "deleteSuccess": "Excluído com sucesso", "broadcastSuccess": "Transmitido"}, "gas": {"noCost": "Sem custo de Gas"}, "SkipNonceAlert": {"alert": "Nonce #{{nonce}} pulado na cadeia {{chainName}}. Isso pode causar transações pendentes à frente. <5></5> <6>Submeta uma tx</6> na cadeia para resolver<7></7>", "clearPendingAlert": "Transação {{chainName}} ({{nonces}}) está pendente há mais de 3 minutos. Você pode <5></5> <6>Limpar Pendentes Localmente</6> <7></7> e reenviar a transação."}, "PredictTime": {"time": "Previsto para ser embalado em {{time}}", "failed": "A previsão de tempo de embalagem falhou", "noTime": "O tempo de empacotamento está sendo previsto"}, "CancelTxConfirmPopup": {"title": "Limpar Pendente Localmente", "desc": "Isso removerá a transação pendente da sua interface. Você poderá então iniciar uma nova transação.", "warning": "A transação removida ainda pode ser confirmada na cadeia, a menos que ela seja substituída."}}, "signedText": {"label": "Texto", "empty": {"title": "Ainda não há textos assinados", "desc": "Todos os textos assinados via <PERSON><PERSON> serão listados aqui."}}}, "receive": {"title": "Receber {{token}} na {{chain}}", "watchModeAlert1": "Este é um endereço no Modo de Observação.", "watchModeAlert2": "Você tem certeza de que deseja usá-lo para receber ativos?"}, "sendToken": {"addressNotInContract": "Não está na lista de endereços. <1></1><2>Adicionar aos contatos</2>", "AddToContactsModal": {"addedAsContacts": "Adicionado aos contatos", "editAddr": {"placeholder": "Insira a Nota do Endereço", "validator__empty": "Por favor, insira a nota do endereço"}, "editAddressNote": "Editar nota do endereço", "error": "Falha ao adicionar aos contatos"}, "allowTransferModal": {"error": "Senha incorreta", "placeholder": "Digite a <PERSON>ha para Confirmar", "validator__empty": "Por favor, insira a senha", "addWhitelist": "Adicionar à lista de permissões"}, "GasSelector": {"confirm": "Confirmar", "level": {"$unknown": "Desconhecido", "custom": "Personalizado", "fast": "Instantâneo", "normal": "<PERSON><PERSON><PERSON><PERSON>", "slow": "Padrão"}, "popupDesc": "O custo do gás será reservado do valor da transferência com base no preço do gás que você definir", "popupTitle": "Definir Preço do Gás (Gwei)"}, "header": {"title": "Enviar"}, "modalConfirmAddToContacts": {"confirmText": "Confirmar", "title": "Adicionar aos contatos"}, "modalConfirmAllowTransferTo": {"cancelText": "<PERSON><PERSON><PERSON>", "confirmText": "Confirmar", "title": "Digite a <PERSON>ha para Confirmar"}, "sectionBalance": {"title": "<PERSON><PERSON>"}, "sectionChain": {"title": "Rede"}, "sectionFrom": {"title": "De"}, "sectionTo": {"addrValidator__empty": "Por favor, insira o endereço", "addrValidator__invalid": "Este endereço é inválido", "searchInputPlaceholder": "Digite o endereço ou pesquise", "title": "Para"}, "sendButton": "Enviar", "tokenInfoFieldLabel": {"chain": "Rede", "contract": "Endereço do Contrato"}, "tokenInfoPrice": "Preço", "whitelistAlert__disabled": "Lista de permissões desativada. Você pode transferir para qualquer endereço.", "whitelistAlert__notWhitelisted": "O endereço não está na lista de permissões. <1 /> Concordo em conceder permissão temporária para a transferência.", "whitelistAlert__temporaryGranted": "Permissão temporária concedida", "whitelistAlert__whitelisted": "O endereço está na lista de permissões", "balanceWarn": {"gasFeeReservation": "Reserva de taxa de gás necessária"}, "balanceError": {"insufficientBalance": "<PERSON><PERSON> insuficiente"}, "max": "MÁX", "sectionMsgDataForEOA": {"title": "Mensagem", "placeholder": "Opcional", "currentIsUTF8": "A entrada atual é UTF-8. Os dados originais são:", "currentIsOriginal": "A entrada atual é Dados Originais. UTF-8 é:"}, "sectionMsgDataForContract": {"simulation": "Simulação de chamada de contrato:", "notHexData": "Apenas dados hex suportados", "title": "Chamada de contrato", "placeholder": "Opcional", "parseError": "Falha ao decodificar a chamada do contrato"}, "blockedTransactionContent": "Esta transação interage com um endereço que está na lista de sanções da OFAC.", "blockedTransaction": "Transação Bloqueada", "blockedTransactionCancelText": "Eu sei"}, "sendTokenComponents": {"GasReserved": "Reservado <1>0</1> {{ tokenName }} para custo de gás", "SwitchReserveGas": "Reservar Gas <1 />"}, "sendNFT": {"header": {"title": "Enviar"}, "sectionChain": {"title": "Rede"}, "sectionFrom": {"title": "De"}, "sectionTo": {"title": "Para", "addrValidator__empty": "Por favor, insira o endereço", "addrValidator__invalid": "Este endereço é inválido", "searchInputPlaceholder": "Digite o endereço ou pesquise"}, "nftInfoFieldLabel": {"Collection": "Coleção", "Contract": "Contrato", "sendAmount": "Quantidade Enviada"}, "sendButton": "Enviar", "whitelistAlert__disabled": "Lista de permissões desativada. Você pode transferir para qualquer endereço.", "whitelistAlert__whitelisted": "O endereço está na lista de permissões", "whitelistAlert__temporaryGranted": "Permissão temporária concedida", "whitelistAlert__notWhitelisted": "O endereço não está na lista de permissões. <1 /> Concordo em conceder permissão temporária para a transferência.", "tipNotOnAddressList": "Não está na lista de endereços.", "tipAddToContacts": "Adicionar aos contatos", "confirmModal": {"title": "Digite a <PERSON>ha para Confirmar"}}, "approvals": {"header": {"title": "Aprovações em {{ address }}"}, "tab-switch": {"contract": "Por Contratos", "assets": "<PERSON>r At<PERSON>"}, "component": {"table": {"bodyEmpty": {"loadingText": "Carregando...", "noMatchText": "<PERSON><PERSON>hum resultado", "noDataText": "<PERSON><PERSON>"}}, "ApprovalContractItem": {"ApprovalCount_one": "Aprovação", "ApprovalCount_other": "Aprovações"}, "RevokeButton": {"btnText_zero": "<PERSON><PERSON><PERSON>", "btnText_one": "Revogar ({{count}})", "btnText_other": "Revogar ({{count}})", "permit2Batch": {"modalTitle_one": "É necessário um total de <2>{{count}}</2> assinatura(s)", "modalTitle_other": "É necessário um total de <2>{{count}}</2> assinaturas", "modalContent": "Os approvals do mesmo contrato Permit2 seriam agrupados sob a mesma assinatura."}}, "ViewMore": {"text": "Ver mais"}}, "search": {"placeholder": "Pesquisar {{ type }} por nome/endereço"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "Contrato", "contractTrustValue": "Valor de Confiança do Contrato", "revokeTrends": "Tendências de Revogação em 24h", "myApprovedAssets": "Meus Ativos Aprovados", "myApprovalTime": "Meu Tempo de Aprovação"}, "columnTip": {"contractTrustValue": "O valor de confiança se refere ao valor total de ativos aprovados e expostos a este Contrato. Um valor de confiança baixo indica risco ou inatividade por 180 dias.", "contractTrustValueWarning": "O valor de confiança do Contrato < $100.000", "contractTrustValueDanger": "O valor de confiança do Contrato < $10.000"}}, "byAssets": {"columnTitle": {"asset": "Ativo", "type": "Tipo", "approvedAmount": "Quantidade Aprovada", "approvedSpender": "<PERSON><PERSON><PERSON><PERSON>", "myApprovalTime": "Meu Tempo de Aprovação"}, "columnCell": {"approvedAmount": {"tipMyBalance": "<PERSON><PERSON>", "tipApprovedAmount": "Quantidade Aprovada"}}}}, "RevokeApprovalModal": {"subTitleTokenAndNFT": "Tokens e NFTs Aprovados", "subTitleContract": "Aprovado para os seguintes Contratos", "selectAll": "Selecionar Todos", "confirm": "Confirmar {{ selectedCount }}", "title": "Aprovações", "tooltipPermit2": "Esta aprovação é aprovada através do contrato Permit2:\n{{ permit2Id }}", "unSelectAll": "<PERSON><PERSON><PERSON>"}, "revokeModal": {"confirmRevokePrivateKey": "Usando uma seed phrase ou endereço de chave privada, você pode revogar {{count}} aprovações em lote com 1 clique.", "connectLedger": "Conectar Ledger", "batchRevoke": "Revogação em Lote", "ledgerSended": "Por favor, assine a solicitação no Ledger ({{current}}/{{total}})", "approvalCount_other": "{{count}} approvals", "cancelTitle": "Cancelar as Revogações Restantes", "paused": "<PERSON><PERSON><PERSON>", "signAndStartRevoke": "Assinar e Iniciar Revogação", "approvalCount_zero": "{{count}} aprovação", "totalRevoked": "Total:", "confirm": "Confirmar", "stillRevoke": "<PERSON><PERSON>", "submitTxFailed": "Falha ao Enviar", "resume": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revokeWithLedger": "Inicie Revogação com Ledger", "gasTooHigh": "A taxa de Gas está alta", "waitInQueue": "Aguarde na fila", "pause": "Pausar", "defaultFailed": "Transação falhou", "confirmRevokeLedger": "Usando um endereço Ledger, você pode revogar {{count}} aprovações em lote com 1 clique.", "confirmTitle": "Revogar em Lote com Um Clique", "ledgerSending": "Enviando solicitação de assinatura ({{current}}/{{total}})", "approvalCount_one": "{{count}} approval", "gasNotEnough": "Gas insuficiente para enviar", "ledgerAlert": "Por favor, abra o aplicativo Ethereum no seu dispositivo Ledger", "revoked": "Revogado:", "revokeOneByOne": "Revogar um por um", "simulationFailed": "Simulação falhou", "ledgerSigned": "Assinado. C<PERSON>do trans<PERSON> ({{current}}/{{total}})", "cancelBody": "Se você fechar esta página, as revogações restantes não serão executadas.", "useGasAccount": "Seu saldo de gás está baixo. Seu GasAccount cobrirá as taxas de gás."}}, "gasTopUp": {"title": "Recarga Instantânea de Gás", "description": "Recarregue o gás enviando-nos tokens disponíveis em outra rede. Transferência instantânea assim que o pagamento for confirmado, sem esperar que seja irreversível.", "topUpChain": "Rede de Recarga", "Amount": "Quantidade", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "InsufficientBalance": "Não há saldo suficiente no endereço do Contrato da Rabby para a rede atual. Por favor, tente novamente mais tarde.", "hightGasFees": "Este valor de recarga é muito pequeno porque a rede de destino exige altas taxas de gás.", "No_Tokens": "<PERSON><PERSON><PERSON>", "InsufficientBalanceTips": "<PERSON><PERSON> insuficiente", "payment": "Pagamento de Recarga de Gás", "Loading_Tokens": "Carregando Tokens...", "Including-service-fee": "Incluindo taxa de serviço de {{fee}}", "service-fee-tip": "Ao fornecer o serviço de Recarga de Gás, a Rabby precisa suportar a perda de flutuação do token e a taxa de gás para a recarga. Portanto, uma taxa de serviço de 20% é cobrada.", "Confirm": "Confirmar", "Select-from-supported-tokens": "Selecionar entre os tokens suportados", "Value": "Valor", "Payment-Token": "<PERSON><PERSON>", "Select-payment-token": "Selecionar token de pagamento", "Token": "Token", "Balance": "<PERSON><PERSON>"}, "swap": {"title": "Trocar", "pendingTip": "Tx enviada. Se a tx estiver pendente por muitas horas, você pode tentar limpar as pendências nas configurações.", "Pending": "Pendente", "completedTip": "Transação na rede, decodificando dados para gerar registro", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slippage_tolerance": "Tolerância de Escorregamento:", "actual-slippage": "Escorregamento Real:", "gas-x-price": "Preço do Gás: {{price}} Gwei.", "no-transaction-records": "Nenhum registro de transação", "swap-history": "Histórico de Troca", "InSufficientTip": "Saldo insuficiente para simulação de transação e estimativa de gás. Cotações originais do agregador são exibidas", "testnet-is-not-supported": "Não oferecemos suporte para rede personalizada", "not-supported": "Não suportada", "slippage-adjusted-refresh-quote": "Escorregamento ajustado. Atualizar cotação.", "price-expired-refresh-quote": "Preço expirado. Atualizar cotação.", "approve-x-symbol": "Aprovar {{symbol}}", "swap-via-x": "<PERSON><PERSON>car via {{name}}", "get-quotes": "Obter cota<PERSON>", "chain": "Rede", "swap-from": "T<PERSON>car <PERSON>", "to": "Para", "search-by-name-address": "Pesquisar por Nome / Endereço", "amount-in": "Quantidade em {{symbol}}", "unlimited-allowance": "Permissão ilimitada", "insufficient-balance": "<PERSON><PERSON> insuficiente", "rabby-fee": "<PERSON><PERSON>", "minimum-received": "<PERSON><PERSON><PERSON>", "there-is-no-fee-and-slippage-for-this-trade": "Não há taxa e escorregamento para esta troca", "approve-tips": "1. <PERSON><PERSON><PERSON> → 2. <PERSON><PERSON><PERSON>", "best": "Mel<PERSON>", "unable-to-fetch-the-price": "Não é possível buscar o preço", "fail-to-simulate-transaction": "Falha na simulação da transação", "security-verification-failed": "Falha na verificação de segurança", "need-to-approve-token-before-swap": "É necessário aprovar o token antes da troca", "this-exchange-is-not-enabled-to-trade-by-you": "Esta exchange não está habilitada para troca por você.", "enable-it": "Habilitar", "this-token-pair-is-not-supported": "Este par de tokens não é suportado", "QuoteLessWarning": "A quantidade a ser recebida é estimada a partir da simulação de transação da Rabby. A oferta fornecida pela dex é {{receive}}. Você receberá {{diff}} a menos do que a oferta esperada.", "by-transaction-simulation-the-quote-is-valid": "Pela simulação de transação, a cotação é válida", "wrap-contract": "Contrato de Wrap", "directlySwap": "Embrulhando tokens {{symbol}} diretamente com o Contrato inteligente", "rates-from-cex": "Taxas da CEX", "edit": "<PERSON><PERSON>", "tradingSettingTips": "{{viewCount}} exchanges oferecem cotações, e {{tradeCount}} habilitam negociação", "the-following-swap-rates-are-found": "As seguintes taxas de troca foram encontradas", "est-payment": "Pagamento Estimado:", "est-receiving": "Recebimento Estimado:", "est-difference": "Diferença Estimada:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "A oferta selecionada difere muito da taxa atual, o que pode causar grandes perdas", "rate": "Taxa", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Baixo escorregamento pode causar transações falhadas devido à alta volatilidade", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "A transação pode ser frontrun devido à alta tolerância ao escorregamento", "recommend-slippage": "Para evitar frontrunning, recomendamos um escorregamento de <2>{{ slippage }}</2>%", "slippage-tolerance": "Tolerância ao Escorregamento", "select-token": "Selecionar <PERSON>", "enable-exchanges": "Habilitar Exchanges", "exchanges": "Exchanges", "view-quotes": "Ver Cotações", "trade": "Negociar", "dex": "<PERSON>", "cex": "Cex", "enable-trading": "Habilitar Negociação", "i-understand-and-accept-it": "Eu entendo e aceito", "confirm": "Confirmar", "tradingSettingTip1": "1. <PERSON>a vez habilitado, você interagirá diretamente com o Contrato da exchange", "tradingSettingTip2": "2. A Rabby não é responsável por quaisquer riscos decorrentes dos Contratos das exchanges", "gas-fee": "Taxa de Gás: {{gasUsed}}", "estimate": "Estimativa:", "actual": "Real:", "rabbyFee": {"title": "<PERSON><PERSON>", "rate": "Taxa de fee", "wallet": "<PERSON><PERSON><PERSON>", "bridgeDesc": "A Rabby Wallet sempre encontrará a melhor taxa possível dos principais agregadores e verificará a confiabilidade de suas ofertas. Rabby cobra uma taxa de 0,25%, que é automaticamente incluída na cotação.", "button": "<PERSON><PERSON><PERSON>", "swapDesc": "Rabby Wallet sempre encontrará a melhor taxa possível a partir dos principais agregadores e verificará a confiabilidade de suas ofertas. Rabby cobra uma taxa de 0,25% (0% para wrapping), que é automaticamente incluída na cotação."}, "lowCreditModal": {"title": "Este token tem um baixo valor de crédito", "desc": "Um valor de crédito baixo geralmente sinaliza alto risco, como um token honeypot ou liquidez muito baixa."}, "max": "MAX", "No-available-quote": "Sem cotação disponível", "fetch-best-quote": "Buscando a me<PERSON>hor cotação", "from": "De", "preferMEV": "Preferir MEV Guardado", "no-slippage-for-wrap": "Sem slippage para Wrap", "source": "Fonte", "Auto": "Auto", "usd-after-fees": "≈ {{usd}}", "two-step-approve": "Assine 2 transações para alterar a permissão", "hidden-no-quote-rates_one": "{{count}} taxa indisponível", "no-fee-for-wrap": "Sem taxa Rabby para Wrap", "loss-tips": "Você está perdendo {{usd}}. Experimente um valor menor em um pequeno mercado.", "Gas-fee-too-high": "Taxa de gás muito alta", "approve-swap": "Aprovar e Trocar", "no-fees-for-wrap": "Sem taxa Rabby para Wrap", "sort-with-gas": "Ordenar com gas", "process-with-two-step-approve": "Prossiga com a aprovação em duas etapas", "preferMEVTip": "Ative o recurso \"MEV Guarded\" para trocas de Ethereum a fim de reduzir os riscos de ataques sandwich. Nota: este recurso não é compatível se você usar um RPC personalizado ou endereço de conexão de carteira", "price-impact": "Impacto no Preço", "approve-and-swap": "<PERSON><PERSON><PERSON> e Trocar via {{name}}", "two-step-approve-details": "O token USDT requer 2 transações para alterar a permissão. <PERSON>iro, você precisará redefinir a permissão para zero e, somente então, definir um novo valor de permissão.", "no-quote-found": "Nenhuma cotação encontrada", "hidden-no-quote-rates_other": "{{count}} taxas indisponíveis"}, "manageAddress": {"no-address": "Nenhum endereço", "no-match": "Sem correspondência", "current-address": "Endereço Atual", "address-management": "Gerenciamento de Endereços", "update-balance-data": "Atualizar dados de saldo", "search": "<PERSON><PERSON><PERSON><PERSON>", "manage-address": "Gerenciar Endereço", "deleted": "Excluído", "whitelisted-address": "Endereço na lista branca", "addressTypeTip": "Importado por {{type}}", "delete-desc": "Antes de excluir, tenha em mente os seguintes pontos para entender como proteger seus ativos.", "delete-checklist-1": "Entendo que, se excluir este endereço, a Chave Privada e a Frase de Recuperação correspondentes a este endereço serão excluídas, e a Rabby NÃO poderá recuperá-las.", "delete-checklist-2": "Confirmo que fiz backup da chave privada ou Frase de Recuperação e estou pronto para excluí-la agora.", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "delete-private-key-modal-title_one": "Excluir {{count}} endereço de chave privada", "delete-private-key-modal-title_other": "Excluir {{count}} endereços de chave privada", "delete-seed-phrase-title_one": "Excluir frase de recuperação e seu {{count}} endereço", "delete-seed-phrase-title_other": "Excluir frase de recuperação e seus {{count}} endereços", "delete-title_one": "Excluir {{count}} endereço {{brand}}", "delete-title_other": "Excluir {{count}} endereços {{brand}}", "delete-empty-seed-phrase": "Excluir frase de recuperação e seus 0 endereços", "hd-path": "Caminho HD:", "no-address-under-seed-phrase": "Você não importou nenhum endereço sob esta frase de recuperação.", "add-address": "<PERSON><PERSON><PERSON><PERSON>", "delete-seed-phrase": "Excluir frase de recuperação", "confirm-delete": "Confirmar <PERSON>", "private-key": "<PERSON>ve <PERSON>", "seed-phrase": "Frase de Recuperação", "watch-address": "Endereço de Observação", "backup-seed-phrase": "Fazer Backup da Frase de Recuperação", "delete-all-addresses-but-keep-the-seed-phrase": "Excluir todos os endereços, mas manter a frase de recuperação", "delete-all-addresses-and-the-seed-phrase": "Excluir todos os endereços e a frase de recuperação", "seed-phrase-delete-title": "Excluir frase de recuperação?", "sort-by-address-note": "Ordenar por nota de endereço", "sort-address": "Ordenar Endereço", "sort-by-balance": "Ordenar por saldo", "addNewAddress": "Adicionar Nov<PERSON> Endereço", "sort-by-address-type": "Classificar por tipo de endereço", "enterPassphraseTitle": "Digite a frase secreta para assinar", "passphraseError": "<PERSON><PERSON>", "enterThePassphrase": "Digite a Passphrase", "CurrentDappAddress": {"desc": "Alterar o endereço do DApp\n"}}, "dashboard": {"home": {"offline": "A rede está desconectada e nenhum dado está sendo obtido", "panel": {"swap": "Troca", "send": "Enviar", "receive": "<PERSON><PERSON><PERSON>", "gasTopUp": "Recarga de Gás", "queue": "<PERSON><PERSON>", "transactions": "Transações", "approvals": "Aprovações", "feedback": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON>", "manageAddress": "Gerenciar Endereço", "nft": "NFT", "ecology": "Ecossistema", "rabbyPoints": "<PERSON><PERSON>", "bridge": "<PERSON><PERSON>", "mobile": "Mobile Sync"}, "comingSoon": "Em breve", "soon": "Em breve", "refreshTheWebPageToTakeEffect": "Atualize a página para que as alterações tenham efeito", "rabbyIsInUseAndMetamaskIsBanned": "Rabby está em uso e o MetaMask está proibido", "flip": "Inverter", "metamaskIsInUseAndRabbyIsBanned": "O MetaMask está em uso e o Rabby está proibido", "transactionNeedsToSign": "transação precisa ser assinada", "transactionsNeedToSign": "transações precisam ser assinadas", "view": "Visualizar", "viewFirstOne": "Visualizar a primeira", "rejectAll": "<PERSON><PERSON><PERSON><PERSON> tudo", "pendingCount": "1 Pendente", "pendingCountPlural": "{{countStr}} Pendentes", "queue": {"title": "<PERSON><PERSON>", "count": "{{count}} na"}, "whatsNew": "O que há de novo", "importType": "Importado por {{type}}", "chainEnd": "rede", "chain": "cadeia,", "missingDataTooltip": "O saldo pode não estar atualizado devido a problemas atuais na rede com {{text}}."}, "recentConnection": {"disconnected": "Desconectado", "rpcUnavailable": "O RPC personalizado não está disponível", "metamaskTooltip": "Você prefere usar o MetaMask com este Dapp. Atualize essa configuração a qualquer momento em Configurações > Dapps Preferidos do MetaMask", "connected": "Conectado", "notConnected": "Não conectado", "connectedDapp": "O Rabby não está conectado ao Dapp atual. Para se conectar, encontre e clique no botão de conexão na página do Dapp.", "noDappFound": "<PERSON><PERSON><PERSON> encontrado", "disconnectAll": "Desconectar Todos", "disconnectRecentlyUsed": {"title": "Desconectar os <strong>{{count}}</strong> DApps usados recentemente", "description": "Os DApps fixados permanecerão conectados", "title_other": "Desconectar <strong>{{count}}</strong> Dapps conectados", "title_one": "Desconectar <strong>{{count}}</strong> Dapp conectada"}, "title": "<PERSON><PERSON>", "pinned": "Fixados", "noPinnedDapps": "<PERSON><PERSON><PERSON>ado", "dragToSort": "Arraste para ordenar", "recentlyConnected": "Conectados Recentemente", "noRecentlyConnectedDapps": "Nenhum <PERSON> conectado recentemente", "dapps": "<PERSON><PERSON>", "noConnectedDapps": "<PERSON><PERSON><PERSON> cone<PERSON>", "metamaskModeTooltip": "Não consegue conectar Rabby neste Dapp? Tente ativar o <1>Modo MetaMask</1>", "metamaskModeTooltipNew": "A Rabby Wallet se conectará quando você selecionar \"MetaMask\" no Dapp. Você pode gerenciar isso em <PERSON> > Conectar Rabby disfarçando-se como MetaMask"}, "feedback": {"directMessage": {"content": "Mensagem Direta", "description": "Converse com a Equipe Rabby Wallet oficial no DeBank"}, "proposal": {"content": "Proposta", "description": "Envie uma proposta para a Rabby Wallet no DeBank"}, "title": "<PERSON><PERSON><PERSON>"}, "nft": {"empty": "Nenhum NFT encontrado em Coleções suportadas", "collectionList": {"collections": {"label": "Coleções"}, "all_nfts": {"label": "Todos os NFTs"}}, "listEmpty": "Você ainda não adquiriu nenhum NFT", "modal": {"collection": "Coleção", "chain": "Rede", "lastPrice": "Último <PERSON>", "purchaseDate": "<PERSON> da Compra", "sendTooltip": "Apenas NFTs ERC-721 e ERC-1155 são suportados por enquanto", "send": "Enviar"}}, "rabbyBadge": {"imageLabel": "<PERSON><PERSON>", "title": "Reivindique a Medalha <PERSON> para", "enterClaimCode": "Digite o código de reivindicação", "swapTip": "Você precisa concluir uma troca com uma dex notável dentro da <PERSON> Wallet primeiro.", "goToSwap": "Ir para a Troca", "claim": "Reivindicar", "viewYourClaimCode": "Ver seu código de reivindicação", "noCode": "Você ainda não ativou o código de reivindicação para este endereço", "learnMoreOnDebank": "<PERSON><PERSON> mais no DeBank", "rabbyValuedUserNo": "<PERSON><PERSON><PERSON><PERSON> Nº {{num}}", "claimSuccess": "Reivindicação bem-sucedida", "viewOnDebank": "Ver no DeBank", "learnMore": "<PERSON><PERSON> mais", "freeGasTitle": "Reivindicar Insígnia de Gas Gratuito para", "freeGasTip": "Por favor, assine uma transação usando Free Gas. O botão 'Free Gas' aparecerá automaticamente quando o seu gás não for suficiente.", "freeGasNoCode": "Clique no botão abaixo para visitar o DeBank e obter o código de reivindicação usando seu endereço atual primeiro.", "rabbyFreeGasUserNo": "<PERSON><PERSON><PERSON><PERSON> Rabby Free Gas Nº {{num}}"}, "contacts": {"noDataLabel": "sem dados", "noData": "Sem dados", "oldContactList": "Lista de Contatos Antiga", "oldContactListDescription": "Devido à fusão de contatos e endereços do modo de observação, os contatos antigos serão armazenados aqui para você, e após algum tempo, excluiremos a lista. Por favor, adicione em tempo se você continuar a usá-la."}, "security": {"tokenApproval": "Aprovação de Tokens", "nftApproval": "Aprovação de NFTs", "comingSoon": "Mais recursos em breve", "title": "Segurança"}, "settings": {"lock": {"never": "Nunca"}, "7Days": "7 dias", "1Day": "1 dia", "4Hours": "4 horas", "1Hour": "1 hora", "10Minutes": "10 minutos", "backendServiceUrl": "URL do Serviço de Backend", "inputOpenapiHost": "Por favor, insira o host do OpenAPI", "pleaseCheckYourHost": "Por favor, verifique o seu host", "host": "Host", "reset": "Restaurar configuração inicial", "save": "<PERSON><PERSON>", "pendingTransactionCleared": "Transações pendentes removidas", "clearPending": "Limpar Pendentes Localmente", "clearPendingTip1": "Esta ação remove a transação pendente da sua interface, ajudando a resolver problemas causados por longas durações de pendência na rede.", "clearPendingTip2": "<PERSON>so não afeta os saldos da sua conta nem exige a reinserção da sua seed phrase. Todos os ativos e detalhes da conta permanecem seguros.", "autoLockTime": "Tempo de Bloqueio Automático", "claimRabbyBadge": "Reivindique a Medalha <PERSON>!", "cancel": "<PERSON><PERSON><PERSON>", "enableWhitelist": "Ativar Lista Branca", "disableWhitelist": "Desativar Lista Branca", "enableWhitelistTip": "De<PERSON><PERSON> de ativada, você só poderá enviar ativos para os endereços na lista branca usando a Rabby.", "disableWhitelistTip": "Você pode enviar ativos para qualquer endereço uma vez desativada", "warning": "Aviso", "clearWatchAddressContent": "Você tem certeza de que deseja excluir todos os endereços do modo de observação?", "updateVersion": {"content": "Uma nova atualização para a Rabby Wallet está disponível. Clique para verificar como atualizar manualmente.", "okText": "Ver Tutorial", "successTip": "Você está usando a versão mais recente", "title": "Atualização Disponível"}, "features": {"label": "Recursos", "lockWallet": "Bloquear Carteira", "signatureRecord": "Registro de Assinaturas", "manageAddress": "Gerenciar Endereço", "connectedDapp": "<PERSON><PERSON>", "gasTopUp": "Recarga de Gas", "searchDapps": "<PERSON><PERSON><PERSON><PERSON>", "rabbyPoints": "<PERSON><PERSON>"}, "settings": {"label": "Configurações", "enableWhitelistForSendingAssets": "Ativar Lista Branca Para Envio de Ativos", "customRpc": "RPC Personalizado", "metamaskPreferredDapps": "Dapps Preferidos do MetaMask", "currentLanguage": "Idioma Atual", "enableTestnets": "Ativar Redes de Teste", "toggleThemeMode": "<PERSON><PERSON> de <PERSON>", "customTestnet": "Adicionar Rede Personalizada", "themeMode": "<PERSON><PERSON>", "metamaskMode": "Conectar Rabby disfarçando como MetaMask", "enableDappAccount": "Alternar Endereços de DApps Independentemente\n"}, "aboutUs": "Sobre nós", "currentVersion": "<PERSON><PERSON><PERSON>", "updateAvailable": "Atualização Disponível", "supportedChains": "<PERSON><PERSON>", "followUs": "Siga-nos", "testnetBackendServiceUrl": "URL do Serviço de Backend de Teste", "clearWatchMode": "Limpar <PERSON> de Observação", "requestDeBankTestnetGasToken": "Solicitar Token de Gás de Testnet DeBank", "clearPendingWarningTip": "A transação removida ainda pode ser confirmada na blockchain a menos que seja substituída.", "DappAccount": {"title": "Alternar Endereço do DApp Independentemente\n", "button": "Ativar\n", "desc": "Uma vez habilitado, você pode escolher qual endereço conectar a cada DApp individualmente. Alterar seu endereço principal não afetará o endereço conectado a cada DApp.\n"}}, "tokenDetail": {"blockedTip": "Tokens bloqueados não serão exibidos na lista de tokens", "blocked": "Bloqueado", "selectedCustom": "Este token não é listado pela Rabby. Você o adicionou à lista de tokens personalizados.", "notSelectedCustom": "Este token não é listado pela Rabby. Ele será adicionado à lista de tokens se você ativá-lo.", "customized": "Personalizado", "scamTx": "Transação Suspeita", "txFailed": "Falha na Transação", "notSupported": "Este token nesta rede não é suportado", "swap": "Trocar", "send": "Enviar", "receive": "<PERSON><PERSON><PERSON>", "noTransactions": "Nenhuma Transação", "customizedButton": "personalizado", "blockedButton": "bloqueado", "customizedButtons": "tokens personalizados", "blockedButtons": "tokens bloqueados", "blockedListTitle": "token bloqueado", "ListedBy": "Listados por", "BridgeIssue": "<PERSON><PERSON> bridged por um terceiro", "customizedListTitles": "tokens personalizados", "NoSupportedExchanges": "Nenhuma exchange suportada disponível", "OriginalToken": "Token Original", "blockedListTitles": "tokens bloqueados", "verifyScamTips": "Isto é um token de golpe", "ContractAddress": "Endereço do Contrato", "AddToMyTokenList": "Adicionar à minha lista de tokens", "TokenName": "Nome do Token", "noIssuer": "Nenhuma informação do emissor disponível", "Chain": " cadeia", "customizedListTitle": "token personalizado", "IssuerWebsite": "Website do Emissor", "blockedTips": "Tokens bloqueados não serão exibidos na lista de tokens.", "SupportedExchanges": "Exchanges Suportados", "maybeScamTips": "Este é um token de baixa qualidade e pode ser uma fraude.", "NoListedBy": "Nenhuma informação de listagem disponível", "customizedHasAddedTips": "O token não está listado pelo Rabby. Você o adicionou manualmente à lista de tokens.", "OriginIssue": "Nativamente emitido nesta blockchain", "fdvTips": "A capitalização de mercado se o fornecimento máximo estivesse em circulação. Avaliação Totalmente Dilucionada (FDV) = Preço x Fornecimento Máximo. Se o Fornecimento Máximo for nulo, FDV = Preço x Fornecimento Total. Se nem o Fornecimento Máximo nem o Fornecimento Total forem definidos ou forem infinitos, o FDV não é exibido.", "myBalance": "<PERSON><PERSON>", "BridgeProvider": "<PERSON><PERSON><PERSON>"}, "assets": {"usdValue": "VALOR EM USD", "amount": "QUANTIDADE", "portfolio": {"nftTips": "Calculado com base no preço mínimo reconhecido por este protocolo.", "fractionTips": "Calculado com base no preço do token ERC20 vinculado."}, "tokenButton": {"subTitle": "O token nesta lista não será adicionado ao saldo total"}, "table": {"assetAmount": "Ativo / Quantidade", "price": "Preço", "useValue": "Valor em USD", "healthRate": "Taxa de Saúde", "debtRatio": "Taxa de Dívida", "unlockAt": "Desbloquear em", "lentAgainst": "EMPRESTADO CONTRA", "type": "Tipo", "strikePrice": "Preço de Exercício", "exerciseEnd": "Fim do Exercício", "tradePair": "Par de Negociação", "side": "Lado", "leverage": "Alavancagem", "PL": "Lucro/Prejuízo", "unsupportedPoolType": "Tipo de Pool não Suportado", "claimable": "Reivindicável", "endAt": "Termina em", "dailyUnlock": "Desbloqueio <PERSON>", "pool": "POOL", "token": "Token", "balanceValue": "Saldo / Valor", "percent": "Porcentagem", "summaryTips": "Valor do ativo dividido pelo patrimônio líquido total", "summaryDescription": "Todos os ativos em protocolos (por exemplo, tokens LP) são resolvidos para os ativos subjacentes para cálculos estatísticos", "noMatch": "Sem Correspondência", "lowValueDescription": "Ativos de baixo valor serão exibidos aqui", "lowValueAssets": "{{count}} ativos de baixo valor", "lowValueAssets_0": "{{count}} low value token", "lowValueAssets_one": "{{count}} token de baixo valor", "lowValueAssets_other": "{{count}} tokens de baixo valor"}, "noAssets": "Sem ativos", "blockLinkText": "Pesquise o endereço para bloquear o token", "blockDescription": "Os tokens bloqueados por você serão exibidos aqui", "unfoldChain": "Expandir 1 rede", "unfoldChainPlural": "Expandir {{moreLen}} redes", "customLinkText": "Pesquise o endereço para adicionar um token personalizado", "customDescription": "Os tokens personalizados adicionados por você serão exibidos aqui", "comingSoon": "Em Breve...", "searchPlaceholder": "Tokens", "AddMainnetToken": {"selectChain": "Selecionar chain", "tokenAddressPlaceholder": "Endereço do Token", "title": "<PERSON><PERSON><PERSON><PERSON>", "searching": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isBuiltInToken": "Token já suportado", "tokenAddress": "Endereço do Token", "notFound": "Token não encontrado"}, "AddTestnetToken": {"selectChain": "Selecionar chain", "title": "<PERSON><PERSON><PERSON><PERSON> de Rede Personalizado", "tokenAddress": "Endereço do Token", "searching": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokenAddressPlaceholder": "Endereço do Token", "notFound": "Token not found"}, "TestnetAssetListContainer": {"add": "Token", "addTestnet": "Rede"}, "customButtonText": "Adicionar token personalizado", "addTokenEntryText": "Token", "noTestnetAssets": "Nenhum ativo da rede personalizada"}, "hd": {"howToConnectLedger": "Como Conectar o Ledger", "userRejectedTheRequest": "O usuário rejeitou a solicitação.", "ledger": {"doc1": "Conecte um único Ledger", "doc2": "Insira o PIN para desbloquear", "doc3": "Abra o aplicativo Ethereum", "reconnect": "Se não funcionar, tente <1>reconectar desde o início.</1>", "connected": "Ledger conectado"}, "howToSwitch": "Como Alternar", "keystone": {"title": "Verifique se o seu Keystone 3 Pro está na página inicial", "doc3": "<PERSON><PERSON>r <PERSON> com o computador", "doc2": "Digite a senha para desbloquear", "reconnect": "Se não funcionar, por favor, tente <1>reconectar desde o início.</1>", "doc1": "Conecte um único Keystone"}, "imkey": {"doc2": "Digite o PIN para desbloquear", "doc1": "Conecte um único imKey"}, "howToConnectKeystone": "Como conectar Keystone", "howToConnectImKey": "Como conectar imKey", "ledgerIsDisconnected": "Seu Ledger não está conectado"}, "GnosisWrongChainAlertBar": {"warning": "O endereço seguro não suporta {{chain}}", "notDeployed": "Seu endereço Safe não está implantado nesta rede"}, "echologyPopup": {"title": "Ecossistema"}, "MetamaskModePopup": {"title": "<PERSON><PERSON>", "footerText": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> ao Modo MetaMask em Mais > Modo MetaMask", "toastSuccess": "Habilitado. Atualize a página para reconectar.", "enableDesc": "Habilitar se o Dapp funcionar apenas com MetaMask", "desc": "Se você não conseguir conectar o Rabby em um Dapp, ative o Modo MetaMask e conecte selecionando a opção MetaMask."}, "offlineChain": {"chain": "{{chain}} em breve não será integrado.", "tips": "A cadeia {{chain}} não será integrada em {{date}}. Seus ativos não serão afetados, mas não serão incluídos no seu saldo total. Para acessá-los, você pode adicioná-la como uma rede personalizada em \"Mais\"."}, "recentConnectionGuide": {"title": "Alterar o endereço para conexão com o Dapp aqui\n", "button": "Entendido\n"}}, "nft": {"floorPrice": "/ Preço Mínimo:", "title": "NFT", "all": "Todos", "starred": "Favoritos ({{count}})", "empty": {"title": "Nenhum NFT Favorito", "description": "Você pode selecionar NFTs em \"Todos\" e adicioná-los aos \"Favoritos\""}, "noNft": "Nenhum NFT"}, "newAddress": {"title": "Adicionar um Endereço", "importSeedPhrase": "Importar Frase de Recuperação", "importPrivateKey": "Importar Chave Privada", "importMyMetamaskAccount": "Importar Minha Conta do MetaMask", "addContacts": {"content": "<PERSON><PERSON><PERSON><PERSON>", "description": "Você também pode usá-lo como um endereço de observação", "required": "Por favor, insira um endereço", "notAValidAddress": "Não é um endereço válido", "scanViaMobileWallet": "Escanear via carteira móvel", "scanViaPcCamera": "Escanear via câmera de PC", "scanQRCode": "Escanear códigos QR com carteiras compatíveis com WalletConnect", "walletConnect": "WalletConnect", "walletConnectVPN": "O WalletConnect será instável se você usar uma VPN.", "cameraTitle": "Por favor, escaneie o código QR com sua câmera", "addressEns": "Endereço / ENS"}, "unableToImport": {"title": "Não é possível importar", "description": "Não é suportada a importação de várias carteiras de hardware baseadas em QR. Por favor, exclua todos os endereços de {{0}} antes de importar outro dispositivo."}, "connectHardwareWallets": "Conectar Carteiras de Hardware", "connectMobileWalletApps": "Conectar Aplicativos de Carteira Móvel", "connectInstitutionalWallets": "Conectar Carteiras Institucionais", "createNewSeedPhrase": "Criar Nova Frase de Recuperação", "importKeystore": "Importar Arquivo Keystore", "selectImportMethod": "Selecionar Método de Importação", "theSeedPhraseIsInvalidPleaseCheck": "A frase de recuperação é inválida, por favor, verifique!", "seedPhrase": {"importTips": "Você pode colar toda a sua frase de recuperação secreta no primeiro campo", "whatIsASeedPhrase": {"question": "O que é uma Frase de Recuperação?", "answer": "Uma frase de 12, 18 ou 24 palavras usada para controlar seus ativos."}, "isItSafeToImportItInRabby": {"question": "É seguro importá-la no Rabby?", "answer": "<PERSON><PERSON>, ela será armazenada localmente em seu navegador e só será acessível por você."}, "importError": "[CreateMnemonics] etapa inesperada {{0}}", "importQuestion4": "Se eu desinstalar o Rabby sem fazer backup da frase de recuperação, o Rabby não poderá recuperá-la para mim.", "riskTips": "<PERSON><PERSON> de começar, leia e tenha em mente as seguintes dicas de segurança.", "showSeedPhrase": "Mostrar Frase de Recuperação", "backup": "Backup da Frase de Recuperação", "backupTips": "Certifique-se de que ninguém mais esteja vendo sua tela ao fazer backup da frase de recuperação", "copy": "Copiar frase de recuperação", "saved": "Eu Salvei a Frase", "pleaseSelectWords": "Por favor, selecione as palavras", "verificationFailed": "Verificação falhou", "createdSuccessfully": "Criado com Sucesso", "verifySeedPhrase": "Verificar Frase de Recuperação", "fillInTheBackupSeedPhraseInOrder": "Preencha a frase de recuperação de backup na ordem correta", "wordPhrase": "Eu tenho uma frase de <1>{{count}}</1> palavras", "clearAll": "<PERSON><PERSON>", "slip39SeedPhrasePlaceholder_other": "Insira suas {{count}}ª partes da seed phrase aqui", "slip39SeedPhrasePlaceholder_one": "Insira suas {{count}}ª partes da frase semente aqui", "pastedAndClear": "Colado e área de transferência limpa", "invalidContent": "<PERSON><PERSON><PERSON><PERSON>", "slip39SeedPhrase": "Eu tenho uma frase-semente <0>{{SLIP39}}</0>", "slip39SeedPhraseWithPassphrase": "Eu tenho uma <0>{{SLIP39}}</0> Seed Phrase com Senha", "wordPhraseAndPassphrase": "Eu tenho uma frase de <1>{{count}}</1> palavras com Passphrase", "inputInvalidCount_one": "1 entrada não está conforme as norm<PERSON> <PERSON>d Phrase, por favor verifique.", "slip39SeedPhrasePlaceholder_two": "Insira suas {{count}}ª partes da seed phrase aqui", "slip39SeedPhrasePlaceholder_few": "Insira aqui suas {{count}}ª partes da frase de recuperação", "passphrase": "Passphrase", "inputInvalidCount_other": "{{count}} inputs não estão em conformidade com as normas da <PERSON>d Phrase, por favor, verifique.", "importQuestion1": "Se eu perder ou compartilhar minha frase semente, perderei o acesso aos meus ativos permanentemente.", "importQuestion2": "Minha frase-semente é armazenada apenas no meu dispositivo. <PERSON><PERSON> não pode acessar.", "importQuestion3": "Se eu desinstalar o Rabby sem fazer backup da minha frase-semente, não poderá ser recuperado pelo <PERSON>."}, "metamask": {"step1": "Exporte a frase de recuperação ou chave privada do MetaMask <1><PERSON><PERSON><PERSON> <2/></1>", "step2": "Importe a frase de recuperação ou chave privada no Rabby", "step3": "A importação está concluída e todos os seus ativos serão <br /> exibidos automaticamente", "how": "Como importar minha Conta do MetaMask?", "step": "Etapa", "importSeedPhrase": "Importar a frase de recuperação ou chave privada", "importSeedPhraseTips": "Ela será armazenada apenas localmente no navegador. O Rabby nunca terá acesso às suas informações privadas.", "tips": "Dicas:", "tipsDesc": "Sua frase-semente/chave privada não pertence à MetaMask ou a qualquer carteira específica; ela pertence somente a você."}, "privateKey": {"required": "Por favor, insira a Chave Privada", "placeholder": "Digite sua Chave Privada", "whatIsAPrivateKey": {"question": "O que é uma Chave Privada?", "answer": "Uma sequência de letras e números usada para controlar seus ativos."}, "isItSafeToImportItInRabby": {"question": "É seguro importá-la no Rabby?", "answer": "<PERSON><PERSON>, ela será armazenada localmente em seu navegador e só será acessível por você."}, "isItPossibleToImportKeystore": {"question": "É possível importar o arquivo Keystore?", "answer": "<PERSON><PERSON>, você pode <1>importar o arquivo Keystore</1> aqui."}, "notAValidPrivateKey": "Chave privada inválida", "repeatImportTips": {"desc": "Este endereço já foi importado.", "question": "Você deseja mudar para este endereço?"}}, "importedSuccessfully": "Importado com Sucesso", "ledger": {"title": "Conectar Ledger", "cameraPermissionTitle": "<PERSON><PERSON><PERSON> que o Rabby acesse a câmera", "cameraPermission1": "<PERSON><PERSON><PERSON> que o Rabby acesse a câmera na janela pop-up do navegador", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> per<PERSON> para o Rabby:", "ledgerPermission1": "Conectar-se a um dispositivo HID", "ledgerPermissionTip": "Por favor, clique em \"Permitir\" abaixo e autorize o acesso ao seu Ledger na janela pop-up seguinte.", "permissionsAuthorized": "Permissões Autorizadas", "nowYouCanReInitiateYourTransaction": "Agora você pode reiniciar sua transação.", "allow": "<PERSON><PERSON><PERSON>", "error": {"ethereum_app_unconfirmed_error": "Você negou a solicitação para abrir o aplicativo Ethereum.", "ethereum_app_open_error": "Por favor, instale/aceite o aplicativo Ethereum no seu dispositivo Ledger.", "running_app_close_error": "Falha ao fechar o aplicativo em execução no seu dispositivo Ledger.", "ethereum_app_not_installed_error": "Por favor, instale o aplicativo Ethereum no seu dispositivo Ledger."}}, "walletConnect": {"connectYour": "Conectar sua", "viaWalletConnect": "via Wallet Connect", "connectedSuccessfully": "Conectado com sucesso", "qrCodeError": "Por favor, verifique sua rede ou atualize o código QR", "qrCode": "Código QR", "url": "URL", "changeBridgeServer": "Alt<PERSON>r servidor de ponte", "status": {"received": "Leitura bem-sucedida. Aguardando confirm<PERSON>", "rejected": "Conexão cancelada. Por favor, escaneie o código QR para tentar novamente.", "brandError": "Aplicativo de carteira incorreto.", "brandErrorDesc": "Por favor, use {{brandName}} para conectar", "accountError": "Endereço não corresponde.", "accountErrorDesc": "Por favor, alterne o endereço em sua carteira móvel", "connected": "Conectado", "duplicate": "O endereço que você está tentando importar é duplicado", "default": "Escaneie com sua {{brand}}"}, "title": "Conectar com {{brandName}}", "disconnected": "Desconectado", "accountError": {}, "tip": {"accountError": {"tip1": "<PERSON><PERSON><PERSON><PERSON>, mas incapaz de assinar.", "tip2": "Por favor, mude para o endereço correto na carteira móvel"}, "disconnected": {"tip": "Não conectado ao {{brandName}}"}, "connected": {"tip": "Conectado ao {{brandName}}"}}, "button": {"disconnect": "Desconectar", "connect": "Conectar", "howToSwitch": "Como Alternar"}}, "hd": {"tooltip": {"removed": "O endereço foi removido do Rabby", "added": "O endereço foi adicionado ao Rabby", "connectError": "A conexão foi interrompida. Por favor, atualize a página para reconectar.", "disconnected": "Não é possível conectar à carteira de hardware. Por favor, tente se reconectar."}, "waiting": "Aguardando", "clickToGetInfo": "Clique para obter informações na cadeia", "addToRabby": "Adicionar a<PERSON>", "basicInformation": "Informações básicas", "addresses": "Endereços", "loadingAddress": "Carregando {{0}}/{{1}} endereços", "notes": "Notas", "getOnChainInformation": "Obter informações na cadeia", "hideOnChainInformation": "Ocultar informações na cadeia", "usedChains": "<PERSON><PERSON> usadas", "firstTransactionTime": "Primeira transação", "balance": "<PERSON><PERSON>", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON>in<PERSON> HD oficial da Ledger. Nos primeiros 3 endereços, há endereços usados na cadeia.", "bip44": "Padrão BIP44: Caminho HD definido pelo protocolo BIP44. Nos primeiros 3 endereços, há endereços usados na cadeia.", "legacy": "Legacy: <PERSON><PERSON><PERSON> HD usado pela MEW / Mycrypto. Nos primeiros 3 endereços, há endereços usados na cadeia."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: Caminho HD oficial da Ledger. Nos primeiros 3 endereços, não há endereços usados na cadeia.", "bip44": "Padrão BIP44: Caminho HD definido pelo protocolo BIP44. Nos primeiros 3 endereços, não há endereços usados na cadeia.", "legacy": "Legacy: <PERSON>in<PERSON> HD usado pela MEW / Mycrypto. Nos primeiros 3 endereços, não há endereços usados na cadeia."}}, "trezor": {"hdPathType": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> HD oficial do Ledger.", "legacy": "Legacy: caminho HD usado por MEW / Mycrypto."}, "hdPathTypeNoChain": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44.", "legacy": "Legacy: HD path used by MEW / Mycrypto.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> HD oficial da Ledger."}, "message": {"disconnected": "{{0}}A conexão foi interrompida. Por favor, atualize a página para reconectar."}}, "onekey": {"hdPathType": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44."}}, "mnemonic": {"hdPathType": {"default": "Padrão: O caminho HD padrão para importar uma frase de recuperação é usado.", "bip44": "Padrão BIP44: HDpath definido pelo protocolo BIP44.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON> HD oficial da Ledger.", "legacy": "Legacy: caminho HD usado por MEW / Mycrypto."}, "hdPathTypeNoChain": {"default": "Padrão: O caminho HD padrão para importar uma frase de recuperação é usado."}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON>in<PERSON> HD oficial da Ledger. Nos primeiros 3 endereços, há endereços usados na cadeia.", "bip44": "Padrão BIP44: Caminho HD definido pelo protocolo BIP44. Nos primeiros 3 endereços, há endereços usados na cadeia.", "legacy": "Legacy: <PERSON><PERSON><PERSON> HD usado pela MEW / Mycrypto. Nos primeiros 3 endereços, há endereços usados na cadeia."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: Caminho HD oficial da Ledger. Nos primeiros 3 endereços, não há endereços usados na cadeia.", "bip44": "Padrão BIP44: Caminho HD definido pelo protocolo BIP44. Nos primeiros 3 endereços, não há endereços usados na cadeia.", "legacy": "Legacy: <PERSON>in<PERSON> HD usado pela MEW / Mycrypto. Nos primeiros 3 endereços, não há endereços usados na cadeia."}, "switch": {"title": "Alternar para um novo dispositivo GridPlus", "content": "Não é suportada a importação de vários dispositivos GridPlus. Se você alternar para um novo dispositivo GridPlus, a lista de endereços do dispositivo atual será removida antes de iniciar o processo de importação."}, "switchToAnotherGridplus": "Alternar para outro GridPlus"}, "keystone": {"hdPathType": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44.", "legacy": "Legado: caminho HD usado por MEW / Mycrypto.", "ledgerLive": "Ledger Live: caminho HD oficial do Ledger. Você só pode gerenciar 10 endereços com o caminho do Ledger Live."}, "hdPathTypeNochain": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44.", "legacy": "Legacy: caminho HD usado por MEW / Mycrypto.", "ledgerLive": "Ledger Live: caminho HD oficial da Ledger. Você só pode gerenciar 10 endereços com o caminho Ledger Live."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: Caminho HD definido pelo protocolo BIP44."}, "disconnected": "Não é possível conectar ao BitBox02. <PERSON>r favor, atualize a página para reconectar. Motivo: {{0}}"}, "selectHdPath": "Selecionar Caminho HD:", "selectIndexTip": "Selecionar o número de série dos endereços para começar a partir de:", "manageAddressFrom": "Gerenciar endereços de {{0}} a {{1}}", "advancedSettings": "Configurações Avançadas", "customAddressHdPath": "Caminho HD de Endereço Personalizado", "connectedToLedger": "Conectado à Ledger", "connectedToTrezor": "Conectado à Trezor", "connectedToOnekey": "Conectado ao OneKey", "manageSeedPhrase": "Gerenciar Frase de Recuperação", "manageGridplus": "Gerenciar GridPlus", "manageKeystone": "Gerenciar Keystone", "manageAirgap": "Gerenciar AirGap", "manageCoolwallet": "Gerenciar CoolWallet", "manageBitbox02": "Gerenciar BitBox02", "manageNgraveZero": "Gerenciar NGRAVE ZERO", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addressesIn": "Endereços em {{0}}", "addressesInRabby": "Endereços em Rabby{{0}}", "qrCode": {"switch": {"title": "Alternar para um novo dispositivo {{0}}", "content": "Não é suportada a importação de múltiplos dispositivos {{0}}. Se você alternar para um novo dispositivo {{0}}, a lista de endereços do dispositivo atual será removida antes de iniciar o processo de importação."}, "switchAnother": "Alternar para outro {{0}}"}, "manageImtokenOffline": "Gerenciar imToken", "manageImKey": "Gerenciar imKey", "importBtn": "Importar ({{count}})"}, "importYourKeystore": "Importar seu Arquivo de Keystore", "incorrectPassword": "Senha incorreta", "keystore": {"description": "Selecione o arquivo de keystore que deseja importar e insira a senha correspondente", "password": {"required": "Por favor, insira a senha", "placeholder": "<PERSON><PERSON>"}}, "coboSafe": {"inputSafeModuleAddress": "Digite o endereço do módulo seguro", "invalidAddress": "Endereço inválido", "whichChainIsYourCoboAddressOn": "Em qual blockchain está o seu endereço Cobo", "addCoboArgusAddress": "Adicionar endereço Cobo Argus", "findTheAssociatedSafeAddress": "Encontrar o endereço seguro associado", "import": "Importar"}, "imkey": {"title": "Conectar imKey", "imkeyPermissionTip": "Por favor, clique em \"Permitir\" abaixo e autorize o acesso ao seu imKey na janela pop-up seguinte."}, "keystone": {"allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> do Rabby para:", "title": "Conectar Keystone", "unknowError": "<PERSON><PERSON> desconhecido, por favor tente novamente", "deviceIsLockedError": "Digite a senha para desbloquear", "deviceRejectedExportAddress": "<PERSON><PERSON><PERSON> com Rabby", "exportAddressJustAllowedOnHomePage": "Export address just allowed on home page", "noDeviceFoundError": "Conecte um único Keystone", "keystonePermission1": "Conectar a um dispositivo USB", "keystonePermissionTip": "Clique em \"Permitir\" abaixo para autorizar o acesso ao seu Keystone na janela pop-up a seguir, e certifique-se de que seu Keystone 3 Pro esteja na página inicial.", "deviceIsBusy": "Dispositivo está ocupado"}, "firefoxLedgerDisableTips": "Ledger não é compatível com Firefox", "addFromCurrentSeedPhrase": "<PERSON><PERSON><PERSON><PERSON>"}, "unlock": {"btn": {"unlock": "Desb<PERSON>que<PERSON>"}, "password": {"required": "Digite a Senha para Desbloquear", "placeholder": "Digite a Senha para Desbloquear", "error": "Senha incorreta"}, "btnForgotPassword": "Esque<PERSON>u a senha?", "title": "<PERSON><PERSON>", "description": "A carteira revolucionária para Ethereum e todas as cadeias EVM"}, "addToken": {"noTokenFound": "Nenhum token encontrado", "tokenSupported": "O token já é suportado no Rabby", "tokenCustomized": "O token atual já foi adicionado aos personalizados", "tokenNotFound": "Token não encontrado neste endereço de contrato", "title": "Adicionar token personalizado a<PERSON>", "balance": "<PERSON><PERSON>", "tokenOnMultiChains": "Endereço do token em várias cadeias. Por favor, escolha uma", "noTokenFoundOnThisChain": "Nenhum token encontrado nesta cadeia", "hasAdded": "Você adicionou este token."}, "switchChain": {"title": "Mudando para {{chain}}", "chainNotSupport": "A cadeia solicitada ainda não é suportada pelo Rabby", "testnetTip": "Por favor, ative \"Ativar Testnets\" em \"Mais\" antes de conectar-se a testnets", "unknownChain": "<PERSON><PERSON> desconhecida", "addChain": "<PERSON><PERSON><PERSON><PERSON>", "requestsReceived": "1 request received", "chainId": "ID da Chain:", "desc": "A rede solicitada ainda não está integrada pelo Rabby. Você pode adicioná-la como uma rede personalizada manualmente.", "chainNotSupportYet": "A cadeia solicitada ainda não é suportada pelo Rabby.", "requestsReceivedPlural": "{{count}} solicita<PERSON><PERSON><PERSON> recebidas", "chainNotSupportAddChain": "A cadeia solicitada ainda não está integrada pelo Rabby. Você pode adicioná-la como uma Rede de Teste Personalizada.", "requestRabbyToSupport": "Solicitar suporte do Rabby"}, "signText": {"title": "<PERSON><PERSON><PERSON>", "message": "Mensagem", "createKey": {"interactDapp": "Interagir com Dapp", "description": "Descrição"}, "sameSafeMessageAlert": "A mesma mensagem é confirmada; nenhuma assinatura adicional é necessária."}, "securityEngine": {"yes": "<PERSON>m", "no": "Não", "whenTheValueIs": "quando o valor for {{value}}", "currentValueIs": "Valor atual é {{value}}", "viewRules": "Ver regras de segurança", "undo": "<PERSON><PERSON><PERSON>", "riskProcessed": "Risco Processado", "ignoreAlert": "Ignorar o alerta", "ruleDisabled": "As regras de segurança foram desativadas. Para sua segurança, você pode ativá-las a qualquer momento.", "unknownResult": "Resultado desconhecido porque a regra de segurança não está disponível", "alertTriggerReason": "Motivo do acionamento do alerta:", "understandRisk": "Eu entendo e aceito a responsabilidade por qualquer perda", "forbiddenCantIgnore": "Risco proibido encontrado que não pode ser ignorado.", "ruleDetailTitle": "<PERSON><PERSON><PERSON> da Regra", "enableRule": "Ativar a regra", "viewRiskLevel": "Ver nível de risco"}, "connect": {"listedBy": "Listado por", "sitePopularity": "Popularidade do site", "myMark": "Minha Marcação", "flagByRabby": "<PERSON><PERSON><PERSON><PERSON>", "flagByMM": "Sin<PERSON><PERSON><PERSON> pelo MetaMask", "flagByScamSniffer": "<PERSON><PERSON><PERSON><PERSON>elo <PERSON>am<PERSON>ni<PERSON>", "verifiedByRabby": "Verificado pelo <PERSON>", "foundForbiddenRisk": "Riscos proibidos encontrados. A conexão está bloqueada.", "markAsTrustToast": "Marcar como \"Confiável\"", "markAsBlockToast": "Marcar como \"Bloqueado\"", "markRemovedToast": "Marc<PERSON> removida", "title": "Conectar ao Dapp", "selectChainToConnect": "Selecione uma cadeia para conectar", "markRuleText": "Minha Marcação", "connectBtn": "Conectar", "noWebsite": "<PERSON><PERSON><PERSON>", "popularLevelHigh": "Alta", "popularLevelMedium": "Média", "popularLevelLow": "Baixa", "popularLevelVeryLow": "<PERSON><PERSON>", "noMark": "Sem marca", "blocked": "Bloqueado", "trusted": "<PERSON><PERSON><PERSON><PERSON>", "addedToWhitelist": "Adicionado à sua lista de permissões", "addedToBlacklist": "Adicionado à sua lista de bloqueios", "removedFromAll": "<PERSON><PERSON><PERSON><PERSON> as listas", "notOnAnyList": "Não está em nenhuma lista", "onYourBlacklist": "Na sua lista de bloqueios", "onYourWhitelist": "Na sua lista de permissões", "manageWhiteBlackList": "Gerenciar lista branca/lista negra", "SignTestnetPermission": {"title": "Permissão de assinatura de testnet"}, "ignoreAll": "<PERSON><PERSON><PERSON> tudo", "SelectWallet": {"desc": "Escolha entre as carteiras que você instalou", "title": "Selecione uma Carteira para Conectar"}, "otherWalletBtn": "Conectar com Outra Wallet", "connectAddress": "Conectar endereço\n"}, "addressDetail": {"add-to-whitelist": "Adicionar à Lista Branca", "remove-from-whitelist": "Remover da Lista Branca", "address-detail": "Detalhes do Endereço", "backup-private-key": "Backup da Chave Privada", "backup-seed-phrase": "<PERSON><PERSON> da <PERSON>ase-secreta (Seed Phrase)", "delete-address": "Excluir <PERSON>", "delete-desc": "Antes de excluir, tenha em mente os seguintes pontos para entender como proteger seus ativos.", "direct-delete-desc": "Este endereço é um endereço {{renderBrand}}, <PERSON><PERSON> não armazena a chave privada ou a frase-secreta (seed phrase) para este endereço, você pode simplesmente excluí-lo", "admins": "Administradores", "tx-requires": "Qualquer transação requer <2>{{num}}</2> <PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-memo-title": "Editar nota do endereço", "please-input-address-note": "Por favor, insira uma nota para o endereço", "address": "Endereço", "address-note": "Nota do Endereço", "assets": "Ativos", "qr-code": "Código QR", "source": "Fonte", "hd-path": "Caminho HD", "manage-seed-phrase": "Gerenciar Frase-secreta (Seed Phrase)", "manage-addresses-under-this-seed-phrase": "Gerenciar endereços sob esta Frase-secreta (Seed Phrase)", "safeModuleAddress": "Endereço do Módulo de Segurança", "coboSafeErrorModule": "O endereço expirou, por favor, exclua-o e importe-o novamente.", "importedDelegatedAddress": "Endereço Delegado Importado", "manage-addresses-under": "Gere<PERSON>ie endereços sob este {{brand}}"}, "preferMetamaskDapps": {"title": "Dapps Preferidos do MetaMask", "desc": "<PERSON><PERSON> <PERSON><PERSON><PERSON> dapps permanecerão conectados através do MetaMask, independentemente da carteira que você tenha selecionado", "howToAdd": "Como Adicionar", "howToAddDesc": "Clique com o botão direito no site e encontre esta opção", "empty": "<PERSON><PERSON><PERSON> dapp"}, "customRpc": {"opened": "Abe<PERSON>o", "closed": "<PERSON><PERSON><PERSON>", "empty": "Nenhuma URL RPC personalizada", "title": "RPC Personalizado", "desc": "Ativar o RPC personalizado substituirá o Rabby como o nó padrão. Para continuar usando o Rabby, desative ou remova o nó RPC personalizado.", "add": "Adicionar R<PERSON>", "EditRPCModal": {"invalidRPCUrl": "URL RPC Inválida", "invalidChainId": "ID da Cadeia Inválido", "rpcAuthFailed": "Falha na Autenticação RPC", "title": "Editar <PERSON>", "rpcUrl": "URL RPC", "rpcUrlPlaceholder": "Insira a URL RPC"}, "EditCustomTestnetModal": {"title": "Adicionar Rede Personalizada", "quickAdd": "Adicionar rapidamente a partir do Chainlist"}}, "requestDebankTestnetGasToken": {"title": "Solicitar Token de Gás de Testnet DeBank", "mintedTip": "Titulares do Rabby Badge podem solicitar uma vez por dia", "notMintedTip": "Solicitação disponível apenas para titulares do Rabby <PERSON>", "claimBadgeBtn": "Reivindicar <PERSON><PERSON>", "time": "Por dia", "requested": "Você já solicitou hoje", "requestBtn": "Solicitar"}, "safeQueue": {"title": "<PERSON><PERSON>", "sameNonceWarning": "Essas transações entram em conflito, pois usam o mesmo nonce. Executar uma delas substituirá automaticamente as outras.", "loading": "Carregando transações pendentes", "noData": "Nenhuma transação pendente", "loadingFaild": "Devido à instabilidade do servidor Safe, os dados não estão disponíveis. Por favor, verifique novamente após 5 minutos", "accountSelectTitle": "Você pode enviar esta transação usando qualquer endereço", "LowerNonceError": "A transação com nonce {{nonce}} precisa ser executada primeiro", "submitBtn": "Enviar transação", "unknownTx": "Transação Desconhecida", "cancelExplain": "Cancelar {{token}} Aprovação para {{protocol}}", "unknownProtocol": "Protocolo Desconhecido", "approvalExplain": "Aprovar {{count}} {{token}} para {{protocol}}", "unlimited": "ilimitado", "action": {"send": "Enviar", "cancel": "Cancelar Transação Pendente"}, "viewBtn": "Visualizar", "ReplacePopup": {"options": {"send": "<PERSON><PERSON><PERSON>", "reject": "Rejeitar Transação"}, "title": "Selecione como substituir esta transação", "desc": "Uma transação assinada não pode ser removida, mas pode ser substituída por uma nova transação com o mesmo nonce."}, "replaceBtn": "Substituir"}, "importSuccess": {"title": "Importado com Sucesso", "addressCount": "{{count}} endere<PERSON>os", "gnosisChainDesc": "Este endereço foi encontrado implantado em {{count}} blockchains"}, "backupSeedPhrase": {"title": "<PERSON><PERSON> da Frase-semente", "alert": "Esta Frase-semente é a credencial para seus ativos. NÃO a perca ou revele a outros, caso contr<PERSON>rio, você pode perder seus ativos para sempre. Por favor, visualize-a em um ambiente seguro e mantenha-a com cuidado.", "clickToShow": "Clique para mostrar a Frase-semente", "copySeedPhrase": "<PERSON><PERSON><PERSON>", "qrCodePopupTitle": "QR Code", "showQrCode": "Mostrar QR Code", "qrCodePopupTips": "Nunca compartilhe o código QR da seed phrase com ninguém. Por favor, visualize-o em um ambiente seguro e guarde-o cuidadosamente."}, "backupPrivateKey": {"title": "Backup da Chave Privada", "alert": "Esta Chave Privada é a credencial para seus ativos. NÃO a perca ou revele a outros, caso contr<PERSON>rio, você pode perder seus ativos para sempre. Por favor, visualize-a em um ambiente seguro e mantenha-a com cuidado.", "clickToShow": "Clique para mostrar a Chave Privada", "clickToShowQr": "Clique para mostrar o Código QR da Chave Privada"}, "ethSign": {"alert": "Assinar com 'eth_sign' pode levar à perda de ativos. Por quest<PERSON><PERSON> de segurança, Rabby não suporta este método."}, "createPassword": {"title": "<PERSON><PERSON><PERSON>", "passwordRequired": "Por favor, insira uma Senha", "passwordMin": "A Senha deve ter pelo menos 8 caracteres", "passwordPlaceholder": "A Senha deve ter pelo menos 8 caracteres", "confirmRequired": "Por favor, Confirme a <PERSON>", "confirmError": "<PERSON> <PERSON><PERSON> n<PERSON>m", "confirmPlaceholder": "Confirmar <PERSON>", "agree": "Li e concordo com os <1/> <2>Termos de Uso</2>"}, "welcome": {"step1": {"title": "Acesse Todos os Dapps", "desc": "<PERSON>bby se conecta a todos os Dapps suportados pelo MetaMask"}, "step2": {"title": "Auto-custódia", "desc": "As chaves privadas são armazenadas localmente com acesso exclusivo a você", "btnText": "<PERSON><PERSON> A<PERSON>a"}}, "importSafe": {"title": "Ad<PERSON>onar <PERSON>", "placeholder": "Por favor, insira o endereço", "error": {"invalid": "Endereço inválido", "required": "Por favor, insira um endereço"}, "loading": "Procurando a blockchain onde este endereço foi implantado", "gnosisChainDesc": "Este endereço foi encontrado implantado em {{count}} blockchains"}, "importQrBase": {"desc": "Digitalize o código QR na carteira de hardware {{brandName}}", "btnText": "Tentar Novamente"}, "bridge": {"showMore": {"source": "Fonte da Bridge", "title": "<PERSON><PERSON> mais"}, "settingModal": {"confirmModal": {"title": "Ative o Trading com este Aggregator", "tip2": "2. <PERSON><PERSON> n<PERSON> responsável por quaisquer riscos decorrentes do contrato deste agregador", "tip1": "Uma vez habilitado, você interagirá diretamente com o contrato a partir deste agregador.", "i-understand-and-accept-it": "Entendo e aceito"}, "confirm": "Confirm", "title": "Ativar aglutinadores de ponte para negociar", "SupportedBridge": "Ponte compatível:"}, "tokenPairDrawer": {"balance": "Valor do Saldo", "noData": "Nenhum Par de Tokens Suportado", "tokenPair": "<PERSON><PERSON> <PERSON>", "title": "Selecione entre os pares de tokens suportados"}, "actual": "Real:", "Balance": "Saldo: ", "select-chain": "Selecionar Chain", "gas-fee": "Taxa de Gás: {{gasUsed}}", "pendingTip": "Tx enviada. Se a tx estiver pendente por horas, você pode tentar limpar as pendências nas configurações.", "tokenPairPlaceholder": "Selecionar Par de Token", "From": "De", "the-following-bridge-route-are-found": "Rota encontrada", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "To": "Para", "bridgeTo": "Ponte Para", "BridgeTokenPair": "<PERSON><PERSON> <PERSON>", "gas-x-price": "Preço do Gas: {{price}} Gwei.", "title": "<PERSON><PERSON>", "estimate": "Estimativa:", "history": "Hist<PERSON><PERSON><PERSON> <PERSON> bridge", "Select": "Selecionar", "Amount": "Quantidade", "no-transaction-records": "Sem registros de transações", "no-quote": "Sem cotação", "getRoutes": "Obter rotas", "no-quote-found": "Nenhuma cotação encontrada. Tente outros pares de tokens.", "insufficient-balance": "<PERSON><PERSON> insuficiente", "completedTip": "Transação na cadeia, decodificando dados para gerar registro", "detail-tx": "<PERSON><PERSON><PERSON>", "Pending": "Pendente", "rabby-fee": "<PERSON><PERSON>", "best": "Mel<PERSON>", "est-receiving": "Est. Receiving:", "loss-tips": "Você está perdendo {{usd}}. Tente um valor diferente.", "estimated-value": "≈ {{value}}", "enable-it": "Ativar isso", "est-difference": "Diferença Est.:", "duration": "{{duration}} min", "approve-x-symbol": "Aprovar {{symbol}}", "approve-and-bridge": "<PERSON>ovar e <PERSON>", "need-to-approve-token-before-bridge": "Precisa aprovar o token antes da bridge", "price-expired-refresh-route": "Preço expirado. Atualize a rota.", "bridge-via-x": "<PERSON><PERSON> em {{name}}", "no-route-found": "Nenhuma rota encontrada", "bridge-cost": "<PERSON><PERSON><PERSON> da ponte", "max-tips": "Este valor é calculado subtraindo o custo de gas para bridge.", "est-payment": "Est. Payment:", "via-bridge": "via {{bridge}}", "price-impact": "Impacto no Preço", "recommendFromToken": "Faça uma ponte de <1></1> para uma cotação disponível", "slippage-adjusted-refresh-quote": "Deslizamento ajustado. Atualizar rota.", "aggregator-not-enabled": "Este agregador não está habilitado para negociação por você.", "unlimited-allowance": "Permissão ilimitada"}, "pendingDetail": {"Header": {"predictTime": "Previsto para ser embalado em"}, "TxStatus": {"pendingBroadcast": "Pendente: Para ser transmitido", "pendingBroadcasted": "Pendente: Transmitido", "reBroadcastBtn": "Re-broadcast", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"created": "Transação criada", "broadcasted": "Transmitido recentemente", "pending": "Verificando status...", "broadcastedCount_ordinal_one": "{{count}}ª transmissão", "broadcastedCount_ordinal_two": "{{count}}ª transmissão", "broadcastedCount_ordinal_other": "{{count}}º broadcast", "broadcastedCount_ordinal_few": "{{count}}ª transmissão"}, "MempoolList": {"col": {"nodeName": "Nome do node", "txStatus": "Status da transação", "nodeOperator": "Operador de nó"}, "txStatus": {"appearedOnce": "Apareceu uma vez", "appeared": "<PERSON><PERSON><PERSON><PERSON>", "notFound": "Não encontrado"}, "title": "Apar<PERSON>eu em {{count}} nós RPC"}, "PendingTxList": {"filterBaseFee": {"tooltip": "Mostrar apenas transações cujo Gas Price atenda aos requisitos de taxa base do bloco", "label": "Atende apenas ao requisito de Base fee"}, "col": {"gasPrice": "Preço do Gás", "action": "Ação da Transação", "balanceChange": "Mudança de saldo", "interact": "Interagir com", "actionType": "Tipo de ação"}, "titleNotFound": "Nenhuma classificação em todas as Txs pendentes", "title": "GasPrice classifica-se em #{{rank}} em todas as Txs pendentes", "titleSameNotFound": "Sem classificação igual ao atual", "titleSame": "GasPrice Classifica #{{rank}} em Mesmo que Atual"}, "Empty": {"noData": "<PERSON>enhum dado encontrado"}, "PrePackInfo": {"col": {"difference": "Verificar resultados", "expectations": "Expectativas", "prePackContent": "Conteúdo p<PERSON>-empacotado", "prePackResults": "Pré-empacotar resultados"}, "type": {"receive": "<PERSON><PERSON><PERSON>", "pay": "<PERSON><PERSON>"}, "error": "{{count}} erro encontrado", "title": "Verificação Pré-embalada", "noLoss": "<PERSON><PERSON>huma perda encontrada", "noError": "Nenhum erro encontrado", "desc": "Simulação executada no bloco mais recente, atualizada {{time}}", "loss": "{{lossCount}} loss found"}, "Predict": {"completed": "Transação Concluída", "predictFailed": "Previsão de tempo de empacotamento falhou", "skipNonce": "Seu endereço sofreu um pulo de Nonce na cadeia Ethereum, fazendo com que a transação atual não possa ser concluída."}}, "dappSearch": {"searchResult": {"totalDapps": "Total <2>{{count}}</2> Dapps", "foundDapps": "Encontrados <2>{{count}}</2> Dapps"}, "selectChain": "Selecionar Cadeia", "emptyFavorite": "<PERSON><PERSON>", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "expand": "Expandir", "emptySearch": "<PERSON><PERSON><PERSON>", "listBy": "Dapp foi listado por"}, "rabbyPoints": {"claimItem": {"claim": "Reivindicar", "go": "Vai", "claimed": "Reivindicado", "disabledTip": "Nenhum ponto a ser reivindicado agora", "earnTip": "Uma vez por dia limitado. <PERSON><PERSON> <PERSON>, ganhe pontos após 00:00 UTC+0"}, "claimModal": {"placeholder": "Insira o código de indicação para pontos extras (opcional)", "rabbyUser": "Usuário Ativo do Rabby", "referral-code": "Código de Referência", "rabbyValuedUserBadge": "<PERSON><PERSON> Valued User Badge", "title": "Reivindicar Pontos Iniciais", "addressBalance": "<PERSON><PERSON>", "snapshotTime": "Tempo da captura: {{time}}", "MetaMaskSwap": "MetaMask Swap", "invalid-code": "c<PERSON><PERSON> in<PERSON>", "activeStats": "Status Ativo", "walletBalance": "<PERSON><PERSON>", "claim": "Reivindicar", "season2": "Temporada 2", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "cantUseOwnCode": "Você não pode usar seu próprio código de referência."}, "referralCode": {"verifyAddressModal": {"verify-address": "Verificar Endereço", "sign": "<PERSON><PERSON><PERSON>", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "Por favor, assine esta mensagem de texto para verificar que você é o proprietário deste endereço", "cancel": "<PERSON><PERSON><PERSON>"}, "referral-code-available": "Código de indicação disponível", "referral-code-already-exists": "O código de indicação já existe", "referral-code-cannot-be-empty": "O código de referência não pode estar vazio", "referral-code-cannot-exceed-15-characters": "O código de indicação não pode exceder 15 caracteres", "my-referral-code": "Meu código de indicação", "refer-a-new-user-to-get-50-points": "Indique um novo usuário para ganhar 50 pontos", "confirm": "Confirmar", "once-set-this-referral-code-is-permanent-and-cannot-change": "Depois de definido, este código de referência é permanente e não pode ser alterado.", "set-my-code": "Definir meu código", "set-my-referral-code": "Definir meu código de indicação", "max-15-characters-use-numbers-and-letters-only": "Máximo de 15 caracteres, use apenas números e letras."}, "title": "<PERSON><PERSON>", "top-100": "Top 100", "referral-code-copied": "Código de indicação copiado", "earn-points": "<PERSON><PERSON><PERSON>", "share-on": "Compartilhar no", "out-of-x-current-total-points": "De um total de {{total}} Pontos Distribuídos", "initialPointsClaimEnded": "A reivindicação de Pontos Iniciais terminou", "code-set-successfully": "Código de indicação definido com sucesso", "secondRoundEnded": "🎉 A segunda rodada de Rabby Points terminou", "firstRoundEnded": "🎉 A primeira rodada de Rabby Points foi encerrada"}, "customTestnet": {"CustomTestnetForm": {"nativeTokenSymbol": "Símbolo da moeda", "idRequired": "Por favor, insira o ID da cadeia", "id": "<PERSON> da Chain", "rpcUrlRequired": "Por favor, insira o URL RPC", "rpcUrl": "URL RPC", "name": "Nome da rede", "nameRequired": "Por favor, insira o nome da rede", "nativeTokenSymbolRequired": "Por favor, insira o símbolo da moeda", "blockExplorerUrl": "URL do explorador de blocos (Opcional)"}, "AddFromChainList": {"tips": {"added": "Você já adicionou esta chain", "supported": "<PERSON><PERSON> já integrada pelo <PERSON>"}, "empty": "Nenhuma cadeia encontrada", "title": "Adição rápida do Chainlist", "search": "Pesquisar nome ou ID de rede customizada"}, "signTx": {"title": "Dados da Transação"}, "ConfirmModifyRpcModal": {"desc": "A cadeia já está integrada pelo Rabby. Você precisa modificar seu URL de RPC?"}, "empty": "Sem rede personalizada", "desc": "Rabby não pode verificar a segurança de redes personalizadas. Por favor, adicione apenas redes confiáveis.", "currency": "<PERSON><PERSON>", "add": "Adicionar Rede Personalizada", "title": "Rede Personalizada", "id": "ID"}, "addChain": {"title": "Adicionar Rede Personalizada ao Rabby", "desc": "Rabby não pode verificar a segurança de redes personalizadas. Por favor, adicione apenas redes confiáveis."}, "sign": {"transactionSpeed": "Velocidade de Transação"}, "ecology": {"sonic": {"home": {"earnBtn": "Em breve", "migrateBtn": "Em breve", "arcadeDesc": "Jogue jogos gratuitos para ganhar pontos para o airdrop S.", "airdrop": "Airdrop", "migrateTitle": "<PERSON><PERSON><PERSON>", "arcadeBtn": "<PERSON><PERSON> agora", "migrateDesc": "→", "socialsTitle": "Participe", "airdropDesc": "~200 milhões S para usuários no Opera e Sonic.", "airdropBtn": "Ganhe pontos", "earnTitle": "Ganhar", "earnDesc": "Faça stake do seu $S"}, "points": {"sonicArcadeBtn": "Comece a jogar", "today": "Hoje", "sonicPoints": "Sonic Points", "pointsDashboard": "<PERSON><PERSON>", "shareOn": "Compartilhar no", "referralCode": "Código de indicação", "sonicArcade": "Sonic Arcade", "referralCodeCopied": "Código de referência copiado", "retry": "Tentar novamente", "getReferralCode": "Obter código de indicação", "errorTitle": "Não foi possível carregar pontos", "pointsDashboardBtn": "Comece a ganhar pontos", "errorDesc": "Houve um erro ao carregar seus pontos. Por favor, tente novamente."}}, "dbk": {"home": {"mintNFTBtn": "Mint", "bridgeBtn": "<PERSON><PERSON>", "mintNFT": "Conceder DBK Genesis NFT", "bridge": "Ponte para DBK Chain", "mintNFTDesc": "<PERSON>ja uma testemunha da DBK Chain", "bridgePoweredBy": "Alimentado por OP Superchain"}, "bridge": {"tabs": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON><PERSON>"}, "info": {"toAddress": "Para endereço", "completeTime": "Tempo de conclusão", "receiveOn": "Re<PERSON>ber em {{chainName}}", "gasFee": "Taxa de Gas"}, "error": {"notEnoughBalance": "<PERSON><PERSON> insuficiente"}, "ActivityPopup": {"status": {"waitingToProve": "Estado root publicado", "withdraw": "<PERSON><PERSON><PERSON>", "challengePeriod": "<PERSON><PERSON><PERSON>", "proved": "Provado", "readyToProve": "Pronto para provar", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "rootPublished": "Estado root publicado", "readyToClaim": "Pronto para reivindicar", "claimed": "Reivindicado"}, "deposit": "<PERSON><PERSON><PERSON><PERSON>", "empty": "Ainda não há atividades", "withdraw": "<PERSON><PERSON><PERSON>", "title": "Atividades", "claimBtn": "Reivindicar", "proveBtn": "Prove"}, "WithdrawConfirmPopup": {"step3": "Reivindicar no Ethereum", "btn": "<PERSON><PERSON>", "question1": "Entendo que levará cerca de 7 dias até que meus fundos possam ser reivindicados no Ethereum após eu provar meu saque", "step1": "In<PERSON>ar saque", "title": "O saque da DBK Chain leva cerca de 7 dias", "tips": "Retirar envolve um processo de 3 etapas, exigindo 1 transação na DBK Chain e 2 transações na Ethereum", "step2": "Prove on Ethereum", "question3": "Eu compreendo que as taxas da rede são aproximadas e irão mudar", "question2": "<PERSON><PERSON><PERSON><PERSON> que, uma vez iniciada uma retirada, ela não pode ser acelerada ou cancelada"}, "labelFrom": "De", "labelTo": "Para"}, "minNFT": {"title": "DBK Genesis", "mintBtn": "Mint", "minted": "<PERSON><PERSON><PERSON><PERSON>", "myBalance": "<PERSON><PERSON>"}}}, "miniSignFooterBar": {"status": {"txSigned": "Assinado. Criando transação", "txSendings": "Enviando solicitação de assinatura ({{current}}/{{total}})", "txSending": "Enviando solicitação de assinatura", "txCreated": "Transação criada"}, "signWithLedger": "Assinar com Ledger"}, "gasAccount": {"history": {"noHistory": "<PERSON><PERSON> his<PERSON>ó<PERSON>"}, "loginInTip": {"login": "Fazer login no GasAccount", "title": "Depositar USDC / USDT", "desc": "Pague Taxas de Gas em Todas as Chains", "gotIt": "<PERSON><PERSON><PERSON>"}, "loginConfirmModal": {"title": "Fazer login com o Endereço Atual", "desc": "<PERSON><PERSON><PERSON> de confirmado, você só pode depositá-lo neste endereço"}, "logoutConfirmModal": {"logout": "<PERSON><PERSON>", "title": "<PERSON>azer logout da GasAccount atual", "desc": "Sair desativa o GasAccount. Você pode restaurar seu GasAccount fazendo login com este endereço."}, "depositPopup": {"amount": "Quantidade", "invalidAmount": "Deve ter menos de 500", "selectToken": "Selecione Token para Depósito", "token": "Token", "title": "<PERSON><PERSON><PERSON><PERSON>", "desc": "Faça um depósito na Conta DeBank L2 da Rabby sem taxas extras — retire a qualquer momento."}, "withdrawPopup": {"selectAddr": "Selecionar Endereço", "amount": "Quantidade", "selectChain": "Selecionar Chain", "title": "<PERSON><PERSON>", "to": "Para", "selectRecipientAddress": "Selecionar endereço do destinatário", "riskMessageFromChain": "Devido ao controle de risco, o limite de retirada depende do montante total depositado nesta cadeia.", "noEnoughGas": "Quantidade insuficiente para cobrir as taxas de gas", "selectDestinationChain": "Selecione a cadeia de destino", "withdrawalLimit": "Limite de retirada", "riskMessageFromAddress": "Devido ao controle de risco, o limite de retirada depende do valor total que este endereço depositou", "noEnoughValuetBalance": "<PERSON>do <PERSON>ault insuficiente. Mude a cadeia ou tente novamente mais tarde.", "noEligibleChain": "Nenhuma cadeia elegível para retirada", "deductGasFees": "O valor recebido deduzir<PERSON> as taxas de gas", "desc": "Você pode retirar o saldo da sua GasAccount para sua DeBank L2 Wallet. Faça login na sua DeBank L2 Wallet para transferir os fundos para uma blockchain compatível conforme necessário.", "destinationChain": "Cadeia de destino", "recipientAddress": "Endereço do destinatário", "noEligibleAddr": "Nenhum endereço elegível para retirada"}, "withdrawConfirmModal": {"title": "Transferido para sua Wallet DeBank L2", "button": "Visualizar no DeBank"}, "GasAccountDepositTipPopup": {"title": "Abrir GasAccount e Depositar", "gotIt": "<PERSON><PERSON><PERSON>"}, "switchLoginAddressBeforeDeposit": {"title": "Altere o endereço antes do depósito", "desc": "Por favor, mude para o seu endereço de login."}, "withdraw": "<PERSON><PERSON><PERSON>", "gasExceed": "O saldo do GasAccount não pode exceder $1000", "title": "GasAccount", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "risk": "Seu endereço atual foi detectado como arriscado, portanto, este recurso está indisponível.", "safeAddressDepositTips": "Endereços multisig não são suportados para depósitos.", "noBalance": "<PERSON>m saldo", "logout": "<PERSON>azer logout da GasAccount atual", "gasAccountList": {"address": "Endereço", "gasAccountBalance": "Saldo de Gas"}, "switchAccount": "Alternar GasAccount", "withdrawDisabledIAP": "Os saques estão desativados porque seu saldo inclui fundos fiat, que não podem ser sacados. Entre em contato com o suporte para sacar o saldo dos seus tokens."}, "safeMessageQueue": {"noData": "Sem mensagens", "loading": "Carregando mensagens"}, "newUserImport": {"guide": {"importAddress": "Já tenho um endereço", "title": "<PERSON><PERSON>-vindo ao <PERSON>", "createNewAddress": "Criar um novo endereço", "desc": "A carteira revolucionária para Ethereum e todas as cadeias EVM"}, "createNewAddress": {"showSeedPhrase": "<PERSON><PERSON> Seed Phrase", "title": "<PERSON><PERSON>", "tip1": "Se eu perder ou compartilhar minha frase-semente, perderei o acesso aos meus ativos permanentemente.", "tip3": "Se eu desinstalar o Rabby sem fazer backup da minha frase-semente, ela não pode ser recuperada pelo <PERSON>.", "tip2": "Minha frase-semente é armazenada apenas no meu dispositivo. <PERSON><PERSON> não pode acessá-la.", "desc": "Por favor, leia e mantenha as seguintes dicas de segurança em mente."}, "importList": {"title": "Selecione o Método de Importação"}, "importPrivateKey": {"pasteCleared": "Colado e área de transferência limpa", "title": "Importar Private Key"}, "PasswordCard": {"form": {"password": {"label": "<PERSON><PERSON>", "required": "Por favor, insira a senha", "min": "A senha deve ter pelo menos 8 caracteres", "placeholder": "Senha (mín<PERSON> de 8 caracteres)"}, "confirmPassword": {"label": "Confirmar Password", "notMatch": "As senhas não coincidem", "required": "Por favor, confirme a senha", "placeholder": "Confirm<PERSON> a <PERSON>"}}, "title": "<PERSON><PERSON><PERSON>", "desc": "Será usado para desbloquear a carteira e criptografar os dados", "agree": "Eu concordo com os<1/> <2>Termos de Uso</2> e a <4>Política de Privacidade</4>"}, "successful": {"start": "<PERSON><PERSON><PERSON>", "addMoreFrom": "Adicionar mais endere<PERSON> de {{name}}", "addMoreAddr": "Adicione mais endereços através desta Seed Phrase", "import": "Importado com sucesso", "create": "Criado com sucesso"}, "readyToUse": {"pin": "<PERSON><PERSON><PERSON>", "guides": {"step2": "Fixar a Carteira Rabby", "step1": "Clique no ícone da extensão do navegador"}, "desc": "Encontre a Rabby Wallet e fixe-a", "title": "Sua carteira Rabby está pronta!", "extensionTip": "Clique <1/> e depois <3/>"}, "importSeedPhrase": {"title": "Importar Seed Phrase"}, "importOneKey": {"tip2": "2. Conecte seu dispositivo OneKey", "tip1": "1. <PERSON><PERSON><PERSON> o <1>OneKey Bridge<1/>", "connect": "Conectar OneKey", "tip3": "3. Desbloque<PERSON> seu dispositivo", "title": "OneKey"}, "importTrezor": {"title": "<PERSON><PERSON><PERSON>", "tip1": "1. Conecte o seu dispositivo Trezor", "tip2": "2. Desbloqueie seu dispositivo", "connect": "Conectar <PERSON>"}, "ImportGridPlus": {"title": "GridPlus", "tip1": "1. Abra seu dispositivo GridPlus", "connect": "Conectar GridPlus", "tip2": "2. Conectar através do Lattice Connector"}, "importLedger": {"title": "Ledger", "connect": "Conectar Ledger", "tip3": "Abra o app Ethereum.", "tip2": "Digite seu PIN para desbloquear.", "tip1": "Conecte seu dispositivo Ledger."}, "importBitBox02": {"tip2": "2. Conecte seu BitBox02", "connect": "Conectar BitBox02", "title": "BitBox02", "tip1": "1. <PERSON><PERSON>e o <1>BitBoxBridge<1/>", "tip3": "3. Desbloque<PERSON> seu dispositivo"}, "importKeystone": {"qrcode": {"desc": "Escaneie o código QR na carteira de hardware Keystone"}, "usb": {"connect": "Conectar Keystone", "tip2": "Digite sua senha para desbloquear", "tip3": "Aprove a conexão com seu computador", "desc": "Certifique-se de que seu Keystone 3 Pro esteja na página inicial", "tip1": "Conecte seu dispositivo Keystone"}}, "importSafe": {"error": {"invalid": "Endereço inválido", "required": "Por favor, insira o endereço"}, "title": "Ad<PERSON>onar <PERSON>", "loading": "Pesquisando a cadeia implantada deste endereço", "placeholder": "Insira o endereço seguro"}}, "metamaskModeDapps": {"title": "Gerenciar Dapps <PERSON>", "desc": "Modo MetaMask ativado para os seguintes Dapps. Você pode conectar o Rabby selecionando a opção MetaMask."}, "forgotPassword": {"home": {"title": "Esqueceu a Senha", "button": "Iniciar processo de redefinição", "description": "A Rabby Wallet não armazena sua senha e não pode ajudá-lo a recuperá-la. Redefina sua carteira para configurar uma nova.", "buttonNoData": "<PERSON><PERSON><PERSON>", "descriptionNoData": "A Rabby Wallet não armazena sua senha e não pode ajudar a recuperá-la. Defina uma nova senha se você a esqueceu"}, "reset": {"alert": {"seed": "Frase Semente", "title": "Os dados serão excluídos e irrecuperáveis:", "privateKey": "<PERSON>ve <PERSON>"}, "tip": {"whitelist": "Configuraç<PERSON><PERSON> de Whitelist", "watch": "Contatos e Endereços Apenas para Visualização", "records": "Registros de Assinaturas", "hardware": "Carteiras de Hardware Importadas", "title": "Os dados serão mantidos:", "safe": "Carteiras Safe Importadas"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "button": "Confirmar <PERSON>efini<PERSON>", "confirm": "Digite <1>RESET</1> na caixa para confirmar e continuar"}, "tip": {"buttonNoData": "<PERSON><PERSON><PERSON><PERSON>", "description": "Crie uma nova senha para continuar", "title": "Redefinição da Rabby Wallet Concluída", "button": "<PERSON><PERSON><PERSON>", "descriptionNoData": "Adicione seu endereço para começar"}, "success": {"title": "Senha configurada com sucesso", "description": "Você está pronto para usar o <PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "eip7702": {"alert": "EIP-7702 ainda não é suportado"}, "metamaskModeDappsGuide": {"toast": {"disabled": "Disfarce desativado. Atualize o Dapp.", "enabled": "Disguise ativado. Atualize o Dapp para reconectar."}, "step2Desc": "Atualizar e conectar via MetaMask", "manage": "Gerenciar Dapps <PERSON>", "alert": "Não consegue se conectar a um Dapp porque ele não mostra a Rabby Wallet como uma opção?", "step2": "Etapa 2", "step1": "Etapa 1", "title": "Conectar Rabby disfarçando como MetaMask", "noDappFound": "<PERSON><PERSON><PERSON> encontrado", "step1Desc": "Permita que <PERSON> se disfarce como MetaMask no Dapp atual"}, "syncToMobile": {"title": "Sincronizar Endereço da Carteira da Extensão Rabby para o Celular", "downloadAppleStore": "App Store", "downloadGooglePlay": "Google Play", "clickToShowQr": "Clique para selecionar o endereço e mostrar o código QR", "steps1": "1. <PERSON><PERSON> o Rabby Mobile", "description": "Seus dados de endereço permanecem totalmente offline, criptografados e transferidos com segurança via um código QR.", "steps2Description": "Seu código QR contém dados sensíveis. Mantenha-o privado e nunca o compartilhe com ninguém.", "steps2": "2. Escanear com Rabby Mobile", "disableSelectAddress": "Sync não suportado para {{type}} endereço", "disableSelectAddressWithPassphrase": "Sync não suportado para {{type}} endereço com a senha", "disableSelectAddressWithSlip39": "Sync não suportado para {{type}} endereço com deslize39", "selectedLenAddressesForSync_one": "Selecionado {{len}} endereço para sincronização", "selectedLenAddressesForSync_other": "Selecionados {{len}} endereços para sincronização", "selectAddress": {"title": "Selecionar Endereços para Sincronizar"}}, "search": {"sectionHeader": {"NFT": "NFT", "Defi": "<PERSON><PERSON><PERSON>", "token": "Tokens", "AllChains": "<PERSON><PERSON> as <PERSON>s"}, "header": {"placeHolder": "Buscar", "searchPlaceHolder": "Pesquisar nome / endereço do token"}, "tokenItem": {"FDV": "FDV", "Issuedby": "Emitido por", "gasToken": "Token de Gas", "listBy": "Lista por {{name}}", "verifyDangerTips": "Este é um token de golpe", "scamWarningTips": "Este é um token de baixa qualidade e pode ser um golpe."}, "searchWeb": {"searching": "Resultados para", "noResults": "<PERSON><PERSON><PERSON>", "noResult": "Sem resultados para", "title": "Todos os Resultados", "searchTips": "Buscar na web"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "Nenhum endereço correspondente", "AddressItem": {"whitelistedAddressTip": "Endereço na lista branca"}}, "AccountSelectDrawer": {"btn": {"cancel": "<PERSON><PERSON><PERSON>", "proceed": "<PERSON><PERSON><PERSON><PERSON>"}}, "AddressList": {"AddressItem": {"addressTypeTip": "Importado por {{type}}"}}, "AuthenticationModal": {"passwordError": "Senha incorreta", "passwordRequired": "Por favor, insira a senha", "passwordPlaceholder": "Digite a <PERSON>ha para Confirmar"}, "ConnectStatus": {"connecting": "Conectando...", "connect": "Conectar", "gridPlusConnected": "GridPlus está conectado", "gridPlusNotConnected": "GridPlus não está conectado", "ledgerNotConnected": "Ledger não está conectado", "ledgerConnected": "Ledger está conectado", "imKeyConnected": "imKey está conectado", "imKeyrNotConnected": "imKey não está conectado", "keystoneNotConnected": "Keystone não está conectado", "keystoneConnected": "Keystone está conectado"}, "Contact": {"AddressItem": {"notWhitelisted": "Este endereço não está na lista branca", "whitelistedTip": "Endereço na lista branca"}, "EditModal": {"title": "Editar nota do endereço"}, "EditWhitelist": {"backModalTitle": "Descartar Alterações", "backModalContent": "As alterações que você fez não serão salvas", "title": "Editar Lista Branca", "tip": "Selecione o endereço que deseja adicionar à lista branca e salve.", "save": "Salvar na Lista Branca ({{count}})"}, "ListModal": {"title": "Selecionar Endereço", "whitelistEnabled": "A lista branca está ativada. Você só pode enviar ativos para um endereço na lista branca ou desativá-la em \"Configurações\"", "whitelistDisabled": "A lista branca está desativada. Você pode enviar ativos para qualquer endereço", "editWhitelist": "Editar Lista Branca", "whitelistUpdated": "Lista Branca Atualizada", "authModal": {"title": "Salvar na Lista Branca"}}}, "LoadingOverlay": {"loadingData": "Carregando dados..."}, "MultiSelectAddressList": {"imported": "<PERSON><PERSON><PERSON><PERSON>"}, "NFTNumberInput": {"erc1155Tips": "Seu saldo é de {{amount}}", "erc721Tips": "Apenas um NFT ERC-721 pode ser enviado de cada vez"}, "TiledSelect": {"errMsg": "A ordem da frase-semente está incorreta, por favor, verifique"}, "Uploader": {"placeholder": "Selecione um arquivo JSON"}, "WalletConnectBridgeModal": {"title": "URL do Servidor de Ponte", "requiredMsg": "Por favor, insira o host do servidor de ponte", "invalidMsg": "Por favor, verifique o host", "restore": "Restaurar configuração inicial"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "Mainnets", "testnet": "Testnets"}}, "ChainSelectorModal": {"searchPlaceholder": "Pesquisar blockchain", "noChains": "Nenhuma blockchain", "addTestnet": "Adicionar Rede Personalizada"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "ATIVO / QUANTIDADE"}, "price": {"title": "PREÇO"}, "usdValue": {"title": "VALOR EM USD"}}, "searchInput": {"placeholder": "Pesquisar por Nome / Endereço"}, "header": {"title": "Selecionar um token"}, "noTokens": "<PERSON><PERSON><PERSON>", "noMatch": "Sem Correspondência", "noMatchSuggestion": "Tente pesquisar o endereço Contrato em {{ chainName }}", "bridge": {"liquidity": "Liquidez", "value": "Valor", "low": "Baixo", "high": "Alto", "token": "Token", "liquidityTips": "Quanto maior o volume histórico de transações, mais provável é que a ponte tenha sucesso."}, "hot": "<PERSON><PERSON>", "common": "Comum", "recent": "<PERSON><PERSON>", "chainNotSupport": "Esta cadeia não é suportada"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "Coleção", "Chain": "Blockchain", "PurchaseDate": "<PERSON> da Compra", "LastPrice": "Último <PERSON>", "PurschaseDate": "Data de Compra"}}, "signPermissionCheckModal": {"title": "Você só permite que este Dapp assine em testnets", "reconnect": "Reconectar Dapp"}, "testnetCheckModal": {"title": "Por favor, ative \"Habilitar Testnets\" em \"Mai<PERSON>\" antes de assinar em testnets"}, "EcologyNavBar": {"providedBy": "Fornecido por {{chainName}}"}, "EcologyNoticeModal": {"title": "Aviso", "notRemind": "Não me lembre novamente", "desc": "Os seguintes serviços serão fornecidos diretamente por Parceiros do Ecossistema de terceiros. <PERSON><PERSON> assume responsabilidade pela segurança desses serviços."}, "ReserveGasPopup": {"title": "Reservar Gas", "instant": "Instant", "fast": "<PERSON><PERSON><PERSON><PERSON>", "doNotReserve": "Não reserve Gas", "normal": "Normal"}, "OpenExternalWebsiteModal": {"button": "<PERSON><PERSON><PERSON><PERSON>", "title": "Você está saindo da <PERSON>", "content": "Você está prestes a visitar um site externo. O Rabby <PERSON>et não é responsável pelo conteúdo ou pela segurança deste site."}, "TokenChart": {"price": "Preço", "holding": "<PERSON><PERSON><PERSON>"}, "externalSwapBrideDappPopup": {"chainNotSupported": "Não suportado nesta cadeia", "selectADapp": "Selecione um Dapp", "thirdPartyDappToProceed": "Por favor, utilize um Dapp de terceiros para prosseguir.", "noQuotesForChain": "Ainda não há cotações disponíveis para esta cadeia.", "help": "Por favor, entre em contato com a equipe oficial desta cadeia para suporte.", "viewDappOptions": "Ver Opções de Dapp", "noDapp": "<PERSON><PERSON><PERSON> disponível", "bridgeOnDapp": "Ponte em <PERSON>\n", "noDapps": "Nenhum aplicativo descentralizado disponível nesta cadeia.\n", "swapOnDapp": "Troca em Dapp\n"}, "AccountSelectorModal": {"searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>\n", "title": "Selecionar endereço\n"}}, "global": {"appName": "<PERSON><PERSON><PERSON>", "appDescription": "A carteira revolucionária para Ethereum e todas as blockchains EVM", "copied": "Copiado", "confirm": "Confirmar", "next": "Próximo", "back": "Voltar", "ok": "OK", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Fal<PERSON>", "scamTx": "Transação suspeita", "gas": "Taxa de gás", "unknownNFT": "NFT desconhecido", "copyAddress": "<PERSON><PERSON><PERSON>", "watchModeAddress": "Endereço no modo de observação", "assets": "ativos", "Confirm": "Confirmar", "Cancel": "<PERSON><PERSON><PERSON>", "Clear": "Limpar", "Save": "<PERSON><PERSON>", "confirmButton": "Confirmar", "cancelButton": "<PERSON><PERSON><PERSON>", "backButton": "Voltar", "proceedButton": "Prosseguir", "editButton": "<PERSON><PERSON>", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "closeButton": "<PERSON><PERSON><PERSON>", "Deleted": "Excluído", "Loading": "Carregando", "nonce": "nonce", "Balance": "<PERSON><PERSON>", "Done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notSupportTesntnet": "Não é compatível com rede personalizada", "tryAgain": "Tente Novamente", "Nonce": "<PERSON><PERSON>"}, "background": {"error": {"noCurrentAccount": "<PERSON><PERSON><PERSON><PERSON> conta atual", "invalidChainId": "ID da blockchain inválido", "notFindChain": "Não é possível encontrar a blockchain {{chain}}", "unknownAbi": "ABI de Contrato desconhecida", "invalidAddress": "Endereço inválido", "notFoundGnosisKeyring": "<PERSON><PERSON><PERSON> keyring Gnosis encontrado", "notFoundTxGnosisKeyring": "Nenhuma transação encontrada no keyring Gnosis", "addKeyring404": "Falha ao adicionar o keyring, o keyring está indefinido", "emptyAccount": "A conta atual está vazia", "generateCacheAliasNames": "[GenerateCacheAliasNames]: é necessário pelo menos um endereço", "invalidPrivateKey": "A chave privada é inválida", "invalidJson": "O arquivo de entrada é inválido", "invalidMnemonic": "A frase-semente é inválida, por favor, verifique!", "notFoundKeyringByAddress": "Não é possível encontrar o keyring pelo endereço", "txPushFailed": "Falha ao enviar a transação", "unlock": "Você precisa desbloquear a carteira primeiro", "duplicateAccount": "A conta que você está tentando importar é duplicada", "canNotUnlock": "Não é possível desbloquear sem uma carteira anterior"}, "transactionWatcher": {"submitted": "Transação enviada", "more": "Clique para ver mais informações", "completed": "Transação concluída", "failed": "Transação falhou", "txCompleteMoreContent": "{{chain}} #{{nonce}} concluído. Clique para ver mais.", "txFailedMoreContent": "{{chain}} #{{nonce}} falhou. Clique para ver mais."}, "alias": {"HdKeyring": "Frase-semente", "SimpleKeyring": "Chave privada importada", "WatchAddressKeyring": "Contato", "watchAddressKeyring": "Contato", "simpleKeyring": "<PERSON>ve <PERSON>"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "<PERSON><PERSON><PERSON> pela <PERSON>-semente", "SimpleKeyring": "Importado pela Chave Privada", "WatchAddressKeyring": "Contato"}, "IMPORTED_HD_KEYRING": "<PERSON><PERSON>rtado pela <PERSON>-semente", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Mainnet e Testnet", "TESTNET": "Apenas Testnets"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "<PERSON>mportado por Seed Phrase (Passphrase)"}}