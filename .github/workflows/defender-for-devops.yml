# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.
#
# Microsoft Security DevOps (MSDO) is a command line application which integrates static analysis tools into the development cycle.
# <PERSON>D<PERSON> installs, configures and runs the latest versions of static analysis tools
# (including, but not limited to, SDL/security and compliance tools).
#
# The Microsoft Security DevOps action is currently in beta and runs on the windows-latest queue,
# as well as Windows self hosted agents. ubuntu-latest support coming soon.
#
# For more information about the action , check out https://github.com/microsoft/security-devops-action
#
# Please note this workflow do not integrate your GitHub Org with Microsoft Defender For DevOps. You have to create an integration
# and provide permission before this can report data back to azure.
# Read the official documentation here : https://learn.microsoft.com/en-us/azure/defender-for-cloud/quickstart-onboard-github

name: "Microsoft Defender For Devops"

on:
  push:
    branches: [ "develop" ]
  pull_request:
    branches: [ "develop" ]
  schedule:
    - cron: '27 22 * * 5'

jobs:
  MSDO:
    # currently only windows latest is supported
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-dotnet@v4
      with:
        dotnet-version: |
          5.0.x
          6.0.x
    - name: Run Microsoft Security DevOps
      uses: microsoft/security-devops-action@v1.6.0
      id: msdo
    - name: Upload results to Security tab
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: ${{ steps.msdo.outputs.sarifFile }}
