{"page": {"transactions": {"empty": {"title": "<PERSON><PERSON><PERSON><PERSON> có giao dịch nào", "desc": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch trên <1>các chuỗi được hỗ trợ</1>"}, "explain": {"approve": "<PERSON><PERSON> {{amount}} {{symbol}} cho {{project}}", "unknown": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c hợp đồng", "cancel": "Hủy bỏ một giao dịch đang chờ xử lý"}, "txHistory": {"tipInputData": "<PERSON><PERSON><PERSON><PERSON> bao gồm một tin nh<PERSON>n", "parseInputDataError": "<PERSON><PERSON> tích tin nhắn thất bại", "scamToolTip": "G<PERSON>o dịch này được khởi xướng bởi những kẻ lừa đảo để gửi token lừa đảo và NFT. Vui lòng không tương tác với nó."}, "modalViewMessage": {"title": "<PERSON><PERSON>"}, "filterScam": {"loading": "<PERSON><PERSON> tải có thể mất một lúc, và có thể có sự chậm trễ dữ liệu.", "title": "Ẩn giao dịch lừa đảo", "btn": "Ẩn giao dịch lừa đảo"}, "title": "<PERSON><PERSON><PERSON>"}, "chainList": {"mainnet": "Mainnets", "testnet": "Testnets", "title": "{{count}} chuỗi đ<PERSON><PERSON><PERSON> tích hợp"}, "signTx": {"gasAccount": {"totalCost": "Tổng chi phí:", "currentTxCost": "Số lượng Gas đã gửi đến địa chỉ của bạn:", "maxGas": "Max Gas:", "estimatedGas": "Dự kiến Gas:", "sendGas": "Gas chuyển cho bạn cho giao dịch hiện tại:", "gasCost": "Chi phí gas để chuyển gas đến địa chỉ của bạn:"}, "balanceChange": {"successTitle": "<PERSON><PERSON><PERSON> quả mô phỏng", "nftOut": "NFT ra mắt", "tokenIn": "Token vào", "tokenOut": "Token ra", "errorTitle": "<PERSON><PERSON><PERSON><PERSON> thể lấy thay đổi số dư", "failedTitle": "<PERSON><PERSON> phỏng thất bại", "noBalanceChange": "<PERSON><PERSON><PERSON><PERSON> thay đổi số dư", "notSupport": "<PERSON><PERSON> phỏng không được hỗ trợ"}, "customRPCErrorModal": {"button": "<PERSON><PERSON> hiệu hóa RPC tùy chỉnh", "title": "Lỗi RPC tùy chỉnh", "content": "RPC tuỳ chỉnh của bạn hiện không khả dụng. Bạn có thể vô hiệu hóa nó và tiếp tục ký bằng RPC chính thức của <PERSON>bby."}, "swap": {"slippageFailToLoad": "<PERSON><PERSON><PERSON><PERSON> thể tải lên", "slippageTolerance": "Tolerans tr<PERSON>", "failLoadReceiveToken": "<PERSON><PERSON><PERSON><PERSON> thể tải lên", "unknownAddress": "Địa chỉ không xác định", "valueDiff": "<PERSON><PERSON><PERSON> tr<PERSON> k<PERSON>", "receiveToken": "Nhậ<PERSON>", "simulationNotSupport": "<PERSON><PERSON> phỏng giao dịch không được hỗ trợ trên chuỗi này", "payToken": "<PERSON><PERSON> toán", "notPaymentAddress": "<PERSON><PERSON><PERSON><PERSON> phải địa chỉ thanh toán", "simulationFailed": "<PERSON><PERSON> phỏng giao dịch thất bại", "title": "<PERSON><PERSON> đ<PERSON>", "minReceive": "<PERSON><PERSON> tiền tối thiểu nhận được", "receiver": "<PERSON><PERSON><PERSON><PERSON>n"}, "crossChain": {"title": "Chuỗi chéo"}, "swapAndCross": {"title": "<PERSON><PERSON> đổi <PERSON>ken và Chuỗi chéo"}, "transferOwner": {"description": "<PERSON><PERSON>", "transferTo": "<PERSON><PERSON><PERSON><PERSON> đến", "title": "Chuyển như<PERSON>ng quyền sở hữu tài sản"}, "swapLimitPay": {"maxPay": "<PERSON><PERSON><PERSON> đa thanh toán", "title": "<PERSON><PERSON><PERSON> hạn mức mã thông báo thanh toán"}, "send": {"sendToken": "G<PERSON>i token", "sendTo": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "receiverIsTokenAddress": "Địa chỉ token", "notOnThisChain": "<PERSON>h<PERSON>ng trên chuỗi này", "notOnWhitelist": "<PERSON><PERSON><PERSON>ng có trong danh sách trắng của tôi", "notTopupAddress": "<PERSON><PERSON><PERSON>ng phải địa chỉ gửi tiền", "addressBalanceTitle": "Số dư địa chỉ", "cexAddress": "Địa chỉ CEX", "scamAddress": "Địa chỉ lừa đảo", "contractNotOnThisChain": "<PERSON>h<PERSON>ng trên chuỗi này", "tokenNotSupport": "{{0}} kh<PERSON><PERSON> được hỗ trợ", "onMyWhitelist": "Trong danh sách trắng của tôi", "whitelistTitle": "<PERSON><PERSON> s<PERSON>ch trắng", "fromMyPrivateKey": "Từ khóa riêng của tôi", "fromMySeedPhrase": "<PERSON>ừ cụm từ hạt giống của tôi"}, "tokenApprove": {"approveTo": "<PERSON><PERSON><PERSON> thuận để", "myBalance": "Số dư của tôi", "title": "<PERSON><PERSON>", "approveToken": "<PERSON><PERSON> token", "eoaAddress": "EOA", "amountPopupTitle": "<PERSON><PERSON> số tiền", "exceed": "<PERSON><PERSON><PERSON><PERSON> quá số dư hiện tại của bạn", "amount": "<PERSON><PERSON> l<PERSON><PERSON> chấp thuận:", "trustValueLessThan": "≤ {{value}}", "deployTimeLessThan": "< {{value}} ngày", "flagByRabby": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h dấu bởi Rabby", "contractTrustValueTip": "<PERSON><PERSON><PERSON> trị tin cậy đề cập đến tổng giá trị tài sản đã chi tiêu bởi hợp đồng này. Một giá trị tin cậy thấp cho thấy có thể có rủi ro hoặc không hoạt động trong 180 ngày."}, "revokeTokenApprove": {"revokeFrom": "<PERSON><PERSON><PERSON> bỏ từ", "title": "<PERSON><PERSON><PERSON> phê du<PERSON> token", "revokeToken": "Hủy bỏ token"}, "sendNFT": {"nftNotSupport": "NFT không được hỗ trợ", "title": "Gửi NFT"}, "nftApprove": {"title": "NFT Approval", "approveNFT": "Phê duyệt NFT", "nftContractTrustValueTip": "<PERSON><PERSON><PERSON> trị tin cậy đề cập đến giá trị NFT cao nhất được chi tiêu bởi hợp đồng này. Một giá trị tin cậy thấp cho thấy có thể có rủi ro hoặc không hoạt động trong 180 ngày."}, "revokeNFTApprove": {"title": "<PERSON><PERSON>y phê duyệt NFT", "revokeNFT": "Hủy bỏ NFT"}, "nftCollectionApprove": {"approveCollection": "<PERSON><PERSON><PERSON> bộ sưu tập", "title": "<PERSON><PERSON> <PERSON>ộ sưu tập NFT"}, "revokeNFTCollectionApprove": {"revokeCollection": "<PERSON><PERSON><PERSON> thu thập", "title": "Hủy bỏ bộ sưu tập NFT"}, "deployContract": {"descriptionTitle": "<PERSON><PERSON>", "description": "Bạn đang triển khai một hợp đồng thông minh", "title": "<PERSON><PERSON><PERSON> khai một hợp đồng"}, "cancelTx": {"txToBeCanceled": "<PERSON><PERSON><PERSON> d<PERSON> sẽ bị hủy bỏ", "title": "<PERSON><PERSON><PERSON> giao dịch đang chờ", "gasPriceAlert": "Đặt giá gas hiện tại cao hơn {{value}} Gwei để hủy giao dịch đang chờ xử lý."}, "submitMultisig": {"multisigAddress": "Địa chỉ Multisig", "title": "<PERSON><PERSON><PERSON> giao d<PERSON><PERSON>"}, "contractCall": {"operation": "<PERSON><PERSON><PERSON> đ<PERSON>", "receiver": "Địa chỉ người nhận", "title": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "suspectedReceiver": "Đ<PERSON>a chỉ ngoại lệ", "operationABIDesc": "Hoạt động đư<PERSON><PERSON> giải mã từ ABI", "operationCantDecode": "Hoạt động không đư<PERSON>c giải mã", "payNativeToken": "<PERSON><PERSON> toán {{symbol}}"}, "revokePermit2": {"title": "<PERSON><PERSON><PERSON> phê duyệt Permit2"}, "batchRevokePermit2": {"title": "H<PERSON>y phê duy<PERSON>t Permit2 hàng loạt"}, "revokePermit": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON> du<PERSON>t <PERSON> hạn"}, "assetOrder": {"title": "<PERSON><PERSON><PERSON> tự Tài sản", "receiveAsset": "<PERSON><PERSON><PERSON><PERSON> tài sản", "listAsset": "<PERSON><PERSON> s<PERSON>ch tài s<PERSON>n"}, "BroadcastMode": {"instant": {"desc": "<PERSON><PERSON><PERSON> d<PERSON>ch sẽ ngay lập tức đư<PERSON><PERSON> phát sóng ra mạng.", "title": "<PERSON><PERSON> l<PERSON> t<PERSON>c"}, "lowGas": {"title": "Tiết k<PERSON> Gas", "desc": "G<PERSON>o dịch sẽ được phát sóng khi gas mạng thấp."}, "mev": {"title": "MEV Guarded", "desc": "G<PERSON>o dịch sẽ được phát đi tới nút MEV được chỉ định"}, "tips": {"notSupported": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ", "walletConnect": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ bởi WalletConnect", "notSupportChain": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ trên chuỗi này", "customRPC": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ khi sử dụng RPC tùy chỉnh"}, "lowGasDeadline": {"1h": "1h", "4h": "4h", "24h": "24h", "label": "<PERSON><PERSON><PERSON><PERSON> gian chờ hết hạn"}, "title": "<PERSON><PERSON> độ Ph<PERSON> sóng"}, "safeTx": {"selfHostConfirm": {"button": "OK", "title": "<PERSON><PERSON><PERSON><PERSON> sang dịch vụ An toàn của <PERSON>", "content": "API an toàn không khả dụng. <PERSON><PERSON><PERSON><PERSON> sang dịch vụ Safe được triển khai bởi Rabby để giữ cho Safe của bạn hoạt động. <strong>Tất cả các người ký Safe phải sử dụng Rabby Wallet để ủy quyền giao dịch.</strong>"}}, "SafeNonceSelector": {"explain": {"contractCall": "<PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> kh<PERSON>ng x<PERSON>c <PERSON>"}, "optionGroup": {"recommendTitle": "<PERSON><PERSON> nghị", "replaceTitle": "Thay thế giao dịch trong hàng đợi"}, "option": {"new": "<PERSON><PERSON><PERSON> m<PERSON>i"}, "error": {"pendingList": "<PERSON><PERSON><PERSON><PERSON> thể tải giao dịch đang chờ, <1/><2><PERSON><PERSON><PERSON> lại</2>"}}, "coboSafeCreate": {"safeWalletTitle": "Safe{Wallet}", "title": "Tạo Cobo Safe", "descriptionTitle": "<PERSON><PERSON>"}, "coboSafeModificationRole": {"safeWalletTitle": "Safe{Wallet}", "descriptionTitle": "<PERSON><PERSON>", "title": "<PERSON><PERSON>i sửa đổi vai trò an toàn"}, "coboSafeModificationDelegatedAddress": {"title": "<PERSON><PERSON>i sửa đổi địa chỉ <PERSON>y quyền", "descriptionTitle": "<PERSON><PERSON>", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationTokenApproval": {"descriptionTitle": "<PERSON><PERSON>", "safeWalletTitle": "Safe{Wallet}", "title": "<PERSON><PERSON><PERSON> sửa đổi phê duyệt token"}, "common": {"descTipSafe": "Chữ ký không gây thay đổi tài sản hoặc xác minh quyền sở hữu địa chỉ.", "interactContract": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c hợp đồng", "descTipWarningAssets": "Chữ ký có thể gây ra sự thay đổi tài sản", "descTipWarningBoth": "Chữ ký có thể gây ra sự thay đổi tài sản và xác minh quyền sở hữu địa chỉ", "descTipWarningPrivacy": "Chữ ký có thể xác minh quyền sở hữu địa chỉ", "description": "<PERSON><PERSON>"}, "nonceTitle": "<PERSON><PERSON>", "gasLimitTitle": "Giới hạn Gas", "noGasRequired": "Không cần gas", "failToFetchGasCost": "Không thể ước lượng gas", "nativeTokenNotEngouthForGas": "Số dư Gas không đủ cho giao dịch", "gasSelectorTitle": "Gas", "gasLimitModifyOnlyNecessaryAlert": "Chỉnh sửa chỉ khi cần thiết", "gasLimitEmptyAlert": "<PERSON><PERSON> lòng nhập giới hạn gas", "myNativeTokenBalance": "Số dư Gas của tôi:", "gasLimitMinValueAlert": "Giới hạn gas phải lớn hơn 21000", "enoughSafeSigCollected": "<PERSON><PERSON> thu thập đủ chữ ký", "nativeTokenForGas": "Sử dụng token {{tokenName}} trên {{chainName}} để thanh toán gas", "recommendGasLimitTip": "Est. {{est}}. <PERSON><PERSON><PERSON> tại {{current}}x, đề xuất", "manuallySetGasLimitAlert": "Bạn đã thiết lập giới hạn Gas theo cách thủ công thành", "nftIn": "NFT trong", "gasMoreButton": "<PERSON><PERSON><PERSON><PERSON>", "gasLimitNotEnough": "G<PERSON><PERSON>i hạn Gas ít hơn 21000. <PERSON><PERSON><PERSON><PERSON> không thể đ<PERSON><PERSON><PERSON> g<PERSON>.", "gasNotRequireForSafeTransaction": "<PERSON>í gas không cần thiết cho các giao dịch <PERSON>.", "gasPriceTitle": "Giá Gas (Gwei)", "multiSigChainNotMatch": "Địa chỉ đa chữ ký không nằm trên chuỗi này và không thể khởi tạo giao dịch.", "gasAccountForGas": "Sử dụng USD từ GasAccount của tôi để thanh toán gas", "safeAddressNotSupportChain": "Đ<PERSON>a chỉ an toàn hiện tại không được hỗ trợ trên chuỗi {{0}}.", "gasPriceMedian": "Giá trị trung vị của 100 giao dịch on-chain gầ<PERSON> nhất:", "nonceLowerThanExpect": "<PERSON><PERSON> quá thấp, tối thiểu nên là {{0}}", "gasLimitLessThanGasUsed": "Giới hạn Gas quá thấp. Có 95% kh<PERSON> năng giao dịch có thể thất bại.", "moreSafeSigNeeded": "<PERSON><PERSON>n {{0}} chữ ký nữa để xác nhận", "gasLimitLessThanExpect": "Giới hạn Gas quá thấp. Có 1% khả năng giao dịch có thể thất bại.", "hardwareSupport1559Alert": "Đảm bảo firmware của ví phần cứng của bạn đã đư<PERSON><PERSON> nâng cấp lên phiên bản hỗ trợ EIP 1559.", "maxPriorityFee": "<PERSON><PERSON> <PERSON> (Gwei)", "canOnlyUseImportedAddress": "Bạn không thể ký các giao dịch bằng địa chỉ chỉ xem.", "wrapToken": "<PERSON><PERSON><PERSON>", "eip1559Desc2": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Ưu Tiên (Tip) = <PERSON><PERSON> Tối Đa - <PERSON><PERSON>. <PERSON><PERSON> khi bạn thiết lập Phí Ưu Tiên T<PERSON>, <PERSON><PERSON> Cơ Bản sẽ được trừ từ đó và phần còn lại sẽ được tip cho thợ mỏ.", "safeAdminSigned": "Đã ký", "unwrap": "Gỡ mã Token", "eip1559Desc1": "Tr<PERSON><PERSON> các chuỗi hỗ trợ EIP-1559, <PERSON><PERSON> Ưu Tiên là tiền boa cho các thợ đào để xử lý giao dịch của bạn. Bạn có thể tiết kiệm chi phí gas cuối cùng của mình bằng cách giảm Phí Ưu Tiên, điều này có thể tốn thêm thời gian để giao dịch được xử lý.", "safeServiceNotAvailable": "<PERSON><PERSON><PERSON> vụ an toàn hiện không kh<PERSON> dụng, vui lòng thử lại sau.", "nftCollection": "<PERSON><PERSON> s<PERSON>u tập NFT", "popularity": "<PERSON><PERSON> phổ biến", "chain": "Chuỗi", "deployTimeTitle": "<PERSON><PERSON><PERSON><PERSON> gian triển khai", "unknownActionType": "<PERSON><PERSON><PERSON> hành động không xác định", "contractAddress": "Đ<PERSON>a chỉ hợp đồng", "protocolTitle": "<PERSON><PERSON><PERSON> th<PERSON>", "noMark": "<PERSON><PERSON><PERSON><PERSON> có dấu hiệu", "viewRaw": "<PERSON><PERSON> thô", "trusted": "<PERSON><PERSON><PERSON><PERSON> tin cậy", "interactContract": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c hợp đồng", "importedAddress": "Địa chỉ đã nhập", "fakeTokenAlert": "<PERSON><PERSON><PERSON> là một mã thông báo lừa đảo đư<PERSON><PERSON> đánh dấu bởi Rabby", "markRemoved": "<PERSON><PERSON><PERSON> dấu đã xóa", "neverTransacted": "<PERSON><PERSON><PERSON> bao giờ giao dịch tr<PERSON><PERSON><PERSON> đây", "unknownAction": "<PERSON><PERSON><PERSON>", "neverInteracted": "<PERSON><PERSON><PERSON> từng tương tác trước đây", "signTransactionOnChain": "<PERSON><PERSON> tên giao dịch {{chain}}", "blocked": "Bị chặn", "interacted": "<PERSON><PERSON> tương tác trước đây", "decodedTooltip": "<PERSON>ữ ký này đ<PERSON><PERSON><PERSON> gi<PERSON>i mã bởi <PERSON><PERSON>", "sigCantDecode": "Chữ ký này không thể được giải mã bởi <PERSON><PERSON>et", "contractPopularity": "Không.{{0}} trên {{1}}", "transacted": "Đ<PERSON> giao dịch tr<PERSON><PERSON><PERSON> đ<PERSON>", "addressNote": "<PERSON><PERSON> chú địa chỉ", "floorPrice": "<PERSON><PERSON><PERSON>", "scamTokenAlert": "<PERSON><PERSON><PERSON> có thể là một token có chất lư<PERSON><PERSON> thấp và lừa đảo dựa trên phát hiện củ<PERSON>.", "markAsBlock": "<PERSON><PERSON> đư<PERSON><PERSON> đánh dấu là bị chặn", "markAsTrust": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h dấu là đáng tin cậy", "speedUpTooltip": "<PERSON><PERSON><PERSON> dịch được tăng tốc này và giao dịch gốc, chỉ có một trong số đó sẽ được hoàn thành cuối cùng.", "collectionTitle": "<PERSON><PERSON> s<PERSON>u tập", "importedDelegatedAddress": "Địa chỉ đư<PERSON><PERSON> <PERSON>y quyền đã nhập", "noDelegatedAddress": "<PERSON><PERSON><PERSON><PERSON> có địa chỉ <PERSON>y quyền nhập khẩu", "trustValue": "<PERSON><PERSON><PERSON> trị tin cậy", "myMarkWithContract": "<PERSON><PERSON><PERSON> của tôi trên hợp đồng {{chainName}}", "firstOnChain": "<PERSON><PERSON><PERSON> tiên trên chuỗi", "myMark": "<PERSON><PERSON><PERSON> c<PERSON>a tôi", "addressTypeTitle": "Lo<PERSON>i địa chỉ", "l2GasEstimateTooltip": "Ước tính gas cho chuỗi L2 không bao gồm phí gas L1. <PERSON><PERSON> thực tế sẽ cao hơn ước tính hiện tại.", "no": "K<PERSON>ô<PERSON>", "amount": "<PERSON><PERSON> tiền", "protocol": "<PERSON><PERSON><PERSON> th<PERSON>", "coboSafeNotPermission": "Địa chỉ đại diện này không có quyền khởi động giao dịch này.", "address": "Địa chỉ", "hasInteraction": "<PERSON><PERSON> tương tác trước đây", "advancedSettings": "Cài đặt Nâng cao", "yes": "<PERSON><PERSON>", "primaryType": "<PERSON><PERSON><PERSON>", "trustValueTitle": "<PERSON><PERSON><PERSON> trị tin cậy", "typedDataMessage": "<PERSON><PERSON> tên <PERSON> liệu Đã đ<PERSON>h máy", "addressSource": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "maxPriorityFeeDisabledAlert": "<PERSON><PERSON> lòng đặt Giá Gas trước.", "label": "<PERSON><PERSON>ã<PERSON>", "contract": "Đ<PERSON><PERSON> chỉ hợp đồng thông minh"}, "signFooterBar": {"gasless": {"notEnough": "Số dư Gas không đủ", "unavailable": "Số dư Gas của bạn không đủ", "GetFreeGasToSign": "Nhận <PERSON>n phí", "customRpcUnavailableTip": "RPC tùy chỉnh không được hỗ trợ cho Free Gas", "walletConnectUnavailableTip": "Ví dụ ví di động kết nối qua WalletConnect không được hỗ trợ cho Free Gas.", "rabbyPayGas": "<PERSON>bby sẽ thanh toán cho gas cần thiết – chỉ cần ký vào thôi", "watchUnavailableTip": "Địa chỉ chỉ xem không được hỗ trợ cho Free Gas"}, "gasAccount": {"useGasAccount": "Sử dụng GasAccount", "notEnough": "GasAccount không đủ", "customRPC": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ khi sử dụng RPC tùy chỉnh", "WalletConnectTips": "WalletConnect không được hỗ trợ bởi GasAccount", "gotIt": "<PERSON><PERSON> hiểu", "login": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON> t<PERSON>", "chainNotSupported": "Chuỗi này không được GasAccount hỗ trợ.", "depositTips": "<PERSON><PERSON> hoàn thành việc gửi tiền v<PERSON><PERSON> GasAccount, giao dịch này sẽ bị hủy. Bạn sẽ cần thực hiện lại giao dịch sau khi gửi tiền.", "loginTips": "<PERSON><PERSON> hoàn tất đăng nhập GasAccount, giao d<PERSON><PERSON> này sẽ bị bỏ qua. Bạn sẽ cần thực hiện lại sau khi đăng nhập.", "loginFirst": "<PERSON>ui lòng đăng nhập vào GasAccount trước"}, "walletConnect": {"switchToCorrectAddress": "<PERSON><PERSON> lòng chuyển đến địa chỉ đúng trong ví di động", "requestFailedToSend": "<PERSON><PERSON><PERSON> cầu ký không thể gửi đi", "chainSwitched": "Bạn đã chuyển sang một chuỗi khác trên ví di động. <PERSON><PERSON> lòng chuyển sang {{0}} trong ví di động.", "connectBeforeSign": "{{0}} ch<PERSON><PERSON> đ<PERSON> kết nối v<PERSON><PERSON>, vui lòng kết nối trư<PERSON><PERSON> khi ký.", "notConnectToMobile": "<PERSON><PERSON><PERSON> kết nối với {{brand}}", "sendingRequest": "<PERSON><PERSON> g<PERSON>i yêu cầu x<PERSON>c <PERSON>n", "latency": "Đ<PERSON> trễ", "connected": "<PERSON><PERSON>t nối và sẵn sàng để ký", "connectedButCantSign": "<PERSON><PERSON> kết nối nhưng không thể ký.", "signOnYourMobileWallet": "<PERSON>ui lòng ký trên ví di động của bạn.", "switchChainAlert": "<PERSON><PERSON> lòng chuy<PERSON>n sang {{chain}} trong ví di động.", "wrongAddressAlert": "Bạn đã chuyển sang một địa chỉ khác trên ví di động. Vui lòng chuyển đến địa chỉ đúng trong ví di động.", "requestSuccessToast": "<PERSON><PERSON><PERSON> cầu đã đư<PERSON><PERSON> gửi thành công", "howToSwitch": "<PERSON><PERSON><PERSON> chuy<PERSON> đ<PERSON>i"}, "addressTip": {"coolwallet": "Địa chỉ CoolWallet", "seedPhrase": "Seed Phrase địa chỉ", "trezor": "<PERSON><PERSON><PERSON> chỉ Trezor", "keystone": "Địa chỉ Keystone", "onekey": "Địa chỉ OneKey", "bitbox": "Địa chỉ BitBox02", "airgap": "Địa chỉ AirGap", "privateKey": "Địa chỉ <PERSON>h<PERSON><PERSON>", "watchAddress": "<PERSON><PERSON><PERSON><PERSON> thể ký với địa chỉ chỉ xem", "safe": "<PERSON><PERSON><PERSON> chỉ an toàn", "coboSafe": "Cobo Argus Địa chỉ", "seedPhraseWithPassphrase": "<PERSON><PERSON><PERSON> địa chỉ Seed Phrase (<PERSON><PERSON><PERSON>h<PERSON>u)"}, "qrcode": {"txFailed": "<PERSON><PERSON><PERSON><PERSON> thể tạo ra", "sigReceived": "Chữ ký đã nhận", "getSig": "<PERSON><PERSON><PERSON> chữ ký", "sigCompleted": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> t<PERSON>o", "signWith": "<PERSON><PERSON> với {{brand}}", "afterSignDesc": "<PERSON><PERSON> <PERSON> k<PERSON>, đặt mã QR trên {{brand}} trước camera máy t<PERSON>h của bạn.", "unknownQRCode": "Lỗi: <PERSON><PERSON><PERSON> tôi không thể nhận diện mã QR đó.", "misMatchSignId": "<PERSON><PERSON> liệu giao dịch không khớp. <PERSON><PERSON> lòng kiểm tra chi tiết giao dịch.", "qrcodeDesc": "Quét với {{brand}} của bạn để ký<br></br><PERSON><PERSON> khi ký xong, nhấn nút bên dưới để nhận chữ ký", "failedToGetExplain": "<PERSON><PERSON><PERSON><PERSON> thể lấy gi<PERSON>i thích"}, "keystone": {"siging": "<PERSON><PERSON><PERSON> yêu cầu ký", "shouldRetry": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON> lòng thử lại.", "txRejected": "<PERSON><PERSON><PERSON><PERSON> bị từ chối", "mismatchedWalletError": "Ví dụ ví không khớp", "verifyPasswordError": "Lỗi chữ ký, vui lòng thử lại sau khi mở khóa.", "signWith": "<PERSON><PERSON><PERSON><PERSON> sang {{method}} đ<PERSON> ký", "shouldOpenKeystoneHomePageError": "<PERSON><PERSON><PERSON> bảo rằng Keystone 3 Pro của bạn nằm trên trang chủ", "misMatchSignId": "<PERSON><PERSON> liệu giao dịch không phù hợp. <PERSON><PERSON> lòng kiểm tra chi tiết giao dịch.", "hardwareRejectError": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> đã bị hủy. <PERSON><PERSON> ti<PERSON> t<PERSON>, vui lòng xác thực lại.", "qrcodeDesc": "Quét để ký. <PERSON><PERSON> <PERSON> ký, nhấp dưới để lấy chữ ký. Đ<PERSON>i với USB, hãy kết nối lại và ủy quyền để bắt đầu quá trình ký lần nữa.", "unsupportedType": "Lỗi: <PERSON><PERSON><PERSON> giao dịch không được hỗ trợ hoặc không được biết đến."}, "ledger": {"txRejected": "<PERSON><PERSON><PERSON><PERSON> bị từ chối", "siging": "<PERSON><PERSON><PERSON> yêu cầu ký", "notConnected": "<PERSON><PERSON> của bạn chưa đư<PERSON><PERSON> kết nối. <PERSON><PERSON> lòng kết nối lại.", "signError": "Ledger sign error:", "resent": "<PERSON><PERSON><PERSON> l<PERSON>i", "txRejectedByLedger": "<PERSON><PERSON><PERSON><PERSON> bị từ chối trên <PERSON> của bạn", "updateFirmwareAlert": "<PERSON><PERSON> lòng cập nhật firmware và Ethereum App trên Ledger của bạn.", "resubmited": "<PERSON><PERSON> nộp lại", "blindSigTutorial": "Hướng dẫn Chữ ký <PERSON>ù từ Ledger", "unlockAlert": "<PERSON><PERSON> lòng cắm và mở khóa Ledger của bạn, sau đó mở Ethereum trên đó.", "submitting": "<PERSON><PERSON> ký. <PERSON><PERSON> t<PERSON>o giao d<PERSON>ch"}, "common": {"notSupport": "{{0}} kh<PERSON><PERSON> được hỗ trợ"}, "ledgerConnected": "Ledger đ<PERSON> đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "keystoneNotConnected": "Keystone không đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "ignoreAll": "Bỏ qua tất cả", "keystoneConnected": "Keystone đã kết nối", "signAndSubmitButton": "<PERSON><PERSON><PERSON> ký", "processRiskAlert": "<PERSON><PERSON> lòng xử lý thông báo trư<PERSON><PERSON> khi ký.", "gridPlusConnected": "GridPlus đã đư<PERSON><PERSON> kết n<PERSON>i", "gridPlusNotConnected": "GridPlus không đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "ledgerNotConnected": "Ledger kh<PERSON>ng đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "requestFrom": "<PERSON><PERSON><PERSON> c<PERSON>u từ", "connecting": "<PERSON><PERSON><PERSON> n<PERSON>...", "beginSigning": "<PERSON><PERSON>t đầu quá trình ký", "imKeyConnected": "<PERSON><PERSON><PERSON> đã đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "testnet": "Testnet", "resend": "<PERSON><PERSON><PERSON> lại", "cancelConnection": "<PERSON><PERSON><PERSON> k<PERSON>", "imKeyNotConnected": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>i", "detectedMultipleRequestsFromThisDapp": "<PERSON><PERSON><PERSON> hi<PERSON>n nhiều yêu cầu từ Dapp này", "cancelTransaction": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "submitTx": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "cancelCurrentConnection": "<PERSON><PERSON><PERSON> kết n<PERSON>i hiện tại", "mainnet": "<PERSON><PERSON><PERSON>", "blockDappFromSendingRequests": "Chặn Dapp gửi yêu cầu trong 1 phút", "cancelCurrentTransaction": "<PERSON><PERSON><PERSON> giao d<PERSON>ch hiện tại", "cancelAll": "<PERSON><PERSON><PERSON> tất cả {{count}} yê<PERSON> c<PERSON>u từ <PERSON>pp", "connectButton": "<PERSON><PERSON><PERSON>"}, "signTypedData": {"permit": {"title": "<PERSON><PERSON>"}, "permit2": {"title": "<PERSON>ê duyệt Token Permit2", "sigExpireTime": "<PERSON><PERSON><PERSON><PERSON> gian hết hạn chữ ký", "approvalExpiretime": "<PERSON><PERSON><PERSON><PERSON> gian hết hạn phê du<PERSON>t", "sigExpireTimeTip": "Thời gian mà chữ ký này có hiệu lực trên chuỗi"}, "swapTokenOrder": {"title": "Đặt hàng Token"}, "sellNFT": {"title": "Đặt hàng NFT", "receiveToken": "Nhận token", "specificBuyer": "<PERSON><PERSON><PERSON><PERSON> mua cụ thể", "listNFT": "<PERSON><PERSON> s<PERSON>"}, "signMultiSig": {"title": "<PERSON><PERSON><PERSON>"}, "createKey": {"title": "Tạo Key"}, "verifyAddress": {"title": "<PERSON><PERSON><PERSON>h địa chỉ"}, "buyNFT": {"payToken": "<PERSON>h toán token", "receiveNFT": "Nhận NFT", "listOn": "<PERSON><PERSON><PERSON> kê trên", "expireTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn"}, "contractCall": {"operationDecoded": "<PERSON><PERSON><PERSON> động đư<PERSON><PERSON> giải mã từ thông điệp"}, "safeCantSignText": "<PERSON><PERSON><PERSON> là một địa chỉ Safe, và nó không thể được sử dụng để ký văn bản.", "signTypeDataOnChain": "<PERSON><PERSON> tên Dữ liệu đã điền {{chain}}", "safeCantSignTypedData": "<PERSON><PERSON><PERSON> là một địa chỉ Safe, và nó chỉ hỗ trợ ký dữ liệu kiểu EIP-712 hoặc chuỗi."}, "activities": {"signedTx": {"empty": {"title": "Chưa có giao dịch nào đ<PERSON><PERSON><PERSON> ký", "desc": "<PERSON><PERSON>t cả các giao dịch được ký qua Rabby sẽ được liệt kê ở đây."}, "common": {"pendingDetail": "<PERSON> tiết đang chờ xử lý", "unknownProtocol": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> không x<PERSON>c đ<PERSON>", "cancel": "Hủy bỏ", "unknown": "Không rõ", "unlimited": "kh<PERSON>ng gi<PERSON>i hạn", "speedUp": "<PERSON><PERSON><PERSON> tốc"}, "tips": {"pendingBroadcastRetryBtn": "Re-broadcast", "pendingBroadcastBtn": "<PERSON><PERSON><PERSON> s<PERSON>g ngay bây giờ", "pendingDetail": "Chỉ một giao dịch sẽ được hoàn thành, và đó gần như luôn luôn là giao dịch có giá gas cao nhất.", "pendingBroadcastRetry": "<PERSON><PERSON><PERSON> sóng không thành công. <PERSON><PERSON>n thử cuối: {{pushAt}}", "pendingBroadcast": "Chế độ tiết kiệm gas: đang chờ phí mạng thấp hơn. Tối đa chờ {{deadline}}h.", "canNotCancel": "Không thể gia tăng tốc độ hoặc hủy: Không phải giao dịch đang chờ đầu tiên"}, "status": {"pending": "<PERSON><PERSON> chờ xử lý", "submitFailed": "<PERSON><PERSON><PERSON><PERSON> thể gửi", "canceled": "Hủy bỏ", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "pendingBroadcast": "Đang chờ: sẽ đư<PERSON><PERSON> phát sóng", "pendingBroadcasted": "Đang chờ: đ<PERSON> ph<PERSON><PERSON> s<PERSON>g", "withdrawed": "<PERSON><PERSON><PERSON>h", "pendingBroadcastFailed": "Chờ xử lý: <PERSON><PERSON><PERSON> sóng không thành công"}, "txType": {"cancel": "Hủy tx", "initial": "<PERSON><PERSON><PERSON><PERSON> ban đ<PERSON>u", "speedUp": "<PERSON><PERSON><PERSON> tốc tx"}, "explain": {"cancel": "<PERSON><PERSON>y {{token}} <PERSON><PERSON> cho {{protocol}}", "send": "Gửi {{amount}} {{symbol}}", "cancelNFTCollectionApproval": "<PERSON><PERSON><PERSON> phê du<PERSON><PERSON>t bộ sưu tập NFT cho {{protocol}}", "cancelSingleNFTApproval": "<PERSON><PERSON>y phê duyệt NFT đơn lẻ cho {{protocol}}", "nftCollectionApproval": "<PERSON><PERSON><PERSON> <PERSON><PERSON> sưu tập NFT cho {{protocol}}", "unknown": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> kh<PERSON>ng x<PERSON>c <PERSON>", "singleNFTApproval": "<PERSON><PERSON> duyệt NFT đơn cho {{protocol}}", "approve": "<PERSON><PERSON> {{count}} {{token}} cho {{protocol}}"}, "CancelTxPopup": {"options": {"quickCancel": {"title": "<PERSON><PERSON><PERSON>", "desc": "<PERSON>ủy tr<PERSON><PERSON><PERSON> khi phát sóng, kh<PERSON>ng phí gas", "tips": "Chỉ hỗ trợ cho các giao dịch chưa đư<PERSON><PERSON> phát sóng."}, "onChainCancel": {"title": "<PERSON><PERSON>y trên chuỗi", "desc": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> mới để hủ<PERSON>, cần gas"}, "removeLocalPendingTx": {"title": "<PERSON><PERSON><PERSON> Địa <PERSON>ư<PERSON>", "desc": "Xóa giao dịch đang chờ từ giao diện"}}, "removeLocalPendingTx": {"title": "<PERSON>ó<PERSON> giao d<PERSON><PERSON> c<PERSON> bộ", "desc": "Hành động này sẽ xóa giao dịch đang chờ xử lý tại chỗ. Giao dịch đang chờ xử lý có thể vẫn được gửi thành công trong tương lai."}, "title": "<PERSON><PERSON><PERSON> giao d<PERSON>ch"}, "MempoolList": {"reBroadcastBtn": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trong bất kỳ nút nào", "title": "<PERSON><PERSON>t hiện trong {{count}} nút RPC"}, "message": {"reBroadcastSuccess": "Đã phát lại", "broadcastSuccess": "<PERSON><PERSON><PERSON>", "cancelSuccess": "Hủy bỏ", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công"}, "gas": {"noCost": "Không tốn Gas"}, "SkipNonceAlert": {"clearPendingAlert": "{{chainName}} <PERSON><PERSON><PERSON> dị<PERSON> ({{nonces}}) đã chờ hơn 3 phút. <PERSON><PERSON>n có thể <5></5> <6><PERSON><PERSON><PERSON> chờ cục bộ</6> <7></7> và gửi lại giao dịch.", "alert": "Nonce #{{nonce}} bị bỏ qua trên chuỗi {{chainName}}. Đi<PERSON>u này có thể gây ra các giao dịch đang chờ phía trước. <5></5> <6>G<PERSON><PERSON> một tx</6> <7></7> trên chuỗi để giải quyết."}, "PredictTime": {"failed": "<PERSON>ự đoán thời gian đóng gói không thành công", "noTime": "Thời gian đóng gói đang được dự đoán", "time": "<PERSON><PERSON> kiến sẽ được đóng gói trong {{time}}"}, "CancelTxConfirmPopup": {"title": "<PERSON><PERSON><PERSON>", "desc": "<PERSON>iều này sẽ xóa giao dịch đang chờ từ giao diện của bạn. Bạn sau đó có thể khởi động một giao dịch mới.", "warning": "<PERSON><PERSON><PERSON> dịch đã bị xóa có thể vẫn được xác nhận trên chuỗi trừ khi nó bị thay thế."}, "label": "<PERSON><PERSON><PERSON>"}, "signedText": {"empty": {"desc": "Tất cả các văn bản được ký qua Rabby sẽ được liệt kê tại đây.", "title": "<PERSON><PERSON><PERSON> có văn bản nào đ<PERSON><PERSON><PERSON> ký"}, "label": "<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON> li<PERSON>u <PERSON> ký"}, "receive": {"watchModeAlert1": "<PERSON><PERSON><PERSON> là một địa chỉ Chế độ <PERSON>.", "title": "Nhận {{token}} trên {{chain}}", "watchModeAlert2": "Bạn có chắc chắn sử dụng nó để nhận tài sản không?"}, "sendToken": {"AddToContactsModal": {"editAddr": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> chú <PERSON>a chỉ", "validator__empty": "<PERSON><PERSON> lòng nhập ghi chú địa chỉ"}, "editAddressNote": "Chỉnh sửa ghi chú địa chỉ", "error": "Thêm vào danh bạ không thành công", "addedAsContacts": "<PERSON><PERSON> thêm vào danh bạ"}, "allowTransferModal": {"error": "mật kh<PERSON>u không ch<PERSON>h xác", "addWhitelist": "<PERSON><PERSON><PERSON><PERSON> vào danh sách trắng", "placeholder": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để xác nhận", "validator__empty": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u"}, "GasSelector": {"level": {"$unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "custom": "<PERSON><PERSON><PERSON> chỉnh", "normal": "<PERSON><PERSON><PERSON>", "fast": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "slow": "<PERSON><PERSON><PERSON>"}, "confirm": "<PERSON><PERSON><PERSON>", "popupTitle": "Đặt giá Gas (Gwei)", "popupDesc": "Chi phí gas sẽ được trừ từ số tiền chuyển dựa trên giá gas mà bạn đã đặt."}, "header": {"title": "<PERSON><PERSON><PERSON>"}, "modalConfirmAddToContacts": {"title": "<PERSON><PERSON><PERSON><PERSON> vào danh bạ", "confirmText": "<PERSON><PERSON><PERSON>"}, "modalConfirmAllowTransferTo": {"confirmText": "<PERSON><PERSON><PERSON>", "cancelText": "Hủy bỏ", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> khẩu để <PERSON>n"}, "sectionBalance": {"title": "Số dư"}, "sectionChain": {"title": "Chuỗi"}, "sectionFrom": {"title": "Từ"}, "sectionTo": {"title": "<PERSON><PERSON><PERSON>", "addrValidator__empty": "<PERSON><PERSON> lòng nhập địa chỉ", "addrValidator__invalid": "Địa chỉ này không hợp lệ", "searchInputPlaceholder": "<PERSON><PERSON><PERSON> kiếm hoặc nhập địa chỉ"}, "tokenInfoFieldLabel": {"contract": "Đ<PERSON>a chỉ hợp đồng", "chain": "Chuỗi"}, "balanceWarn": {"gasFeeReservation": "Cần đặt trước phí Gas"}, "balanceError": {"insufficientBalance": "Số dư không đủ"}, "sectionMsgDataForEOA": {"placeholder": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON><PERSON>", "currentIsUTF8": "<PERSON>ữ liệu đầu vào hiện tại là UTF-8. <PERSON><PERSON> liệu gốc là:", "currentIsOriginal": "<PERSON>ữ liệu đầu vào hiện tại là Dữ liệu gốc. UTF-8 là:"}, "sectionMsgDataForContract": {"parseError": "<PERSON><PERSON><PERSON><PERSON> thể giải mã cuộc gọi hợp đồng", "title": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "simulation": "<PERSON><PERSON> phỏng g<PERSON>i hợp đồng:", "placeholder": "<PERSON><PERSON><PERSON>", "notHexData": "Chỉ hỗ trợ dữ liệu hex"}, "addressNotInContract": "<PERSON><PERSON><PERSON><PERSON> có trong danh sách địa chỉ. <1></1><2>Thêm vào danh bạ</2>", "sendButton": "<PERSON><PERSON><PERSON>", "whitelistAlert__whitelisted": "Địa chỉ đã được đưa vào danh sách trắng", "whitelistAlert__notWhitelisted": "Địa chỉ này không có trong danh sách cho phép. <1 /> T<PERSON><PERSON> đồng ý cấp quyền tạm thời để chuyển nhượng.", "tokenInfoPrice": "Giá", "max": "TỐI ĐA", "whitelistAlert__disabled": "<PERSON><PERSON> sách trắng đã bị vô hiệu hóa. Bạn có thể chuyển đến bất kỳ địa chỉ nào.", "whitelistAlert__temporaryGranted": "<PERSON><PERSON><PERSON><PERSON> tạm thời đã đ<PERSON><PERSON><PERSON> cấp", "blockedTransactionCancelText": "<PERSON><PERSON><PERSON>", "blockedTransaction": "<PERSON><PERSON><PERSON> bị chặn", "blockedTransactionContent": "<PERSON><PERSON><PERSON> d<PERSON>ch này tương tác với một địa chỉ nằm trong danh sách trừng phạt của OFAC."}, "sendTokenComponents": {"SwitchReserveGas": "Dự trữ Gas <1 />", "GasReserved": "<PERSON><PERSON> dành <1>0</1> {{ tokenName }} cho chi phí gas"}, "sendNFT": {"header": {"title": "<PERSON><PERSON><PERSON>"}, "sectionChain": {"title": "Chuỗi"}, "sectionFrom": {"title": "Từ"}, "sectionTo": {"title": "Để", "searchInputPlaceholder": "<PERSON><PERSON><PERSON> kiếm hoặc nhập địa chỉ", "addrValidator__invalid": "Địa chỉ này không hợp lệ", "addrValidator__empty": "<PERSON><PERSON> lòng nhập địa chỉ"}, "nftInfoFieldLabel": {"Collection": "<PERSON><PERSON> s<PERSON>u tập", "sendAmount": "<PERSON><PERSON><PERSON>", "Contract": "<PERSON><PERSON><PERSON>"}, "confirmModal": {"title": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để xác nhận"}, "sendButton": "<PERSON><PERSON><PERSON>", "whitelistAlert__temporaryGranted": "<PERSON><PERSON><PERSON><PERSON> tạm thời đã đ<PERSON><PERSON><PERSON> cấp", "tipAddToContacts": "<PERSON><PERSON><PERSON><PERSON> vào danh bạ", "whitelistAlert__disabled": "<PERSON><PERSON> sách trắng đã bị vô hiệu hóa. Bạn có thể chuyển đến bất kỳ địa chỉ nào.", "tipNotOnAddressList": "<PERSON><PERSON><PERSON><PERSON> có trong danh sách địa chỉ.", "whitelistAlert__notWhitelisted": "Địa chỉ này không nằm trong danh sách trắng. <1 /> Tôi đồng ý cấp quyền tạm thời để chuyển giao.", "whitelistAlert__whitelisted": "Địa chỉ đã được đưa vào danh sách trắng."}, "approvals": {"header": {"title": "<PERSON><PERSON> trên {{ address }}"}, "tab-switch": {"assets": "Bởi <PERSON><PERSON><PERSON> sản", "contract": "Bằng <PERSON><PERSON><PERSON>"}, "component": {"table": {"bodyEmpty": {"noMatchText": "Không khớp", "loadingText": "<PERSON><PERSON> tả<PERSON>...", "noDataText": "<PERSON><PERSON><PERSON><PERSON> có phê du<PERSON>"}}, "ApprovalContractItem": {"ApprovalCount_other": "<PERSON><PERSON>", "ApprovalCount_one": "<PERSON><PERSON>"}, "RevokeButton": {"permit2Batch": {"modalTitle_one": "<PERSON><PERSON><PERSON> cộng cần <2>{{count}}</2> chữ ký.", "modalTitle_other": "<PERSON><PERSON><PERSON> cộng cần <2>{{count}}</2> chữ ký.", "modalContent": "<PERSON><PERSON><PERSON> phê duyệt từ cùng một hợp đồng Permit2 sẽ được gộp lại dưới cùng một chữ ký."}, "btnText_other": "Hủy bỏ ({{count}})", "btnText_zero": "Hủy bỏ", "btnText_one": "<PERSON><PERSON> h<PERSON> ({{count}})"}, "ViewMore": {"text": "<PERSON><PERSON>"}}, "search": {"placeholder": "T<PERSON>m kiếm {{ type }} theo tên/địa chỉ"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "<PERSON><PERSON><PERSON>", "contractTrustValue": "<PERSON><PERSON><PERSON> tr<PERSON> <PERSON> cậy <PERSON> đồng", "myApprovalTime": "<PERSON>h<PERSON><PERSON> gian phê duyệt của tôi", "myApprovedAssets": "<PERSON><PERSON><PERSON> sản đã đư<PERSON><PERSON> phê duyệt của tôi", "revokeTrends": "24h Revoke Trends"}, "columnTip": {"contractTrustValueWarning": "<PERSON><PERSON><PERSON> trị niềm tin hợp đồng < $100,000", "contractTrustValueDanger": "<PERSON><PERSON><PERSON> trị tin cậy của hợp đồng < $10,000", "contractTrustValue": "Gi<PERSON> trị niềm tin đề cập đến tổng giá trị tài sản đã chi tiêu bởi hợp đồng này. Một giá trị niềm tin thấp cho thấy hoặc là rủi ro hoặc là không hoạt động trong 180 ngày."}}, "byAssets": {"columnTitle": {"type": "<PERSON><PERSON><PERSON>", "asset": "<PERSON><PERSON><PERSON>", "myApprovalTime": "<PERSON>h<PERSON><PERSON> gian phê duyệt của tôi", "approvedSpender": "<PERSON><PERSON><PERSON><PERSON> chi tiê<PERSON> đ<PERSON><PERSON><PERSON> phê du<PERSON>t", "approvedAmount": "<PERSON><PERSON> tiền đ<PERSON><PERSON><PERSON> phê <PERSON>"}, "columnCell": {"approvedAmount": {"tipMyBalance": "Số Dư <PERSON>", "tipApprovedAmount": "Số tiền đã phê du<PERSON>t"}}}}, "RevokeApprovalModal": {"title": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON> {{ selectedCount }}", "unSelectAll": "Bỏ chọn tất cả", "subTitleContract": "<PERSON><PERSON> phê duyệt cho các hợp đồng sau", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "tooltipPermit2": "Sự phê duyệt này được phê duyệt qua hợp đồng Permit2:  \n{{ permit2Id }}", "subTitleTokenAndNFT": "Token và NFT đã được phê duyệt"}, "revokeModal": {"gasNotEnough": "Không đủ Gas để gửi", "approvalCount_other": "{{count}} ph<PERSON>", "gasTooHigh": "Phí gas cao", "totalRevoked": "Tổng:", "submitTxFailed": "<PERSON><PERSON><PERSON><PERSON> thể gửi", "batchRevoke": "<PERSON><PERSON><PERSON> theo lô", "signAndStartRevoke": "<PERSON>ý và Bắt đầu <PERSON> bỏ", "revokeOneByOne": "<PERSON><PERSON><PERSON> từng c<PERSON>i một", "approvalCount_one": "{{count}} ph<PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON><PERSON><PERSON>", "approvalCount_zero": "{{count}} ph<PERSON>", "cancelTitle": "<PERSON><PERSON><PERSON> c<PERSON>c yêu cầu thu hồi còn lại", "revoked": "Revoked:", "cancelBody": "<PERSON><PERSON><PERSON> bạn đóng trang nà<PERSON>, các yêu cầu hủy bỏ còn lại sẽ không được thực hiện.", "done": "<PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON>", "confirmRevokeLedger": "Sử dụng địa chỉ Ledger, bạn có thể thu hồi hàng loạt {{count}} phê duyệt chỉ với 1 cú nhấp chuột.", "defaultFailed": "<PERSON><PERSON><PERSON> d<PERSON>ch thất b<PERSON>i", "confirmRevokePrivateKey": "Bằng cách sử dụng cụm từ hạt giống hoặc địa chỉ khóa riêng, bạn có thể thu hồi {{count}} quyền phê duyệt chỉ với 1 cú nhấp chuột.", "confirmTitle": "<PERSON><PERSON><PERSON> <PERSON>hi<PERSON>u lần với một cú nhấp chuột", "simulationFailed": "<PERSON><PERSON> phỏng không thành công", "paused": "<PERSON><PERSON><PERSON>", "revokeWithLedger": "<PERSON><PERSON><PERSON> đầu thu hồi với <PERSON>", "stillRevoke": "Vẫn thu hồi", "waitInQueue": "Chờ trong hàng đợi", "connectLedger": "<PERSON><PERSON><PERSON><PERSON>", "ledgerSended": "<PERSON><PERSON> lòng ký yêu cầu trên <PERSON> ({{current}}/{{total}})", "ledgerSigned": "<PERSON><PERSON> ký. <PERSON><PERSON> tạo giao d<PERSON>ch ({{current}}/{{total}})", "ledgerAlert": "<PERSON><PERSON> lòng mở ứng dụng Ethereum trên thiết bị <PERSON> của bạn", "useGasAccount": "Số dư gas của bạn thấp. GasAccount của bạn sẽ chi trả phí gas.", "ledgerSending": "<PERSON><PERSON><PERSON> yêu cầu ký ({{current}}/{{total}})"}}, "gasTopUp": {"Amount": "<PERSON><PERSON> tiền", "Value": "<PERSON><PERSON><PERSON> trị", "payment": "Thanh toán nạp Gas", "Select-payment-token": "<PERSON><PERSON><PERSON> mã thông báo thanh toán", "Confirm": "<PERSON><PERSON><PERSON>", "title": "Nạp Gas Ngay L<PERSON>", "Loading_Tokens": "<PERSON><PERSON> t<PERSON>...", "No_Tokens": "<PERSON><PERSON><PERSON><PERSON> có token", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Including-service-fee": "<PERSON><PERSON> gồm phí dịch vụ {{fee}}", "Payment-Token": "<PERSON><PERSON>", "InsufficientBalanceTips": "Số dư không đủ", "hightGasFees": "<PERSON><PERSON> tiền nạp này quá nhỏ vì mạng mục tiêu yêu cầu phí gas cao.", "topUpChain": "<PERSON><PERSON><PERSON> Chuỗi", "Token": "Token", "InsufficientBalance": "<PERSON>hông đủ số dư trong địa chỉ hợp đồng của <PERSON><PERSON> cho chuỗi hiện tại. <PERSON><PERSON> lòng thử lại sau.", "Select-from-supported-tokens": "<PERSON><PERSON><PERSON> từ các token được hỗ trợ", "description": "Nạp gas bằng cách gửi cho chúng tôi các token có sẵn trên chuỗi khác. Chuyển tiền ngay khi thanh toán của bạn được xác nhận, không cần phải chờ đợi cho đến khi nó không thể đảo ngược.", "service-fee-tip": "Bằng cách cung cấp dịch vụ Nạp Gas, <PERSON><PERSON> phải chịu mất mát từ sự biến động của token và phí gas cho việc nạp. <PERSON>, một khoản phí dịch vụ 20% sẽ đượ<PERSON> tính.", "Balance": "Số dư"}, "swap": {"rabbyFee": {"wallet": "Ví", "rate": "Tỷ lệ phí", "title": "<PERSON><PERSON> ph<PERSON>", "button": "<PERSON><PERSON> hiểu", "bridgeDesc": "Rabby Wallet sẽ luôn tìm ra tỷ lệ tốt nhất có thể từ các nhà tổng hợp hàng đầu và xác minh độ tin cậy của các ưu đãi của họ. Rabby tính phí 0,25%, kho<PERSON>n phí này sẽ tự động được bao gồm trong báo giá.", "swapDesc": "<PERSON>bby Wallet sẽ luôn tìm ra tỷ lệ tốt nhất từ các nhà tổng hợp hàng đầu và xác minh độ tin cậy của các đề nghị của họ. Rabby tính phí 0,25% (0% cho việ<PERSON> bọc), đ<PERSON><PERSON><PERSON> tự động bao gồm trong báo giá."}, "lowCreditModal": {"title": "Token này có giá trị tín dụng thấp.", "desc": "Một giá trị tín dụng thấp thư<PERSON><PERSON> báo hiệu rủi ro cao, chẳng hạn như một token honeypot hoặc thanh khoản rất thấp."}, "from": "Từ", "chain": "Chuỗi", "unlimited-allowance": "<PERSON><PERSON><PERSON><PERSON> chi tiêu không giới hạn", "no-transaction-records": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> hồ sơ giao dịch", "swap-history": "<PERSON><PERSON><PERSON> sử ho<PERSON> đổi", "gas-x-price": "Giá gas: {{price}} Gwei.", "title": "<PERSON><PERSON>", "actual-slippage": "Actual Slippage:", "Completed": "<PERSON><PERSON><PERSON> t<PERSON>t", "get-quotes": "<PERSON><PERSON><PERSON><PERSON> báo giá", "slippage_tolerance": "Toleransi slippage:", "approve-swap": "<PERSON><PERSON> và <PERSON> đổi", "approve-x-symbol": "<PERSON><PERSON> {{symbol}}", "not-supported": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ", "amount-in": "<PERSON><PERSON> lượng trong {{symbol}}", "swap-via-x": "<PERSON><PERSON> đổi qua {{name}}", "no-fee-for-wrap": "<PERSON><PERSON><PERSON>ng có phí <PERSON>bby cho Wrap", "to": "<PERSON><PERSON><PERSON>", "slippage-adjusted-refresh-quote": "<PERSON>iều chỉnh trượt giá. Làm mới báo giá.", "testnet-is-not-supported": "<PERSON>ạng tùy chỉnh không được hỗ trợ", "approve-and-swap": "<PERSON><PERSON><PERSON> thuận và <PERSON><PERSON> đổi qua {{name}}", "swap-from": "<PERSON><PERSON><PERSON><PERSON> đổi từ", "Pending": "<PERSON><PERSON> chờ xử lý", "completedTip": "<PERSON><PERSON><PERSON> dịch trên chuỗi, gi<PERSON>i mã dữ liệu để tạo bản ghi", "pendingTip": "Tx đã đư<PERSON><PERSON> gửi. Nếu tx đang trong trạng thái chờ quá lâu, bạn có thể thử xóa pending trong cài đặt.", "insufficient-balance": "Số dư không đủ", "price-expired-refresh-quote": "<PERSON><PERSON><PERSON> đã hết hạn. <PERSON><PERSON><PERSON> mới báo giá.", "search-by-name-address": "<PERSON><PERSON><PERSON> kiếm the<PERSON> / Đ<PERSON><PERSON> chỉ", "InSufficientTip": "<PERSON><PERSON> dư không đủ để thực hiện mô phỏng giao dịch và ước tính gas. <PERSON><PERSON><PERSON> báo giá gốc của aggregator được hiển thị.", "unable-to-fetch-the-price": "<PERSON><PERSON><PERSON><PERSON> thể lấy giá", "edit": "Chỉnh sửa", "rabby-fee": "<PERSON><PERSON>", "est-payment": "<PERSON><PERSON><PERSON> <PERSON>:", "no-slippage-for-wrap": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> giá cho <PERSON>rap", "hidden-no-quote-rates_other": "{{count}} tỷ lệ không khả dụng", "wrap-contract": "Wrap Contract", "best": "<PERSON><PERSON><PERSON>", "rates-from-cex": "Tỷ giá từ CEX", "hidden-no-quote-rates_one": "{{count}} tỷ lệ không khả dụng", "this-token-pair-is-not-supported": "Cặp token không được hỗ trợ", "est-receiving": "<PERSON><PERSON><PERSON> <PERSON>:", "security-verification-failed": "<PERSON><PERSON><PERSON> <PERSON>h b<PERSON>o mật thất bại", "preferMEV": "Ưu tiên MEV Guarded", "minimum-received": "<PERSON><PERSON><PERSON> thi<PERSON><PERSON> n<PERSON>ận đ<PERSON>", "the-following-swap-rates-are-found": "<PERSON><PERSON><PERSON> thấy các tỷ lệ sau đây", "enable-it": "<PERSON><PERSON><PERSON> n<PERSON> lên", "sort-with-gas": "Sắp xếp theo gas", "by-transaction-simulation-the-quote-is-valid": "Bằng cách mô phỏng giao dịch, báo gi<PERSON> là hợp lệ", "need-to-approve-token-before-swap": "<PERSON><PERSON><PERSON> chấp thuận token tr<PERSON><PERSON><PERSON> khi hoán đổi", "Gas-fee-too-high": "Phí Gas quá cao", "there-is-no-fee-and-slippage-for-this-trade": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>ng bị tr<PERSON><PERSON> gi<PERSON>.", "directlySwap": "<PERSON><PERSON><PERSON> c<PERSON>c token {{symbol}} tr<PERSON><PERSON> tiếp với hợp đồng thông minh", "fail-to-simulate-transaction": "<PERSON><PERSON><PERSON><PERSON> thể mô phỏng giao dịch", "tradingSettingTips": "{{viewCount}} sàn giao dịch cung cấp báo gi<PERSON>, và {{tradeCount}} cho phép giao dịch", "approve-tips": "1.<PERSON><PERSON><PERSON> → 2.<PERSON><PERSON> đ<PERSON>i", "preferMEVTip": "<PERSON><PERSON>t t<PERSON>h năng \"MEV Guarded\" cho các giao dịch hoán đổi Ethereum để giảm rủi ro tấn công sandwich. Lưu ý: t<PERSON>h năng này không được hỗ trợ nếu bạn sử dụng RPC tùy chỉnh hoặc địa chỉ wallet connect.", "est-difference": "Est. Difference:", "QuoteLessWarning": "<PERSON>ố tiền nhận được được ước tính từ mô phỏng giao dịch của Rabby. Đề nghị được cung cấp bởi dex là {{receive}}. Bạn sẽ nhận được {{diff}} ít hơn so với đề nghị mong đợi.", "usd-after-fees": "≈ {{usd}}", "this-exchange-is-not-enabled-to-trade-by-you": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> này không đư<PERSON><PERSON> phép thực hiện bởi bạn.", "view-quotes": "<PERSON><PERSON> b<PERSON>o giá", "slippage-tolerance": "<PERSON><PERSON><PERSON>", "fetch-best-quote": "<PERSON><PERSON><PERSON> b<PERSON>o giá tốt nhất", "exchanges": "<PERSON><PERSON>n giao d<PERSON>ch", "max": "TỐI ĐA", "gas-fee": "GasFee: {{gasUsed}}", "price-impact": "<PERSON><PERSON><PERSON> động giá", "confirm": "<PERSON><PERSON><PERSON>", "enable-trading": "<PERSON><PERSON><PERSON> ho<PERSON> G<PERSON>", "dex": "<PERSON>", "trade": "<PERSON><PERSON><PERSON>", "estimate": "Ước tính:", "rate": "Tỷ lệ", "enable-exchanges": "<PERSON><PERSON><PERSON> ho<PERSON> G<PERSON>", "actual": "<PERSON><PERSON><PERSON><PERSON> tế:", "select-token": "<PERSON><PERSON><PERSON>", "no-fees-for-wrap": "<PERSON><PERSON><PERSON>ng có phí <PERSON>bby cho Wrap", "cex": "Cex", "two-step-approve": "Ký 2 giao dịch để thay đổi quyền hạn", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "<PERSON><PERSON><PERSON> d<PERSON>ch có thể bị frontrun vì độ trư<PERSON>t giá cao.", "two-step-approve-details": "Token USDT yêu cầu 2 giao dịch để thay đổi quyền cho phép. <PERSON><PERSON><PERSON> tiên, bạn cần đặt lại quyền cho phép về không, và chỉ sau đó đặt giá trị quyền cho phép mới.", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Slippage thấp có thể gây ra giao dịch bị thất bại do sự biến động cao.", "tradingSettingTip2": "2. <PERSON><PERSON> không chịu trách nhiệm về bất kỳ rủi ro nào phát sinh từ hợp đồng của các sàn giao dịch", "i-understand-and-accept-it": "<PERSON><PERSON><PERSON> hiểu và chấp nhận điều đó", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "Ưu đãi được chọn khác xa với tỷ lệ hiện tại, có thể gây ra tổn thất lớn.", "process-with-two-step-approve": "<PERSON><PERSON><PERSON><PERSON> hành phê duyệt hai bư<PERSON>c", "recommend-slippage": "<PERSON><PERSON> ngăn chặn việc chạy trước, chúng tôi khuyên bạn nên đặt mức trượt giá dướ<PERSON> <2>{{ slippage }}</2>%", "tradingSettingTip1": "1. <PERSON><PERSON> đ<PERSON><PERSON><PERSON>, bạn sẽ tương tác với hợp đồng từ sàn giao dịch trực tiếp.", "Auto": "<PERSON><PERSON> động", "No-available-quote": "<PERSON><PERSON><PERSON><PERSON> có báo giá nào khả dụng", "source": "<PERSON><PERSON><PERSON><PERSON>", "loss-tips": "Bạn đang mất {{usd}}. <PERSON><PERSON><PERSON> thử một số tiền nhỏ hơn trong một thị trường nhỏ.", "no-quote-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy báo giá"}, "bridge": {"showMore": {"title": "<PERSON><PERSON><PERSON> thị thêm", "source": "<PERSON><PERSON><PERSON>"}, "settingModal": {"confirmModal": {"title": "<PERSON><PERSON>t giao d<PERSON>ch với Aggregator này", "i-understand-and-accept-it": "<PERSON><PERSON><PERSON> hiểu và chấp nhận điều đó", "tip2": "2. <PERSON><PERSON> không chị<PERSON> tr<PERSON><PERSON> nhiệm cho bất kỳ rủi ro nào phát sinh từ hợp đồng của nhà tổng hợp này.", "tip1": "1. <PERSON><PERSON> đ<PERSON><PERSON><PERSON>, bạn sẽ tương tác trực tiếp với hợp đồng từ trình tổng hợp này."}, "confirm": "<PERSON><PERSON><PERSON>", "SupportedBridge": "<PERSON><PERSON><PERSON> được hỗ trợ:", "title": "Kích hoạt Bridge Aggregators để giao dịch"}, "tokenPairDrawer": {"tokenPair": "Cặp Token", "title": "<PERSON><PERSON><PERSON> từ cặp token được hỗ trợ", "noData": "Kh<PERSON>ng có Cặp Token Hỗ Trợ", "balance": "<PERSON><PERSON><PERSON> trị số dư"}, "history": "<PERSON><PERSON><PERSON> s<PERSON> cầu", "Completed": "<PERSON><PERSON><PERSON> th<PERSON>", "title": "<PERSON><PERSON><PERSON>", "From": "Từ", "To": "<PERSON><PERSON><PERSON>", "Select": "<PERSON><PERSON><PERSON>", "Pending": "<PERSON><PERSON> chờ xử lý", "no-transaction-records": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> hồ sơ giao dịch", "select-chain": "<PERSON><PERSON><PERSON> Chuỗi", "estimate": "Ước tính:", "no-quote-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy báo giá. <PERSON><PERSON> lòng thử các cặp token khác.", "gas-fee": "GasFee: {{gasUsed}}", "the-following-bridge-route-are-found": "<PERSON><PERSON><PERSON> thấy lộ trình sau đây", "Balance": "Số dư:", "no-quote": "<PERSON><PERSON><PERSON><PERSON> báo giá", "actual": "<PERSON><PERSON><PERSON><PERSON> tế:", "pendingTip": "Tx đã đư<PERSON><PERSON> gửi. Nếu tx đang chờ lâu, bạn có thể thử xóa trạng thái chờ trong cài đặt.", "duration": "{{duration}} phút", "getRoutes": "<PERSON><PERSON><PERSON> c<PERSON> lộ trình", "bridge-via-x": "Bridge trên {{name}}", "est-payment": "<PERSON><PERSON><PERSON> <PERSON>:", "estimated-value": "≈ {{value}}", "detail-tx": "<PERSON> ti<PERSON>", "recommendFromToken": "Bridge từ <1></1> cho một báo giá hiện có", "bridgeTo": "<PERSON><PERSON><PERSON>", "est-difference": "Est. Difference:", "aggregator-not-enabled": "<PERSON><PERSON><PERSON><PERSON> tổng hợp này không đ<PERSON><PERSON><PERSON> bạn bật để giao dịch.", "price-impact": "<PERSON><PERSON><PERSON> động giá", "tokenPairPlaceholder": "Chọn Cặp Token", "est-receiving": "<PERSON><PERSON><PERSON> <PERSON>:", "best": "<PERSON><PERSON><PERSON>", "approve-and-bridge": "<PERSON><PERSON> và <PERSON>", "unlimited-allowance": "Số dư không gi<PERSON>i hạn", "no-route-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đường dẫn", "approve-x-symbol": "<PERSON><PERSON><PERSON> thu<PERSON>n {{symbol}}", "insufficient-balance": "Số dư không đủ", "gas-x-price": "Giá gas: {{price}} Gwei.", "enable-it": "<PERSON><PERSON><PERSON> n<PERSON> lên", "slippage-adjusted-refresh-quote": "<PERSON>iều chỉnh trượt giá. Làm mới lộ trình.", "bridge-cost": "<PERSON> phí cầu nối", "BridgeTokenPair": "Cặp Token Bridge", "need-to-approve-token-before-bridge": "<PERSON><PERSON><PERSON> phê duy<PERSON>t token tr<PERSON><PERSON><PERSON> khi chuyển đổi.", "Amount": "Số lượng", "rabby-fee": "<PERSON><PERSON> ph<PERSON>", "loss-tips": "Bạn đang mất {{usd}}. <PERSON><PERSON><PERSON> thử một số tiền kh<PERSON>c.", "price-expired-refresh-route": "<PERSON><PERSON><PERSON> đã hết hạn. <PERSON><PERSON><PERSON> mới lộ trình.", "via-bridge": "qua {{bridge}}", "max-tips": "<PERSON><PERSON><PERSON> trị này đ<PERSON> tính bằng cách trừ chi phí gas cho cầu.", "completedTip": "<PERSON><PERSON><PERSON> dịch trên chuỗi, gi<PERSON>i mã dữ liệu để tạo bản ghi"}, "manageAddress": {"no-match": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "no-address": "<PERSON><PERSON><PERSON><PERSON> có địa chỉ", "delete-private-key-modal-title_one": "Xóa {{count}} địa chỉ khóa riêng", "confirm": "<PERSON><PERSON><PERSON>", "whitelisted-address": "Địa chỉ trong danh sách trắng", "search": "<PERSON><PERSON><PERSON>", "update-balance-data": "<PERSON><PERSON><PERSON> nhật dữ liệu số dư", "manage-address": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> chỉ", "cancel": "Hủy bỏ", "addressTypeTip": "<PERSON><PERSON><PERSON><PERSON> khẩu bởi {{type}}", "deleted": "Xóa bỏ", "current-address": "Đ<PERSON>a chỉ hiện tại", "delete-desc": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi xóa, h<PERSON><PERSON> ghi nhớ những điểm sau để hiểu cách bảo vệ tài sản của bạn.", "delete-checklist-2": "Tô<PERSON> xác nhận rằng tôi đã sao lưu private key hoặc Seed Phrase và tôi đã sẵn sàng để xóa nó bây giờ.", "delete-checklist-1": "Tôi hiểu rằng nếu tôi xóa địa chỉ này, <PERSON><PERSON><PERSON><PERSON> Riêng và Cụm Hạt tương ứng của địa chỉ này sẽ bị xóa và Rabby sẽ KHÔNG thể phục hồi nó.", "address-management": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> chỉ", "confirm-delete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "passphraseError": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> không hợp lệ", "sort-address": "<PERSON><PERSON><PERSON> xếp <PERSON> chỉ", "add-address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "watch-address": "<PERSON> chỉ", "sort-by-balance": "<PERSON><PERSON><PERSON> xếp theo số dư", "sort-by-address-note": "<PERSON><PERSON><PERSON> xếp theo ghi chú địa chỉ", "delete-private-key-modal-title_other": "Xóa {{count}} địa chỉ khóa riêng", "addNewAddress": "<PERSON><PERSON><PERSON><PERSON> Chỉ Mới", "seed-phrase": "<PERSON><PERSON><PERSON> từ hạt giống", "delete-seed-phrase-title_other": "<PERSON><PERSON><PERSON> cụm từ hạt giống và {{count}} địa chỉ của nó", "enterPassphraseTitle": "<PERSON><PERSON><PERSON><PERSON> cụm mật khẩu để ký", "private-key": "Khóa riêng", "backup-seed-phrase": "<PERSON><PERSON> <PERSON><PERSON><PERSON> cụm từ hạt giống", "delete-seed-phrase-title_one": "<PERSON><PERSON><PERSON> cụm từ hạt giống và {{count}} địa chỉ của nó", "delete-empty-seed-phrase": "<PERSON><PERSON><PERSON> cụm từ hạt giống và địa chỉ 0 của nó", "delete-title_one": "Xóa {{count}} địa chỉ {{brand}}", "hd-path": "HD đường dẫn:", "no-address-under-seed-phrase": "<PERSON><PERSON><PERSON> chưa nhập bất kỳ địa chỉ nào dưới cụm từ hạt giống này.", "delete-all-addresses-and-the-seed-phrase": "<PERSON><PERSON><PERSON> tất cả địa chỉ và cụm từ hạt giống", "enterThePassphrase": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>m mật kh<PERSON>u", "sort-by-address-type": "<PERSON><PERSON><PERSON> xếp theo lo<PERSON>i địa chỉ", "delete-title_other": "Xóa {{count}} địa chỉ {{brand}}", "seed-phrase-delete-title": "<PERSON><PERSON><PERSON> cụm từ hạt giố<PERSON>?", "delete-all-addresses-but-keep-the-seed-phrase": "<PERSON><PERSON><PERSON> tất cả các địa chỉ, nhưng giữ lại cụm từ khôi phục", "delete-seed-phrase": "<PERSON><PERSON><PERSON> cụm từ hạt giống", "CurrentDappAddress": {"desc": "Đ<PERSON>i địa chỉ Dapp\n"}}, "dashboard": {"home": {"panel": {"receive": "Nhậ<PERSON>", "swap": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "mobile": "Mobile Sync", "nft": "NFT", "transactions": "<PERSON><PERSON><PERSON>", "queue": "<PERSON><PERSON><PERSON>", "ecology": "<PERSON><PERSON> sinh thái", "bridge": "<PERSON><PERSON><PERSON>", "rabbyPoints": "<PERSON><PERSON>", "gasTopUp": "Nạp Gas", "manageAddress": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> chỉ", "approvals": "<PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON>", "feedback": "<PERSON><PERSON><PERSON>"}, "queue": {"title": "<PERSON><PERSON><PERSON>", "count": "{{count}} trong"}, "offline": "<PERSON>ạng đã bị ngắt kết nối và không có dữ liệu nào đư<PERSON>c thu thập.", "pendingCount": "1 <PERSON><PERSON> chờ", "transactionNeedsToSign": "giao d<PERSON>ch c<PERSON>n ký", "viewFirstOne": "<PERSON><PERSON> cái đầu tiên", "view": "Xem", "refreshTheWebPageToTakeEffect": "<PERSON><PERSON><PERSON> mới trang web để có hiệu lực", "whatsNew": "<PERSON>ó gì mới", "rejectAll": "<PERSON>ừ chối Tất cả", "flip": "<PERSON><PERSON><PERSON>", "metamaskIsInUseAndRabbyIsBanned": "MetaMask đang đư<PERSON><PERSON> sử dụng và <PERSON>bby bị cấm.", "comingSoon": "S<PERSON><PERSON> ra mắt", "missingDataTooltip": "<PERSON><PERSON> dư có thể không đư<PERSON><PERSON> cập nhật do sự cố mạng hiện tại với {{text}}.", "soon": "Sớm", "transactionsNeedToSign": "giao d<PERSON>ch c<PERSON>n ký", "importType": "<PERSON><PERSON><PERSON><PERSON> khẩu bởi {{type}}", "rabbyIsInUseAndMetamaskIsBanned": "<PERSON><PERSON> đang đ<PERSON><PERSON><PERSON> sử dụng và Metamask bị cấm", "chainEnd": "chuỗi", "chain": "chuỗi,", "pendingCountPlural": "{{countStr}} <PERSON><PERSON> chờ xử lý"}, "recentConnection": {"disconnectRecentlyUsed": {"description": "<PERSON><PERSON><PERSON>pp được ghim sẽ vẫn được kết nối.", "title_one": "<PERSON><PERSON><PERSON> kết nối <strong>{{count}}</strong> <PERSON><PERSON> đã kết nối", "title_other": "<PERSON><PERSON><PERSON> kết nối <strong>{{count}}</strong> <PERSON><PERSON> đã kết nối", "title": "<PERSON><PERSON><PERSON> kết n<PERSON> <strong>{{count}}</strong> DApps đã sử dụng gần đây"}, "connected": "<PERSON><PERSON><PERSON>", "noDappFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON>", "rpcUnavailable": "RPC tùy chỉnh không khả dụng", "dragToSort": "<PERSON><PERSON><PERSON> để sắp xếp", "dapps": "<PERSON><PERSON>", "noConnectedDapps": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> kết n<PERSON>i", "title": "<PERSON><PERSON> đã kết nối", "notConnected": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "noRecentlyConnectedDapps": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> nào đư<PERSON><PERSON> kết nối gần đây", "noPinnedDapps": "<PERSON><PERSON><PERSON><PERSON> có dapps đã ghim", "disconnected": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "pinned": "<PERSON><PERSON> l<PERSON>", "disconnectAll": "<PERSON><PERSON><PERSON> kết n<PERSON>i tất cả", "metamaskModeTooltip": "<PERSON><PERSON><PERSON><PERSON> thể kết nối Rabby trên <PERSON> này? <PERSON><PERSON><PERSON> thử bật <1>Chế độ MetaMask</1>", "connectedDapp": "<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> kết nối với <PERSON> hiện tại. <PERSON><PERSON> kết nối, hãy tìm và nhấn nút kết nối trên trang web củ<PERSON>.", "metamaskModeTooltipNew": "Ví dụ Rabby Wallet sẽ kết nối khi bạn chọn \"MetaMask\" trê<PERSON>. Bạn có thể quản lý điều này trong More > Connect Rabby bằng cách giả dạng như MetaMask.", "recentlyConnected": "Gần đ<PERSON>y đã kết nối", "metamaskTooltip": "<PERSON><PERSON>n thích sử dụng MetaMask với dapp này. <PERSON><PERSON><PERSON> nhật cài đặt này bất cứ lúc nào trong Cài đặt > Dapps ưu tiên MetaMask."}, "feedback": {"directMessage": {"description": "Tr<PERSON> ch<PERSON> v<PERSON><PERSON> Official tr<PERSON><PERSON>", "content": "<PERSON> nh<PERSON>n trực tiếp"}, "proposal": {"content": "<PERSON><PERSON> xuất", "description": "<PERSON><PERSON><PERSON> một đề xuất cho Ra<PERSON> trên <PERSON>"}, "title": "<PERSON><PERSON><PERSON>"}, "nft": {"collectionList": {"collections": {"label": "<PERSON><PERSON> s<PERSON>u tập"}, "all_nfts": {"label": "<PERSON><PERSON><PERSON> c<PERSON> NFTs"}}, "modal": {"purchaseDate": "<PERSON><PERSON><PERSON> mua", "send": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON> s<PERSON>u tập", "chain": "Chuỗi", "lastPrice": "<PERSON><PERSON><PERSON> cu<PERSON>i c<PERSON>ng", "sendTooltip": "Chỉ có ERC 721 và ERC 1155 NFTs được hỗ trợ hiện tại"}, "empty": "<PERSON><PERSON><PERSON><PERSON> tìm thấy NFTs trong các Bộ sưu tập được hỗ trợ", "listEmpty": "Bạn ch<PERSON>a có b<PERSON>t kỳ NFT nào."}, "rabbyBadge": {"claim": "<PERSON><PERSON><PERSON> c<PERSON>", "imageLabel": "rabby badge", "rabbyFreeGasUserNo": "<PERSON>bby Free <PERSON> Người dùng <PERSON> {{num}}", "enterClaimCode": "<PERSON><PERSON><PERSON><PERSON> mã yêu cầu", "viewOnDebank": "<PERSON><PERSON> tr<PERSON><PERSON>", "viewYourClaimCode": "<PERSON>em mã yêu cầu của bạn trên <PERSON>", "rabbyValuedUserNo": "<PERSON><PERSON> dùng giá trị số {{num}}", "learnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "title": "<PERSON><PERSON><PERSON><PERSON> cho", "freeGasTip": "<PERSON><PERSON> lòng ký một giao dịch bằng cách sử dụng Free Gas. Nút 'Free Gas' sẽ xuất hiện tự động khi gas của bạn không đủ.", "freeGasTitle": "Nhận Gas Badge MIỄN PHÍ cho", "noCode": "Bạn chưa kích hoạt mã yêu cầu cho địa chỉ này.", "learnMoreOnDebank": "<PERSON><PERSON>m hiểu thêm trê<PERSON>", "goToSwap": "<PERSON><PERSON>", "claimSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u thành công", "freeGasNoCode": "<PERSON><PERSON> lòng nhấp và<PERSON> nút bên dưới để truy cập DeBank và lấy mã yêu cầu bằng địa chỉ hiện tại của bạn trước.", "swapTip": "<PERSON><PERSON>n cần hoàn thành một giao dịch hoán đổi với dex nổi bật trong Rabby Wallet trước."}, "contacts": {"noDataLabel": "không có dữ liệu", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "oldContactList": "<PERSON><PERSON> s<PERSON>ch liên h<PERSON> cũ", "oldContactListDescription": "<PERSON>, do sự kết hợp giữa danh bạ và địa chỉ mô hình theo dõi, các danh bạ cũ sẽ được sao lưu cho bạn ở đây và sau một thời gian, chúng tôi sẽ xóa danh sách. <PERSON>ui lòng thêm kịp thời nếu bạn tiếp tục sử dụng."}, "security": {"title": "<PERSON><PERSON><PERSON>", "comingSoon": "<PERSON><PERSON><PERSON> t<PERSON>h năng mới sẽ sớm ra mắt", "tokenApproval": "<PERSON><PERSON>", "nftApproval": "Phê duyệt NFT"}, "settings": {"lock": {"never": "<PERSON><PERSON><PERSON><PERSON> bao giờ"}, "updateVersion": {"content": "<PERSON><PERSON><PERSON> bản cập nhật mới cho Rabby Wallet đã có sẵn. Nhấp vào đây để kiểm tra cách cập nhật thủ công.", "successTip": "Bạn đang sử dụng phiên bản mới nhất", "okText": "<PERSON><PERSON> dẫn", "title": "<PERSON><PERSON><PERSON> nhật có sẵn"}, "features": {"rabbyPoints": "<PERSON><PERSON>", "searchDapps": "<PERSON><PERSON><PERSON>", "connectedDapp": "<PERSON><PERSON> đã kết nối", "lockWallet": "Khóa Ví", "signatureRecord": "Signature Record", "label": "<PERSON><PERSON><PERSON>", "gasTopUp": "Nạp Gas", "manageAddress": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> chỉ"}, "settings": {"label": "Cài đặt", "enableTestnets": "<PERSON><PERSON><PERSON>", "currentLanguage": "<PERSON><PERSON><PERSON> ngữ hiện tại", "metamaskMode": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> bằng cách ngụy trang thành <PERSON>", "toggleThemeMode": "<PERSON><PERSON> độ chủ đề", "customTestnet": "<PERSON><PERSON><PERSON><PERSON> Chỉnh", "customRpc": "<PERSON><PERSON>a đổi RPC URL", "enableWhitelistForSendingAssets": "<PERSON><PERSON><PERSON> ho<PERSON> sách Trắng <PERSON> sản", "metamaskPreferredDapps": "MetaMask Dapps Ưa Thích", "themeMode": "<PERSON><PERSON> độ chủ đề", "enableDappAccount": "Thay đổi địa chỉ <PERSON><PERSON> độc lập\n"}, "1Hour": "1 giờ", "7Days": "7 ngày", "autoLockTime": "<PERSON>hời gian tự động khóa", "host": "Ch<PERSON> sở hữu", "cancel": "Hủy bỏ", "enableWhitelist": "<PERSON><PERSON><PERSON> s<PERSON>ch <PERSON>", "1Day": "1 ngày", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "save": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON> phục cài đặt ban đầu", "pendingTransactionCleared": "<PERSON><PERSON><PERSON> d<PERSON>ch đang chờ đã đ<PERSON><PERSON><PERSON> xóa.", "inputOpenapiHost": "<PERSON><PERSON> l<PERSON> nh<PERSON>p host openapi", "backendServiceUrl": "URL Dịch vụ Backend", "pleaseCheckYourHost": "<PERSON><PERSON> lòng kiểm tra máy chủ của bạn", "4Hours": "4 giờ", "disableWhitelist": "<PERSON><PERSON> hi<PERSON> h<PERSON> s<PERSON>ch trắng", "10Minutes": "10 phút", "claimRabbyBadge": "<PERSON><PERSON><PERSON><PERSON> huy hiệu <PERSON>!", "clearPending": "<PERSON><PERSON><PERSON> Địa <PERSON>ư<PERSON>", "enableWhitelistTip": "<PERSON><PERSON> đư<PERSON><PERSON> k<PERSON>, bạn chỉ có thể gửi tài sản đến các địa chỉ trong danh sách trắng bằng cách sử dụng Rabby.", "clearWatchAddressContent": "Bạn có chắc chắn xóa tất cả địa chỉ Chế độ Xem không?", "disableWhitelistTip": "Bạn có thể gửi tài sản đến bất kỳ địa chỉ nào sau khi đã vô hiệu hóa.", "clearPendingTip2": "<PERSON><PERSON> không ảnh hưởng đến số dư tài khoản của bạn hoặc yêu cầu bạn nhập lại cụm từ khôi phục. Tất cả tài sản và thông tin tài khoản đều đượ<PERSON> bảo mật.", "clearPendingTip1": "Hành động này sẽ xóa giao dịch đang chờ từ giao diện của bạn, g<PERSON><PERSON><PERSON> gi<PERSON><PERSON> quyết các vấn đề do thời gian chờ lâu trên mạng gây ra.", "followUs": "<PERSON> ch<PERSON>g tôi", "supportedChains": "Chuỗi tích hợp", "testnetBackendServiceUrl": "URL dịch vụ backend Testnet", "currentVersion": "<PERSON><PERSON><PERSON> bản hiện tại", "aboutUs": "<PERSON><PERSON> chúng tôi", "requestDeBankTestnetGasToken": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON>ken Gas DeBank Testnet", "clearWatchMode": "<PERSON><PERSON><PERSON> chế độ xem", "updateAvailable": "<PERSON><PERSON><PERSON> nhật có sẵn", "clearPendingWarningTip": "<PERSON><PERSON>o dịch đã xóa có thể vẫn được xác nhận trên chuỗi trừ khi nó bị thay thế.", "DappAccount": {"button": "<PERSON><PERSON><PERSON>\n", "title": "Thay đổi địa chỉ DApp độc lập\n", "desc": "B<PERSON>t t<PERSON><PERSON> năng <PERSON>, bạn có thể chọn địa chỉ kết nối với từng ứng dụng phi tập trung (Dapp) một cách độc lập. Thay đổi địa chỉ chính của bạn sẽ không ảnh hưởng đến địa chỉ đã kết nối với từng Dapp.\n"}}, "tokenDetail": {"txFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "receive": "Nhậ<PERSON>", "blockedButtons": "token bị chặn", "send": "<PERSON><PERSON><PERSON>", "scamTx": "Scam tx", "blocked": "Bị chặn", "swap": "<PERSON><PERSON>", "customizedButtons": "token tùy chỉnh", "customizedButton": "token tùy chỉnh", "noTransactions": "<PERSON><PERSON><PERSON><PERSON> có giao dịch", "blockedButton": "token bị chặn", "blockedTip": "Token bị chặn sẽ không được hiển thị trong danh sách token.", "notSelectedCustom": "Token chưa được liệt kê bởi Rabby. Nó sẽ được thêm vào danh sách token nếu bạn bật lên.", "selectedCustom": "Token này không đượ<PERSON> liệt kê bởi Rabby. Bạn đã thêm nó vào danh sách token một cách thủ công.", "notSupported": "Token trên chuỗi này không được hỗ trợ.", "customized": "<PERSON><PERSON><PERSON> chỉnh", "Chain": "Chuỗi", "BridgeProvider": "<PERSON><PERSON><PERSON> cung cấp cầu", "IssuerWebsite": "Trang web của nhà phát hành", "blockedListTitle": "token bị chặn", "OriginalToken": "<PERSON><PERSON>", "myBalance": "Số Dư <PERSON>", "TokenName": "<PERSON><PERSON><PERSON>", "blockedListTitles": "token bị chặn", "ListedBy": "<PERSON><PERSON><PERSON><PERSON> li<PERSON> kê bởi", "OriginIssue": "<PERSON><PERSON><PERSON><PERSON> phát hành một cách tự nhiên trên blockchain này", "customizedListTitle": "token tùy chỉnh", "NoSupportedExchanges": "<PERSON><PERSON><PERSON><PERSON> có sàn giao dịch nào được hỗ trợ.", "verifyScamTips": "Đ<PERSON>y là một mã thông báo lừa đảo", "customizedListTitles": "token tùy chỉnh", "ContractAddress": "Đ<PERSON>a chỉ hợp đồng", "noIssuer": "<PERSON>hông có thông tin người phát hành nào có sẵn", "NoListedBy": "<PERSON><PERSON>ông có thông tin niêm yết nào có sẵn", "BridgeIssue": "<PERSON><PERSON> đư<PERSON><PERSON> cầu nối bởi bên thứ ba", "SupportedExchanges": "<PERSON><PERSON>n giao d<PERSON>ch đ<PERSON> hỗ trợ", "blockedTips": "Token bị chặn sẽ không được hiển thị trong danh sách token.", "customizedHasAddedTips": "Token không đư<PERSON><PERSON> liệt kê bởi Rabby. Bạn đã thêm nó vào danh sách token thủ công.", "AddToMyTokenList": "Thê<PERSON> vào danh sách token của tôi", "maybeScamTips": "<PERSON><PERSON>y là một token chất lượng thấp và có thể là một trò lừa đảo.", "fdvTips": "<PERSON><PERSON><PERSON> hóa thị trường nếu số cung tối đa được lưu hành. Định giá hoàn toàn pha loãng (FDV) = Giá x Số cung tối đa. Nếu Số cung tối đa là null, FDV = Gi<PERSON> x Tổng cung. Nếu cả Số cung tối đa và Tổng cung đều không được xác định hoặc vô hạn, FDV sẽ không được hiển thị."}, "assets": {"portfolio": {"fractionTips": "<PERSON><PERSON>h toán dựa trên giá của token ERC20 liên kết.", "nftTips": "<PERSON><PERSON><PERSON> toán dựa trên giá sàn được công nhận bởi giao thức này."}, "tokenButton": {"subTitle": "<PERSON><PERSON><PERSON> token trong danh sách này sẽ không được cộng vào tổng số dư."}, "table": {"healthRate": "Tỷ l<PERSON> sức khỏe", "assetAmount": "<PERSON><PERSON><PERSON> / <PERSON><PERSON> l<PERSON>", "useValue": "Giá trị USD", "price": "Giá", "lentAgainst": "VAY MƯỢN CHỐNG LẠI", "strikePrice": "<PERSON><PERSON><PERSON> th<PERSON>n", "debtRatio": "Tỷ lệ nợ", "type": "<PERSON><PERSON><PERSON>", "unlockAt": "Mở khóa tại", "lowValueAssets_0": "{{count}} token gi<PERSON> trị thấp", "PL": "P&L", "pool": "POOL", "side": "<PERSON><PERSON><PERSON>", "token": "Token", "exerciseEnd": "<PERSON><PERSON><PERSON> th<PERSON>c bài tập", "unsupportedPoolType": "Loại pool không được hỗ trợ", "leverage": "<PERSON><PERSON><PERSON> d<PERSON>", "balanceValue": "Số dư / <PERSON><PERSON><PERSON> trị", "dailyUnlock": "Mở khóa hàng ngày", "tradePair": "Cặp giao d<PERSON>ch", "noMatch": "Không khớp", "lowValueAssets_other": "{{count}} token gi<PERSON> trị thấp", "claimable": "<PERSON><PERSON> thể yêu cầu", "endAt": "<PERSON><PERSON><PERSON> th<PERSON> t<PERSON>i", "percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "lowValueAssets_one": "{{count}} token gi<PERSON> thấp", "summaryTips": "G<PERSON><PERSON> trị tài sản chia cho tổng giá trị ròng", "summaryDescription": "Tất cả tài sản trong các giao thứ<PERSON> (ví dụ: LP tokens) đều được quy đổi thành tài sản cơ sở để thực hiện các phép tính thống kê.", "lowValueDescription": "<PERSON><PERSON><PERSON> sản có giá trị thấp sẽ được hiển thị ở đây"}, "AddMainnetToken": {"title": "<PERSON><PERSON><PERSON><PERSON> Chỉnh", "tokenAddress": "Địa chỉ Token", "selectChain": "<PERSON><PERSON><PERSON> chuỗi", "notFound": "Token không tìm thấy", "searching": "<PERSON><PERSON><PERSON> k<PERSON>", "isBuiltInToken": "Token đã được hỗ trợ.", "tokenAddressPlaceholder": "Địa chỉ Token"}, "AddTestnetToken": {"selectChain": "<PERSON><PERSON><PERSON> chuỗi", "notFound": "<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> tìm thấy", "title": "<PERSON><PERSON><PERSON><PERSON>hông <PERSON> Mạng Tùy Chỉnh", "searching": "<PERSON><PERSON><PERSON> k<PERSON>", "tokenAddressPlaceholder": "Địa chỉ Token", "tokenAddress": "Địa chỉ Token"}, "TestnetAssetListContainer": {"addTestnet": "Mạng", "add": "Token"}, "amount": "SỐ TIỀN", "usdValue": "GIÁ TRỊ USD", "searchPlaceholder": "Tokens", "unfoldChain": "Mở 1 chuỗi", "blockLinkText": "Tìm địa chỉ để khóa token", "noAssets": "<PERSON><PERSON><PERSON><PERSON> có tài sản", "comingSoon": "Sắp ra mắt...", "customDescription": "Token tùy chỉnh do bạn thêm sẽ được hiển thị ở đây.", "unfoldChainPlural": "Mở rộng {{moreLen}} chuỗi", "blockDescription": "Token bị chặn bởi bạn sẽ được hiển thị ở đây", "addTokenEntryText": "Token", "noTestnetAssets": "<PERSON><PERSON><PERSON><PERSON> có <PERSON>ài sản Mạng Tùy chỉnh", "customButtonText": "Thêm token tùy chỉnh"}, "hd": {"ledger": {"doc2": "<PERSON><PERSON><PERSON><PERSON> pin để mở khóa", "connected": "Ledger đ<PERSON> kết n<PERSON>i", "doc1": "<PERSON><PERSON>m vào một Ledger đơn lẻ", "doc3": "Mở Ứng Dụng Ethereum", "reconnect": "<PERSON><PERSON><PERSON> nó không hoạt động, h<PERSON><PERSON> thử <1>kết nối lại từ đầu.</1>"}, "keystone": {"doc1": "Cắm vào một Keystone đơn lẻ", "title": "<PERSON><PERSON><PERSON> bảo rằng Keystone 3 Pro của bạn ở trang chính.", "doc3": "<PERSON><PERSON>t kết nối với máy t<PERSON>h", "doc2": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để mở khóa", "reconnect": "<PERSON><PERSON><PERSON> nó không hoạt động, vui lòng thử <1>kết nối lại từ đầu.</1>"}, "imkey": {"doc2": "<PERSON><PERSON><PERSON><PERSON> mã pin để mở khóa", "doc1": "<PERSON><PERSON>m vào một imKey đơn lẻ"}, "howToConnectKeystone": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>", "howToSwitch": "<PERSON><PERSON><PERSON> chuy<PERSON> đ<PERSON>i", "userRejectedTheRequest": "Ngư<PERSON>i dùng đã từ chối yêu cầu.", "howToConnectLedger": "<PERSON><PERSON><PERSON>", "ledgerIsDisconnected": "<PERSON><PERSON> của bạn không đ<PERSON><PERSON><PERSON> kết nối", "howToConnectImKey": "<PERSON><PERSON><PERSON> kết n<PERSON> im<PERSON>ey"}, "GnosisWrongChainAlertBar": {"notDeployed": "Địa chỉ Safe của bạn chưa được triển khai trên chuỗi này."}, "echologyPopup": {"title": "<PERSON><PERSON> sinh thái"}, "MetamaskModePopup": {"title": "MetaMask Mode", "footerText": "Thêm nhiều <PERSON> vào chế độ MetaMask trong Thêm > Chế độ MetaMask", "toastSuccess": "<PERSON><PERSON> bật. <PERSON><PERSON><PERSON> mới trang để kết nối lại.", "enableDesc": "<PERSON><PERSON><PERSON> n<PERSON>u <PERSON> chỉ hoạt động với MetaMask", "desc": "<PERSON><PERSON><PERSON> bạn không thể kết nối <PERSON><PERSON> trên m<PERSON><PERSON>, h<PERSON><PERSON> bật Chế độ MetaMask và kết nối bằng cách chọn tùy chọn MetaMask."}, "offlineChain": {"chain": "{{chain}} sẽ sớm không được tích hợp.", "tips": "{{chain}} Chain sẽ không được tích hợp vào ngày {{date}}. Tài sản của bạn sẽ không bị ảnh hưởng nhưng sẽ không được bao gồm trong tổng số dư của bạn. <PERSON><PERSON> truy cập v<PERSON><PERSON>, bạn có thể thêm nó như một mạng tùy chỉnh trong “More”."}, "recentConnectionGuide": {"title": "Thay đổi địa chỉ để kết nối DApp tại đây\n", "button": "<PERSON><PERSON>\n"}}, "nft": {"empty": {"title": "<PERSON><PERSON><PERSON><PERSON> có NFT được đánh dấu sao", "description": "Bạn có thể chọn NFT từ \"All\" và thêm vào \"Starred\""}, "all": "<PERSON><PERSON><PERSON> c<PERSON>", "noNft": "Không có NFT", "title": "NFT", "floorPrice": "/ Giá sàn:", "starred": "<PERSON><PERSON> đánh dấu ({{count}})"}, "newAddress": {"addContacts": {"required": "<PERSON><PERSON> lòng nhập địa chỉ", "scanViaPcCamera": "Quét qua camera máy t<PERSON>h", "content": "<PERSON><PERSON><PERSON><PERSON>", "scanViaMobileWallet": "Quét qua ví di động", "notAValidAddress": "Địa chỉ không hợp lệ", "description": "<PERSON><PERSON><PERSON> cũng có thể sử dụng nó như một địa chỉ chỉ xem.", "walletConnectVPN": "WalletConnect sẽ không ổn định nếu bạn sử dụng VPN.", "walletConnect": "<PERSON><PERSON><PERSON> n<PERSON>i ví", "addressEns": "Địa chỉ / ENS", "scanQRCode": "Quét mã QR với ví tương thích WalletConnect", "cameraTitle": "<PERSON><PERSON> lòng quét mã QR bằng camera của bạn"}, "unableToImport": {"title": "<PERSON><PERSON><PERSON><PERSON> thể nhập khẩu", "description": "Việt hóa: <PERSON><PERSON><PERSON><PERSON> nhập nhiều ví phần cứng dựa trên mã QR không được hỗ trợ. Vui lòng xóa tất cả các địa chỉ từ {{0}} trước khi nhập thiết bị khác."}, "seedPhrase": {"whatIsASeedPhrase": {"answer": "<PERSON><PERSON><PERSON> cụm từ gồm 12, 18 hoặc 24 từ được sử dụng để kiểm soát tài sản của bạn.", "question": "Seed Phrase là gì?"}, "isItSafeToImportItInRabby": {"question": "Có an toàn khi nhập nó vào Ra<PERSON> không?", "answer": "<PERSON><PERSON>, nó sẽ được lưu trữ cục bộ trên trình duyệt của bạn và chỉ bạn có thể truy cập."}, "showSeedPhrase": "<PERSON><PERSON><PERSON> Seed Phrase", "importError": "[CreateMnemonics] b<PERSON><PERSON><PERSON> không mong đợi {{0}}", "copy": "<PERSON><PERSON> ch<PERSON><PERSON> cụm từ hạt giống", "backup": "<PERSON><PERSON> <PERSON><PERSON><PERSON> cụm từ khôi phục", "importTips": "Bạn có thể dán toàn bộ cụm từ khôi phục bí mật của bạn vào trường đầu tiên.", "riskTips": "<PERSON>r<PERSON><PERSON><PERSON> khi bạn bắt đầu, vui lòng đọc và ghi nhớ các mẹo bảo mật sau đây.", "backupTips": "<PERSON><PERSON><PERSON> bảo không ai khác đang nhìn vào màn hình của bạn khi bạn sao lưu cụm từ hạt giống.", "clearAll": "<PERSON><PERSON><PERSON>", "passphrase": "<PERSON><PERSON><PERSON>h<PERSON> phụ", "verifySeedPhrase": "<PERSON><PERSON><PERSON>h cụm từ hạt giống", "slip39SeedPhrasePlaceholder_other": "<PERSON><PERSON><PERSON><PERSON> chia sẻ cụm từ hạt giống {{count}} của bạn vào đây", "saved": "<PERSON><PERSON><PERSON> đã lưu cụm từ.", "createdSuccessfully": "<PERSON><PERSON><PERSON> thành công", "fillInTheBackupSeedPhraseInOrder": "<PERSON><PERSON><PERSON><PERSON> cụm từ sao lưu theo thứ tự.", "slip39SeedPhrasePlaceholder_two": "<PERSON><PERSON><PERSON><PERSON> các phần chia seed phrase thứ {{count}} của bạn ở đây", "verificationFailed": "<PERSON><PERSON><PERSON> minh không thành công", "slip39SeedPhrasePlaceholder_few": "<PERSON><PERSON><PERSON><PERSON> các phần chia seed phrase {{count}}rd của bạn ở đây", "pastedAndClear": "<PERSON><PERSON> và bộ nhớ tạm đã đư<PERSON><PERSON> x<PERSON>a", "slip39SeedPhrase": "<PERSON><PERSON><PERSON> có một cụm từ hạt giống <0>{{SLIP39}}</0>", "pleaseSelectWords": "<PERSON><PERSON> lòng chọn từ", "importQuestion4": "<PERSON><PERSON><PERSON> tôi gỡ cài đặt <PERSON><PERSON> mà không sao lưu cụm từ khôi phục, <PERSON><PERSON> sẽ không thể lấy lại cho tôi.", "inputInvalidCount_other": "{{count}} inputs không tuân thủ các tiêu chuẩn Seed Phrase, vui lòng kiểm tra.", "inputInvalidCount_one": "1 đầu vào không tuân thủ các tiêu chuẩn Seed Phrase, vui lòng kiểm tra.", "invalidContent": "<PERSON><PERSON><PERSON> dung không hợp lệ", "wordPhraseAndPassphrase": "<PERSON><PERSON><PERSON> có một cụm từ <1>{{count}}</1> từ với Passphrase", "wordPhrase": "<PERSON><PERSON><PERSON> có một cụm từ <1>{{count}}</1> từ", "slip39SeedPhrasePlaceholder_one": "<PERSON><PERSON><PERSON><PERSON> các phần chia sẻ cụm từ hạt giống thứ {{count}} của bạn vào đây", "slip39SeedPhraseWithPassphrase": "<PERSON><PERSON><PERSON> có một cụm từ hạt giống <0>{{SLIP39}}</0> với mật kh<PERSON>u.", "importQuestion2": "<PERSON><PERSON><PERSON> gốc của tôi chỉ được lưu trữ trên thiết bị của tôi. <PERSON><PERSON> không thể truy cập nó.", "importQuestion3": "<PERSON><PERSON><PERSON> tôi gỡ cài đặt Rabby mà không sao lưu cụm từ khôi phục của mình, nó sẽ không thể được phục hồi bởi Rabby.", "importQuestion1": "<PERSON><PERSON>u tôi mất hoặc chia sẻ cụm từ hạt giống của mình, tôi sẽ mất quyền truy cập vào tài sản của mình vĩnh viễn."}, "metamask": {"tips": "Mẹo:", "step2": "<PERSON><PERSON><PERSON><PERSON> cụm từ hạt giống hoặc khóa riêng trong Rabby", "step": "Bước", "how": "<PERSON><PERSON><PERSON> thế nào để nhập tài khoản MetaMask của tôi?", "step3": "<PERSON><PERSON><PERSON><PERSON> khẩu đã hoàn tất và tất cả tài sản của bạn sẽ <br /> xuất hiện tự động.", "tipsDesc": "<PERSON><PERSON><PERSON> thần chú / khóa riêng của bạn không thuộc về MetaMask hay bất kỳ ví cụ thể nào; nó chỉ thuộc về bạn.", "step1": "<PERSON><PERSON><PERSON> khẩu cụm từ hạt giống hoặc chìa khóa riêng từ MetaMask <br /> <1>Nhấp để xem hướng dẫn <1/></1>", "importSeedPhraseTips": "<PERSON><PERSON> sẽ chỉ được lưu trữ cục bộ trên trình duyệt. <PERSON><PERSON> sẽ không bao giờ có quyền truy cập vào thông tin riêng tư của bạn.", "importSeedPhrase": "<PERSON><PERSON><PERSON><PERSON> cụm từ hạt giống hoặc khóa riêng"}, "privateKey": {"whatIsAPrivateKey": {"question": "<PERSON>h<PERSON>a riêng tư là gì?", "answer": "Một chuỗi chữ cái và số được sử dụng để kiểm soát tài sản của bạn."}, "repeatImportTips": {"desc": "Địa chỉ này đã được nhập.", "question": "Bạn có muốn chuyển sang địa chỉ này không?"}, "isItSafeToImportItInRabby": {"question": "Nó có an toàn để nhập vào <PERSON> không?", "answer": "<PERSON><PERSON>, nó sẽ được lưu trữ cục bộ trên trình duyệt của bạn và chỉ bạn có thể truy cập."}, "isItPossibleToImportKeystore": {"answer": "<PERSON><PERSON><PERSON>, bạn có thể <1> import KeyStore </1> ở đây.", "question": "Có thể nhập KeyStore không?"}, "required": "<PERSON><PERSON> lòng nhập Private key", "notAValidPrivateKey": "Không phải là một khóa riêng hợp lệ", "placeholder": "Nhập Private key c<PERSON><PERSON> b<PERSON>n"}, "ledger": {"error": {"running_app_close_error": "<PERSON><PERSON><PERSON><PERSON> thể đóng ứng dụng đang chạy trên thiết bị <PERSON> của bạn.", "ethereum_app_unconfirmed_error": "Bạn đã từ chối yêu cầu mở ứng dụng Ethereum.", "ethereum_app_not_installed_error": "<PERSON><PERSON> lòng cài đặt ứng dụng Ethereum trên thiết bị <PERSON> của bạn.", "ethereum_app_open_error": "<PERSON><PERSON> lòng cài đặt/chấp nhận ứng dụng Ethereum trên thiết bị <PERSON> của bạn."}, "title": "<PERSON><PERSON><PERSON><PERSON>", "ledgerPermission1": "<PERSON><PERSON><PERSON> n<PERSON>i với thiết bị HID", "allowRabbyPermissionsTitle": "<PERSON> phép <PERSON><PERSON> quyền để:", "cameraPermissionTitle": "Cho phép <PERSON><PERSON> truy cập camera", "allow": "<PERSON> phép", "ledgerPermissionTip": "<PERSON><PERSON> lòng nh<PERSON><PERSON> và<PERSON> \"Allow\" dưới đây và ủy quyền truy cập vào Ledger của bạn trong cửa sổ pop-up dưới đây.", "permissionsAuthorized": "<PERSON>uyền đã đư<PERSON><PERSON> cấp phép", "nowYouCanReInitiateYourTransaction": "B<PERSON>y giờ bạn có thể khởi động lại giao dịch của mình.", "cameraPermission1": "Cho phép <PERSON><PERSON> truy cập camera trong cửa sổ pop-up của trình du<PERSON>t"}, "imkey": {"title": "<PERSON><PERSON><PERSON> n<PERSON>", "imkeyPermissionTip": "<PERSON><PERSON> lòng nhấp và<PERSON> \"Allow\" bên dư<PERSON>i và cấp quyền truy cập vào imKey của bạn trong cửa sổ bật lên sau."}, "keystone": {"allowRabbyPermissionsTitle": "<PERSON> phép <PERSON><PERSON> các quyền để:", "keystonePermission1": "<PERSON><PERSON><PERSON> n<PERSON>i với thiết bị <PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "noDeviceFoundError": "<PERSON><PERSON><PERSON> một Keystone duy nhất", "unknowError": "Lỗi kh<PERSON>ng x<PERSON><PERSON>, vui lòng thử lại", "keystonePermissionTip": "<PERSON><PERSON> lòng nhấn \"Allow\" bên dưới để cấp quyền truy cập vào Keystone của bạn trong cửa sổ pop-up tiế<PERSON> theo, và đảm bảo rằng Keystone 3 Pro của bạn đang ở trang chính.", "deviceRejectedExportAddress": "<PERSON><PERSON><PERSON> thu<PERSON>n kết n<PERSON>i v<PERSON><PERSON>", "deviceIsBusy": "<PERSON><PERSON><PERSON><PERSON> bị đang bận", "deviceIsLockedError": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để mở khóa", "exportAddressJustAllowedOnHomePage": "<PERSON><PERSON><PERSON> khẩu địa chỉ chỉ được phép trên trang chủ"}, "walletConnect": {"status": {"default": "<PERSON><PERSON>t với {{brand}}", "brandError": "Ứng dụng ví sai.", "connected": "<PERSON><PERSON><PERSON>", "brandErrorDesc": "<PERSON><PERSON> lòng sử dụng {{brandName}} để kết nối", "accountErrorDesc": "<PERSON>ui lòng chuyển đổi địa chỉ trong ví di động của bạn", "rejected": "<PERSON><PERSON>t nối đã bị hủy. <PERSON><PERSON> lòng quét mã QR để thử lại.", "duplicate": "Đ<PERSON><PERSON> chỉ bạn đang cố gắng nhập trùng lặp", "accountError": "Địa chỉ không khớp.", "received": "<PERSON><PERSON><PERSON> thành công. <PERSON><PERSON> chờ xác nh<PERSON>n."}, "accountError": {}, "tip": {"accountError": {"tip1": "<PERSON><PERSON><PERSON> n<PERSON>i nhưng không thể ký.", "tip2": "<PERSON><PERSON> lòng chuyển đến địa chỉ chính xác trong ví di động."}, "disconnected": {"tip": "<PERSON><PERSON><PERSON><PERSON> kết nối với {{brandName}}"}, "connected": {"tip": "<PERSON><PERSON><PERSON> n<PERSON>i với {{brandName}}"}}, "button": {"connect": "<PERSON><PERSON><PERSON>", "howToSwitch": "<PERSON><PERSON><PERSON> chuy<PERSON> đ<PERSON>i", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>"}, "url": "URL", "disconnected": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "viaWalletConnect": "qua Wallet Connect", "connectedSuccessfully": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "changeBridgeServer": "<PERSON>hay đ<PERSON>i máy chủ cầu nối", "qrCode": "Mã QR", "title": "<PERSON><PERSON><PERSON> n<PERSON>i với {{brandName}}", "connectYour": "<PERSON><PERSON><PERSON> n<PERSON>i ví của bạn", "qrCodeError": "<PERSON>ui lòng kiểm tra mạng của bạn hoặc làm mới mã QR"}, "hd": {"tooltip": {"removed": "Địa chỉ đã được xóa khỏi Rabby", "disconnected": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với ví phần cứng. <PERSON><PERSON> lòng thử kết nối lại.", "connectError": "Kết nối đã dừng. <PERSON>ui lòng làm mới trang để kết nối lại.", "added": "Địa chỉ đã đư<PERSON><PERSON> thêm vào <PERSON>"}, "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: Đường dẫn <PERSON> ch<PERSON><PERSON> thức của Ledger. Trong 3 địa chỉ đầu tiên, c<PERSON> các địa chỉ được sử dụng trên chuỗi.", "legacy": "Legacy: Đ<PERSON>ờng dẫn HD được sử dụng bởi MEW / Mycrypto. Trong 3 địa chỉ đầu tiên, c<PERSON> các địa chỉ được sử dụng trên chuỗi.", "bip44": "Ti<PERSON><PERSON> chuẩn BIP44: <PERSON><PERSON> được định nghĩa bởi giao thức BIP44. Trong 3 địa chỉ đầ<PERSON> tiên, c<PERSON> các địa chỉ được sử dụng trên chuỗi."}, "hdPathTypeNoChain": {"legacy": "Di sản: Đ<PERSON>ờng dẫn HD được sử dụng bởi MEW / Mycrypto. Trong 3 địa chỉ đầu tiên, không có địa chỉ nào được sử dụng trên chuỗi.", "ledgerLive": "Ledger Live: Đường dẫn <PERSON> ch<PERSON><PERSON> thức của Ledger. Trong 3 địa chỉ đầu tiên, không có địa chỉ nào được sử dụng trên chuỗi.", "bip44": "Ti<PERSON><PERSON> chuẩn BIP44: Đ<PERSON>ờng dẫn HD được định nghĩa bởi giao thức BIP44. Trong 3 địa chỉ đầu tiên, khô<PERSON> có địa chỉ nào được sử dụng trên chuỗi."}}, "trezor": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON><PERSON> dẫn <PERSON> ch<PERSON>h thức của Ledger.", "bip44": "BIP44: <PERSON><PERSON> đ<PERSON> xác định bởi giao thức BIP44.", "legacy": "Legacy: HD path đư<PERSON>c sử dụng bởi MEW / Mycrypto."}, "hdPathTypeNoChain": {"legacy": "Legacy: HD path đư<PERSON>c sử dụng bởi MEW / Mycrypto.", "ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON><PERSON> dẫn <PERSON> ch<PERSON>h thức của Ledger.", "bip44": "BIP44: <PERSON><PERSON> đ<PERSON> định nghĩa bởi giao thức BIP44."}, "message": {"disconnected": "{{0}}Connect đã dừng lại. Vui lòng làm mới trang để kết nối lại."}}, "onekey": {"hdPathType": {"bip44": "BIP44: <PERSON><PERSON> đ<PERSON> xác định bởi giao thức BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: <PERSON><PERSON> đ<PERSON> xác định bởi giao thức BIP44."}}, "mnemonic": {"hdPathType": {"ledgerLive": "Ledger Live: <PERSON><PERSON><PERSON><PERSON> dẫn <PERSON> ch<PERSON>h thức của Ledger.", "default": "Mặc định: Đ<PERSON>ờng dẫn HD mặc định để nhập cụm từ hạt giống được sử dụng.", "legacy": "Di sản: <PERSON><PERSON><PERSON><PERSON> <PERSON> được sử dụng bởi MEW / Mycrypto.", "bip44": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>ẩn BIP44: <PERSON><PERSON> đ<PERSON><PERSON>c xác định bởi giao thức BIP44."}, "hdPathTypeNoChain": {"default": "Mặc định: Đ<PERSON>ờng dẫn HD mặc định để nhập cụm từ hạt giống được sử dụng."}}, "gridplus": {"hdPathType": {"legacy": "Di sản: Đ<PERSON>ờng dẫn HD được sử dụng bởi MEW / Mycrypto. Trong 3 địa chỉ đầu tiên, có các địa chỉ được sử dụng trên chuỗi.", "bip44": "Ti<PERSON><PERSON> chuẩn BIP44: <PERSON><PERSON> được xác định bởi giao thức BIP44. Trong 3 địa chỉ đầu tiên, c<PERSON> các địa chỉ được sử dụng trên chuỗi.", "ledgerLive": "Ledger Live: Đường dẫn <PERSON> ch<PERSON><PERSON> thức của Ledger. Trong 3 địa chỉ đầu tiên, c<PERSON> các địa chỉ được sử dụng trên chuỗi."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: Đường dẫn <PERSON> ch<PERSON><PERSON> thức của Ledger. Trong 3 địa chỉ đầu tiên, không có địa chỉ nào được sử dụng trên chuỗi.", "legacy": "Di sản: Đ<PERSON>ờng dẫn HD được sử dụng bởi MEW / Mycrypto. Trong 3 địa chỉ đầu tiên, không có địa chỉ nào được sử dụng trên chuỗi.", "bip44": "BIP44 Standard: HD path được xác định bởi giao thức BIP44. Trong 3 địa chỉ đầu tiên, không có địa chỉ nào được sử dụng trên chuỗi."}, "switch": {"title": "<PERSON><PERSON><PERSON><PERSON> sang thiết bị GridPlus mới", "content": "<PERSON><PERSON><PERSON>ng hỗ trợ nhập nhiều thiết bị GridPlus. <PERSON><PERSON><PERSON> bạ<PERSON> chuyể<PERSON> sang một thiết bị GridPlus mới, danh sách địa chỉ của thiết bị hiện tại sẽ bị xóa trước khi bắt đầu quá trình nhập."}, "switchToAnotherGridplus": "<PERSON><PERSON><PERSON><PERSON> sang một GridPlus kh<PERSON>c"}, "keystone": {"hdPathType": {"bip44": "BIP44: <PERSON><PERSON><PERSON><PERSON> <PERSON>path đ<PERSON><PERSON><PERSON> xác định bởi giao thức BIP44.", "ledgerLive": "Ledger Live: Đường dẫn <PERSON> ch<PERSON>h thức của Ledger. Bạn chỉ có thể quản lý 10 địa chỉ với đường dẫn Ledger Live.", "legacy": "Legacy: HD path đư<PERSON>c sử dụng bởi MEW / Mycrypto."}, "hdPathTypeNochain": {"legacy": "Di sản: Đ<PERSON>ờng dẫn HD được sử dụng bởi MEW / Mycrypto.", "ledgerLive": "Ledger Live: Đường dẫn <PERSON> ch<PERSON>h thức của Ledger. Bạn chỉ có thể quản lý 10 địa chỉ với đường dẫn Ledger Live.", "bip44": "BIP44: <PERSON><PERSON> đ<PERSON> xác định bởi giao thức BIP44."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: <PERSON><PERSON> đ<PERSON> xác định bởi giao thức BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: <PERSON><PERSON> đ<PERSON> xác định bởi giao thức BIP44."}, "disconnected": "<PERSON><PERSON><PERSON>ng thể kết nối với BitBox02. <PERSON><PERSON> lòng làm mới trang để kết nối lại. Lý do: {{0}}"}, "qrCode": {"switch": {"title": "<PERSON><PERSON><PERSON><PERSON> sang thiết bị {{0}} mới", "content": "<PERSON><PERSON><PERSON><PERSON> hỗ trợ nhập nhiều thiết bị {{0}}. <PERSON><PERSON><PERSON> bạn chuyển sang một thiết bị {{0}} mới, danh sách địa chỉ của thiết bị hiện tại sẽ bị xóa trước khi bắt đầu quá trình nhập."}, "switchAnother": "<PERSON><PERSON><PERSON><PERSON> sang {{0}} kh<PERSON><PERSON>"}, "balance": "Số dư", "loadingAddress": "<PERSON><PERSON> tải {{0}}/{{1}} địa chỉ", "waiting": "<PERSON><PERSON> chờ", "notes": "<PERSON><PERSON><PERSON>", "hideOnChainInformation": "Ẩn thông tin on-chain", "getOnChainInformation": "<PERSON><PERSON><PERSON> thông tin trên chuỗi", "addresses": "Địa chỉ", "usedChains": "Chuỗi đã sử dụng", "basicInformation": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "addToRabby": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "firstTransactionTime": "<PERSON><PERSON><PERSON><PERSON> gian giao dịch đầu tiên", "clickToGetInfo": "Nhấn để lấy thông tin trên chuỗi", "selectHdPath": "<PERSON><PERSON><PERSON> đường dẫn HD:", "advancedSettings": "Cài đặt Nâng cao", "selectIndexTip": "Chọn số serial của địa chỉ để bắt đầu từ:", "manageAddressFrom": "<PERSON><PERSON><PERSON><PERSON> lý địa chỉ từ {{0}} đến {{1}}", "customAddressHdPath": "Đường dẫn HD địa chỉ tùy chỉnh", "manageImtokenOffline": "<PERSON><PERSON><PERSON><PERSON> lý imToken", "manageNgraveZero": "Quản lý NGRAVE ZERO", "manageImKey": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "connectedToTrezor": "<PERSON><PERSON><PERSON> n<PERSON> v<PERSON><PERSON>", "manageKeystone": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>", "manageCoolwallet": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "manageAirgap": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>Gap", "addressesInRabby": "<PERSON><PERSON><PERSON> chỉ trong Rabby{{0}}", "connectedToOnekey": "<PERSON><PERSON><PERSON> n<PERSON> với OneKey", "manageSeedPhrase": "<PERSON><PERSON><PERSON><PERSON>", "connectedToLedger": "<PERSON><PERSON><PERSON> n<PERSON> vớ<PERSON>", "manageGridplus": "<PERSON><PERSON><PERSON><PERSON> lý GridPlus", "manageBitbox02": "Quản lý BitBox02", "importBtn": "<PERSON><PERSON><PERSON>p ({{count}})", "addressesIn": "<PERSON><PERSON><PERSON> chỉ trong {{0}}"}, "keystore": {"password": {"placeholder": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON> lòng nh<PERSON>p <PERSON><PERSON>"}, "description": "<PERSON><PERSON><PERSON> tệp keystore bạn muốn nhập và nhập mật khẩu tương <PERSON>ng"}, "coboSafe": {"import": "<PERSON><PERSON><PERSON><PERSON>", "whichChainIsYourCoboAddressOn": "<PERSON><PERSON> address của bạn đang ở chuỗi nào?", "inputSafeModuleAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ Safe Module", "invalidAddress": "Địa chỉ không hợp lệ", "addCoboArgusAddress": "Thêm địa chỉ Cobo Argus", "findTheAssociatedSafeAddress": "<PERSON><PERSON><PERSON> địa chỉ an toàn liên kết"}, "importPrivateKey": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> một Đ<PERSON> chỉ", "importSeedPhrase": "<PERSON><PERSON><PERSON><PERSON> cụm từ hạt giống", "importMyMetamaskAccount": "<PERSON><PERSON>ậ<PERSON><PERSON>aM<PERSON>", "firefoxLedgerDisableTips": "Ledger không tư<PERSON><PERSON> thích với Firefox", "importKeystore": "Nhập KeyStore", "connectMobileWalletApps": "<PERSON><PERSON><PERSON> n<PERSON>i <PERSON>ng dụng ví di động", "theSeedPhraseIsInvalidPleaseCheck": "<PERSON><PERSON><PERSON> gốc là không hợp lệ, vui lòng kiểm tra!", "connectInstitutionalWallets": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "connectHardwareWallets": "<PERSON><PERSON><PERSON> n<PERSON>i ví phần cứng", "selectImportMethod": "<PERSON><PERSON><PERSON>", "createNewSeedPhrase": "<PERSON><PERSON><PERSON> cụm từ hạt giống mới", "importedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>u thành công", "importYourKeystore": "<PERSON><PERSON><PERSON><PERSON> KeyStore của bạn", "incorrectPassword": "mật kh<PERSON>u không ch<PERSON>h xác", "addFromCurrentSeedPhrase": "<PERSON><PERSON><PERSON><PERSON> từ <PERSON><PERSON><PERSON>"}, "unlock": {"btn": {"unlock": "Mở khóa"}, "password": {"error": "mật kh<PERSON>u không đúng", "required": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để mở khóa", "placeholder": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để mở khóa"}, "title": "<PERSON><PERSON> dụ ví <PERSON>", "description": "Ví dụ ví cách mạng cho Ethereum và tất cả các chuỗi EVM", "btnForgotPassword": "<PERSON>uên mật khẩu?"}, "addToken": {"noTokenFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token", "tokenNotFound": "Token không đư<PERSON><PERSON> tìm thấy từ địa chỉ hợp đồng này", "title": "Thêm token tùy chỉnh vào <PERSON>bby", "balance": "Số dư", "hasAdded": "Bạn đã đư<PERSON><PERSON> thêm token này.", "tokenCustomized": "Token hiện tại đã được thêm vào danh sách tùy chỉnh.", "noTokenFoundOnThisChain": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token nào trên chuỗi này", "tokenSupported": "Token đã được hỗ trợ trên <PERSON>", "tokenOnMultiChains": "Địa chỉ Token trên nhiều chuỗi. <PERSON><PERSON> lòng chọn một cái."}, "switchChain": {"requestsReceived": "1 yêu cầu đã nhận", "addChain": "<PERSON><PERSON><PERSON><PERSON>", "unknownChain": "Chuỗi không xác định", "title": "<PERSON><PERSON><PERSON><PERSON> Chỉnh vào <PERSON>", "requestsReceivedPlural": "{{count}} yêu cầu đã n<PERSON>n", "chainNotSupport": "Chuỗi được yêu cầu hiện chưa được Rabby hỗ trợ.", "chainId": "Chain ID:", "testnetTip": "<PERSON><PERSON> lòng bật \"Enable Testnets\" dưới \"More\" trướ<PERSON> khi kết nối với testnets.", "requestRabbyToSupport": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> hỗ trợ", "chainNotSupportYet": "Chuỗi yêu c<PERSON>u ch<PERSON><PERSON> đ<PERSON><PERSON> Rabby hỗ trợ.", "chainNotSupportAddChain": "Chuỗi yêu cầu ch<PERSON><PERSON> đư<PERSON>c <PERSON><PERSON> tích hợp. <PERSON><PERSON><PERSON> có thể thêm nó dưới dạng Testnet tùy chỉnh.", "desc": "<PERSON><PERSON>ng yêu cầu vẫn chưa được tích hợp bởi Rabby. Bạn có thể thêm nó như một mạng tùy chỉnh bằng tay."}, "signText": {"createKey": {"description": "<PERSON><PERSON>", "interactDapp": "<PERSON><PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON> v<PERSON><PERSON> bản", "message": "<PERSON> v<PERSON>n bản", "sameSafeMessageAlert": "Thông điệp giống nhau đã đư<PERSON><PERSON> xác nhận; không cần chữ ký bổ sung."}, "securityEngine": {"ignoreAlert": "Bỏ qua thông báo", "viewRiskLevel": "<PERSON><PERSON> m<PERSON> độ rủi ro", "whenTheValueIs": "khi giá trị là {{value}}", "enableRule": "<PERSON><PERSON><PERSON> quy tắc", "no": "K<PERSON>ô<PERSON>", "alertTriggerReason": "Lý do kích hoạt cảnh báo:", "understandRisk": "<PERSON><PERSON><PERSON> hiểu và chấp nhận trách nhiệm cho bất kỳ tổn thất nào.", "viewRules": "<PERSON><PERSON> quy tắc b<PERSON><PERSON> mật", "undo": "<PERSON><PERSON><PERSON>", "ruleDetailTitle": "<PERSON> tiết rủi ro", "currentValueIs": "<PERSON><PERSON><PERSON> trị hiện tại là {{value}}", "unknownResult": "<PERSON>ết quả không xác định vì động cơ bảo mật hiện không khả dụng.", "yes": "<PERSON><PERSON>", "ruleDisabled": "<PERSON><PERSON><PERSON> quy tắc bảo mật đã bị vô hiệu hóa. <PERSON><PERSON> đảm bảo an toàn cho bạn, bạn có thể bật lại bất kỳ lúc nào.", "forbiddenCantIgnore": "<PERSON><PERSON><PERSON> hiện rủi ro cấm mà không thể bỏ qua.", "riskProcessed": "<PERSON><PERSON><PERSON> báo rủi ro đã bị bỏ qua"}, "connect": {"SignTestnetPermission": {"title": "<PERSON><PERSON> tên quyền hạn"}, "SelectWallet": {"title": "<PERSON><PERSON><PERSON> một Ví để Kết nối", "desc": "<PERSON><PERSON><PERSON> từ các ví bạn đã cài đặt"}, "markAsBlockToast": "<PERSON><PERSON><PERSON> dấu là \"Blocked\"", "sitePopularity": "Sự phổ biến của trang web", "myMark": "<PERSON><PERSON><PERSON> c<PERSON>a tôi", "flagByRabby": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h dấu bởi Rabby", "verifiedByRabby": "<PERSON><PERSON> xác thực bởi Rabby", "markAsTrustToast": "<PERSON><PERSON><PERSON> dấu là \"Trusted\"", "listedBy": "<PERSON><PERSON><PERSON><PERSON> li<PERSON> kê bởi", "flagByMM": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h dấu bởi MetaMask", "flagByScamSniffer": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h dấu bởi ScamSniffer", "title": "<PERSON><PERSON><PERSON> n<PERSON> v<PERSON><PERSON>", "popularLevelHigh": "<PERSON>", "popularLevelVeryLow": "<PERSON><PERSON><PERSON>", "blocked": "Đã chặn", "otherWalletBtn": "<PERSON><PERSON><PERSON> n<PERSON> với <PERSON>c", "noWebsite": "Không có gì", "notOnAnyList": "<PERSON><PERSON><PERSON>ng có trong danh sách nào", "onYourBlacklist": "Trong danh sách đen của bạn", "popularLevelMedium": "<PERSON>rung bình", "onYourWhitelist": "Trong danh sách trắng của bạn", "markRemovedToast": "<PERSON><PERSON><PERSON> dấu đã bị xóa", "foundForbiddenRisk": "<PERSON><PERSON> tìm thấy rủi ro bị cấm. <PERSON><PERSON><PERSON> n<PERSON>i bị chặn.", "connectBtn": "<PERSON><PERSON><PERSON>", "selectChainToConnect": "<PERSON><PERSON><PERSON> một chuỗi để kết nối cho", "noMark": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h dấu", "manageWhiteBlackList": "<PERSON><PERSON><PERSON><PERSON> lý danh sách trắng/danh sách đen", "addedToBlacklist": "<PERSON><PERSON> thêm vào danh sách đen của bạn", "addedToWhitelist": "<PERSON><PERSON> thêm vào danh sách trắng của bạn", "ignoreAll": "Bỏ qua tất cả", "popularLevelLow": "<PERSON><PERSON><PERSON><PERSON>", "trusted": "<PERSON><PERSON><PERSON> tin cậy", "markRuleText": "<PERSON><PERSON><PERSON> c<PERSON>a tôi", "removedFromAll": "<PERSON>ã loại bỏ khỏi tất cả danh sách", "connectAddress": "<PERSON><PERSON><PERSON> n<PERSON>i địa chỉ\n"}, "addressDetail": {"backup-private-key": "Sao lưu Private Key", "remove-from-whitelist": "Xóa khỏi Danh sách trắng", "backup-seed-phrase": "<PERSON><PERSON> G<PERSON>", "add-to-whitelist": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> s<PERSON>ch <PERSON>rắ<PERSON>", "address-detail": "<PERSON> tiết địa chỉ", "source": "<PERSON><PERSON><PERSON><PERSON>", "admins": "<PERSON><PERSON><PERSON><PERSON> trị viên", "please-input-address-note": "<PERSON><PERSON> lòng nhập ghi chú địa chỉ", "importedDelegatedAddress": "Địa chỉ <PERSON>y quyền đã nhập", "safeModuleAddress": "Đ<PERSON>a chỉ <PERSON><PERSON><PERSON>", "assets": "<PERSON><PERSON><PERSON>", "address-note": "<PERSON><PERSON> chú địa chỉ", "manage-addresses-under": "<PERSON><PERSON><PERSON><PERSON> lý các địa chỉ dưới {{brand}}", "manage-seed-phrase": "<PERSON><PERSON><PERSON><PERSON>", "qr-code": "Mã QR", "manage-addresses-under-this-seed-phrase": "<PERSON><PERSON><PERSON>n lý địa chỉ dưới cụm từ khởi động này", "tx-requires": "<PERSON><PERSON><PERSON> kỳ giao dịch nào đều yêu cầu <2>{{num}}</2> x<PERSON>c n<PERSON>n.", "delete-address": "<PERSON><PERSON><PERSON> địa chỉ", "edit-memo-title": "Chỉnh sửa ghi chú địa chỉ", "hd-path": "HD Đường dẫn", "coboSafeErrorModule": "Địa chỉ đã hết hạn, vui lòng xóa và nhập lại địa chỉ.", "delete-desc": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi bạn xóa, h<PERSON><PERSON> lưu ý những điểm sau để hiểu cách bảo vệ tài sản của bạn.", "address": "Địa chỉ", "direct-delete-desc": "Đ<PERSON><PERSON> chỉ này là địa chỉ {{renderBrand}}, <PERSON><PERSON> không lưu trữ khóa riêng hoặc cụm từ hạt giống cho địa chỉ này, bạn có thể xóa nó."}, "preferMetamaskDapps": {"howToAdd": "<PERSON><PERSON><PERSON> thê<PERSON>", "empty": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> da<PERSON>", "title": "MetaMask Ưu tiên <PERSON>", "desc": "<PERSON><PERSON><PERSON> da<PERSON> sau đây sẽ vẫn được kết nối qua MetaMask, bất kể ví nào bạn đã chuyển sang.", "howToAddDesc": "<PERSON><PERSON><PERSON><PERSON> chuột phải trên trang web và tìm tùy chọn này."}, "customRpc": {"EditRPCModal": {"invalidRPCUrl": "URL RPC không hợp lệ", "title": "Chỉnh sửa URL RPC", "rpcUrl": "RPC URL", "rpcUrlPlaceholder": "Nhập URL RPC", "rpcAuthFailed": "<PERSON><PERSON><PERSON> thực R<PERSON> thất bại", "invalidChainId": "ID chuỗi không hợp lệ"}, "EditCustomTestnetModal": {"quickAdd": "<PERSON><PERSON><PERSON><PERSON> từ Chainlist", "title": "<PERSON><PERSON><PERSON><PERSON> Chỉnh"}, "closed": "<PERSON><PERSON> đóng", "opened": "Mở", "add": "Sửa đổi URL RPC", "empty": "Không có URL RPC tùy chỉnh", "title": "Sửa đổi URL RPC", "desc": "<PERSON><PERSON> đã sử<PERSON> đổi, R<PERSON> tùy chỉnh sẽ thay thế nút của <PERSON>. <PERSON><PERSON> tiếp tục sử dụng nút củ<PERSON>, hãy xóa RPC tùy chỉnh."}, "requestDebankTestnetGasToken": {"mintedTip": "Người sở hữu <PERSON> có thể yêu cầu một lần mỗi ngày.", "requested": "Bạn đã yêu cầu hôm nay", "time": "Mỗi ngày", "title": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON>ken Gas DeBank Testnet", "notMintedTip": "Chỉ dành cho người nắm giữ Rabby <PERSON> yêu cầu.", "claimBadgeBtn": "<PERSON><PERSON><PERSON><PERSON>", "requestBtn": "<PERSON><PERSON><PERSON> c<PERSON>"}, "safeQueue": {"action": {"send": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON> giao dịch đang chờ thanh toán"}, "ReplacePopup": {"options": {"reject": "Từ chối <PERSON>", "send": "<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON> cách để thay thế giao dịch này", "desc": "Một giao dịch đã được ký không thể bị xóa nhưng có thể được thay thế bằng một giao dịch mới với nonce giống nhau."}, "unknownProtocol": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> không x<PERSON>c đ<PERSON>", "unlimited": "kh<PERSON>ng gi<PERSON>i hạn", "noData": "<PERSON><PERSON><PERSON><PERSON> có giao dịch đang chờ xử lý", "submitBtn": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "approvalExplain": "<PERSON><PERSON><PERSON> thuận {{count}} {{token}} cho {{protocol}}", "unknownTx": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> kh<PERSON>ng x<PERSON>c <PERSON>", "cancelExplain": "<PERSON><PERSON>y {{token}} Ch<PERSON><PERSON> thuận cho {{protocol}}", "title": "<PERSON><PERSON><PERSON> đợi ({{total}})", "loading": "<PERSON><PERSON> tải giao dịch đang chờ xử lý", "LowerNonceError": "<PERSON><PERSON><PERSON> d<PERSON>ch với nonce {{nonce}} c<PERSON>n <PERSON><PERSON><PERSON><PERSON> thực thi trước.", "loadingFaild": "<PERSON> tính không ổn định của máy chủ Safe, dữ liệu không có sẵn, vui lòng kiểm tra lại sau 5 phút.", "replaceBtn": "<PERSON>hay thế", "viewBtn": "Xem", "accountSelectTitle": "<PERSON><PERSON>n có thể gửi giao dịch này bằng bất kỳ địa chỉ nào.", "sameNonceWarning": "<PERSON><PERSON><PERSON> giao dịch này xung đột vì chúng sử dụng cùng một nonce. <PERSON><PERSON><PERSON><PERSON> thực thi một giao dịch sẽ tự động thay thế giao dịch kh<PERSON>c."}, "importSuccess": {"addressCount": "{{count}} đ<PERSON>a chỉ", "gnosisChainDesc": "Đ<PERSON>a chỉ này đã được phát hiện triển khai trên {{count}} chuỗi.", "title": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>u thành công"}, "backupSeedPhrase": {"showQrCode": "Hi<PERSON>n thị QR Code", "qrCodePopupTitle": "Mã QR", "clickToShow": "Nhấn để hiển thị cụm từ khôi phục", "title": "<PERSON><PERSON> <PERSON><PERSON><PERSON> cụm từ hạt giống", "qrCodePopupTips": "<PERSON><PERSON><PERSON><PERSON> bao giờ chia sẻ mã QR cụm từ hạt giống cho người khác. <PERSON>ui lòng xem nó trong một môi trường an toàn và giữ gìn cẩn thận.", "copySeedPhrase": "<PERSON><PERSON> ch<PERSON><PERSON> cụm từ hạt giống", "alert": "<PERSON><PERSON><PERSON> Seed Phrase này là thông tin xác thực cho tài sản của bạn. ĐỪNG làm mất hoặc tiết lộ cho người khác, nếu không bạn có thể mất tài sản của mình mãi mãi. Vui lòng xem nó trong một môi trường an toàn và giữ gìn cẩn thận."}, "backupPrivateKey": {"title": "Sao lưu Private Key", "clickToShow": "<PERSON><PERSON><PERSON>n để hiển thị private key", "clickToShowQr": "<PERSON><PERSON><PERSON><PERSON> để hiển thị mã QR khóa riêng tư", "alert": "Khóa riêng này là thông tin xác thực cho tài sản của bạn. ĐỪNG làm mất nó hoặc tiết lộ cho người khác, nếu không bạn có thể mất tài sản của mình mãi mãi. Xin vui lòng xem nó trong một môi trường an toàn và giữ gìn cẩn thận."}, "ethSign": {"alert": "<PERSON><PERSON> tên bằng 'eth_sign' có thể dẫn đến mất tài sản. <PERSON><PERSON> đảm bảo an toàn cho bạn, <PERSON><PERSON> không hỗ trợ phương pháp này."}, "createPassword": {"passwordRequired": "<PERSON><PERSON> lòng nh<PERSON>p <PERSON><PERSON>", "confirmError": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "confirmRequired": "<PERSON><PERSON> lòng xác nhận mật kh<PERSON>u", "passwordMin": "<PERSON><PERSON>t khẩu phải dài ít nhất 8 ký tự", "title": "Đặt <PERSON><PERSON><PERSON>", "confirmPlaceholder": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "passwordPlaceholder": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự.", "agree": "Tôi đã đọc và đồng ý với <1/> <2><PERSON><PERSON><PERSON></2> và <4><PERSON><PERSON><PERSON></4>"}, "welcome": {"step1": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> tất c<PERSON>", "desc": "<PERSON><PERSON> kết nối với tất cả các <PERSON> mà MetaMask hỗ trợ"}, "step2": {"title": "Tự giữ", "btnText": "<PERSON><PERSON><PERSON> đ<PERSON>u", "desc": "Private keys đ<PERSON><PERSON><PERSON> lưu trữ cục bộ với quyền truy cập duy nhất của bạn."}}, "importSafe": {"error": {"invalid": "<PERSON><PERSON><PERSON>ng phải là địa chỉ hợp lệ", "required": "<PERSON><PERSON> lòng nhập địa chỉ"}, "title": "<PERSON><PERSON><PERSON><PERSON> địa chỉ Safe", "placeholder": "<PERSON><PERSON> lòng nhập địa chỉ", "gnosisChainDesc": "Địa chỉ này được tìm thấy được triển khai trên {{count}} chuỗi", "loading": "<PERSON><PERSON> tìm kiếm chuỗi đã triển khai của địa chỉ này"}, "importQrBase": {"btnText": "<PERSON><PERSON><PERSON> lại", "desc": "Q<PERSON>t mã QR trên ví phần cứng {{brandName}}"}, "pendingDetail": {"Header": {"predictTime": "Dự đoán sẽ được đóng gói trong"}, "TxStatus": {"pendingBroadcast": "Đang chờ: Sẽ đư<PERSON><PERSON> phát sóng", "completed": "<PERSON><PERSON><PERSON> t<PERSON>t", "pendingBroadcasted": "Đang chờ: <PERSON><PERSON> ph<PERSON><PERSON> s<PERSON>g", "reBroadcastBtn": "<PERSON><PERSON><PERSON>"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcastedCount_ordinal_one": "{{count}}st phát sóng", "broadcastedCount_ordinal_two": "{{count}}nd ph<PERSON>t sóng", "pending": "<PERSON><PERSON> kiểm tra trạng thái...", "broadcastedCount_ordinal_few": "{{count}}rd broadcast", "created": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> t<PERSON>o", "broadcastedCount_ordinal_other": "{{count}} ph<PERSON>t sóng thứ {{count}}", "broadcasted": "Gần đ<PERSON>y đã đư<PERSON><PERSON> phát sóng"}, "MempoolList": {"col": {"txStatus": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "nodeName": "<PERSON><PERSON><PERSON>", "nodeOperator": "<PERSON><PERSON><PERSON> điều hành nút"}, "txStatus": {"appeared": "<PERSON><PERSON><PERSON>", "appearedOnce": "<PERSON><PERSON><PERSON> hi<PERSON>n một lần", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy"}, "title": "<PERSON><PERSON>t hiện trong {{count}} nút RPC"}, "PendingTxList": {"filterBaseFee": {"label": "Chỉ đáp <PERSON>ng yêu cầu mức phí c<PERSON> bản", "tooltip": "Chỉ hiển thị các giao dịch có Gas Price đáp ứng yêu cầu về Base fee của khối."}, "col": {"interact": "Tương tác với", "gasPrice": "Giá Gas", "action": "<PERSON><PERSON><PERSON> đ<PERSON>", "balanceChange": "<PERSON><PERSON> đ<PERSON>i số dư", "actionType": "<PERSON><PERSON><PERSON> hành động"}, "titleSame": "GasPrice xếp hạng #{{rank}} gi<PERSON><PERSON> nh<PERSON> hiện tại", "titleNotFound": "<PERSON><PERSON><PERSON><PERSON> có xếp hạng trong tất cả giao dịch đang chờ", "titleSameNotFound": "<PERSON><PERSON><PERSON><PERSON>ng <PERSON>", "title": "GasPrice xếp hạng #{{rank}} trong tất cả các giao dịch đang chờ xử lý."}, "Empty": {"noData": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu"}, "PrePackInfo": {"col": {"expectations": "Kỳ vọng", "prePackContent": "<PERSON><PERSON><PERSON> dung trư<PERSON><PERSON> đóng gói", "difference": "<PERSON><PERSON><PERSON> tra kết quả", "prePackResults": "<PERSON><PERSON><PERSON> quả trư<PERSON><PERSON> khi đóng gói"}, "type": {"receive": "Nhậ<PERSON>", "pay": "<PERSON><PERSON> toán"}, "noLoss": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mất mát", "title": "<PERSON><PERSON><PERSON> tra trư<PERSON><PERSON> khi đóng gói", "desc": "<PERSON><PERSON> phỏng được thực hiện trong khối mới nhất, đã cập nhật {{time}}", "error": "{{count}} lỗi được tìm thấy", "noError": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lỗi", "loss": "{{lossCount}} lỗi được tìm thấy"}, "Predict": {"completed": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đã hoàn tất", "predictFailed": "<PERSON>ự đoán thời gian đóng gói đã thất bại", "skipNonce": "Địa chỉ của bạn đã bỏ qua Nonce trên chuỗi Ethereum, gây ra việc giao dịch hiện tại không thể hoàn thành."}}, "dappSearch": {"searchResult": {"totalDapps": "T<PERSON><PERSON> cộng <2>{{count}}</2> <PERSON><PERSON>", "foundDapps": "<PERSON><PERSON><PERSON> thấy <2>{{count}}</2> <PERSON><PERSON>"}, "selectChain": "<PERSON><PERSON><PERSON> Chuỗi", "emptyFavorite": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> thích", "emptySearch": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON>", "listBy": "Dapp đã đư<PERSON><PERSON> liệt kê bởi", "favorite": "<PERSON><PERSON><PERSON>ch", "expand": "Mở rộng"}, "rabbyPoints": {"claimItem": {"go": "<PERSON><PERSON>", "claim": "<PERSON><PERSON><PERSON> c<PERSON>", "disabledTip": "<PERSON><PERSON><PERSON> tại không có điểm nào để yêu cầu.", "claimed": "<PERSON><PERSON> yêu cầu", "earnTip": "<PERSON><PERSON><PERSON><PERSON> hạn một lần trong ngày. <PERSON><PERSON> lòng kiếm điểm sau 00:00 UTC+0"}, "claimModal": {"MetaMaskSwap": "MetaMask Swap", "walletBalance": "Số dư ví", "title": "<PERSON><PERSON><PERSON><PERSON> Điểm Khởi Đầu", "snapshotTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>: {{time}}", "rabbyUser": "<PERSON><PERSON>", "addressBalance": "Số dư ví", "placeholder": "<PERSON><PERSON><PERSON><PERSON> mã giới thiệu để nhận thêm điểm (t<PERSON><PERSON> ch<PERSON>n)", "referral-code": "Mã Giới Thiệu", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "rabbyValuedUserBadge": "<PERSON><PERSON> <PERSON><PERSON>", "claim": "<PERSON><PERSON><PERSON> c<PERSON>", "activeStats": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "season2": "Mùa 2", "cantUseOwnCode": "Bạn không thể sử dụng mã giới thiệu của ch<PERSON>h mình.", "invalid-code": "mã kh<PERSON>ng hợp lệ"}, "referralCode": {"verifyAddressModal": {"cancel": "Hủy bỏ", "sign": "<PERSON><PERSON> t<PERSON>n", "verify-address": "<PERSON><PERSON><PERSON>h địa chỉ", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "<PERSON><PERSON> lòng ký thông điệp văn bản này để xác nhận rằng bạn là chủ sở hữu của địa chỉ này."}, "my-referral-code": "Mã giới thiệu của tôi", "referral-code-available": "Mã giới thiệu có sẵn", "referral-code-already-exists": "Mã giới thiệu đã tồn tại", "referral-code-cannot-be-empty": "<PERSON>ã giới thiệu không được để trống", "refer-a-new-user-to-get-50-points": "Gi<PERSON>i thiệu một người dùng mới để nhận 50 điểm", "referral-code-cannot-exceed-15-characters": "<PERSON>ã giới thiệu không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 15 ký tự.", "confirm": "<PERSON><PERSON><PERSON>", "once-set-this-referral-code-is-permanent-and-cannot-change": "<PERSON>hi đã thiết lập, mã giới thiệu này sẽ là vĩnh viễn và không thể thay đổi.", "set-my-code": "Đặt mã của tôi", "set-my-referral-code": "Đặt mã giới thiệu của tôi", "max-15-characters-use-numbers-and-letters-only": "Tối đa 15 ký tự, chỉ sử dụng số và chữ cái."}, "top-100": "Top 100", "referral-code-copied": "Mã giới thiệu đã đư<PERSON><PERSON> sao chép", "title": "<PERSON><PERSON>", "earn-points": "<PERSON><PERSON><PERSON>", "out-of-x-current-total-points": "<PERSON><PERSON><PERSON><PERSON> {{total}} <PERSON><PERSON><PERSON><PERSON> Phân Phối", "share-on": "<PERSON><PERSON> sẻ trên", "initialPointsClaimEnded": "<PERSON><PERSON><PERSON><PERSON> đầu tiên đã kết thúc.", "secondRoundEnded": "🎉 Vòng thứ hai của Rabby Points đã kết thúc", "firstRoundEnded": "🎉 Vòng đầu tiên của Rabby Points đã kết thúc", "code-set-successfully": "<PERSON>ã giới thiệu đã đ<PERSON><PERSON><PERSON> thiết lập thành công"}, "customTestnet": {"CustomTestnetForm": {"name": "<PERSON><PERSON><PERSON> m<PERSON>", "id": "Mã chuỗi", "nativeTokenSymbol": "<PERSON><PERSON><PERSON><PERSON> tượng tiền tệ", "blockExplorerUrl": "URL trình <PERSON> (<PERSON><PERSON><PERSON>)", "idRequired": "<PERSON><PERSON> lòng nhập id chuỗi", "nativeTokenSymbolRequired": "<PERSON><PERSON> lòng nhập ký hiệu tiền tệ", "rpcUrlRequired": "<PERSON><PERSON> lòng nhập URL RPC", "nameRequired": "<PERSON><PERSON> lòng nhập tên mạng", "rpcUrl": "RPC URL"}, "AddFromChainList": {"tips": {"added": "Bạn đã thêm chuỗi này rồi.", "supported": "Chuỗi đã đ<PERSON><PERSON><PERSON> tích hợp bởi <PERSON><PERSON> Wallet"}, "empty": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chuỗi nào", "title": "<PERSON><PERSON><PERSON><PERSON> từ Chainlist", "search": "T<PERSON><PERSON> kiếm tên hoặc ID mạng tùy chỉnh"}, "signTx": {"title": "<PERSON><PERSON> li<PERSON>u <PERSON>"}, "ConfirmModifyRpcModal": {"desc": "Chuỗi này đã đư<PERSON><PERSON> tích hợp bởi Rabby. Bạn có cần thay đổi URL RPC của nó không?"}, "id": "ID", "empty": "<PERSON><PERSON><PERSON>ng có mạng tùy chỉnh", "add": "<PERSON><PERSON><PERSON><PERSON> Chỉnh", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "title": "<PERSON><PERSON>ng tùy chỉnh", "desc": "<PERSON><PERSON> không thể xác minh tính bảo mật của các mạng tùy chỉnh. <PERSON><PERSON> lòng chỉ thêm các mạng đáng tin cậy."}, "addChain": {"title": "<PERSON><PERSON><PERSON><PERSON> Chỉnh vào <PERSON>", "desc": "<PERSON><PERSON> không thể xác minh tính bảo mật của các mạng tùy chỉnh. <PERSON><PERSON> lòng chỉ thêm các mạng đáng tin cậy."}, "sign": {"transactionSpeed": "<PERSON><PERSON><PERSON> <PERSON><PERSON>"}, "ecology": {"sonic": {"home": {"migrateTitle": "<PERSON>", "earnTitle": "<PERSON><PERSON><PERSON> t<PERSON>", "earnBtn": "S<PERSON><PERSON> ra mắt", "airdropBtn": "<PERSON><PERSON><PERSON> điể<PERSON>", "airdrop": "Airdrop", "migrateDesc": "请提供您希望翻译的英文文案。", "socialsTitle": "Tham gia", "airdropDesc": "~200 triệu S cho người dùng trên Opera và Sonic.", "migrateBtn": "S<PERSON><PERSON> ra mắt", "earnDesc": "Đặt cược $S của bạn", "arcadeDesc": "<PERSON><PERSON><PERSON> các trò chơi miễn phí để kiếm điểm cho airdrop S.", "arcadeBtn": "<PERSON><PERSON><PERSON> ngay"}, "points": {"sonicPoints": "Sonic Points", "getReferralCode": "Nhận mã giới thiệu", "retry": "<PERSON><PERSON><PERSON> lại", "sonicArcadeBtn": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>", "pointsDashboard": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON> nay", "pointsDashboardBtn": "<PERSON><PERSON><PERSON> đầu kiếm điểm", "referralCodeCopied": "Mã giới thiệu đã đư<PERSON><PERSON> sao chép", "shareOn": "<PERSON><PERSON> sẻ trên", "errorTitle": "<PERSON><PERSON><PERSON><PERSON> thể tải điểm", "sonicArcade": "Sonic Arcade", "referralCode": "<PERSON>ã giới thiệu", "errorDesc": "<PERSON><PERSON> có lỗi khi tải điểm của bạn. <PERSON><PERSON> lòng thử lại."}}, "dbk": {"home": {"bridge": "<PERSON><PERSON><PERSON> đ<PERSON>n DBK Chain", "mintNFT": "Mint DBK Genesis NFT", "mintNFTBtn": "<PERSON><PERSON><PERSON>", "bridgeBtn": "<PERSON><PERSON><PERSON>", "mintNFTDesc": "<PERSON><PERSON><PERSON> là nhân chứng của DBK Chain", "bridgePoweredBy": "Được powered by OP Superchain"}, "bridge": {"tabs": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON><PERSON>"}, "info": {"gasFee": "Phí gas", "receiveOn": "<PERSON><PERSON><PERSON><PERSON> trên {{chainName}}", "completeTime": "<PERSON><PERSON><PERSON><PERSON> gian hoàn thành", "toAddress": "<PERSON><PERSON> g<PERSON><PERSON> quy<PERSON>"}, "error": {"notEnoughBalance": "Số dư không đủ"}, "ActivityPopup": {"status": {"withdraw": "<PERSON><PERSON><PERSON>", "proved": "<PERSON><PERSON> chứng minh", "deposit": "<PERSON><PERSON><PERSON> t<PERSON>", "rootPublished": "Công bố root trạng thái", "waitingToProve": "<PERSON><PERSON><PERSON> trạng thái gốc đã đ<PERSON><PERSON><PERSON> công bố", "claimed": "<PERSON><PERSON> yêu cầu", "readyToProve": "Sẵn sàng để chứng minh", "challengePeriod": "<PERSON><PERSON><PERSON><PERSON> gian thách thức", "readyToClaim": "Sẵn sàng để yêu cầu"}, "deposit": "<PERSON><PERSON><PERSON> t<PERSON>n", "empty": "Chưa có hoạt động nào", "withdraw": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> đ<PERSON>", "claimBtn": "<PERSON><PERSON><PERSON> c<PERSON>", "proveBtn": "<PERSON><PERSON><PERSON> minh"}, "WithdrawConfirmPopup": {"step2": "Chứng minh trên Ethereum", "btn": "<PERSON><PERSON><PERSON>", "step1": "Khởi tạo rút tiền", "question3": "T<PERSON>i hiểu rằng phí mạng là ước tính và sẽ thay đổi.", "title": "<PERSON><PERSON><PERSON> ti<PERSON>n DBK Chain mất kho<PERSON>ng ~7 ngày", "question2": "T<PERSON>i hiểu rằng một khi yêu cầu rút tiền được khởi động, nó không thể bị tăng tốc hoặc hủy bỏ.", "tips": "<PERSON><PERSON><PERSON> tiền bao gồm một quy trình 3 b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> c<PERSON>u 1 giao dịch DBK Chain và 2 giao dịch Ethereum.", "question1": "Tôi hiểu rằng sẽ mất ~7 ngày cho đến khi các khoản tiền của tôi có thể được yêu cầu trên Ethereum sau khi tôi xác minh việc rút tiền của mình.", "step3": "<PERSON><PERSON><PERSON> c<PERSON>u trê<PERSON>"}, "labelTo": "<PERSON><PERSON><PERSON>", "labelFrom": "Từ"}, "minNFT": {"myBalance": "Số dư của tôi", "mintBtn": "Mint", "title": "DBK Genesis", "minted": "Minted"}}}, "miniSignFooterBar": {"status": {"txSending": "<PERSON><PERSON><PERSON> yêu cầu ký", "txSigned": "<PERSON><PERSON> ký. <PERSON><PERSON> t<PERSON>o giao d<PERSON>ch", "txSendings": "<PERSON><PERSON><PERSON> yêu cầu ký ({ {current}}/{{total}})", "txCreated": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> t<PERSON>o"}, "signWithLedger": "<PERSON><PERSON> v<PERSON>i <PERSON>"}, "gasAccount": {"history": {"noHistory": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> lịch sử"}, "loginInTip": {"login": "<PERSON><PERSON><PERSON> GasAccount", "gotIt": "<PERSON><PERSON> hiểu", "title": "Nạp USDC / USDT", "desc": "<PERSON>h toán phí Gas trên tất cả các chuỗi"}, "loginConfirmModal": {"title": "<PERSON><PERSON><PERSON> một địa chỉ để đăng nhập", "desc": "<PERSON>hi đã x<PERSON>c <PERSON>, bạn chỉ có thể gửi vào địa chỉ này"}, "gasAccountList": {"address": "Địa chỉ", "gasAccountBalance": "Số dư Gas"}, "logoutConfirmModal": {"logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "title": "<PERSON><PERSON><PERSON> xu<PERSON>t GasAccount hiện tại", "desc": "<PERSON><PERSON>ng xuất sẽ vô hiệu hóa GasAccount. Bạn có thể khôi phục GasAccount của mình bằng cách đăng nhập với địa chỉ này."}, "depositPopup": {"amount": "<PERSON><PERSON> tiền", "title": "<PERSON><PERSON><PERSON> t<PERSON>n", "invalidAmount": "Phải dưới 500", "token": "<PERSON><PERSON> thông báo", "selectToken": "<PERSON><PERSON><PERSON> để gửi", "desc": "<PERSON>ạp tiền vào Tài khoản DeBank L2 của Rabby mà không tính phí bổ sung—rút bất cứ lúc nào."}, "withdrawPopup": {"noEnoughGas": "<PERSON>ố tiền quá thấp để trang trải phí gas", "noEnoughValuetBalance": "S<PERSON> dư <PERSON> không đủ. Chuyển đổi chuỗi hoặc thử lại sau.", "destinationChain": "Chuỗi đích", "noEligibleChain": "<PERSON><PERSON><PERSON><PERSON> có chuỗi đủ điều kiện để rút tiền", "selectDestinationChain": "<PERSON><PERSON><PERSON> chuỗi đích", "to": "Để", "title": "<PERSON><PERSON><PERSON>", "recipientAddress": "Địa chỉ người nhận", "amount": "Số lượng", "noEligibleAddr": "<PERSON><PERSON><PERSON><PERSON> có địa chỉ đủ điều kiện để rút tiền", "selectChain": "<PERSON><PERSON><PERSON> Chuỗi", "withdrawalLimit": "<PERSON><PERSON><PERSON><PERSON> hạn rút tiền", "riskMessageFromAddress": "Do rủi ro ki<PERSON><PERSON> so<PERSON>, giới hạn rút tiền phụ thuộc vào tổng số tiền mà địa chỉ này đã gửi.", "deductGasFees": "<PERSON><PERSON> tiền nhận được sẽ bị trừ phí gas.", "riskMessageFromChain": "Do hạn chế rủi ro, giới hạn rút tiền phụ thuộc vào tổng số tiền đã gửi từ chuỗi này.", "selectRecipientAddress": "<PERSON><PERSON><PERSON> địa chỉ người nhận", "desc": "Bạn có thể rút số dư GasAccount của mình vào ví DeBank L2. <PERSON><PERSON><PERSON> nhập vào ví DeBank L2 của bạn để chuyển tiền vào blockchain được hỗ trợ khi cần.", "selectAddr": "<PERSON><PERSON><PERSON> chỉ"}, "withdrawConfirmModal": {"button": "<PERSON><PERSON> tr<PERSON><PERSON>", "title": "Chuyển tới ví DeBank L2 của bạn"}, "GasAccountDepositTipPopup": {"gotIt": "<PERSON><PERSON> hiểu", "title": "Mở GasAccount và <PERSON><PERSON> tiền"}, "switchLoginAddressBeforeDeposit": {"title": "Chuyển địa chỉ trước khi nạp tiền", "desc": "<PERSON><PERSON> lò<PERSON> chuy<PERSON> sang địa chỉ đăng nhập của bạn."}, "noBalance": "<PERSON><PERSON><PERSON><PERSON> có số dư", "switchAccount": "<PERSON><PERSON><PERSON><PERSON>Account", "withdraw": "<PERSON><PERSON><PERSON>", "gasExceed": "Số dư GasAccount kh<PERSON><PERSON> thể vư<PERSON>t quá $1000", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>t GasAccount hiện tại", "deposit": "<PERSON><PERSON><PERSON> t<PERSON>", "safeAddressDepositTips": "Địa chỉ <PERSON>sig không được hỗ trợ cho việc gửi tiền.", "title": "GasAccount", "risk": "Địa chỉ hiện tại của bạn đã đư<PERSON><PERSON> phát hiện là có rủi ro, vì vậy tính năng này không khả dụng.", "withdrawDisabledIAP": "<PERSON><PERSON><PERSON> tiền bị vô hiệu hóa vì số dư của bạn bao gồm tiền fiat, không thể rút ra. <PERSON>ên hệ với hỗ trợ để rút số dư token của bạn."}, "safeMessageQueue": {"noData": "<PERSON><PERSON><PERSON><PERSON> có tin nhắn", "loading": "<PERSON><PERSON> tải tin nh<PERSON>n"}, "newUserImport": {"guide": {"importAddress": "Tôi đã có một địa chỉ", "title": "<PERSON><PERSON><PERSON> mừng bạn đến vớ<PERSON>et", "createNewAddress": "<PERSON><PERSON>o một địa chỉ mới", "desc": "Ví dụ ví cách mạng cho Ethereum và tất cả các chuỗi EVM"}, "createNewAddress": {"showSeedPhrase": "<PERSON><PERSON><PERSON> thị cụm từ hạt giống", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Bắt Đầu", "desc": "<PERSON><PERSON> lòng đọc và ghi nhớ những mẹo bảo mật sau đây.", "tip3": "<PERSON><PERSON><PERSON> tôi gỡ cài đặt Rabby mà không sao lưu cụm từ khôi phục, nó không thể được khôi phục bởi Rabby.", "tip1": "<PERSON><PERSON>u tôi mất hoặc chia sẻ cụm từ khôi phục của mình, tôi sẽ mất quyền truy cập vào tài sản của mình vĩnh viễn.", "tip2": "<PERSON><PERSON><PERSON> nói hạt giống của tôi chỉ được lưu trữ trên thiết bị của tôi. <PERSON><PERSON> không thể truy cập vào nó."}, "importList": {"title": "<PERSON><PERSON><PERSON>"}, "importPrivateKey": {"title": "<PERSON><PERSON><PERSON><PERSON>", "pasteCleared": "Dán và đã xóa clipboard"}, "PasswordCard": {"form": {"password": {"label": "<PERSON><PERSON><PERSON>", "min": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự.", "required": "<PERSON><PERSON> lòng nh<PERSON>p <PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> (tối thiểu 8 ký tự)"}, "confirmPassword": {"notMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "label": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON> lòng xác nhận mật kh<PERSON>u", "placeholder": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u"}}, "title": "Đặt <PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON> sẽ được sử dụng để mở khóa ví và mã hóa dữ liệu.", "agree": "<PERSON><PERSON><PERSON> đồng ý với<1/> <2><PERSON><PERSON><PERSON></2> và <4><PERSON><PERSON><PERSON><PERSON></4>"}, "successful": {"create": "<PERSON><PERSON><PERSON> thành công", "start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "import": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>u thành công", "addMoreFrom": "<PERSON><PERSON><PERSON><PERSON> nhiều địa chỉ từ {{name}}", "addMoreAddr": "Thê<PERSON> nhiều địa chỉ từ cụm từ hạt giống này"}, "readyToUse": {"pin": "<PERSON><PERSON>", "guides": {"step2": "<PERSON><PERSON> ví <PERSON><PERSON>", "step1": "<PERSON><PERSON>ấn vào biểu tượng mở rộng trình du<PERSON>t"}, "title": "<PERSON>í của bạn trên Rabby đã sẵn sàng!", "extensionTip": "Click <1/> và sau đó <3/>", "desc": "<PERSON><PERSON><PERSON> và ghim nó"}, "importSeedPhrase": {"title": "<PERSON><PERSON><PERSON><PERSON> cụm từ khôi phục"}, "importOneKey": {"title": "OneKey", "tip1": "1. <PERSON><PERSON><PERSON> đặt <1>OneKey Bridge<1/>", "tip3": "3. Mở kh<PERSON><PERSON> thiết bị của bạn", "tip2": "2. <PERSON><PERSON><PERSON> thi<PERSON> bị <PERSON><PERSON>ey của bạn vào.", "connect": "<PERSON><PERSON><PERSON> n<PERSON>"}, "importTrezor": {"connect": "<PERSON><PERSON><PERSON><PERSON>", "tip2": "2. Mở kh<PERSON><PERSON> thiết bị của bạn", "title": "<PERSON><PERSON><PERSON>", "tip1": "1. <PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> bị <PERSON> của bạn vào."}, "ImportGridPlus": {"title": "GridPlus", "connect": "<PERSON><PERSON><PERSON><PERSON>", "tip1": "1. Mở thi<PERSON>t bị Grid<PERSON>lus của bạn", "tip2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> qua Lattice Connector"}, "importLedger": {"connect": "<PERSON><PERSON><PERSON><PERSON>", "tip2": "<PERSON>hập mã PIN của bạn để mở khóa.", "tip3": "Mở ứng dụng Ethereum.", "tip1": "<PERSON><PERSON><PERSON> thi<PERSON><PERSON> bị <PERSON>ger của bạn vào.", "title": "Ledger"}, "importBitBox02": {"tip3": "3. Mở kh<PERSON><PERSON> thiết bị của bạn", "tip2": "2. Cắm BitBox02 của bạn vào.", "connect": "<PERSON><PERSON>t nối BitBox02", "title": "BitBox02", "tip1": "1. <PERSON><PERSON><PERSON> đặt <1>BitBoxBridge<1/>"}, "importKeystone": {"qrcode": {"desc": "Q<PERSON>t mã QR trên ví phần cứng {{brandName}}"}, "usb": {"tip2": "<PERSON><PERSON><PERSON><PERSON> mật khẩu của bạn để mở khóa", "desc": "<PERSON><PERSON><PERSON> bảo rằng Keystone 3 Pro của bạn ở trên trang ch<PERSON>h.", "tip3": "<PERSON><PERSON><PERSON> nhận kết nối với máy t<PERSON>h của bạn", "connect": "<PERSON><PERSON><PERSON><PERSON>", "tip1": "<PERSON><PERSON><PERSON> thi<PERSON><PERSON> bị <PERSON> của bạn vào."}}, "importSafe": {"error": {"required": "<PERSON><PERSON> lòng nhập địa chỉ", "invalid": "<PERSON><PERSON><PERSON><PERSON> phải là một địa chỉ hợp lệ"}, "title": "<PERSON><PERSON><PERSON><PERSON> Chỉ An <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ an toàn", "loading": "<PERSON><PERSON> tìm kiếm chuỗi đã triển khai của địa chỉ này"}}, "metamaskModeDapps": {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON> p<PERSON>p", "desc": "Chế độ MetaMask đã đư<PERSON><PERSON> bật cho các <PERSON> sau đây. Bạn có thể kết nối Rabby bằng cách chọn tùy chọn MetaMask."}, "forgotPassword": {"home": {"buttonNoData": "Đặt <PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "button": "<PERSON><PERSON><PERSON> đầu quy trình đặt lại", "description": "<PERSON><PERSON> không lưu trữ mật khẩu của bạn và không thể giúp bạn khôi phục nó. Đặt lại ví của bạn để thiết lập một cái mới.", "descriptionNoData": "<PERSON><PERSON> không lưu trữ mật khẩu của bạn và không thể giúp bạn khôi phục nó. Đặt một mật khẩu mới nếu bạn quên."}, "reset": {"alert": {"seed": "<PERSON><PERSON><PERSON> cụ thể", "title": "<PERSON><PERSON> liệu sẽ bị xóa và không thể khôi phục.", "privateKey": "Khóa riêng"}, "tip": {"records": "<PERSON><PERSON><PERSON> ghi chữ ký", "whitelist": "<PERSON><PERSON>i đặt Danh sách trắng", "hardware": "Ví dụ: <PERSON><PERSON> cứng đã nhập", "safe": "<PERSON>í dụ ví an toàn đã nhập", "title": "<PERSON><PERSON> liệu sẽ được giữ:", "watch": "<PERSON>h bạ và Địa chỉ Chỉ Xem"}, "title": "Đặt lại ví Rabby", "confirm": "Gõ <1>RESET</1> vào ô để xác nhận và tiếp tục", "button": "<PERSON><PERSON><PERSON> nhận Đặt lại"}, "tip": {"buttonNoData": "<PERSON><PERSON><PERSON><PERSON> Chỉ", "title": "<PERSON><PERSON> Wallet Đặt <PERSON><PERSON><PERSON>t", "description": "<PERSON><PERSON><PERSON> một mật khẩu mới để tiếp tục", "button": "Đặt mật khẩu", "descriptionNoData": "<PERSON>hê<PERSON> địa chỉ của bạn để bắt đầu"}, "success": {"button": "<PERSON><PERSON><PERSON> t<PERSON>t", "description": "Bạn đã sẵn sàng để sử dụng <PERSON>bby <PERSON>.", "title": "<PERSON><PERSON><PERSON> khẩu đã được đặt thành công"}}, "eip7702": {"alert": "EIP-7702 chưa được hỗ trợ."}, "metamaskModeDappsGuide": {"toast": {"disabled": "<PERSON><PERSON> hiệu hóa chế độ ẩn danh. <PERSON><PERSON><PERSON> mớ<PERSON>.", "enabled": "<PERSON><PERSON> bật chế độ <PERSON>n danh. <PERSON><PERSON><PERSON> mới Dapp để kết nối lại."}, "step2Desc": "<PERSON><PERSON><PERSON> mới và kết nối qua MetaMask", "step1": "Bước 1", "noDappFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON>", "title": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> bằng cách giả mạo MetaM<PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> <PERSON> p<PERSON>p", "step2": "Bước 2", "alert": "<PERSON>hông thể kết nối với một Dapp vì nó không hiển thị Rabby <PERSON>et như một tùy chọn?", "step1Desc": "<PERSON> phép <PERSON> ng<PERSON> trang thành <PERSON>M<PERSON> trên <PERSON> hiện tại."}, "syncToMobile": {"selectAddress": {"title": "<PERSON><PERSON><PERSON> Địa chỉ để Đồng bộ hóa"}, "steps1": "1. <PERSON><PERSON><PERSON> Rabby Mobile", "downloadGooglePlay": "Google Play", "disableSelectAddress": "<PERSON><PERSON><PERSON>ng hỗ trợ đồng bộ cho địa chỉ {{type}}", "disableSelectAddressWithSlip39": "<PERSON>h<PERSON>ng hỗ trợ đồng bộ cho địa chỉ {{type}} với slip39", "downloadAppleStore": "<PERSON><PERSON><PERSON> h<PERSON>ng <PERSON>ng dụng", "clickToShowQr": "Nhấn để chọn địa chỉ và hiển thị mã QR", "disableSelectAddressWithPassphrase": "<PERSON>h<PERSON>ng hỗ trợ đồng bộ cho địa chỉ {{type}} với cụm mật khẩu.", "title": "<PERSON>ồng bộ địa chỉ ví từ mở rộng <PERSON><PERSON> sang di động", "selectedLenAddressesForSync_other": "<PERSON><PERSON> chọn {{len}} địa chỉ để đồng bộ hóa", "steps2": "2. <PERSON><PERSON><PERSON> bằng <PERSON><PERSON> Mobile", "selectedLenAddressesForSync_one": "<PERSON><PERSON> chọn {{len}} địa chỉ để đồng bộ", "description": "Dữ liệu địa chỉ của bạn luôn ở chế độ ngoại tuyến, đư<PERSON><PERSON> mã hóa và được chuyển giao một cách an toàn qua mã QR.", "steps2Description": "*Mã QR của bạn chứa dữ liệu nhạy cảm. Hãy giữ nó riêng tư và không bao giờ chia sẻ với ai.*"}, "search": {"sectionHeader": {"AllChains": "<PERSON><PERSON><PERSON> c<PERSON> Chuỗi", "Defi": "<PERSON><PERSON><PERSON>", "token": "Token", "NFT": "NFT"}, "header": {"placeHolder": "<PERSON><PERSON><PERSON>", "searchPlaceHolder": "<PERSON><PERSON><PERSON> k<PERSON> / <PERSON><PERSON><PERSON> chỉ Token"}, "tokenItem": {"listBy": "<PERSON><PERSON> s<PERSON>ch bởi {{name}}", "verifyDangerTips": "Đ<PERSON><PERSON> là một mã thông báo lừa đảo.", "Issuedby": "<PERSON><PERSON><PERSON> h<PERSON>nh bởi", "gasToken": "Token Gas", "FDV": "FDV", "scamWarningTips": "<PERSON><PERSON>y là một token chất lượng thấp và có thể là một trò lừa đảo."}, "searchWeb": {"searchTips": "<PERSON><PERSON><PERSON> kiếm trên web", "searching": "<PERSON><PERSON><PERSON> qu<PERSON> cho", "noResults": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "title": "<PERSON><PERSON><PERSON> cả Kết quả", "noResult": "<PERSON><PERSON><PERSON><PERSON> có kết quả cho"}}}, "component": {"AccountSearchInput": {"AddressItem": {"whitelistedAddressTip": "<PERSON><PERSON><PERSON> chỉ đ<PERSON><PERSON><PERSON> chấp thuận"}, "noMatchAddress": "<PERSON><PERSON><PERSON><PERSON> tìm thấy địa chỉ"}, "AccountSelectDrawer": {"btn": {"proceed": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "cancel": "Hủy bỏ"}}, "AddressList": {"AddressItem": {"addressTypeTip": "<PERSON><PERSON><PERSON><PERSON> khẩu bởi {{type}}"}}, "AuthenticationModal": {"passwordError": "mật kh<PERSON>u không ch<PERSON>h xác", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để xác nhận", "passwordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u"}, "ConnectStatus": {"ledgerNotConnected": "Ledger kh<PERSON>ng đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "connect": "<PERSON><PERSON><PERSON>", "imKeyConnected": "<PERSON><PERSON><PERSON> đã đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "ledgerConnected": "Ledger đ<PERSON> đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "keystoneConnected": "Keystone đã đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "connecting": "<PERSON><PERSON><PERSON> n<PERSON>...", "imKeyrNotConnected": "<PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "gridPlusConnected": "GridPlus đã đư<PERSON><PERSON> kết n<PERSON>i", "gridPlusNotConnected": "GridPlus chưa đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "keystoneNotConnected": "Keystone không đ<PERSON><PERSON><PERSON> kết n<PERSON>i"}, "Contact": {"AddressItem": {"whitelistedTip": "Địa chỉ trong danh sách trắng", "notWhitelisted": "Địa chỉ này không đư<PERSON><PERSON> đưa vào danh sách trắng"}, "EditModal": {"title": "Chỉnh sửa ghi chú địa chỉ"}, "EditWhitelist": {"backModalTitle": "<PERSON><PERSON>y bỏ thay đổi", "title": "Chỉnh s<PERSON>a <PERSON> s<PERSON>ch <PERSON>", "tip": "<PERSON>ọn địa chỉ bạn muốn đưa vào danh sách đư<PERSON><PERSON> phép và lưu lại.", "save": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> s<PERSON>ch <PERSON> ({{count}})", "backModalContent": "<PERSON><PERSON><PERSON> thay đổi bạn thực hiện sẽ không đượ<PERSON> lưu."}, "ListModal": {"authModal": {"title": "<PERSON><PERSON><PERSON> v<PERSON> s<PERSON>ch trắng"}, "title": "<PERSON><PERSON><PERSON> chỉ", "whitelistUpdated": "<PERSON><PERSON><PERSON> nh<PERSON>t danh sách trắng", "editWhitelist": "Chỉnh sửa <PERSON> sách trắng", "whitelistEnabled": "<PERSON>h sách trắng đã được kích hoạt. Bạn chỉ có thể gửi tài sản tới một địa chỉ trong danh sách trắng hoặc bạn có thể vô hiệu hóa nó trong \"Cài đặt\".", "whitelistDisabled": "<PERSON><PERSON> sách trắng đã bị vô hiệu hóa. Bạn có thể gửi tài sản đến bất kỳ địa chỉ nào."}}, "LoadingOverlay": {"loadingData": "<PERSON><PERSON> tải dữ liệu..."}, "MultiSelectAddressList": {"imported": "<PERSON><PERSON><PERSON><PERSON>"}, "NFTNumberInput": {"erc1155Tips": "S<PERSON> dư của bạn là {{amount}}", "erc721Tips": "Chỉ có một NFT của ERC 721 có thể được gửi tại một thời điểm."}, "TiledSelect": {"errMsg": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> từ khóa không đúng, vui lòng kiểm tra."}, "Uploader": {"placeholder": "<PERSON><PERSON><PERSON> m<PERSON>t tệp JSON"}, "WalletConnectBridgeModal": {"restore": "<PERSON><PERSON><PERSON><PERSON> phục cài đặt ban đầu", "title": "URL máy chủ Bridge", "invalidMsg": "<PERSON><PERSON> lòng kiểm tra máy chủ của bạn", "requiredMsg": "<PERSON>n vui lòng nhập địa chỉ máy chủ cầu."}, "PillsSwitch": {"NetSwitchTabs": {"testnet": "Mạng <PERSON>ù<PERSON> Chỉnh", "mainnet": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>p"}}, "ChainSelectorModal": {"noChains": "<PERSON><PERSON><PERSON>ng có chuỗi", "addTestnet": "<PERSON><PERSON><PERSON><PERSON> Chỉnh", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm chuỗi"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "TÀI SẢN / SỐ TIỀN"}, "price": {"title": "GIÁ"}, "usdValue": {"title": "GIÁ USD"}}, "bridge": {"value": "<PERSON><PERSON><PERSON> trị", "token": "Token", "low": "<PERSON><PERSON><PERSON><PERSON>", "liquidityTips": "<PERSON><PERSON><PERSON><PERSON> lượng giao dịch lịch sử càng cao, c<PERSON><PERSON> càng có khả năng thành công hơn.", "high": "<PERSON>", "liquidity": "<PERSON><PERSON><PERSON> k<PERSON>n"}, "searchInput": {"placeholder": "<PERSON><PERSON><PERSON> kiếm the<PERSON> / Đ<PERSON><PERSON> chỉ"}, "header": {"title": "<PERSON><PERSON><PERSON>"}, "recent": "<PERSON><PERSON><PERSON><PERSON>", "noTokens": "<PERSON><PERSON><PERSON><PERSON> có <PERSON>", "hot": "Nóng", "common": "<PERSON><PERSON><PERSON><PERSON> thường", "noMatchSuggestion": "<PERSON><PERSON><PERSON> cố gắng tìm địa chỉ hợp đồng trên {{ chainName }}", "noMatch": "Không khớp", "chainNotSupport": "Chuỗi này không được hỗ trợ"}, "ModalPreviewNFTItem": {"FieldLabel": {"Chain": "Chuỗi", "Collection": "<PERSON><PERSON> s<PERSON>u tập", "LastPrice": "<PERSON><PERSON><PERSON>", "PurschaseDate": "<PERSON><PERSON><PERSON> mua"}}, "signPermissionCheckModal": {"reconnect": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>", "title": "Bạn chỉ cho phép <PERSON> này ký trên các testnets."}, "testnetCheckModal": {"title": "<PERSON><PERSON> lòng bật \"Enable Testnets\" trong \"More\" trước khi đăng nhập vào testnets."}, "EcologyNavBar": {"providedBy": "Cung cấp bởi {{chainName}}"}, "EcologyNoticeModal": {"title": "<PERSON><PERSON><PERSON><PERSON> báo", "notRemind": "<PERSON><PERSON><PERSON> nh<PERSON>c tôi n<PERSON>a.", "desc": "<PERSON><PERSON><PERSON> dịch vụ sau sẽ được cung cấp trực tiếp bởi đối tác hệ sinh thái bên thứ ba. <PERSON><PERSON> <PERSON><PERSON> không chịu trách nhiệm về sự an toàn của các dịch vụ này."}, "ReserveGasPopup": {"instant": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fast": "<PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON>", "title": "Dự trữ Gas", "doNotReserve": "Đừng dành <PERSON>"}, "OpenExternalWebsiteModal": {"button": "<PERSON><PERSON><PERSON><PERSON>", "title": "Bạn đang rời khỏi <PERSON><PERSON> Wallet", "content": "Bạn sắp truy cập một trang web bên ngo<PERSON>i. <PERSON><PERSON> không chịu trách nhiệm về nội dung hoặc tính bảo mật của trang này."}, "TokenChart": {"price": "Giá", "holding": "<PERSON><PERSON><PERSON> trị nắm giữ"}, "externalSwapBrideDappPopup": {"noQuotesForChain": "<PERSON><PERSON>a có báo giá nào cho chuỗi này.", "chainNotSupported": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ trên chuỗi này", "selectADapp": "<PERSON><PERSON><PERSON>", "noDapp": "<PERSON><PERSON><PERSON><PERSON> có Dapp nào khả dụng", "viewDappOptions": "<PERSON><PERSON> tùy <PERSON>", "thirdPartyDappToProceed": "<PERSON><PERSON> lòng sử dụng một <PERSON><PERSON> bên thứ ba để tiếp tục.", "help": "<PERSON><PERSON> lòng liên hệ với đội ngũ chính thức của chuỗi này để được hỗ trợ", "noDapps": "<PERSON><PERSON><PERSON><PERSON> có Dapp nào khả dụng trên chuỗi này\n", "bridgeOnDapp": "<PERSON><PERSON><PERSON><PERSON>\n", "swapOnDapp": "<PERSON><PERSON><PERSON><PERSON> trao đổi\n"}, "AccountSelectorModal": {"searchPlaceholder": "<PERSON><PERSON><PERSON> đ<PERSON> chỉ\n", "title": "<PERSON><PERSON><PERSON> địa chỉ\n"}}, "global": {"scamTx": "Scam tx", "ok": "OK", "appName": "<PERSON><PERSON> dụ ví <PERSON>", "copied": "Sao chép", "copyAddress": "<PERSON>o ch<PERSON>p địa chỉ", "confirm": "<PERSON><PERSON><PERSON>", "back": "Quay lại", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "gas": "Gas", "appDescription": "Ví <PERSON> đột phá cho Ethereum và tất cả các chuỗi EVM", "unknownNFT": "NFT không x<PERSON>c đ<PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "cancelButton": "Hủy bỏ", "Cancel": "Hủy bỏ", "Done": "<PERSON><PERSON>", "Deleted": "Xóa", "Confirm": "<PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON><PERSON>", "nonce": "nonce", "tryAgain": "<PERSON><PERSON><PERSON> lại", "closeButton": "Đ<PERSON><PERSON>", "backButton": "Quay lại", "Nonce": "<PERSON><PERSON>", "confirmButton": "<PERSON><PERSON><PERSON>", "editButton": "Chỉnh sửa", "Balance": "Số dư", "notSupportTesntnet": "Không hỗ trợ cho mạng tùy chỉnh", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "watchModeAddress": "<PERSON>ế độ Giám sát địa chỉ", "assets": "t<PERSON><PERSON> s<PERSON>n", "Loading": "<PERSON><PERSON> t<PERSON>", "Clear": "Xóa", "proceedButton": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh"}, "background": {"error": {"emptyAccount": "tà<PERSON> k<PERSON><PERSON>n hiện tại trống", "unknownAbi": "abi h<PERSON>p đồng không x<PERSON>c đ<PERSON>nh", "invalidChainId": "Chuỗi <PERSON> không hợp lệ", "notFindChain": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy chuỗi {{chain}}", "noCurrentAccount": "<PERSON><PERSON><PERSON><PERSON> có tài khoản hiện tại", "notFoundGnosisKeyring": "<PERSON><PERSON><PERSON><PERSON> tìm thấy keyring Gnosis", "notFoundTxGnosisKeyring": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch nào trong Gnosis keyring.", "addKeyring404": "thất b<PERSON><PERSON> khi thêm keyring, keyring kh<PERSON>ng đ<PERSON><PERSON><PERSON> đ<PERSON>nh ngh<PERSON>a", "invalidAddress": "<PERSON><PERSON><PERSON>ng phải là địa chỉ hợp lệ", "invalidPrivateKey": "<PERSON>hó<PERSON> riêng không hợp lệ", "unlock": "bạn cần mở khóa ví trước", "txPushFailed": "<PERSON><PERSON><PERSON> giao dịch không thành công", "duplicateAccount": "<PERSON><PERSON><PERSON> k<PERSON>n bạn đang cố gắng nhập là trùng lặp.", "invalidJson": "t<PERSON><PERSON> đ<PERSON>u v<PERSON>o không hợp lệ", "invalidMnemonic": "<PERSON><PERSON><PERSON> thần chú không hợp lệ, vui lòng kiểm tra!", "canNotUnlock": "Không thể mở khóa mà không có kho báu trước đó", "generateCacheAliasNames": "[GenerateCacheAliasNames]: cần ít nhất một địa chỉ", "notFoundKeyringByAddress": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy keyring theo địa chỉ"}, "transactionWatcher": {"completed": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đã hoàn tất", "submitted": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> g<PERSON>", "more": "nhấp vào đây để xem thêm thông tin", "txCompleteMoreContent": "{{chain}} #{{nonce}} đã hoàn thành. Nhấn vào đây để xem thêm.", "failed": "<PERSON><PERSON><PERSON> d<PERSON>ch thất b<PERSON>i", "txFailedMoreContent": "{{chain}} #{{nonce}} thất bại. <PERSON>h<PERSON>p vào để xem thêm."}, "alias": {"watchAddressKeyring": "<PERSON><PERSON><PERSON>", "simpleKeyring": "Khóa riêng", "HdKeyring": "<PERSON><PERSON><PERSON> c<PERSON>t đầu tiên"}}, "constant": {"KEYRING_TYPE_TEXT": {"WatchAddressKeyring": "<PERSON><PERSON><PERSON>", "SimpleKeyring": "Nhập bằng Private Key", "HdKeyring": "<PERSON><PERSON><PERSON><PERSON> tạo bởi Seed Phrase"}, "SIGN_PERMISSION_OPTIONS": {"TESTNET": "Chỉ Testnets", "MAINNET_AND_TESTNET": "Mainnet & Testnet"}, "IMPORTED_HD_KEYRING": "<PERSON><PERSON><PERSON><PERSON> khẩu bằng cụm từ hạt giống", "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "<PERSON><PERSON><PERSON><PERSON> bằng Seed Phrase (Passphrase)"}}