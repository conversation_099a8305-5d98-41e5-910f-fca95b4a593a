# Each PR will build preview site that help to check code is work as expect.

name: Build

on:
  pull_request:
    types: [opened, synchronize, reopened]
  push:
    tags:
      - '*'

jobs:
  # Prepare node modules. Reuse cache if available
  setup:
    name: prepare build
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'
    steps:
      - name: checkout
        uses: actions/checkout@v3

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: '18.20.4'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}

      - uses: actions/cache@v3
        id: yarn-node_modules # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: node_modules
          key: ${{ runner.os }}-node_modules-${{ hashFiles('**/yarn.lock') }}

      - name: Get Yarn Cache
        if: steps.yarn-cache.outputs.cache-hit == 'true'
        run: yarn --prefer-offline

      - name: Use NPM Token with organization read access
        uses: heisenberg-2077/use-npm-token-action@v1
        with:
          token: '${{ secrets.NPM_AUTH_TOKEN }}'

      - name: Install Dependencies
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

  build-pro:
    name: build pro
    runs-on: ubuntu-latest
    needs: setup
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'
    steps:
      - name: checkout
        uses: actions/checkout@v3

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}

      - uses: actions/cache@v3
        id: yarn-node_modules # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: node_modules
          key: ${{ runner.os }}-node_modules-${{ hashFiles('**/yarn.lock') }}

      - name: build
        run: yarn build:pro

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        if: ${{ github.event_name == 'pull_request' }}
        with:
          name: Rabby_${{github.sha}}
          path: dist
          retention-days: 7

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        if: ${{ github.event_name == 'push' }}
        with:
          name: Rabby_${{github.ref_name}}
          path: dist
          retention-days: 7

  build-debug:
    name: build debug
    runs-on: ubuntu-latest
    needs: setup
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'
    steps:
      - name: checkout
        uses: actions/checkout@v3

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}

      - uses: actions/cache@v3
        id: yarn-node_modules # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: node_modules
          key: ${{ runner.os }}-node_modules-${{ hashFiles('**/yarn.lock') }}

      - name: build
        run: yarn build:debug

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        if: ${{ github.event_name == 'pull_request' }}
        with:
          name: Rabby_${{github.sha}}_debug
          path: dist
          retention-days: 7

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        if: ${{ github.event_name == 'push' }}
        with:
          name: Rabby_${{github.ref_name}}_debug
          path: dist
          retention-days: 7
