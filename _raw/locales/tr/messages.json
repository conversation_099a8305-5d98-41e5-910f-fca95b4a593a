{"page": {"transactions": {"title": "İşlemler", "empty": {"title": "İşlem yok", "desc": "<1>Desteklenen zincirlerde</1> işlem bulunamadı"}, "explain": {"approve": "{{project}} için {{amount}} {{symbol}} onayla", "unknown": "Sözleşme Etkileşimi", "cancel": "Bekleyen işlem iptal edildi"}, "txHistory": {"parseInputDataError": "Mesajı ayrıştırma başarısız oldu", "tipInputData": "İşlem bir mesaj içeriyor", "scamToolTip": "Bu işlem dolandırıcılar tarafından dolandırıcılık amaçlı tokenlar ve NFT'ler göndermek için başlatılmıştır. Lütfen bununla etkileşime girmekten kaçının."}, "modalViewMessage": {"title": "Mesajı <PERSON>ö<PERSON>"}, "filterScam": {"title": "Dolandırıcılık işlemlerini gizle", "btn": "Dolandırıcılık işlemlerini gizle", "loading": "Yükleme biraz zaman alabilir ve veri gecikmeleri mümkün olabilir"}}, "chainList": {"title": "{{count}} chains Integrated", "mainnet": "<PERSON>", "testnet": "Test Ağları"}, "signTx": {"nftIn": "NFT girişi", "gasLimitNotEnough": "Gas limiti 21000'den az. İşlem gönderilemez", "gasLimitLessThanExpect": "Gas limiti düşük. İşlemin başarısız olma olasılığı %1.", "gasLimitLessThanGasUsed": "Gas limiti çok düşük. İşlemin başarısız olma o<PERSON>ılığı %95.", "nativeTokenNotEngouthForGas": "Cüzdanınızda yeterli gas yok", "nonceLowerThanExpect": "<PERSON><PERSON> ç<PERSON> düşük, minimum olması gereken {{0}}", "canOnlyUseImportedAddress": "You can only use imported addresses to sign", "multiSigChainNotMatch": "Çoklu imza adresleri bu zincirde değil ve işlem başlatılamaz", "safeAddressNotSupportChain": "Mevcut safe adresi {{0}} zincirinde desteklenmiyor", "noGasRequired": "Gas gerekli değil", "gasSelectorTitle": "Gas", "failToFetchGasCost": "Gas maliyeti alınamadı", "gasMoreButton": "<PERSON><PERSON> fazla", "manuallySetGasLimitAlert": "Gas limitini manuel olarak şu değere ayarladınız ", "gasNotRequireForSafeTransaction": "Safe işlemleri için gas ücreti gerekli değil", "gasPriceTitle": "Gas Ücreti (Gwei)", "maxPriorityFee": "Maksimum Öncelik Ücreti (Gwei)", "eip1559Desc1": "EIP-1559'u <PERSON><PERSON><PERSON>, Öncelik Ücreti işleminizi işlemek için madencilere olan bahşiştir. Öncelik Ücretini düşürerek Gas ücretinden tasarruf edebili<PERSON>iniz, bu iş<PERSON>in işlenmesinin daha fazla zaman almasına yol açabilir.", "eip1559Desc2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (Bahşiş) = Maks<PERSON>um Ücret - Baz Ücret olarak hesaplanır. Maksimum Öncelik Ücretini ayarladıktan sonra, Baz Ücret bundan düşülür ve geri kalan madencilere bahşiş olarak verilir.", "hardwareSupport1559Alert": "Donanım cüzdanı yazılımının EIP 1559'u destekleyen bir sürüme yükseltildiğinden emin olun", "gasLimitTitle": "Gas limiti", "recommendGasLimitTip": "<PERSON><PERSON><PERSON> {{est}}. Mevcut {{current}}x, <PERSON><PERSON><PERSON>n ", "nonceTitle": "<PERSON><PERSON>", "gasLimitModifyOnlyNecessaryAlert": "<PERSON><PERSON>e gerekli olduğunda değiştirin", "gasPriceMedian": "Son 100 on-chain işlemin medyanı: ", "myNativeTokenBalance": "<PERSON><PERSON>: ", "gasLimitEmptyAlert": "Lütfen gas limitini girin", "gasLimitMinValueAlert": "Gas limiti 21000'den fazla olmalı", "balanceChange": {"successTitle": "İşlem Simülasyon Sonuçları", "failedTitle": "İşlem Simülasyonu Başarısız", "noBalanceChange": "Bakiye değişikliği yok", "tokenOut": "Çıkan <PERSON>", "tokenIn": "<PERSON><PERSON><PERSON>", "errorTitle": "Bakiye değişikliği alınamadı", "notSupport": "İşlem Simülasyonu Desteklenmiyor", "nftOut": "Çıkan NFT"}, "enoughSafeSigCollected": "<PERSON><PERSON><PERSON> imza toplandı", "moreSafeSigNeeded": "{{0}} daha fazla onay gerekiyor", "safeAdminSigned": "İmzalandı", "swap": {"title": "Token Takası", "payToken": "Ödenen", "receiveToken": "Alınan", "failLoadReceiveToken": "<PERSON><PERSON><PERSON>nemed<PERSON>", "valueDiff": "<PERSON><PERSON><PERSON>", "simulationFailed": "İşlem simülasyonu başarısız", "simulationNotSupport": "Bu zincirde işlem simülasyonu desteklenmiyor", "minReceive": "Minimum Alınacak", "slippageFailToLoad": "<PERSON><PERSON> to<PERSON> yüklenemedi", "slippageTolerance": "<PERSON><PERSON> to<PERSON>", "receiver": "Alıcı", "notPaymentAddress": "<PERSON>deme ad<PERSON>", "unknownAddress": "Bilinmeyen adres"}, "crossChain": {"title": "<PERSON><PERSON><PERSON><PERSON>r <PERSON>"}, "swapAndCross": {"title": "Token Takası ve Zincirler Arası"}, "wrapToken": "Tokenı Sar (Wrap)", "unwrap": "Sarılı Tokenı Çöz (Unwrap)", "send": {"title": "<PERSON><PERSON>", "sendToken": "<PERSON><PERSON> gö<PERSON>", "sendTo": "Şuna gönder", "receiverIsTokenAddress": "<PERSON><PERSON> adresi", "contractNotOnThisChain": "Sözleşme adresi bu zincirde <PERSON>ğil", "notTopupAddress": "<PERSON><PERSON><PERSON><PERSON>", "tokenNotSupport": "{{0}} desteklenmiyor", "onMyWhitelist": "<PERSON><PERSON>", "notOnThisChain": "Bu zincirde değil", "cexAddress": "Merkezi Bo<PERSON> Adresi", "addressBalanceTitle": "<PERSON><PERSON>", "whitelistTitle": "Beyaz Liste", "notOnWhitelist": "Beyaz listemde <PERSON>ğil", "scamAddress": "Dolandırıcılık adresi", "fromMyPrivateKey": "<PERSON><PERSON>", "fromMySeedPhrase": "<PERSON><PERSON>"}, "tokenApprove": {"title": "Token Onayı", "approveToken": "<PERSON><PERSON><PERSON> on<PERSON>", "myBalance": "<PERSON><PERSON>", "approveTo": "<PERSON><PERSON><PERSON>", "eoaAddress": "EOA adresi", "trustValueLessThan": "<PERSON><PERSON><PERSON> ≤ {{value}}", "deployTimeLessThan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> < {{value}} gün", "amountPopupTitle": "<PERSON><PERSON><PERSON>", "flagByRabby": "<PERSON><PERSON>", "contractTrustValueTip": "<PERSON><PERSON><PERSON>, bu kontrat i<PERSON>in on<PERSON>lanan ve kontrata maruz kalan toplam tokeni ifade eder. Düşük bir g<PERSON><PERSON>, ya riski ya da 180 gün boyunca hareketsizliği gösterir.", "amount": "<PERSON><PERSON>:", "exceed": "<PERSON><PERSON><PERSON> bakiyenizi aşıyor"}, "revokeTokenApprove": {"title": "Token Onayını Iptal Et", "revokeFrom": "Şundan iptal et", "revokeToken": "Tokeni iptal et"}, "sendNFT": {"title": "NFT Gönder", "nftNotSupport": "NFT desteklenmiyor"}, "nftApprove": {"title": "NFT Onayı", "approveNFT": "NFT onayla", "nftContractTrustValueTip": "<PERSON><PERSON><PERSON>, bu kontrat i<PERSON>in on<PERSON>lanan ve kontrata maruz kalan toplam tokeni ifade eder. Düşük bir g<PERSON><PERSON>, ya riski ya da 180 gün boyunca hareketsizliği gösterir."}, "revokeNFTApprove": {"title": "NFT Onayını İptal Et", "revokeNFT": "NFT'yi iptal et"}, "nftCollectionApprove": {"title": "NFT Koleksiyonu Onayı", "approveCollection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "revokeNFTCollectionApprove": {"title": "NFT Koleksiyonu Onayını İptal Et", "revokeCollection": "Koleksiyonu iptal et"}, "deployContract": {"title": "Bir Kontrat <PERSON>", "descriptionTitle": "<PERSON><PERSON>ı<PERSON><PERSON>", "description": "Bir akıllı kontrat yayınlıyorsunuz"}, "cancelTx": {"title": "Bekleyen İşlemi İptal Et", "txToBeCanceled": "İptal edilecek işlem", "gasPriceAlert": "Bekleyen işlemi iptal etmek için mevcut gas ücretini {{value}} Gwei'den fazlasına ayarlayın"}, "submitMultisig": {"title": "Çoklu İmza İşlemi Gönder", "multisigAddress": "Çoklu imza adresi"}, "contractCall": {"title": "Kontrat Çağrısı", "operation": "İşlem", "operationABIDesc": "İşlem ABI'den çözüldü", "operationCantDecode": "İşlem çözülemedi", "payNativeToken": "{{symbol}} öde", "suspectedReceiver": "<PERSON><PERSON><PERSON><PERSON>", "receiver": "Alıcı Adresi"}, "revokePermit2": {"title": "Permit2 Token Onayını İptal Et"}, "unknownAction": "Bilinmeyen", "interactContract": "<PERSON><PERSON><PERSON>", "markAsTrust": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>şaretlendi", "markAsBlock": "Engellenmiş olarak işaretlendi", "interacted": "<PERSON><PERSON> et<PERSON>leşim<PERSON> bulunuldu", "neverInteracted": "Daha önce hiç etkileşimde bulunulmadı", "transacted": "Daha önce işlem yapıldı", "neverTransacted": "Daha önce hiç işlem yapılmadı", "fakeTokenAlert": "<PERSON><PERSON>, <PERSON><PERSON> dolandırıcı olarak işaretlenmiş bir tokendir", "scamTokenAlert": "<PERSON><PERSON>, <PERSON><PERSON>'nin tespitine göre potansiyel olarak düşük kaliteli ve dolandırıcı bir tokendir", "trusted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blocked": "<PERSON>gel<PERSON>di", "noMark": "İşaret yok", "markRemoved": "İşaret kaldırıldı", "speedUpTooltip": "Bu hızlandırılmış işlem ve orijinal işlem çiftidir, yalnızca biri sonunda tamamlanacak", "signTransactionOnChain": "{{chain}} İş<PERSON><PERSON>", "viewRaw": "Hamı Gö<PERSON>ü<PERSON>", "unknownActionType": "Bilinmeyen işlem türü", "sigCantDecode": "<PERSON>u <PERSON>za Rabby tara<PERSON>ından <PERSON>", "nftCollection": "NFT Koleksiyonu", "floorPrice": "Taban fiyat", "contractAddress": "<PERSON><PERSON><PERSON>", "protocolTitle": "Protokol", "deployTimeTitle": "Ya<PERSON>ı<PERSON><PERSON>a zamanı", "popularity": "Popülerlik", "contractPopularity": "{{1}} üzerinde No.{{0}}", "addressNote": "<PERSON><PERSON> notu", "myMarkWithContract": "{{chainName}} kontratındaki işaretim", "myMark": "<PERSON><PERSON>", "collectionTitle": "Koleksiyon", "addressTypeTitle": "<PERSON><PERSON>", "firstOnChain": "Zincir üzeri ilk", "trustValue": "<PERSON><PERSON><PERSON>", "importedDelegatedAddress": "İçe aktarılan delege adresi", "noDelegatedAddress": "İçe aktarılmış delege adresi yok", "coboSafeNotPermission": "Bu delege adresinin bu işlemi başlatma izni yok", "gasAccount": {"totalCost": "Toplam maliyet: ", "currentTxCost": "Adresinize gönderilen Gas miktarı:", "sendGas": "Mevcut işlem için size aktarılan Gas:", "estimatedGas": "Tahmini Gas:", "maxGas": "<PERSON><PERSON><PERSON><PERSON> Gaz:", "gasCost": "Adresinize gas aktarımı için gas maliyeti:"}, "customRPCErrorModal": {"title": "Özel RPC Hatası", "button": "Özel RPC'yi <PERSON> Dışı Bırak", "content": "Özel RPC'niz şu anda kull<PERSON>lamıyor. Onu devre dışı bırakabilir ve Rabby'nin resmi RPC'sini kullanarak imzalamaya devam edebilirsiniz."}, "transferOwner": {"transferTo": "Transfer to", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "title": "Varlıkların Sahipliğini Aktarın"}, "swapLimitPay": {"maxPay": "Maksimum pay", "title": "Token Swap Limit Ödeme"}, "batchRevokePermit2": {"title": "Toplu Permit2 Onayını İptal Et"}, "revokePermit": {"title": "İzin Verilen Token Onayını Geri Çek"}, "assetOrder": {"listAsset": "Varlık listele", "receiveAsset": "Varlık al", "title": "Varlık Sıralaması"}, "BroadcastMode": {"instant": {"title": "Anında", "desc": "İşlemler hemen ağa yayınlanacaktır"}, "lowGas": {"title": "Gas tasarrufu", "desc": "<PERSON><PERSON><PERSON><PERSON>, ağ gas ücreti düşük olduğunda yayınlanacaktır."}, "mev": {"title": "MEV Korumalı", "desc": "İşlemler belirlenen MEV düğümüne yayınlanacak"}, "tips": {"notSupportChain": "Bu zincirde desteklenmiyor", "customRPC": "Özel RPC kullanırken desteklenmez", "notSupported": "Desteklenmiyor", "walletConnect": "WalletConnect tarafından desteklenmiyor"}, "lowGasDeadline": {"1h": "1s", "24h": "24s", "label": "Zaman Aşımı", "4h": "4sa"}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "SafeNonceSelector": {"explain": {"send": "<PERSON><PERSON>", "unknown": "Bilinmeyen İşlem", "contractCall": "Sözleşme Çağrısı"}, "optionGroup": {"replaceTitle": "Kuyruktaki işlemi değiştir", "recommendTitle": "<PERSON><PERSON><PERSON><PERSON> nonce"}, "option": {"new": "Yeni İşlem"}, "error": {"pendingList": "<PERSON><PERSON><PERSON> i<PERSON>, <1/><2><PERSON><PERSON><PERSON> dene</2>"}}, "coboSafeCreate": {"safeWalletTitle": "<PERSON><PERSON><PERSON><PERSON>{Wallet}", "title": "Cobo Safe Oluştur", "descriptionTitle": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "coboSafeModificationRole": {"safeWalletTitle": "<PERSON><PERSON><PERSON><PERSON>{Wallet}", "title": "Güvenli Rol Değişikliği Gönder", "descriptionTitle": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "coboSafeModificationDelegatedAddress": {"safeWalletTitle": "<PERSON><PERSON><PERSON><PERSON>{Wallet}", "descriptionTitle": "<PERSON><PERSON>ı<PERSON><PERSON>", "title": "Yetkilendirilmiş Adres Değişikliği Gönder"}, "coboSafeModificationTokenApproval": {"title": "<PERSON><PERSON>", "safeWalletTitle": "<PERSON><PERSON><PERSON><PERSON>{Wallet}", "descriptionTitle": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "common": {"description": "<PERSON><PERSON>ı<PERSON><PERSON>", "interactContract": "Sözleşme ile etkileşim kur", "descTipSafe": "<PERSON><PERSON><PERSON>, var<PERSON><PERSON>k değişikliğine neden olmaz veya adres sahipliğini doğrulamaz。", "descTipWarningPrivacy": "İmza adres sahipliğini doğrulayabilir", "descTipWarningAssets": "İmza varlık değişikliğine neden olabilir", "descTipWarningBoth": "<PERSON><PERSON><PERSON>, var<PERSON><PERSON><PERSON>ğişikliğine neden olabilir ve adres sahipliğini doğrulayabilir。"}, "nativeTokenForGas": "{{chainName}} üzerinde gaz ücretini ödemek için {{tokenName}} token<PERSON> kullanın", "gasAccountForGas": "Gas ödemesi yapmak için GasAccount'ımdaki USD'yi kullan", "chain": "<PERSON>in<PERSON>r", "importedAddress": "İthal edilen adres", "decodedTooltip": "Bu imza Rabby Wallet tarafından çözüldü", "address": "<PERSON><PERSON>", "typedDataMessage": "Imzalı Tip Verileri", "amount": "<PERSON><PERSON><PERSON>", "advancedSettings": "Gelişmiş <PERSON>", "hasInteraction": "Önceden etkileşimde bulunuldu", "contract": "Akıllı sözleşme", "l2GasEstimateTooltip": "L2 zinciri için gaz tahmini, L1 gaz ücretini içermez. Gerçek ücret, mevcut tahminden daha yüksek olacaktır.", "trustValueTitle": "<PERSON><PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "protocol": "Protokol", "addressSource": "<PERSON><PERSON>", "label": "Etiket", "maxPriorityFeeDisabledAlert": "Lütfen önce Gas Price ayarlayın", "yes": "<PERSON><PERSON>", "primaryType": "<PERSON><PERSON><PERSON><PERSON>", "safeServiceNotAvailable": "<PERSON><PERSON><PERSON><PERSON> hizmet <PERSON> anda mev<PERSON>, lü<PERSON><PERSON> daha sonra tekrar den<PERSON>in.", "safeTx": {"selfHostConfirm": {"button": "OK", "title": "<PERSON><PERSON>'nin <PERSON> Servisi'<PERSON>", "content": "Safe API kullanılamıyor. Safe'inizin işlevselliğini sürdürmek için Rabby tarafından dağıtılan Safe hizmetine geçin. <strong>Tüm Safe imzalayıcılarının işlemleri yetkilendirmek için Rabby Wallet kullanması gerekiyor.<strong>"}}}, "signFooterBar": {"requestFrom": "<PERSON><PERSON> eden", "processRiskAlert": "Lütfen imzalamadan önce uyarıya göz atın", "ignoreAll": "<PERSON><PERSON><PERSON> yoksay", "gridPlusConnected": "GridPlus bağlandı", "gridPlusNotConnected": "GridPlus bağlı değil", "connectButton": "Bağlan", "connecting": "Bağlanıyor...", "ledgerNotConnected": "Ledger bağ<PERSON><PERSON> değil", "ledgerConnected": "Ledger bağlandı", "signAndSubmitButton": "<PERSON><PERSON><PERSON><PERSON>", "walletConnect": {"connectedButCantSign": "Bağlandı ama imzalanamıyor.", "switchToCorrectAddress": "Lütfen mobil cüzdanınızda doğru adrese geçin", "switchChainAlert": "Lütfen mobil cüzdanınızda {{chain}}'e geçin", "notConnectToMobile": "{{brand}} ile bağlantı kurulmadı", "connected": "Bağlandı ve imzalamaya hazır", "howToSwitch": "<PERSON><PERSON><PERSON><PERSON>tirilir", "wrongAddressAlert": "Mobil cüzdanınızda farklı bir adrese geçtiniz. Lütfen mobil cüzdanınızda doğru adrese geçin", "connectBeforeSign": "{{0}} <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>, imzalamadan önce lütfen bağlanın", "chainSwitched": "Mobil cüzdanınızda farklı bir zincire geçtiniz. Lütfen mobil cüzdanınızda {{0}}'e geçin", "latency": "<PERSON><PERSON><PERSON><PERSON>", "requestSuccessToast": "Talep başarıyla gönderildi.", "sendingRequest": "İmzalama talebi gönderiliyor", "signOnYourMobileWallet": "Lütfen mobil cüzdanınızda imzalayın.", "requestFailedToSend": "<PERSON><PERSON><PERSON><PERSON> tale<PERSON>"}, "beginSigning": "İmzal<PERSON> b<PERSON>", "addressTip": {"onekey": "<PERSON><PERSON><PERSON>", "trezor": "<PERSON><PERSON><PERSON>", "bitbox": "BitBox02 adresi", "keystone": "Keystone adresi", "airgap": "AirGap adresi", "coolwallet": "CoolWallet adresi", "privateKey": "<PERSON><PERSON>", "seedPhrase": "Seed Phrase adresi", "watchAddress": "<PERSON><PERSON><PERSON> i<PERSON> ad<PERSON> imzal<PERSON>", "safe": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "coboSafe": "Cobo Argus Adresi", "seedPhraseWithPassphrase": "Seed Phrase adresi (Parola)"}, "qrcode": {"signWith": "{{brand}} ile im<PERSON>a", "failedToGetExplain": "Açıklama alınamadı", "txFailed": "İşlem başarısız oldu", "sigReceived": "<PERSON><PERSON><PERSON>ı", "sigCompleted": "<PERSON><PERSON>za <PERSON>landı", "getSig": "<PERSON><PERSON><PERSON>", "qrcodeDesc": "İmzalamak için {{brand}} ile ta<PERSON><br></br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> son<PERSON>, imzayı almak için aşağıdaki butona tıklayın", "misMatchSignId": "Uyuşmayan işlem verileri. Lütfen işlem detaylarını kontrol edin.", "unknownQRCode": "Hata: Bu QR kodunu tanımlayamadık", "afterSignDesc": "İmzaladıktan sonra bilgisayar kamerasının önüne {{brand}} QR kodunu yerleştirin"}, "ledger": {"resent": "<PERSON><PERSON><PERSON> g<PERSON>il<PERSON>", "signError": "Ledger imza hatası:", "notConnected": "Cüzdanınız bağlı değil. Lütfen yeniden bağlayın.", "siging": "İmzalama talebi gönderiliyor...", "txRejected": "İşlem reddedildi", "unlockAlert": "Lütfen Ledger'ınızı takın ve kilidini açın, üzerinde Ethereum'u açın", "updateFirmwareAlert": "Lütfen Ledger'ınızdaki firmware ve Ethereum Uygulamasını güncelleyin", "txRejectedByLedger": "İşlem Ledger'ınızda reddedildi", "blindSigTutorial": "Led<PERSON>'<PERSON><PERSON>", "resubmited": "<PERSON><PERSON><PERSON> g<PERSON>il<PERSON>", "submitting": "İmzalandı. İşlem oluşturuluyor"}, "common": {"notSupport": "{{0}} desteklenmiyor"}, "resend": "<PERSON><PERSON><PERSON>", "submitTx": "İşlemi Gönder", "testnet": "Test ağı", "mainnet": "<PERSON>", "gasless": {"watchUnavailableTip": "Sadece izleme adresi Free Gas için desteklenmez.", "rabbyPayGas": "<PERSON><PERSON>, gerekli gaz ücretini karşılayacak – sadece im<PERSON>n", "GetFreeGasToSign": "Ücretsiz Gas Al", "notEnough": "Gas bakiyesi yeterli değil", "customRpcUnavailableTip": "Özel RPC'ler Free Gas için desteklenmemektedir.", "unavailable": "Gas bakiyeniz yetersiz.", "walletConnectUnavailableTip": "Mobile wallet connected via WalletConnect, Free Gas için desteklenmez."}, "gasAccount": {"useGasAccount": "GasA<PERSON>unt <PERSON>", "login": "<PERSON><PERSON><PERSON> yap", "WalletConnectTips": "GasAccount tarafından WalletConnect desteklenmiyor", "deposit": "Para Yatırma", "notEnough": "GasAccount <PERSON><PERSON><PERSON>", "gotIt": "<PERSON><PERSON><PERSON><PERSON>", "loginFirst": "Lütfen önce GasAccount'a giriş yapın", "chainNotSupported": "Bu zincir GasAccount tarafından desteklenmiyor", "customRPC": "Özel RPC kullanıldığında desteklenmez", "loginTips": "GasAccount giri<PERSON><PERSON> tamamlamak için bu işlem iptal edilecektir. <PERSON><PERSON><PERSON> yaptıktan sonra işlemi yeniden oluşturmanız gerekecek.", "depositTips": "GasAccount para yatırma işlemini tamamlamak için, bu işlem iptal edilecektir. Para yatırma işleminden sonra yeniden yapmanız gerekecek."}, "keystone": {"siging": "<PERSON><PERSON>za isteği gönderiliyor", "hardwareRejectError": "Keystone isteği iptal edildi. Devam etmek için lütfen tekrar yetkilendirin.", "signWith": "İmzalama için {{method}} yöntemine geçiş yapın", "txRejected": "İşlem reddedildi", "unsupportedType": "Hata: İşlem tipi desteklenmiyor veya bilinmiyor.", "shouldRetry": "Bazı hatalar oluştu. Lütfen tekrar deneyin.", "misMatchSignId": "Uyumsuz işlem verileri. Lütfen işlem ayrıntılarını kontrol edin.", "mismatchedWalletError": "Uyumsuz cüzdan", "verifyPasswordError": "<PERSON><PERSON><PERSON>, lütfen kilidi açtıktan sonra tekrar deneyin", "shouldOpenKeystoneHomePageError": "Keystone 3 Pro'nuzun ana sayfada olduğundan emin olun", "qrcodeDesc": "Taramak için ta<PERSON>ın. İmzaland<PERSON><PERSON><PERSON>, imzayı almak için aşağıya tıklayın. USB için, imzalama işlemini tekrar başlatmak üzere yeniden bağlanın ve yetkilendirin."}, "keystoneConnected": "Keystone bağlandı", "keystoneNotConnected": "Keystone bağlı değil", "cancelCurrentTransaction": "Geçerli işlemi iptal et", "cancelAll": "Tüm {{count}} is<PERSON><PERSON><PERSON>'ten iptal et", "detectedMultipleRequestsFromThisDapp": "<PERSON><PERSON><PERSON>tan birden fazla istek tespit edildi.", "cancelCurrentConnection": "Geçerli bağlantıyı iptal et", "cancelTransaction": "İşlemi İptal Et", "cancelConnection": "Bağlantıyı iptal et", "imKeyNotConnected": "imKey bağlantısı yok", "imKeyConnected": "im<PERSON><PERSON> bağlı", "blockDappFromSendingRequests": "Dapp'in 1 dakika boyunca istek göndermesini engelle"}, "signTypedData": {"signTypeDataOnChain": "{{chain}} Yazılmış Veriyi İmzala", "safeCantSignText": "Bu bir Safe adresidir ve metni imzalamak için kullanılamaz.", "permit": {"title": "Token Onayına İzin Ver"}, "permit2": {"title": "Permit2 Token Onayı", "sigExpireTimeTip": "Bu imzanın zincir üzerinde geçerli olacağı süre", "sigExpireTime": "<PERSON><PERSON><PERSON> sona erme sü<PERSON>i", "approvalExpiretime": "Onay sona erme süresi"}, "swapTokenOrder": {"title": "<PERSON><PERSON>"}, "sellNFT": {"title": "NFT Emri", "receiveToken": "Tokeni al", "listNFT": "NFT Listele", "specificBuyer": "<PERSON><PERSON><PERSON> alı<PERSON>ı"}, "signMultiSig": {"title": "İşlemi Onayla"}, "createKey": {"title": "<PERSON><PERSON><PERSON>"}, "verifyAddress": {"title": "<PERSON><PERSON><PERSON>"}, "buyNFT": {"payToken": "Token öde", "receiveNFT": "NFT al", "expireTime": "Son geç<PERSON><PERSON><PERSON> süresi", "listOn": "Listele"}, "contractCall": {"operationDecoded": "İşlem mesajdan <PERSON>"}, "safeCantSignTypedData": "Bu bir Safe adresidir ve yalnızca EIP-712 tipinde verileri veya stringleri imzalamayı destekler."}, "activities": {"title": "İmza <PERSON>", "signedTx": {"label": "İşlemler", "empty": {"title": "Henüz imzalanmış işlem yok", "desc": "<PERSON><PERSON><PERSON> imzalanan tüm işlemler burada listelenecektir."}, "common": {"unlimited": "sınırsız", "unknownProtocol": "Bilinmeyen protokol", "unknown": "Bilinmeyen", "speedUp": "Hızlandır", "cancel": "İptal", "pendingDetail": "<PERSON><PERSON><PERSON> detay"}, "tips": {"pendingDetail": "Yalnızca bir işlem tamamlanacak. Tamamlanacak işlem neredeyse her zaman en yüksek gas ücretine sahip olandır", "canNotCancel": "Hızlandırılamaz veya iptal edilemez: Bekleyen ilk işlem değil", "pendingBroadcastRetryBtn": "<PERSON><PERSON><PERSON> ya<PERSON>ınla", "pendingBroadcastBtn": "Ş<PERSON>di <PERSON>ı<PERSON>", "pendingBroadcastRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON>a başar<PERSON>s<PERSON>z oldu. Son deneme: {{pushAt}}", "pendingBroadcast": "Gaz tasarrufu modu: <PERSON><PERSON> ağ ücretlerini bekliyor. Maksimum {{deadline}} saat bekleme süresi."}, "status": {"canceled": "İptal edildi", "failed": "Başarısız oldu", "submitFailed": "G<PERSON>nderme başarısız oldu", "pending": "Beklemede", "withdrawed": "Hızlı iptal", "pendingBroadcast": "Beklemede: <PERSON><PERSON><PERSON><PERSON>", "pendingBroadcastFailed": "Beklemede: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> başarısız oldu", "pendingBroadcasted": "Beklemede: yayınlandı"}, "txType": {"initial": "İlk işlem", "cancel": "İptal işlemi", "speedUp": "Hızlandırma işlemi"}, "explain": {"unknown": "Bilinmeyen İşlem", "send": "{{amount}} {{symbol}} <PERSON><PERSON><PERSON>", "cancel": "{{token}} için {{protocol}} Onayını İptal Et", "approve": "{{protocol}} için {{count}} {{token}} <PERSON><PERSON>la", "cancelNFTCollectionApproval": "{{protocol}} için NFT Koleksiyon Onayını İptal Et", "cancelSingleNFTApproval": "{{protocol}} için Tek NFT Onayını İptal Et", "singleNFTApproval": "{{protocol}} için Tek NFT Onayı", "nftCollectionApproval": "{{protocol}} için NFT Koleksiyon Onayı"}, "CancelTxPopup": {"options": {"removeLocalPendingTx": {"title": "<PERSON><PERSON>", "desc": "Arayüzden bekleyen işlemi kaldır"}, "quickCancel": {"title": "Hızlı İptal", "desc": "Yayınlamadan önce iptal et, gas ücreti yok", "tips": "Yalnızca yayınlanmamış işlemler için desteklenir"}, "onChainCancel": {"title": "On-chain Cancel", "desc": "İptal etmek için yeni işlem, gas gerektirir"}}, "removeLocalPendingTx": {"title": "İşlemi Yerel O<PERSON>ak Sil", "desc": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, bekleyen işlemi yerel olarak silecektir. \nBekleyen işlem gelecekte de başarıyla gönderilebilir."}, "title": "İşlemi iptal et"}, "MempoolList": {"empty": "Herhangi bir düğü<PERSON>de bulunamadı", "reBroadcastBtn": "<PERSON><PERSON><PERSON> ya<PERSON>ınla", "title": "{{count}} RPC düğümünde göründü"}, "message": {"deleteSuccess": "Başar<PERSON><PERSON>", "cancelSuccess": "İptal Edildi", "reBroadcastSuccess": "Yeniden yayınlandı", "broadcastSuccess": "Yayınlandı"}, "gas": {"noCost": "Gaz maliyeti yok"}, "SkipNonceAlert": {"alert": "Nonce #{{nonce}} {{chainName}} zincirinde atlandı. Bu, bekleyen işlem sayısının artmasına neden olabilir. <5></5> <6>Çözmek için zincire bir tx gönderin</6> <7></7>", "clearPendingAlert": "{{chainName}} İşlemi ({{nonces}}) 3 dakikadan uzun süredir beklemede. İşlemi <5></5> <6><PERSON><PERSON></6> <7></7> ile silebilir ve yeniden gönderebilirsiniz."}, "PredictTime": {"noTime": "Paketleme süresi tahmin ed<PERSON>r", "time": "{{time}} ta<PERSON><PERSON>de dolması bekleniyor", "failed": "Paketleme süresi tahmini başarısız oldu"}, "CancelTxConfirmPopup": {"title": "<PERSON><PERSON><PERSON> Bekleyenleri Temizle", "warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yerine yenisi geçmedikçe zincir üzerinde hala onaylanabilir.", "desc": "B<PERSON>, arayüzünüzden bekleyen işlemi kaldıracaktır. Ardından yeni bir işlem başlatabilirsiniz."}}, "signedText": {"label": "<PERSON><PERSON>", "empty": {"title": "<PERSON><PERSON><PERSON><PERSON> imzalan<PERSON>ış metin yok", "desc": "<PERSON><PERSON><PERSON> im<PERSON>anan tüm metinler burada listelenecektir."}}}, "receive": {"title": "{{chain}} üzerinde {{token}} Ya<PERSON><PERSON>r", "watchModeAlert1": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>me Modu adresidir.", "watchModeAlert2": "Varlıkları yatırmak için bunu kullanmak istediğinizden emin misiniz?"}, "sendToken": {"addressNotInContract": "Adres listesinde değil. <1></1><2><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></2>", "AddToContactsModal": {"addedAsContacts": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "editAddr": {"placeholder": "<PERSON><PERSON>", "validator__empty": "Lütfen adres notu girin"}, "editAddressNote": "<PERSON><PERSON> not<PERSON> d<PERSON>", "error": "Re<PERSON>bere ekleme başarısız oldu"}, "allowTransferModal": {"error": "yanlış şifre", "placeholder": "Onaylamak iç<PERSON>", "validator__empty": "Lütfen şif<PERSON> girin", "addWhitelist": "Beyaz Listeye ekle"}, "GasSelector": {"confirm": "<PERSON><PERSON><PERSON>", "level": {"$unknown": "Bilinmeyen", "custom": "<PERSON><PERSON>", "fast": "Anında", "normal": "Hızlı", "slow": "<PERSON><PERSON>"}, "popupDesc": "Ayarladığınız gas ücretine göre gas maliyeti transfer tutarından alınacaktır", "popupTitle": "Gas Ücretini Ayarla (Gwei)"}, "header": {"title": "<PERSON><PERSON><PERSON>"}, "modalConfirmAddToContacts": {"confirmText": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>"}, "modalConfirmAllowTransferTo": {"cancelText": "İptal", "confirmText": "<PERSON><PERSON><PERSON>", "title": "Onaylamak iç<PERSON>"}, "sectionBalance": {"title": "Bakiye"}, "sectionChain": {"title": "<PERSON>in<PERSON>r"}, "sectionFrom": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "sectionTo": {"addrValidator__empty": "Lütfen adres girin", "addrValidator__invalid": "Bu adres geçersiz", "searchInputPlaceholder": "<PERSON><PERSON> girin veya a<PERSON>ın", "title": "Alıcı"}, "sendButton": "<PERSON><PERSON><PERSON>", "tokenInfoFieldLabel": {"chain": "<PERSON>in<PERSON>r", "contract": "<PERSON><PERSON><PERSON>"}, "tokenInfoPrice": "<PERSON><PERSON><PERSON>", "whitelistAlert__disabled": "Beyaz Liste devre dışı. Herhangi bir adrese transfer yapabilirsiniz.", "whitelistAlert__notWhitelisted": "Adres beyaz listeye alınmamış. Geçici izin vererek transfer yapmayı onaylıyorum.", "whitelistAlert__temporaryGranted": "Geçici izin verildi", "whitelistAlert__whitelisted": "<PERSON><PERSON> be<PERSON>z listeye eklendi", "balanceWarn": {"gasFeeReservation": "Gas ücretinin ayrılması gerekli"}, "balanceError": {"insufficientBalance": "<PERSON><PERSON><PERSON>"}, "max": "MAKS", "sectionMsgDataForEOA": {"placeholder": "İsteğe bağlı", "title": "<PERSON><PERSON>", "currentIsUTF8": "<PERSON><PERSON><PERSON><PERSON><PERSON> giriş UTF-8'dir. <PERSON><PERSON><PERSON>eri:", "currentIsOriginal": "Geç<PERSON><PERSON> g<PERSON>ş Orijinal Veri'dir. UTF-8:"}, "sectionMsgDataForContract": {"title": "Sözleşme çağrısı", "placeholder": "Opsiyonel", "parseError": "Sözleşme çağrısı çözümlenemedi", "notHexData": "Yalnızca desteklenen onaltılık veri", "simulation": "Sözleşme çağrısı simülasyonu:"}, "blockedTransaction": "Engellenmiş İşlem", "blockedTransactionCancelText": "Bili<PERSON><PERSON>", "blockedTransactionContent": "<PERSON><PERSON> <PERSON><PERSON>, OFAC yaptırım listesinde yer alan bir adresle etkileşime giriyor."}, "sendTokenComponents": {"GasReserved": "Gas maliyeti için <1>0</1> {{ tokenName }} ayrıldı", "SwitchReserveGas": "<PERSON><PERSON> <1 />"}, "sendNFT": {"header": {"title": "<PERSON><PERSON><PERSON>"}, "sectionChain": {"title": "<PERSON>in<PERSON>r"}, "sectionFrom": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "sectionTo": {"title": "Alıcı", "addrValidator__empty": "Lütfen adres girin", "addrValidator__invalid": "Bu adres geçersiz", "searchInputPlaceholder": "<PERSON><PERSON> girin veya a<PERSON>ın"}, "nftInfoFieldLabel": {"Collection": "Koleksiyon", "Contract": "<PERSON><PERSON><PERSON>", "sendAmount": "<PERSON><PERSON><PERSON><PERSON>"}, "sendButton": "<PERSON><PERSON><PERSON>", "whitelistAlert__disabled": "Beyaz Liste devre dışı. Herhangi bir adrese transfer yapabilirsiniz.", "whitelistAlert__whitelisted": "<PERSON><PERSON> be<PERSON>z listeye eklendi", "whitelistAlert__temporaryGranted": "Geçici izin verildi", "whitelistAlert__notWhitelisted": "Adres beyaz listeye alınmamış. Geçici izin vererek transfer yapmayı onaylıyorum.", "tipNotOnAddressList": "Adres listesinde değil.", "tipAddToContacts": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "confirmModal": {"title": "Onaylamak iç<PERSON>"}}, "approvals": {"header": {"title": "{{ address }} <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab-switch": {"contract": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assets": "Varlıklara Göre"}, "component": {"table": {"bodyEmpty": {"loadingText": "Yükleniyor...", "noMatchText": "Eşleşme Yok", "noDataText": "<PERSON><PERSON>"}}, "ApprovalContractItem": {"ApprovalCount_one": "<PERSON><PERSON>", "ApprovalCount_other": "<PERSON><PERSON><PERSON>"}, "RevokeButton": {"btnText_zero": "İptal Et", "btnText_one": "İptal Et ({{count}})", "btnText_other": "İptal Et ({{count}})", "permit2Batch": {"modalTitle_one": "Toplamda <2>{{count}}</2> imza gereklidir", "modalTitle_other": "Toplam <2>{{count}}</2> imza gereklidir", "modalContent": "Aynı Permit2 sözleşmesinden gelen onaylar aynı imza altında birleştirilecektir."}}, "ViewMore": {"text": "<PERSON><PERSON> faz<PERSON>"}}, "search": {"placeholder": "İsme/adrese göre {{ type }} ara"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "<PERSON><PERSON><PERSON>", "contractTrustValue": "<PERSON><PERSON><PERSON>", "revokeTrends": "24s <PERSON><PERSON><PERSON> Eğilimleri", "myApprovedAssets": "Onayladığım Varlıklar", "myApprovalTime": "<PERSON><PERSON><PERSON><PERSON>"}, "columnTip": {"contractTrustValue": "<PERSON><PERSON><PERSON>, bu kontrat i<PERSON>in on<PERSON>lanan ve kontrata maruz kalan toplam tokeni ifade eder. Düşük bir g<PERSON><PERSON>, ya riski ya da 180 gün boyunca hareketsizliği gösterir.", "contractTrustValueWarning": "Ko<PERSON><PERSON> g<PERSON><PERSON> < $100,000", "contractTrustValueDanger": "Ko<PERSON><PERSON> g<PERSON><PERSON> < $10,000"}}, "byAssets": {"columnTitle": {"asset": "Varlık", "type": "<PERSON><PERSON><PERSON>", "approvedAmount": "<PERSON><PERSON><PERSON><PERSON>", "approvedSpender": "Onaylanmış Harcayıcı", "myApprovalTime": "<PERSON><PERSON><PERSON><PERSON>"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "Onaylanmış Miktar", "tipMyBalance": "Bakiyem"}}}}, "RevokeApprovalModal": {"subTitleTokenAndNFT": "Onaylanan Token ve NFT", "subTitleContract": "Aşağıdaki Kontratlar İçin Onaylandı", "selectAll": "Tümünü Seç", "confirm": "{{ selectedCount }} <PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "unSelectAll": "<PERSON><PERSON>m Seçimleri Kaldır", "tooltipPermit2": "<PERSON>u onay, Permit2 sözleşmesi aracılığıyla onaylanmıştır: {{ permit2Id }}"}, "revokeModal": {"confirmRevokePrivateKey": "Tohum cümlesi veya özel anahtar adresi kullanarak, 1 tıkla {{count}} onayı toplu olarak iptal edebilirsiniz.", "confirm": "<PERSON><PERSON><PERSON>", "totalRevoked": "Toplam:", "approvalCount_other": "{{count}} approvals", "submitTxFailed": "<PERSON><PERSON><PERSON><PERSON>", "done": "Tamamlandı", "paused": "Duraklatıldı", "pause": "<PERSON><PERSON><PERSON>", "revokeOneByOne": "Revoke One by One", "ledgerSending": "<PERSON><PERSON><PERSON> isteği g<PERSON> ({{current}}/{{total}})", "waitInQueue": "<PERSON><PERSON><PERSON> be<PERSON>", "cancelTitle": "Kalan İptalleri İptal Et", "stillRevoke": "<PERSON><PERSON>", "approvalCount_one": "{{count}} onay", "revoked": "İptal edildi:", "defaultFailed": "İşlem başarısız oldu", "connectLedger": "<PERSON><PERSON>", "gasTooHigh": "Gas ücreti yüksek", "ledgerSigned": "Signed. İşlem oluşturuluyor ({{current}}/{{total}})", "approvalCount_zero": "{{count}} approval", "resume": "<PERSON><PERSON> et", "signAndStartRevoke": "İmzala ve İptali <PERSON>lat", "batchRevoke": "Toplu İptal", "confirmTitle": "Tek Tıkla Toplu İptal", "ledgerAlert": "Lütfen Ledger cihazınızda Ethereum Uygulamasını açın", "gasNotEnough": "Göndermek için yet<PERSON>iz <PERSON>", "ledgerSended": "Lütfen talebi Ledger üzerinde imzalayın ({{current}}/{{total}})", "confirmRevokeLedger": "Bir Ledger ad<PERSON>, {{count}} onayı 1 tıklama ile toplu olarak iptal edebilirsiniz.", "revokeWithLedger": "Ledger ile <PERSON>", "cancelBody": "<PERSON><PERSON> sayfa<PERSON><PERSON> ka<PERSON>, kalan iptaller gerçekleştirilmez.", "simulationFailed": "Simülasyon Başarısız Oldu", "useGasAccount": "Gas bakiyeniz düşük. GasAccount gaz ücretlerini karşılayacaktır."}}, "gasTopUp": {"title": "Anında Gas Yükleme", "description": "Başka bir zincirdeki mevcut tokenleri bize göndererek gas yükleyin. Ödemeniz onaylandığında anında transfer olur ve geri döndürülemez olmasını beklemenize gerek kalmaz.", "topUpChain": "<PERSON><PERSON><PERSON><PERSON>", "Amount": "<PERSON><PERSON><PERSON>", "Continue": "<PERSON><PERSON>", "InsufficientBalance": "Ra<PERSON>'nin kontrat adresinde bu zincir i<PERSON>in yeterli bakiye yok. Lütfen daha sonra tekrar deneyin.", "hightGasFees": "<PERSON>u yükleme miktarı çok küçük, hedef ağ yüksek gaz ücretleri gerektiriyor.", "No_Tokens": "Token Yok", "InsufficientBalanceTips": "<PERSON><PERSON><PERSON>", "payment": "Gas Yükleme Ödemesi", "Loading_Tokens": "Tokenler Yükleniyor...", "Including-service-fee": "{{fee}} <PERSON><PERSON><PERSON>", "service-fee-tip": "Gas Yükleme hizmeti i<PERSON><PERSON>, token değişimindeki kaybı ve yükleme için gas ücretini karşılamak zorundadır. Bu nedenle %20 hizmet ücreti alınır.", "Confirm": "<PERSON><PERSON><PERSON>", "Select-from-supported-tokens": "Desteklenen tokenlerden seçin", "Value": "<PERSON><PERSON><PERSON>", "Payment-Token": "<PERSON><PERSON><PERSON>", "Select-payment-token": "Ödeme <PERSON>", "Token": "Token", "Balance": "Bakiye"}, "swap": {"title": "<PERSON><PERSON>", "pendingTip": "Tx gönderildi. Eğer tx uzun saatler boyunca beklemede kalı<PERSON>, a<PERSON><PERSON> sekmesinden beklemedeki işlemleri temizlemeyi deneyebilirsiniz.", "Pending": "Beklemede", "completedTip": "İşlem zincire kaydedildi, kayıt oluşturmak için veri çözümlemesi yapılıyor", "Completed": "Tamamlandı", "slippage_tolerance": "<PERSON><PERSON> to<PERSON>:", "actual-slippage": "<PERSON><PERSON><PERSON><PERSON>:", "gas-x-price": "Gas fiyatı: {{price}} Gwei.", "no-transaction-records": "İşlem kaydı yok", "swap-history": "Takas geçmişi", "InSufficientTip": "İşlem simülasyonu ve gas tahmini yapmak için yetersiz bakiye. Orijinal fiyat toplayıcı teklifleri gösteriliyor", "testnet-is-not-supported": "Özel ağ desteklenmiyor", "not-supported": "Desteklenmiyor", "slippage-adjusted-refresh-quote": "<PERSON><PERSON> to<PERSON> ayarlandı. Tek<PERSON><PERSON>.", "price-expired-refresh-quote": "Fiyatın süresi doldu. Teklifi <PERSON>.", "approve-x-symbol": "{{symbol}}'yi onayla", "swap-via-x": "{{name}} ile takas", "get-quotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> al", "chain": "<PERSON>in<PERSON>r", "swap-from": "Takas kaynağı", "to": "Alınacak token", "search-by-name-address": "<PERSON><PERSON> / <PERSON>res ile ara", "amount-in": "<PERSON><PERSON><PERSON> {{symbol}}", "unlimited-allowance": "Sınırsız izin", "insufficient-balance": "<PERSON><PERSON><PERSON>", "rabby-fee": "<PERSON><PERSON>", "minimum-received": "Minimum alınacak", "there-is-no-fee-and-slippage-for-this-trade": "Bu alım-satım için ücret ve kayma yoktur", "approve-tips": "1.<PERSON><PERSON><PERSON> → 2.<PERSON><PERSON>", "best": "En iyi", "unable-to-fetch-the-price": "<PERSON><PERSON>t alı<PERSON>ı", "fail-to-simulate-transaction": "İşlem simülasyonu başarısız", "security-verification-failed": "Güvenlik doğrulaması başarısız", "need-to-approve-token-before-swap": "Takas yapmadan önce token onayı gerekli", "this-exchange-is-not-enabled-to-trade-by-you": "Bu takas platformu sizin için ticarete açık değil.", "enable-it": "Etkinleştir", "this-token-pair-is-not-supported": "Bu token çifti desteklenmiyor", "QuoteLessWarning": "Alınacak miktar Rabby işlem simülasyonundan tahmin edilir. Dex tarafından sağlanan teklif {{receive}}. Beklenen tekliften {{diff}} daha az alacaksınız.", "by-transaction-simulation-the-quote-is-valid": "İşlem simülasyonuna göre teklif geçerli", "wrap-contract": "Kontratı Sar", "directlySwap": "Akıllı kontratla {{symbol}} tokenlerini doğrudan sarın", "rates-from-cex": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "tradingSettingTips": "{{viewCount}} borsa teklif sunuyor ve {{tradeCount}} tica<PERSON>i etkinleştiriyor", "the-following-swap-rates-are-found": "Aşağıdaki takas oranları bulundu", "est-payment": "<PERSON><PERSON><PERSON>:", "est-receiving": "<PERSON><PERSON><PERSON>:", "est-difference": "<PERSON><PERSON><PERSON>:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "Seçilen teklif mevcut orandan büyük ölçüde farklı, büyük kayıplara neden olabilir", "rate": "<PERSON><PERSON>", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Düşük kayma toleransı yüksek volatilite nedeniyle işlemlerin başarısız sonuçlanmasına yol açabilir", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "Yüksek kayma toleransı nedeniyle işlem frontrun'lanabilir", "recommend-slippage": "Frontrunning'i önlemek için <2>{{ slippage }}</2>% kayma <PERSON>i<PERSON>z", "slippage-tolerance": "<PERSON><PERSON> to<PERSON>", "select-token": "Token Seç", "enable-exchanges": "Borsaları etkinleştir", "exchanges": "Borsalar", "view-quotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> bak", "trade": "<PERSON><PERSON><PERSON>", "dex": "<PERSON>", "cex": "Cex", "enable-trading": "<PERSON><PERSON><PERSON><PERSON>", "i-understand-and-accept-it": "Anlıyorum ve kabul ediyorum", "confirm": "<PERSON><PERSON><PERSON>", "tradingSettingTip1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> s<PERSON><PERSON>ş<PERSON>iyle etkileşimde bulunacaksınız", "tradingSettingTip2": "2. <PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>n sözleşmesinden kaynaklanan risklerden sorumlu değildir", "gas-fee": "Gas Ücreti: {{gasUsed}}", "estimate": "<PERSON><PERSON><PERSON>:", "actual": "Gerçek:", "rabbyFee": {"rate": "Ücret oranı", "title": "<PERSON><PERSON>", "wallet": "Cüzdan", "swapDesc": "<PERSON><PERSON>, her zaman en iyi oranı üst düzey toplayıcılardan bulur ve tekliflerinin güvenilirliğini doğrular. Rabby %0.25 ücret (sararken %0) al<PERSON><PERSON>, bu ücret otomatik olarak teklife dahil edilir.", "button": "<PERSON><PERSON><PERSON><PERSON>", "bridgeDesc": "<PERSON><PERSON>, en iyi toplayı<PERSON>ılardan en iyi mümkün olan oranı her zaman bulacak ve tekliflerinin güvenilirliğini doğrulayacaktır. <PERSON><PERSON>, otomatik olarak teklife dahil edilen %0.25 ücret alır."}, "lowCreditModal": {"title": "<PERSON><PERSON> <PERSON>in düşük bir kredi değeri var", "desc": "Düşük bir kredi değeri genellikle yüksek riski işaret eder, <PERSON><PERSON><PERSON><PERSON> bir honeypot tokeni veya çok düşük likidite."}, "no-slippage-for-wrap": "<PERSON><PERSON> i<PERSON><PERSON> kayma yok", "max": "MAKSIMUM", "from": "<PERSON><PERSON>", "price-impact": "<PERSON><PERSON>t <PERSON>", "Gas-fee-too-high": "Gas ücreti çok yüksek", "Auto": "Otomatik", "source": "<PERSON><PERSON><PERSON>", "approve-and-swap": "{{name}} aracılığıyla Onayla ve Takas Et", "no-quote-found": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "No-available-quote": "Mevcut teklif yok", "approve-swap": "<PERSON><PERSON><PERSON> ve <PERSON>", "sort-with-gas": "Gaz ile sırala", "usd-after-fees": "≈ {{usd}}", "preferMEV": "MEV Korumalı Tercih Et", "no-fee-for-wrap": "<PERSON><PERSON> <PERSON><PERSON><PERSON> yok", "process-with-two-step-approve": "İki adımlı onayla devam et", "loss-tips": "{{usd}} kaybediyorsunuz. Küçük bir pazarda daha küçük bir miktar deneyin.", "no-fees-for-wrap": "<PERSON><PERSON> <PERSON><PERSON><PERSON> yok", "fetch-best-quote": "En iyi teklifi getiriliyor", "two-step-approve": "2 işlemi imzalayarak izni değiştirin", "hidden-no-quote-rates_one": "{{count}} or<PERSON><PERSON> kullanılamıyor", "hidden-no-quote-rates_other": "{{count}} oranları kullanılamıyor", "two-step-approve-details": "Token USDT, hakkı değiştirmek için 2 işlem gerektirir. Öncelikle, hakkı sıfıra ayarlamanız gerekir ve ardından yeni hakkı değeri belirleyebilirsiniz.", "preferMEVTip": "Ethereum takasları için \"MEV Guarded\" özelliğini etkinleştirerek sandwich saldırı risklerini azaltın. Not: Özel bir RPC veya cüzdan bağlanma adresi kullanıyorsanız bu özellik desteklenmez."}, "manageAddress": {"no-address": "<PERSON><PERSON> yok", "no-match": "Eşleşme yok", "current-address": "Mev<PERSON>", "address-management": "<PERSON><PERSON>", "update-balance-data": "Bakiye verisini gü<PERSON>lle", "search": "Ara", "manage-address": "<PERSON><PERSON><PERSON>", "deleted": "<PERSON><PERSON><PERSON>", "whitelisted-address": "Beyaz Listeye alınmış adres", "addressTypeTip": "{{type}} tarafından içe aktarıldı", "delete-desc": "Silmedan önce, varlıklarınızı nasıl koruyacağınızı anlamak için şu noktaları aklınızda bulundurun.", "delete-checklist-1": "Bu ad<PERSON><PERSON>, bu adresin <PERSON>ın & Seed Phrase'inin de silineceğini ve Rabby'nin bunu kurtaramayacağını anlıyorum.", "delete-checklist-2": "Özel Anahtarı veya Seed Phrase'i yedeklediğimi onaylıyorum ve şimdi silmeye hazırım.", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "delete-private-key-modal-title_one": "{{count}} <PERSON><PERSON> anahtar adresini sil", "delete-private-key-modal-title_other": "{{count}} <PERSON><PERSON> anahtar adreslerini sil", "delete-seed-phrase-title_one": "Seed Phrase'i ve {{count}} ad<PERSON>ini sil", "delete-seed-phrase-title_other": "Seed Phrase'i ve {{count}} ad<PERSON>lerini sil", "delete-title_one": "{{count}} {{brand}} adresini sil", "delete-title_other": "{{count}} {{brand}} adreslerini sil", "delete-empty-seed-phrase": "Seed Phrase'i ve 0 adresini sil", "hd-path": "HD path:", "no-address-under-seed-phrase": "Bu seed phrase altında herhangi bir adres içe aktarmadınız.", "add-address": "<PERSON><PERSON>", "delete-seed-phrase": "Seed Phrase'i sil", "confirm-delete": "<PERSON><PERSON><PERSON><PERSON>", "private-key": "<PERSON><PERSON>", "seed-phrase": "Seed Phrase", "watch-address": "<PERSON><PERSON><PERSON>", "backup-seed-phrase": "Seed Phrase'<PERSON>", "delete-all-addresses-but-keep-the-seed-phrase": "<PERSON><PERSON><PERSON> ad<PERSON>i sil, an<PERSON><PERSON> Seed Phrase'i sakla", "delete-all-addresses-and-the-seed-phrase": "Tüm adresleri ve Seed Phrase'i sil", "seed-phrase-delete-title": "Seed Phrase'i sil?", "sort-address": "<PERSON><PERSON><PERSON>", "enterThePassphrase": "<PERSON><PERSON> girin", "sort-by-balance": "Bakiyeye göre sırala", "passphraseError": "Parola geçersiz", "sort-by-address-note": "Adres notuna göre sırala", "enterPassphraseTitle": "Parafrayı İmzalamak İçin Girin", "sort-by-address-type": "<PERSON><PERSON> tü<PERSON> gö<PERSON> s<PERSON>", "addNewAddress": "<PERSON><PERSON>", "CurrentDappAddress": {"desc": "DApp Adresini <PERSON>\n"}}, "dashboard": {"home": {"offline": "<PERSON><PERSON> bağlantısı kesildi ve veri alınamıyor", "panel": {"swap": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "receive": "Al", "gasTopUp": "Gas Yükleme", "queue": "Kuyruk", "transactions": "İşlemler", "approvals": "<PERSON><PERSON><PERSON>", "feedback": "<PERSON><PERSON>", "more": "<PERSON><PERSON>", "manageAddress": "<PERSON><PERSON><PERSON>", "nft": "NFT", "rabbyPoints": "<PERSON><PERSON>", "ecology": "Ekosistem", "bridge": "Köprü", "mobile": "Mobile Sync"}, "comingSoon": "Çok Yakında", "soon": "Yakında", "refreshTheWebPageToTakeEffect": "Etkili olması için web sayfasını yenileyin", "rabbyIsInUseAndMetamaskIsBanned": "<PERSON><PERSON> k<PERSON> ve Metamask yasaklandı", "flip": "<PERSON><PERSON><PERSON>", "metamaskIsInUseAndRabbyIsBanned": "MetaMask kullanımda ve Ra<PERSON> yasaklandı", "transactionNeedsToSign": "işlem imzalanmalı", "transactionsNeedToSign": "işlemler imzalanmalı", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewFirstOne": "<PERSON><PERSON><PERSON> görü<PERSON>le", "rejectAll": "Tümünü Reddet", "pendingCount": "1 Bekliyor", "pendingCountPlural": "{{countStr}} Bek<PERSON><PERSON>r", "queue": {"title": "Kuyruk", "count": "{{count}} i<PERSON><PERSON>e"}, "whatsNew": "<PERSON><PERSON><PERSON><PERSON>", "importType": "{{type}} tarafından içe aktarıldı", "missingDataTooltip": "<PERSON><PERSON>, {{text}} ile ilgili mevcut ağ sorunları nedeniyle güncellenmeyebilir.", "chainEnd": "zincir", "chain": "zincir,"}, "recentConnection": {"disconnected": "Bağlantı kesildi", "rpcUnavailable": "Özel RPC kullanılamıyor", "metamaskTooltip": "Bu dApp ile MetaMask'ı kullanmayı tercih ediyorsunuz. Bu ayarı Ayarlar > MetaMask Tercih Edilen Dapps'ten istediğiniz zaman güncelleyin", "connected": "Bağlandı", "notConnected": "Bağlı değil", "connectedDapp": "<PERSON><PERSON>, <PERSON>u an<PERSON>a bağl<PERSON> değil. Bağlanmak için Dapp'ın web sayfasında bağlan butonunu bulup tıklayın.", "noDappFound": "Dapp bulunamadı", "disconnectAll": "Tüm Bağlantıları Kes", "disconnectRecentlyUsed": {"title": "<PERSON> k<PERSON> <strong>{{count}}</strong> DApp'lerin ba<PERSON> kes", "description": "Sabitlenmiş DAppler bağlı kalacak", "title_other": "{{count}} bağlantılı Dapp'i ayır", "title_one": "<strong>{{count}}</strong> ba<PERSON><PERSON><PERSON> Dapp'i bağlantısını kes"}, "title": "Bağlı Dapp", "pinned": "<PERSON><PERSON><PERSON><PERSON>", "noPinnedDapps": "Sabitlenmiş dapp yok", "dragToSort": "Sıralamak için <PERSON>", "recentlyConnected": "<PERSON> b<PERSON><PERSON>", "noRecentlyConnectedDapps": "Son ba<PERSON><PERSON><PERSON>lan Dapp yok", "noConnectedDapps": "Bağlı Dapp yok", "dapps": "<PERSON><PERSON>", "metamaskModeTooltip": "Bu Dapp'te <PERSON><PERSON> ba<PERSON>lanamıyor mu? <1>MetaMask Modu</1>'nu etkinleştirmey<PERSON> deneyin.", "metamaskModeTooltipNew": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> \"MetaMask\" seçtiğinizde bağlanacaktır. <PERSON>unu Daha Fazla > MetaMask olarak Gizlenerek Rabby Bağlan'da yönetebilirsiniz"}, "feedback": {"directMessage": {"content": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>et Official il<PERSON> sohbet edin"}, "proposal": {"content": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> i<PERSON><PERSON>'ta bir öneri sunun"}, "title": "<PERSON><PERSON>"}, "nft": {"empty": "Desteklenen Koleksiyonlarda NFT bulunamadı", "collectionList": {"collections": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "all_nfts": {"label": "<PERSON><PERSON><PERSON>ler"}}, "listEmpty": "<PERSON><PERSON><PERSON><PERSON> bir NFT'niz yok", "modal": {"collection": "Koleksiyon", "chain": "<PERSON>in<PERSON>r", "lastPrice": "<PERSON>", "purchaseDate": "<PERSON><PERSON><PERSON><PERSON>", "sendTooltip": "Şu an sadece ERC 721 ve ERC 1155 NFT'leri desteklenmektedir", "send": "<PERSON><PERSON><PERSON>"}}, "rabbyBadge": {"imageLabel": "rabby rozeti", "title": "<PERSON><PERSON><PERSON> Rozeti <PERSON>p Et", "enterClaimCode": "<PERSON><PERSON> kodunu girin", "swapTip": "Önce Rabby Cüzdanı ile tanınmış bir dex'te takas yapmanız gerekiyor.", "goToSwap": "<PERSON><PERSON><PERSON>", "claim": "Talep Et", "viewYourClaimCode": "Talep kodunuzu gö<PERSON><PERSON><PERSON>", "noCode": "<PERSON>u adres için talep kodunu aktive etmediniz", "learnMoreOnDebank": "DeBank'ta daha fazla bilgi edinin", "rabbyValuedUserNo": "<PERSON><PERSON> Valued User No.{{num}}", "claimSuccess": "Talep Başarılı", "viewOnDebank": "DeBank'ta Görüntüle", "learnMore": "<PERSON><PERSON>", "freeGasTitle": "Bedava Gas Rozeti Talep Et", "freeGasNoCode": "Lütfen aşağıdaki düğmeye tıklayarak DeBank'i ziyaret edin ve ilk önce mevcut adresinizi kullanarak talep kodunu alın.", "rabbyFreeGasUserNo": "Rabby Free Gas Kullanıcısı No.{{num}}", "freeGasTip": "Ücretsiz Gas kullanarak bir işlem imzalayın. Gas'ınız yeterli olmadığında 'Ücretsiz Gas' düğmesi otomatik olarak görünecektir."}, "contacts": {"noDataLabel": "veri yok", "noData": "Veri yok", "oldContactList": "<PERSON><PERSON>", "oldContactListDescription": "Rehberin ve izleme modeli adreslerinin birleştirilmesi nedeniyle eski rehber bilgileriniz sizin için burada yedeklenecektir. Bir süre sonra bu listeyi sileceğiz. Eğer kullanmaya devam ediyorsanız zamanında ekleyin."}, "security": {"tokenApproval": "Token Onayı", "nftApproval": "NFT Onayı", "comingSoon": "Daha fazla özellik yakında", "title": "Güvenlik"}, "settings": {"lock": {"never": "<PERSON><PERSON>"}, "7Days": "7 gün", "1Day": "1 gün", "4Hours": "4 saat", "1Hour": "1 saat", "10Minutes": "10 dakika", "backendServiceUrl": "Backend Servis URL'si", "inputOpenapiHost": "Lütfen openapi host'unu girin", "pleaseCheckYourHost": "Lütfen host'u kontrol edin", "host": "Host", "reset": "Restore initial setting", "save": "<PERSON><PERSON>", "pendingTransactionCleared": "<PERSON><PERSON>en i<PERSON>lem <PERSON>", "clearPending": "<PERSON><PERSON>", "clearPendingTip1": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, be<PERSON>en işlemi arayüzünüzden kaldırarak, ağda uzun bekleme sürelerinden kaynaklanan sorunları çözmenize yardımcı olur.", "clearPendingTip2": "<PERSON><PERSON>, hesap baki<PERSON>lerinizi etkilemez veya seed phrase'inizi yeniden girmenizi gerektirmez. Tüm varlıklar ve hesap detayları güvende kalır.", "autoLockTime": "Otomatik kilitlenme süresi", "claimRabbyBadge": "<PERSON><PERSON>!", "cancel": "İptal", "enableWhitelist": "Beyaz Listeyi Etkinleştir", "disableWhitelist": "Beyaz Listeyi Devre Dışı Bırak", "enableWhitelistTip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> k<PERSON> sad<PERSON>e beyaz <PERSON>eki ad<PERSON>lere varlık gönderebilirsiniz.", "disableWhitelistTip": "Devre dışı bırakıldığında herhangi bir adrese varlık gönderebilirsiniz", "warning": "Uyarı", "clearWatchAddressContent": "<PERSON>üm İzleme Modu adreslerini silmek istediğinizden emin misiniz?", "updateVersion": {"content": "<PERSON><PERSON> için yeni bir güncelleme mevcut. <PERSON> nasıl güncelleneceğini kontrol etmek için tıkla<PERSON>ın.", "okText": "Eğiticiye Bak", "successTip": "En güncel sürümü kullanıyorsunuz", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "features": {"label": "<PERSON><PERSON><PERSON><PERSON>", "lockWallet": "Cüzdanı Kilitle", "signatureRecord": "İmza <PERSON>", "manageAddress": "<PERSON><PERSON><PERSON>", "connectedDapp": "Bağlı Dapp'ler", "gasTopUp": "Gas <PERSON>i", "searchDapps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rabbyPoints": "<PERSON><PERSON>"}, "settings": {"label": "<PERSON><PERSON><PERSON>", "enableWhitelistForSendingAssets": "Varlık Gönderimi İçin Beyaz Listeyi Etkinleştir", "customRpc": "RPC URL'yi <PERSON>", "metamaskPreferredDapps": "MetaMask Tercih <PERSON>", "currentLanguage": "Mevcut Dil", "enableTestnets": "Testnetleri Etkinleştir", "customTestnet": "<PERSON>zel <PERSON>", "themeMode": "<PERSON><PERSON>", "toggleThemeMode": "<PERSON><PERSON>", "metamaskMode": "MetaMask gibi Gizlenerek Rabby'yi <PERSON>", "enableDappAccount": "Dapp Adreslerini Bağımsız Olarak Değiştirin\n"}, "aboutUs": "Hakkımızda", "currentVersion": "<PERSON><PERSON><PERSON>", "updateAvailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supportedChains": "<PERSON>teg<PERSON>", "followUs": "Bizi Takip Ed<PERSON>", "testnetBackendServiceUrl": "Testnet Backend Servis URL'si", "clearWatchMode": "<PERSON><PERSON><PERSON>", "requestDeBankTestnetGasToken": "DeBank Testnet Gas Tokeni İste", "clearPendingWarningTip": "Çıkarılan işlem, başka bir işlem ile değiştirilmedikçe zincirde hâlâ onaylanabilir.", "DappAccount": {"button": "Etkinleştir\n", "title": "Dapp Adreslerini Bağımsız Olarak Değiştir\n", "desc": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON>, her bir <PERSON><PERSON>'e bağ<PERSON>ms<PERSON>z olarak hangi adresi bağlayacağınızı seçebilirsiniz. <PERSON> adres<PERSON><PERSON>, her Dapp'e bağlı adresi et<PERSON>.\n"}}, "tokenDetail": {"blockedTip": "Engellenen token, token listesinde gösterilmeyecek", "blocked": "<PERSON>gel<PERSON>di", "selectedCustom": "Bu token Rabby tara<PERSON>ından listelenmemiş. <PERSON><PERSON> o<PERSON>ak token listesine e<PERSON>z.", "notSelectedCustom": "Bu token Rabby tarafından listelenmemiş. Aktif ederseniz token listesine eklenecek.", "customized": "Özelleştirilmiş", "scamTx": "Sa<PERSON>e işlem", "txFailed": "Başarısız", "notSupported": "<PERSON>incirde<PERSON> bu <PERSON> desteklenmiyor", "swap": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "receive": "Al", "noTransactions": "İşlem Yok", "customizedButton": "özelleştirilmiş", "blockedButton": "<PERSON><PERSON><PERSON><PERSON>", "customizedButtons": "<PERSON><PERSON> tokenler", "blockedButtons": "engel<PERSON><PERSON><PERSON> token'lar", "blockedListTitles": "engellenen tokenlar", "ListedBy": "Listeleyen", "OriginalToken": "Orijinal Token", "BridgeProvider": "Köprü Sağlayıcı", "NoListedBy": "Liste bilgisi mevcut değil", "verifyScamTips": "Bu bir dolandırıcılık token'ıdır.", "ContractAddress": "Sözleşme Adresi", "customizedListTitles": "<PERSON><PERSON> tokenler", "TokenName": "Token Adı", "Chain": "<PERSON>in<PERSON>r", "AddToMyTokenList": "Token listeme ekle", "myBalance": "Bakiyem", "noIssuer": "Verici bilgisi mevcut de<PERSON>", "OriginIssue": "Bu blockchain üzerinde yerel olarak ihraç edilmiştir.", "blockedTips": "Engellenen token, token listesinde gösterilmeyecek.", "NoSupportedExchanges": "Desteklenen borsa yok", "customizedListTitle": "özel token", "maybeScamTips": "Bu düşük kaliteli bir token ve dolandırıcılık olabilir.", "BridgeIssue": "Üçüncü taraf tarafından köprülenmiş token", "SupportedExchanges": "Desteklenen <PERSON>", "customizedHasAddedTips": "Token Rabby tarafından listelenmemiştir. Bunu token listesine manuel o<PERSON>.", "IssuerWebsite": "Yayımcının Web Sitesi", "blockedListTitle": "blokajlı token", "fdvTips": "<PERSON><PERSON><PERSON>, maksimum arz dolaşımdayken. Tam Sulandırılmış Değerlendirme (FDV) = Fiyat x Maksimum Arz. <PERSON><PERSON><PERSON>ksimum Arz yoksa, FDV = Fiyat x Toplam Arz. Eğer ne Maksimum Arz ne de Toplam Arz tanımlı veya sonsuzsa, FDV gösterilmez."}, "assets": {"usdValue": "USD DEĞERİ", "amount": "MİKTAR", "portfolio": {"nftTips": "Bu protokol tarafından tanınan taban fiyata göre hesaplandı.", "fractionTips": "İlgili ERC20 token fiyatına göre hesapla."}, "tokenButton": {"subTitle": "<PERSON><PERSON> <PERSON><PERSON>i token, toplam bakiyeye eklenmeyecek"}, "table": {"assetAmount": "Varlık / Miktar", "price": "<PERSON><PERSON><PERSON>", "useValue": "USD Değeri", "healthRate": "Sağlık Oranı", "debtRatio": "<PERSON><PERSON><PERSON>", "unlockAt": "Şu Tarihte Aç", "lentAgainst": "KARŞILIĞINDA BORÇ VERİLDİ", "type": "<PERSON><PERSON><PERSON>", "strikePrice": "Kullanım Fiyatı", "exerciseEnd": "<PERSON><PERSON><PERSON><PERSON>", "tradePair": "<PERSON><PERSON><PERSON>", "side": "<PERSON><PERSON>", "leverage": "Kaldıraç", "PL": "P&L", "unsupportedPoolType": "Desteklenmeyen havuz tipi", "claimable": "Talep <PERSON>", "endAt": "<PERSON><PERSON>", "dailyUnlock": "Günlük Kilit Açılımı", "pool": "HAVUZ", "token": "Token", "balanceValue": "Bakiye / Değer", "percent": "<PERSON><PERSON>z<PERSON>", "summaryTips": "Varlık değeri toplam net varlık ile bölünmüştür", "summaryDescription": "Protokollerdeki tüm varl<PERSON>klar (örn. LP tokenleri) istatistiksel hesaplamalar için temel varlıklara dönüştürüldü", "noMatch": "Eşleşme Yok", "lowValueDescription": "Düşük değerdeki varlıklar burada gösterilecek", "lowValueAssets": "{{count}} düşük değerli varlık", "lowValueAssets_other": "{{count}} d<PERSON><PERSON><PERSON><PERSON> değerli tokenlar", "lowValueAssets_one": "{{count}} d<PERSON><PERSON><PERSON><PERSON> token", "lowValueAssets_0": "{{count}} d<PERSON><PERSON><PERSON><PERSON> token"}, "noAssets": "Varlık Yok", "blockLinkText": "Tokeni engellemek için adresi ara", "blockDescription": "Tarafınızdan engellenen token burada gösterilecek", "unfoldChain": "1 zinciri aç", "unfoldChainPlural": "{{moreLen}} zinciri aç", "customLinkText": "Farklı bir token eklemek için adresi ara", "customDescription": "Tarafınızdan özel olarak eklenen token burada gösterilecek", "comingSoon": "Yakında...", "searchPlaceholder": "<PERSON><PERSON><PERSON>", "AddMainnetToken": {"isBuiltInToken": "Token zaten destekleniyor", "notFound": "Token bulunamadı", "title": "<PERSON><PERSON>", "searching": "<PERSON><PERSON>", "tokenAddressPlaceholder": "Token Address", "tokenAddress": "Token Address", "selectChain": "<PERSON><PERSON><PERSON><PERSON>"}, "AddTestnetToken": {"tokenAddressPlaceholder": "Token Address", "tokenAddress": "Token Address", "selectChain": "<PERSON><PERSON><PERSON><PERSON>", "title": "Özel Ağ Tokeni <PERSON>", "searching": "<PERSON><PERSON>", "notFound": "Token bulunamadı"}, "TestnetAssetListContainer": {"add": "Token", "addTestnet": "Ağ"}, "noTestnetAssets": "Özel Ağ Varlığı Yok", "addTokenEntryText": "Token", "customButtonText": "Özel token ekle"}, "hd": {"howToConnectLedger": "Ledger Nasıl Bağlanır", "userRejectedTheRequest": "Kullanıcı tale<PERSON>.", "ledger": {"doc1": "Tek bir Ledger bağlayın", "doc2": "<PERSON><PERSON><PERSON> açmak için pin girin", "doc3": "Ethereum Uygulamasını Aç", "reconnect": "Ç<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lütfen <1>tekrar bağlanmayı deneyin.</1>", "connected": "Ledger bağlı"}, "howToSwitch": "<PERSON><PERSON><PERSON><PERSON>tirilir", "keystone": {"reconnect": "Ç<PERSON><PERSON><PERSON><PERSON>zsa, lütfen <1>baştan yeniden bağlanmayı deneyin.</1>", "doc3": "Bilgis<PERSON>ra bağlantıyı onayla", "title": "Keystone 3 Pro'nuzun ana sayfada olduğundan emin olun", "doc1": "Tek bir Keystone takın", "doc2": "<PERSON><PERSON><PERSON>i g<PERSON> kili<PERSON> a<PERSON>ın"}, "imkey": {"doc1": "Tek bir imKey takın", "doc2": "PIN kodunu girerek kilidi açın"}, "howToConnectImKey": "imKey Na<PERSON>ıl Bağlanır", "howToConnectKeystone": "Keystone Nasıl Bağlanır", "ledgerIsDisconnected": "Defteriniz bağlı değil"}, "GnosisWrongChainAlertBar": {"warning": "G<PERSON><PERSON><PERSON> adres {{chain}} desteklemiyor", "notDeployed": "Safe adresiniz bu zincirde dağıtılmamış."}, "echologyPopup": {"title": "Ekosistem"}, "MetamaskModePopup": {"title": "MetaMask Modu", "enableDesc": "Dapp yalnızca MetaMask ile çalışıyorsa etkinleştirin", "footerText": "Daha fazla Dapp eklemek için Daha Fazla > MetaMask Modu altında MetaMask Modu", "toastSuccess": "Etkinleştirildi. Yeniden bağlanmak için sayfayı yenileyin.", "desc": "<PERSON><PERSON>'te Ra<PERSON>'<PERSON><PERSON><PERSON><PERSON>, MetaMask Modunu etkinleştirin ve MetaMask seçeneğini seçerek bağlanın."}, "offlineChain": {"chain": "{{chain}} yakında entegre edilmeyecek.", "tips": "{{chain}} Zinciri {{date}} tarihinde entegre edilmeyecek. Varlıklarınız etkilenmeyecek ancak toplam bakiyenize dahil edilmeyecektir. Onlara erişmek için “Daha Fazla” altında özel bir ağ olarak ekleyebilirsiniz."}, "recentConnectionGuide": {"button": "<PERSON><PERSON><PERSON><PERSON>\n", "title": "Dapp bağlantısı için burada adres değiştirin.\n"}}, "nft": {"floorPrice": "Taban Fiyat:", "title": "NFT", "all": "<PERSON><PERSON><PERSON>", "starred": "Yıldızlı ({{count}})", "empty": {"title": "Yıldızlı NFT Yok", "description": "\"Hepsi\" kısmından NFT seçebilir ve \"Yıldızlı\"'ya ekleyebilirsiniz"}, "noNft": "NFT Yok"}, "newAddress": {"title": "<PERSON><PERSON>", "importSeedPhrase": "Seed Phrase'i İçe Aktar", "importPrivateKey": "Özel Anahtarı İçe Aktar", "importMyMetamaskAccount": "Metamask Hesabımı İçe Aktar", "addContacts": {"content": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> sad<PERSON>e izlemeye alınmış adres olarak da kullanabilirsiniz", "required": "Lütfen adresi girin", "notAValidAddress": "Geçerli bir ad<PERSON>", "scanViaMobileWallet": "<PERSON><PERSON>ra", "scanViaPcCamera": "PC kamerasıyla tara", "scanQRCode": "WalletConnect uyumlu cüzdanlarla QR kodu tara", "walletConnect": "Wallet connect", "walletConnectVPN": "VPN kullanırsanız WalletConnect stabil olmayabilir.", "cameraTitle": "Lütfen kameranızla QR kodu tarayın", "addressEns": "Adres / ENS"}, "unableToImport": {"title": "İçe aktarılamıyor", "description": "Birden fazla QR tabanlı donanım cüzdanlarının içe aktarılması desteklenmiyor. Başka bir cihazı içe aktarmadan önce lütfen {{0}}'dan tüm adresleri silin."}, "connectHardwareWallets": "Donanım Cüzdanlarını Bağla", "connectMobileWalletApps": "Mobil Cüzdan Uygulamalarını Bağla", "connectInstitutionalWallets": "Kurumsal Cüzdanları Bağla", "createNewSeedPhrase": "<PERSON>ni Seed Phrase <PERSON>", "importKeystore": "KeyStore'u İçe Aktar", "selectImportMethod": "İçe Aktarma Yöntemini Seçin", "theSeedPhraseIsInvalidPleaseCheck": "Seed phrase ge<PERSON><PERSON><PERSON>, lütfen kontrol edin!", "seedPhrase": {"importTips": "Gizli seed phrase'inizin tamamını 1. al<PERSON>", "whatIsASeedPhrase": {"question": "Seed Phrase Ned<PERSON>?", "answer": "Varlıklarınızı kontrol etmek için kullanılan 12, 18 veya 24 kelimelik bir dizidir."}, "isItSafeToImportItInRabby": {"question": "Rabby'e aktarmak güvenli mi?", "answer": "<PERSON><PERSON>, yaln<PERSON><PERSON><PERSON> ta<PERSON>ıcınızda yerel olarak saklanır ve sadece sizin tarafınızdan erişilebilir."}, "importError": "[CreateMnemonics] beklenmeyen adım {{0}}", "importQuestion4": "Seed Phrase ye<PERSON><PERSON><PERSON> almadan <PERSON><PERSON><PERSON>, <PERSON><PERSON> bunu benim i<PERSON> geri al<PERSON>.", "riskTips": "Başlamadan önce, lütfen aşağıdaki güvenlik ipuçlarını okuyun ve aklınızda bulundurun.", "showSeedPhrase": "Seed Ph<PERSON>'<PERSON>", "backup": "Seed Ph<PERSON>'<PERSON>", "backupTips": "Seed Phrase'i yedeklerken ekranınızı başka kimse görmesin", "copy": "Seed Phrase'i kopyala", "saved": "Ph<PERSON>'<PERSON>", "pleaseSelectWords": "Lütfen kelimeleri seçin", "verificationFailed": "Doğrulama başarısız", "createdSuccessfully": "Başarıyla oluşturuldu", "verifySeedPhrase": "Seed Ph<PERSON><PERSON><PERSON>", "fillInTheBackupSeedPhraseInOrder": "<PERSON><PERSON>'i sı<PERSON>la do<PERSON>n", "wordPhrase": "Bir <1>{{count}}</1>-kelimelik diziye sahi<PERSON>m", "clearAll": "<PERSON><PERSON><PERSON>", "slip39SeedPhrase": "Bir <0>{{SLIP39}}</0> Seed Phrase'im var", "slip39SeedPhrasePlaceholder_other": "Buraya {{count}}. to<PERSON> if<PERSON>i <PERSON>ınızı girin", "inputInvalidCount_other": "{{count}} gir<PERSON> Seed Phrase norm<PERSON><PERSON>, lütfen kontrol edin.", "invalidContent": "Geçersiz içerik", "slip39SeedPhrasePlaceholder_one": "{{count}}. an<PERSON><PERSON> kelime <PERSON>ınızı buraya girin", "pastedAndClear": "Yapıştırıldı ve panoya alınanlar temizlendi", "wordPhraseAndPassphrase": "Bir Passphrase ile <1>{{count}}</1> keli<PERSON>ik bir if<PERSON>ye sa<PERSON>m", "slip39SeedPhraseWithPassphrase": "Parolalı bir <0>{{SLIP39}}</0> Seed Phrase'im var", "slip39SeedPhrasePlaceholder_two": "{{count}}nci tohum ifadesi payınızı buraya girin", "slip39SeedPhrasePlaceholder_few": "{{count}}. to<PERSON> cümlesi paylarınızı buraya girin", "passphrase": "<PERSON><PERSON><PERSON>", "inputInvalidCount_one": "1 giriş Seed Phrase norm<PERSON><PERSON>, lütfen kontrol edin.", "importQuestion3": "<PERSON><PERSON>er Rabby'yi seed if<PERSON><PERSON> kald<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>.", "importQuestion1": "<PERSON><PERSON><PERSON> kurtarma kelimelerimi kaybeder veya <PERSON>m, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kalıcı olarak erişimimi ka<PERSON>ğim.", "importQuestion2": "Kı<PERSON><PERSON><PERSON> kelime ifadem yalnızca cihazımda saklanıyor. <PERSON><PERSON> buna eri<PERSON>."}, "metamask": {"step1": "MetaMask'tan seed phrase'i veya özel anahtarı dışa aktarın <1><PERSON><PERSON><PERSON> <2/></1>", "step2": "Seed phrase'i veya özel anahtarı Rabby'de içe aktarın", "step3": "İçe aktarma tamamlandı ve tüm varlıklarınız otomatik olarak görünecek", "how": "MetaMask Hesabımı Nasıl İçe Aktarırım?", "step": "<PERSON><PERSON><PERSON>", "importSeedPhrase": "Seed phrase'i veya özel anahtarı içe aktar", "importSeedPhraseTips": "<PERSON><PERSON><PERSON>da yerel olarak saklanır. <PERSON><PERSON>, <PERSON><PERSON> bilgilerinize as<PERSON> er<PERSON>.", "tips": "İpuçları:", "tipsDesc": "Tohum cümleniz/özel anahtarınız MetaMask veya herhangi bir belirli cüzdana ait değildir; yalnızca size aittir."}, "privateKey": {"required": "Lütfen Özel Anahtarı girin", "placeholder": "Özel Anahtarınızı girin", "whatIsAPrivateKey": {"question": "<PERSON><PERSON>?", "answer": "Varlıklarınızı kontrol etmek için kullanılan harf ve sayılardan oluşan bir dizedir."}, "isItSafeToImportItInRabby": {"question": "Onu Rabby'e aktarmak güvenli mi?", "answer": "<PERSON><PERSON>, yaln<PERSON><PERSON><PERSON> ta<PERSON>ıcınızda yerel olarak saklanır ve sadece sizin tarafınızdan erişilebilir."}, "isItPossibleToImportKeystore": {"question": "KeyStore'u içe aktarmak mümkün mü?", "answer": "<PERSON><PERSON>, b<PERSON><PERSON> <1> KeyStore'u </1> içe aktarabilirsiniz."}, "notAValidPrivateKey": "Geçerli bir özel anahtar <PERSON>ğ<PERSON>", "repeatImportTips": {"question": "Bu adrese geçmek istiyor musunuz?", "desc": "Bu adres içe aktarıldı."}}, "importedSuccessfully": "Başarıyla İçe Aktarıldı", "ledger": {"title": "Led<PERSON><PERSON><PERSON>ğlan", "cameraPermissionTitle": "<PERSON><PERSON>'nin kameraya erişimine izin ver", "cameraPermission1": "Tarayıcı açılır penceresinde Rabby'nin kameraya erişimine izin ver", "allowRabbyPermissionsTitle": "Rabby'nin a<PERSON><PERSON>dak<PERSON> yetkilere sahip olmasını izin ver:", "ledgerPermission1": "Bir HID cihazına bağlan", "ledgerPermissionTip": "Lütfen aşağıda \"İzin Ver\"i tıklayın ve sonraki açılır pencerede Ledger'a erişimi on<PERSON>ın.", "permissionsAuthorized": "İzinler Onaylandı", "nowYouCanReInitiateYourTransaction": "Şimdi işleminizi yeniden başlatabilirsiniz.", "allow": "<PERSON><PERSON> V<PERSON>", "error": {"ethereum_app_unconfirmed_error": "Ethereum uygulamasını açma talebini reddettiniz.", "ethereum_app_open_error": "Lütfen Ledger cihazınıza Ethereum uygulamasını yükleyin/kabul edin.", "ethereum_app_not_installed_error": "Lütfen Ledger cihazınıza Ethereum uygulamasını yükleyin.", "running_app_close_error": "Ledger cihazınızdaki çalışan uygulamayı kapatma başarısız oldu."}}, "walletConnect": {"connectYour": "Bağla", "viaWalletConnect": "Wallet Connect ile", "connectedSuccessfully": "Başarıyla Bağlandı", "qrCodeError": "Lütfen ağınızı kontrol edin veya QR kodunu yenileyin", "qrCode": "QR kodu", "url": "URL", "changeBridgeServer": "Köpr<PERSON><PERSON>", "status": {"received": "<PERSON><PERSON> başarıl<PERSON>. <PERSON><PERSON> be<PERSON>", "rejected": "Bağlantı iptal edildi. QR kodunu tarayarak yeniden deneyin.", "brandError": "Yanlış cüzdan uygulaması.", "brandErrorDesc": "Lütfen bağlanmak için {{brandName}} kullanın", "accountError": "<PERSON><PERSON> eşleşmiyor.", "accountErrorDesc": "Lütfen mobil cüzdanınızda adresi değiştirin", "connected": "Bağlandı", "duplicate": "İçe aktarmaya çalıştığınız adres ikinci kopya", "default": "{{brand}} ile ta<PERSON><PERSON>n"}, "title": "{{brandName}} il<PERSON>", "disconnected": "Bağlantı Kesildi", "accountError": {}, "tip": {"accountError": {"tip1": "Bağlandı ancak imzalama yapılamıyor.", "tip2": "Lütfen mobil cüzdanınızdan doğru adrese geçiş yapın"}, "disconnected": {"tip": "{{brandName}}'a bağ<PERSON><PERSON> değil"}, "connected": {"tip": "{{brandName}}'a bağlı"}}, "button": {"disconnect": "Bağlantıyı Kes", "connect": "Bağlan", "howToSwitch": "<PERSON><PERSON><PERSON><PERSON>tirilir"}}, "hd": {"tooltip": {"removed": "<PERSON>res Rabby'den kaldırıldı", "added": "<PERSON><PERSON>'ye eklendi", "connectError": "Bağlantı durdu. Lütfen sayfayı yenileyerek tekrar bağlanmayı deneyin.", "disconnected": "Donanım cüzdanına bağlanılamıyor. Lütfen yeniden bağlanmayı deneyin."}, "waiting": "Bekleniyor", "clickToGetInfo": "Zincirden bilgi almak için tıklayın", "addToRabby": "<PERSON><PERSON>'ye <PERSON>", "basicInformation": "<PERSON><PERSON> bilgiler", "addresses": "<PERSON><PERSON><PERSON>", "loadingAddress": "{{0}}/{{1}} adres <PERSON>r", "notes": "Notlar", "getOnChainInformation": "Zincirden bilgi al", "hideOnChainInformation": "Zincirden alınan bilgiyi gizle", "usedChains": "Kullanılan zincirler", "firstTransactionTime": "İlk işlem zamanı", "balance": "Bakiye", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: Ledger'ın resmi HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adresler bulunmaktadır.", "bip44": "BIP44 Standartı: BIP44 protokolü tarafından tanımlanan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adresler bulunmaktadır.", "legacy": "Eski: MEW / Mycrypto tarafından kullanılan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adresler bulunmaktadır."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: Ledger'ın resmi HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adres bulunmamaktadır.", "bip44": "BIP44 Standartı: BIP44 protokolü tarafından tanımlanan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adres bulunmamaktadır.", "legacy": "Eski: MEW / Mycrypto tarafından kullanılan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adres bulunmamaktadır."}}, "trezor": {"hdPathType": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu.", "ledgerLive": "Ledger Live: Ledger resmi HD yolu.", "legacy": "Legacy: MEW / Mycrypto tarafından kullanılan HD path."}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu.", "ledgerLive": "Ledger Live: Ledger resmi HD yolu.", "legacy": "Legacy: MEW / Mycrypto tarafından kullanılan HD yolu."}, "message": {"disconnected": "{{0}}Bağlantı durdu. Lütfen sayfayı yenileyerek tekrar bağlanmayı deneyin."}}, "onekey": {"hdPathType": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu."}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu."}}, "mnemonic": {"hdPathType": {"default": "Varsayılan: Bir seed phrase içe aktarmak için kullanılan Varsayılan HD yolu.", "legacy": "Legacy: MEW / Mycrypto tarafından kullanılan HD path.", "ledgerLive": "Ledger Live: Ledger resmi HD yolu.", "bip44": "BIP44 Standardı: BIP44 protokolü tarafından tanımlanan HDpath."}, "hdPathTypeNoChain": {"default": "Varsayılan: Bir seed phrase içe aktarmak için kullanılan Varsayılan HD yolu."}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: Ledger'ın resmi HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adresler bulunmaktadır.", "bip44": "BIP44 Standartı: BIP44 protokolü tarafından tanımlanan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adresler bulunmaktadır.", "legacy": "Eski: MEW / Mycrypto tarafından kullanılan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adresler bulunmaktadır."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: Ledger'ın resmi HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adres bulunmamaktadır.", "bip44": "BIP44 Standartı: BIP44 protokolü tarafından tanımlanan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adres bulunmamaktadır.", "legacy": "Eski: MEW / Mycrypto tarafından kullanılan HD yolu. İlk 3 adreste, zincir üzerinde kullanılan adres bulunmamaktadır."}, "switch": {"title": "Yeni bir GridPlus cihazına geçin", "content": "Birden fazla GridPlus cihazının içe aktarılması desteklenmiyor. Yeni bir GridPlus cihazına geçiş yaparsanız, mevcut cihazın adres listesi içe aktarma işlemine başlamadan önce kaldırılacaktır."}, "switchToAnotherGridplus": "Başka bir GridPlus'a geçiş yap"}, "keystone": {"hdPathType": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu.", "legacy": "Legacy: MEW / Mycrypto tarafından kullanılan HD yolu.", "ledgerLive": "Ledger Live: Ledger resmi HD yolu. Ledger Live yolu ile yalnızca 10 adres yönetebilirsiniz."}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu."}, "hdPathTypeNochain": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HDpath.", "legacy": "Legacy: MEW / Mycrypto tarafından kullanılan HD yolu.", "ledgerLive": "Ledger Live: Ledger resmi HD yolu. Ledger Live yoluyla yalnızca 10 adres yönetebilirsiniz."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu."}, "hdPathTypeNoChain": {"bip44": "BIP44: BIP44 protokolü tarafından tanımlanan HD yolu."}, "disconnected": "BitBox02'ye bağlanılamıyor. Lütfen sayfayı yenileyerek tekrar bağlanmayı deneyin. Sebep: {{0}}"}, "selectHdPath": "HD yolu seç:", "selectIndexTip": "Başlangıç için adres serisini seçin:", "manageAddressFrom": "{{0}} ile {{1}} arasındaki adresi y<PERSON>net", "advancedSettings": "Gelişmiş <PERSON>", "customAddressHdPath": "Özel Adres HD yolu", "connectedToLedger": "Ledger'a Bağlandı", "connectedToTrezor": "Trezor'a Bağlandı", "connectedToOnekey": "OneKey'e Bağlandı", "manageSeedPhrase": "Seed Ph<PERSON><PERSON><PERSON>", "manageGridplus": "GridPlus'ı Yönet", "manageKeystone": "Keystone'u Yönet", "manageAirgap": "AirGap'i Yönet", "manageCoolwallet": "CoolWallet'ı Yönet", "manageBitbox02": "BitBox02'yi <PERSON>", "manageNgraveZero": "NGRAVE ZERO'u Yönet", "done": "<PERSON><PERSON>", "addressesIn": "{{0}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "addressesInRabby": "<PERSON><PERSON><PERSON><PERSON><PERSON>{{0}}", "qrCode": {"switch": {"title": "<PERSON><PERSON> bir {{0}} cihazına geçin", "content": "Birden fazla {{0}} cihazının içe aktarımı desteklenmiyor. Yeni bir {{0}} cihazına geçiş yaparsanız, mevcut cihazın adres listesi içe aktarma işlemine başlamadan önce kaldırılacaktır."}, "switchAnother": "Başka bir {{0}}'a geçiş yap"}, "importBtn": "<PERSON><PERSON><PERSON> Aktar ({{count}})", "manageImtokenOffline": "imToken'i Yönetin", "manageImKey": "imKey'i Yönet"}, "importYourKeystore": "KeyStore'unuzu İçe Aktarın", "incorrectPassword": "yanlış şifre", "keystore": {"description": "İçe aktarmak istediğiniz keystore dosyasını seçin ve ilgili şifreyi girin", "password": {"required": "Lütfen Şifre G<PERSON>n", "placeholder": "Şifre"}}, "coboSafe": {"inputSafeModuleAddress": "Safe Module adresini girin", "invalidAddress": "Geçersiz adres", "whichChainIsYourCoboAddressOn": "Cobo adresiniz hangi zincirde", "addCoboArgusAddress": "Cobo Argus adresini ekleyin", "findTheAssociatedSafeAddress": "İlişkilendirilmiş safe adresini bulun", "import": "İçe Aktar"}, "imkey": {"title": "imKey'e Bağlan", "imkeyPermissionTip": "Lütfen aşağıdaki \"Allow\" dü<PERSON><PERSON>ine tıklayın ve ardından açılan pencerede imKey'inize erişim izni verin."}, "keystone": {"deviceIsBusy": "<PERSON><PERSON><PERSON>", "keystonePermission1": "Bir USB cihazına bağlan", "exportAddressJustAllowedOnHomePage": "Adres yalnızca ana sayfada dışa aktarılmasına izin verildi", "allowRabbyPermissionsTitle": "<PERSON><PERSON> ver:", "unknowError": "Bilinmeyen hata, lü<PERSON><PERSON> tekrar deneyin", "deviceRejectedExportAddress": "<PERSON><PERSON> ile bağlantıy<PERSON> onayla", "deviceIsLockedError": "<PERSON><PERSON><PERSON>i g<PERSON> kili<PERSON> a<PERSON>ın", "noDeviceFoundError": "Tek bir Keystone takın", "keystonePermissionTip": "Lütfen aşağıdaki açılır pencerede Keystone erişimini yetkilendirmek için \"İzin Ver\" düğmesine tıklayın ve Keystone 3 Pro'nuzun ana sayfada olduğundan emin olun.", "title": "Keystone'u Bağla"}, "firefoxLedgerDisableTips": "<PERSON><PERSON>, Firefox ile uyumlu değil", "addFromCurrentSeedPhrase": "Mevcut Seed Phrase'ten Ekle"}, "unlock": {"btn": {"unlock": "<PERSON><PERSON><PERSON>"}, "password": {"required": "Kilidi Açmak İçin Şifre Girin", "placeholder": "Kilidi Açmak İçin Şifre Girin", "error": "yanlış şifre"}, "btnForgotPassword": "Şifrenizi mi unuttunuz?", "title": "<PERSON><PERSON>", "description": "Ethereum ve tüm EVM zincirleri için çığır açan cüzdan"}, "addToken": {"noTokenFound": "Token bulunamadı", "tokenSupported": "<PERSON><PERSON>, <PERSON><PERSON> destekleniyor", "tokenCustomized": "Mevcut token zaten özel olarak eklendi", "tokenNotFound": "Bu kontrat adresinden token bulunamadı", "title": "Rabby'ye özel token ekleyin", "balance": "Bakiye", "tokenOnMultiChains": "Token adresi birden fazla zincirde bulunuyor. Lütfen bir tanesini seçin", "noTokenFoundOnThisChain": "Bu zincirde token bulunamadı", "hasAdded": "Bu token eklendi."}, "switchChain": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainNotSupport": "İstenen zincir Rabby tarafından henüz desteklenmiyor", "testnetTip": "Testnet'lere bağlanmadan önce \"Daha Fazla\" altında \"Testnet'le<PERSON>\" se<PERSON><PERSON><PERSON><PERSON> açın", "requestsReceived": "1 request received", "unknownChain": "Bilinmeyen zincir", "requestsReceivedPlural": "{{count}} istek alındı", "chainNotSupportYet": "İstenen zincir henüz Rabby tarafından desteklenmiyor.", "addChain": "Testnet Ekle", "requestRabbyToSupport": "Rabby'den Destek İsteyin", "chainId": "<PERSON><PERSON><PERSON><PERSON>:", "chainNotSupportAddChain": "İstenen zincir Rabby tarafından henüz entegre edilmedi. Bunu Özel Testnet olarak ekleyebilirsiniz.", "desc": "İstenen ağ henüz Rabby tarafından entegre edilmedi. <PERSON> olarak özel bir ağ olarak ekleyebilirsiniz."}, "signText": {"title": "<PERSON><PERSON>", "message": "<PERSON><PERSON>", "createKey": {"interactDapp": "Dapp ile Etkileşimde Bulun", "description": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "sameSafeMessageAlert": "Aynı mesaj <PERSON>; ek bir imza gerekmez."}, "securityEngine": {"yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "whenTheValueIs": "{{value}} <PERSON><PERSON><PERSON>", "currentValueIs": "<PERSON>v<PERSON> değer {{value}}", "viewRules": "Güvenlik kurallarını görüntüle", "undo": "<PERSON><PERSON>", "riskProcessed": "Risk İşlendi", "ignoreAlert": "Uyarıyı Yoksay", "ruleDisabled": "Güvenlik kuralları devre dışı bırakıldı. Güvenliğiniz için istediğiniz zaman tekrar açabilirsiniz.", "unknownResult": "Güvenlik kuralı kullanılamadığından sonuç bilinmiyor", "alertTriggerReason": "Uyarının tetikleme sebebi:", "understandRisk": "<PERSON><PERSON> an<PERSON>r ve her türlü kayıp için so<PERSON>luluğu kabul ediyorum", "forbiddenCantIgnore": "Görmezden gelinemeyecek, yasaklı risk bulundu.", "ruleDetailTitle": "Kural Detayı", "enableRule": "Kuralı Etkinleştir", "viewRiskLevel": "Risk sevi<PERSON><PERSON> g<PERSON>"}, "connect": {"listedBy": "Listeleyen", "sitePopularity": "Site Popülerliği", "myMark": "<PERSON><PERSON>", "flagByRabby": "<PERSON><PERSON>", "flagByMM": "MetaMask Tarafından İşaretlendi", "flagByScamSniffer": "ScamSniffer <PERSON>ından İşaretlendi", "verifiedByRabby": "<PERSON><PERSON>", "foundForbiddenRisk": "Yasaklı riskler bulundu. Bağlantı engellendi.", "markAsTrustToast": "\"Güven<PERSON>r\" o<PERSON><PERSON>", "markAsBlockToast": "\"Engellenmiş\" olarak işaretle", "markRemovedToast": "İşaret kaldırıldı", "title": "Dapp'a Bağlan", "selectChainToConnect": "Bağlanmak için bir zincir seçin", "markRuleText": "<PERSON><PERSON>", "connectBtn": "Bağlan", "noWebsite": "Yok", "popularLevelHigh": "<PERSON><PERSON><PERSON><PERSON>", "popularLevelMedium": "Orta", "popularLevelLow": "Düşük", "popularLevelVeryLow": "Çok Düşük", "noMark": "İşaret Yok", "blocked": "<PERSON>gel<PERSON>di", "trusted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addedToWhitelist": "Beyaz listeye eklendi", "addedToBlacklist": "Kara listeye eklendi", "removedFromAll": "Tüm listelerden kaldırıldı", "notOnAnyList": "<PERSON><PERSON><PERSON> bi<PERSON> <PERSON><PERSON>", "onYourBlacklist": "<PERSON>", "onYourWhitelist": "<PERSON><PERSON>", "manageWhiteBlackList": "<PERSON><PERSON> listeyi/kara listeyi y<PERSON>net", "SignTestnetPermission": {"title": "İmzalama izni"}, "ignoreAll": "Tümünü <PERSON>", "SelectWallet": {"title": "Bir Cüzdan Seç ve Bağlan", "desc": "Yükledi<PERSON><PERSON>z cü<PERSON>nlardan seçim ya<PERSON>ın"}, "otherWalletBtn": "Başka Bir Cüzdan ile Bağlan", "connectAddress": "Bağlantı Adresi\n"}, "addressDetail": {"add-to-whitelist": "Beyaz Listeye Ekle", "remove-from-whitelist": "Beyaz Listeden Kaldır", "address-detail": "<PERSON><PERSON>", "backup-private-key": "Özel Anahtarı Yedekle", "backup-seed-phrase": "Seed Phrase'<PERSON>", "delete-address": "<PERSON><PERSON><PERSON>", "delete-desc": "<PERSON><PERSON><PERSON><PERSON> ö<PERSON>, varlıklarınızı nasıl koruyacağınızı anlamak için aşağıdaki noktaları göz önünde bulundurun.", "direct-delete-desc": "Bu adres bir {{renderBrand}} <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> bu adres için <PERSON> anahtarınızı veya seed phrase'inizi sa<PERSON>, sadece sile<PERSON>", "admins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tx-requires": "<PERSON><PERSON><PERSON> bir <PERSON><PERSON><PERSON> <2>{{num}}</2> onay gere<PERSON>", "edit-memo-title": "<PERSON><PERSON> not<PERSON> d<PERSON>", "please-input-address-note": "Lütfen adres notunu girin", "address": "<PERSON><PERSON>", "address-note": "<PERSON><PERSON>", "assets": "Varlıklar", "qr-code": "QR Kod", "source": "<PERSON><PERSON><PERSON>", "hd-path": "HD Yolu", "manage-seed-phrase": "Seed Ph<PERSON><PERSON><PERSON>", "manage-addresses-under-this-seed-phrase": "Bu Seed Phrase altındaki adresleri yönet", "safeModuleAddress": "Safe Mo<PERSON><PERSON>", "coboSafeErrorModule": "<PERSON><PERSON>in süresi doldu, lütfen adresi silin ve tekrar içe aktarın.", "importedDelegatedAddress": "İçe Aktarılan Delege Adresi", "manage-addresses-under": "Bu {{brand}} altındaki adresleri yönetin"}, "preferMetamaskDapps": {"title": "MetaMask Tercih<PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>ler, hangi cüzdanı seçtiğinizden bağımsız olarak MetaMask üzerinden bağlı kalacak", "howToAdd": "<PERSON><PERSON><PERSON><PERSON>", "howToAddDesc": "Web sitesine sağ tıklayın ve bu seçeneği bulun", "empty": "Dapp yok"}, "customRpc": {"opened": "Açık", "closed": "<PERSON><PERSON><PERSON>", "empty": "Özel RPC URL yok", "title": "RPC URL'sini <PERSON>r", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, özel RPC Rabby'nin düğü<PERSON>ünü değiştirecektir. Rabby'nin düğümünü kullanmaya devam etmek için özel RPC'yi silin.", "add": "RPC URL'yi <PERSON>", "EditRPCModal": {"invalidRPCUrl": "Geçersiz RPC URL'i", "invalidChainId": "Geçersiz Zincir ID'si", "rpcAuthFailed": "RPC doğrulaması başarısız", "title": "RPC URL'yi <PERSON>", "rpcUrl": "RPC URL", "rpcUrlPlaceholder": "RPC URL'sini girin"}, "EditCustomTestnetModal": {"quickAdd": "Chainlist'ten hızlı ekle", "title": "<PERSON>zel <PERSON>"}}, "requestDebankTestnetGasToken": {"title": "DeBank Testnet Gas Tokeni İste", "mintedTip": "Rabby Rozeti sahipleri günde bir kez istekte bulunabilir", "notMintedTip": "<PERSON><PERSON><PERSON>i talep edebilir", "claimBadgeBtn": "<PERSON><PERSON>", "time": "Günde bir kez", "requested": "<PERSON><PERSON><PERSON>n talepte bulund<PERSON>uz", "requestBtn": "İstek"}, "safeQueue": {"title": "Queue", "sameNonceWarning": "Bu işlemler aynı nonce değerini kullandığı için çakışıyor.                   Biri gerçekleştiğinde otomatik olarak diğer(ler)inin yerini alacak.", "loading": "Bekleyen işlemler yükleniyor", "noData": "Bekleyen işlem yok", "loadingFaild": "Safe sunucusunun değişkenliği nedeniyle veriler mevcut değil, lütfen 5 dakika sonra tekrar kontrol edin", "accountSelectTitle": "<PERSON>u i<PERSON><PERSON>i herhangi bir adresi kull<PERSON>rak gönderebilirsiniz", "LowerNonceError": "Nonce'u {{nonce}} olan işlem önce gerçekleştirilmelidir", "submitBtn": "İşlemi Gönder", "unknownTx": "Bilinmeyen İşlem", "cancelExplain": "{{protocol}} için {{token}} Onayını İptal Et", "unknownProtocol": "Bilinmeyen protokol", "approvalExplain": "{{protocol}} için {{count}} {{token}} <PERSON><PERSON>la", "unlimited": "sınırsız", "action": {"send": "<PERSON><PERSON><PERSON>", "cancel": "Bekleyen İşlemi İptal Et"}, "viewBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReplacePopup": {"options": {"send": "<PERSON><PERSON>", "reject": "İşlemi Reddet"}, "desc": "İmzalanmış bir işlem kaldırılamaz ancak aynı nonce’a sahip yeni bir işlemle değiştirilebilir.", "title": "Bu işlemi nasıl değiştireceğinizi seçin"}, "replaceBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "importSuccess": {"title": "Başarıyla İçe Aktarıldı", "addressCount": "{{count}} adres", "gnosisChainDesc": "Bu adres {{count}} zincirde bulundu"}, "backupSeedPhrase": {"title": "Seed Phrase'<PERSON>", "alert": "<PERSON><PERSON> <PERSON>, var<PERSON><PERSON>klar<PERSON>n<PERSON>za eri<PERSON><PERSON> i<PERSON> yet<PERSON> anah<PERSON>ı<PERSON>. <PERSON><PERSON><PERSON>yin veya başkalarıyla <PERSON>, aksi tak<PERSON>de varlıklarınızı sonsuza dek kaybedebilirsiniz. Lütfen güvenli bir ortamda görüntüleyin ve dikkatlice saklayın.", "clickToShow": "Seed Phrase'i göstermek için tıklayın", "copySeedPhrase": "Seed phrase'i kopyala", "qrCodePopupTips": "Asla seed phrase QR kodunu başkalarıyla paylaşmayın. Lütfen güvenli bir ortamda görüntüleyin ve dikkatlice saklayın.", "qrCodePopupTitle": "QR Code", "showQrCode": "QR Kodunu Gö<PERSON>"}, "backupPrivateKey": {"title": "Özel Anahtarı Yedekle", "alert": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON> i<PERSON> yet<PERSON> anah<PERSON>ı<PERSON>. <PERSON>betmeyin veya başkaları<PERSON>, aksi tak<PERSON>de varlıklarınızı sonsuza dek kaybedebilirsiniz. Lütfen güvenli bir ortamda görüntüleyin ve dikkatlice saklayın.", "clickToShow": "Özel anahtarı göstermek için tıklayın", "clickToShowQr": "Özel anahtar QR Kodunu göstermek için tıklayın"}, "ethSign": {"alert": "'eth_sign' ile imzal<PERSON> varlık kaybına yol açabilir. Güvenliğiniz için             Rabby bu yöntemi desteklememektedir."}, "createPassword": {"title": "<PERSON><PERSON><PERSON>", "passwordRequired": "Lütfen Şifre G<PERSON>n", "passwordMin": "Şifre en az 8 karakter uzunluğunda olmalıdır", "passwordPlaceholder": "Şifre en az 8 karakter uzunluğunda olmalıdır", "confirmRequired": "Lütfen Şifreyi Onaylayın", "confirmError": "<PERSON><PERSON><PERSON><PERSON>", "confirmPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "agree": "Okudum ve <1/> <2>Kullanım Şartları</2>'nı kabul ediyorum"}, "welcome": {"step1": {"title": "<PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>'ın des<PERSON>kled<PERSON>ği tüm <PERSON>lere ba<PERSON>r"}, "step2": {"title": "<PERSON><PERSON>", "desc": "<PERSON>zel anahtarlar yalnızca sizin erişebileceğiniz <PERSON>, yerel olarak saklanır", "btnText": "Başlamak için <PERSON>"}}, "importSafe": {"title": "Safe adresi ekle", "placeholder": "Lütfen adresi girin", "error": {"invalid": "Geçerli bir ad<PERSON>", "required": "Lütfen adresi girin"}, "loading": "Bu adresin yayınlandığı zincir aranıyor", "gnosisChainDesc": "Bu adres {{count}} zincirde bulundu"}, "importQrBase": {"desc": "{{brandName}} donanım cüzdanındaki QR kodunu tarayın", "btnText": "<PERSON><PERSON><PERSON>"}, "bridge": {"showMore": {"title": "<PERSON><PERSON>", "source": "Köprü Kaynağı"}, "settingModal": {"confirmModal": {"title": "Bu Toplayıcı ile Ticaret Yapmayı Etkinleştir", "tip1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>şmeyle etkileşime gireceksiniz.", "tip2": "2. <PERSON><PERSON>, bu <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n sözleşmesinden kaynaklanan herhangi bir riskten sorumlu değildir.", "i-understand-and-accept-it": "<PERSON><PERSON><PERSON><PERSON> ve kabul ediyorum"}, "confirm": "<PERSON><PERSON><PERSON>", "SupportedBridge": "Desteklenen Bridge:", "title": "Bridge Agregatörlerini ticaret i<PERSON><PERSON>"}, "tokenPairDrawer": {"tokenPair": "Token Pair", "balance": "Bakiye Değeri", "noData": "Desteklenen Token Çifti Yok", "title": "Desteklenen token çiftinden seçin"}, "bridgeTo": "Bridge To", "estimate": "<PERSON><PERSON><PERSON>:", "Amount": "<PERSON><PERSON><PERSON>", "getRoutes": "Rotaları al", "history": "Bridge geçmişi", "gas-fee": "GasFee: {{gasUsed}}", "To": "<PERSON><PERSON>in", "tokenPairPlaceholder": "Token Çifti Seç", "title": "Köprü", "Balance": "Bakiye: ", "the-following-bridge-route-are-found": "<PERSON><PERSON><PERSON> rota", "no-quote-found": "Teklif bulunamadı. Lütfen diğer token çiftlerini deneyin.", "gas-x-price": "Gas fiyatı: {{price}} Gwei.", "unlimited-allowance": "Sınırsız allowance", "actual": "Mevcut:", "detail-tx": "Detay", "From": "İtibaren", "Pending": "Beklemede", "no-quote": "<PERSON><PERSON><PERSON><PERSON>", "no-transaction-records": "İşlem kaydı yok", "Select": "Seç", "Completed": "Tamamlandı", "BridgeTokenPair": "Bridge Token Pair", "insufficient-balance": "<PERSON><PERSON><PERSON>", "via-bridge": "{{bridge}} aracılığıyla", "best": "En İyi", "approve-and-bridge": "<PERSON><PERSON><PERSON> ve Bridge", "completedTip": "Zincir üzerinde işlem, kaydı oluşturmak için verileri çözme", "bridge-cost": "Bridge cost", "rabby-fee": "<PERSON><PERSON>", "est-difference": "<PERSON><PERSON><PERSON>:", "duration": "{{duration}} dk", "aggregator-not-enabled": "<PERSON><PERSON>, si<PERSON> tarafınızdan ticaret yapmak için etkinleştirilmedi.", "approve-x-symbol": "<PERSON><PERSON><PERSON> {{symbol}}", "price-impact": "<PERSON><PERSON>t <PERSON>", "est-payment": "<PERSON><PERSON><PERSON>:", "bridge-via-x": "{{name}} üstünde köprü", "price-expired-refresh-route": "Fiyat süresi doldu. Rotayı yenileyin.", "est-receiving": "<PERSON><PERSON><PERSON>:", "loss-tips": "{{usd}} kaybediyorsunuz. Farklı bir miktar deneyin.", "estimated-value": "≈ {{value}}", "recommendFromToken": "<1></1>'dan bir köprüden kullanılabilir bir teklif al", "max-tips": "<PERSON><PERSON>, köprü için gaz maliyeti çıkarılarak hesaplanır.", "enable-it": "Etkinleştir", "no-route-found": "Rota bulunamadı", "slippage-adjusted-refresh-quote": "滑点已调整。刷新路线。", "select-chain": "<PERSON><PERSON><PERSON><PERSON>", "pendingTip": "Tx gönderildi. Tx uzun saatler boyunca beklemede kalı<PERSON>, <PERSON><PERSON><PERSON><PERSON> beklemeyi temizlemeyi deneyebilirsiniz.", "need-to-approve-token-before-bridge": "Bridge öncesinde token onayı gerekiyor"}, "pendingDetail": {"Header": {"predictTime": "<PERSON><PERSON><PERSON> edilen p<PERSON>"}, "TxStatus": {"pendingBroadcasted": "Beklemede: Ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingBroadcast": "Bekliyor: Yayınlanacak", "completed": "Tamamlandı", "reBroadcastBtn": "<PERSON><PERSON><PERSON> ya<PERSON>ınla"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcastedCount_ordinal_few": "{{count}}rd yayın", "broadcastedCount_ordinal_other": "{{count}}inci yayın", "broadcastedCount_ordinal_two": "{{count}}. yayın", "created": "İşlem oluşturuldu", "broadcasted": "<PERSON> zaman<PERSON>a ya<PERSON>ı<PERSON>ı", "broadcastedCount_ordinal_one": "{{count}}. yayın", "pending": "Durum kontrol ediliyor..."}, "MempoolList": {"col": {"txStatus": "İşlem durumu", "nodeName": "<PERSON><PERSON><PERSON><PERSON><PERSON> adı", "nodeOperator": "Düğüm operatörü"}, "txStatus": {"appeared": "Ortaya çıktı", "appearedOnce": "Bir kez göründü", "notFound": "Bulunamadı"}, "title": "{{count}} RPC düğümlerinde göründü"}, "PendingTxList": {"filterBaseFee": {"label": "Yalnızca Base fee gereksinimini karşılar", "tooltip": "Yalnızca Gas Fiyatı blokun Taban ücreti gereksinimlerini karşılayan işlemleri göster"}, "col": {"gasPrice": "Gas Price", "actionType": "<PERSON><PERSON><PERSON>", "balanceChange": "Bakiye değişikliği", "interact": "Etkileşime geç", "action": "İşlem Eylemi"}, "titleNotFound": "<PERSON><PERSON><PERSON> Bekleyen İşlemlerde Sıra Yok", "title": "<PERSON><PERSON><PERSON>, tüm be<PERSON>en Txs'te #{{rank}} s<PERSON><PERSON>.", "titleSameNotFound": "Şu Anki ile Aynı Olanların Sıralaması Yok", "titleSame": "GasPrice Mevcut ile Aynı Sırada #{{rank}}"}, "Empty": {"noData": "<PERSON><PERSON> bulunamadı"}, "PrePackInfo": {"col": {"prePackContent": "Önceden paketlenmiş içerik", "expectations": "<PERSON><PERSON><PERSON><PERSON>", "prePackResults": "Önceden paketleme sonuçları", "difference": "Sonuçları kontrol et"}, "type": {"receive": "<PERSON><PERSON><PERSON>", "pay": "Ö<PERSON>"}, "noError": "Hata bulunamadı", "error": "{{count}} hata bulundu", "loss": "{{lossCount}} kayıp bulundu", "title": "<PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON> en son blokta y<PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON> {{time}}", "noLoss": "<PERSON><PERSON>p bulunamadı"}, "Predict": {"predictFailed": "Paketleme süresi tahmini başarısız oldu", "skipNonce": "Adresinizin Ethereum zincirinde Nonce atlandığı için mevcut işlem tamamlanamıyor.", "completed": "İşlem Tamamlandı"}}, "dappSearch": {"searchResult": {"totalDapps": "Toplam <2>{{count}}</2> Dapp", "foundDapps": "<2>{{count}}</2> Dapp bulundu"}, "favorite": "<PERSON><PERSON><PERSON><PERSON>", "emptySearch": "Dapp Bulunamadı", "expand": "Genişlet", "selectChain": "<PERSON><PERSON><PERSON><PERSON>", "emptyFavorite": "<PERSON><PERSON><PERSON>", "listBy": "Dapp listelendi tarafından"}, "rabbyPoints": {"claimItem": {"claimed": "<PERSON><PERSON> edildi", "disabledTip": "<PERSON><PERSON> anda talep edilecek puan yok", "go": "<PERSON><PERSON><PERSON><PERSON>", "claim": "Talep Et", "earnTip": "Günde bir kez sınır. Lütfen puanları 00:00 UTC+0'dan sonra kazanın."}, "claimModal": {"rabbyValuedUserBadge": "<PERSON><PERSON>llanıcı Rozeti", "invalid-code": "geçersiz kod", "claim": "<PERSON><PERSON>", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "cantUseOwnCode": "Kendi referral kodunuzu kullanamazsınız.", "title": "Başlangıç Puanlarını Talep Edin", "addressBalance": "<PERSON>ü<PERSON><PERSON> bakiyesi", "walletBalance": "Cüzdan Bakiyesi", "MetaMaskSwap": "MetaMask Swap", "activeStats": "Akt<PERSON>", "season2": "Season 2", "rabbyUser": "<PERSON><PERSON>", "placeholder": "Ekstra puanlar için Tavsiye Kodunu girin (isteğe bağlı)", "snapshotTime": "<PERSON><PERSON><PERSON><PERSON> gör<PERSON><PERSON><PERSON> z<PERSON>: {{time}}", "referral-code": "<PERSON><PERSON><PERSON>"}, "referralCode": {"verifyAddressModal": {"cancel": "İptal", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "Lütfen bu adresin sahibi olduğunuzu doğrulamak için bu metin mesajını imzalayın", "sign": "İmzala", "verify-address": "<PERSON><PERSON>"}, "referral-code-already-exists": "Referans kodu zaten mevcut", "referral-code-cannot-exceed-15-characters": "Referans kodu 15 karakteri geçemez", "referral-code-cannot-be-empty": "Referans kodu bo<PERSON> o<PERSON>az", "refer-a-new-user-to-get-50-points": "Yeni bir kullanıcı yönlendirerek 50 puan kazanın", "set-my-code": "<PERSON><PERSON><PERSON>", "max-15-characters-use-numbers-and-letters-only": "Maks<PERSON><PERSON> 15 karakter, sadece rakam ve harf kullanın.", "set-my-referral-code": "Referans kodumu a<PERSON>la", "referral-code-available": "Referans kodu mevcut", "my-referral-code": "Referans kodum", "confirm": "<PERSON><PERSON><PERSON>", "once-set-this-referral-code-is-permanent-and-cannot-change": "Bir kez ayarlandığı<PERSON>, bu referans kodu kalıcıdır ve değiştirilemez."}, "earn-points": "<PERSON><PERSON>", "referral-code-copied": "Referans kodu kopyalandı", "top-100": "İlk 100", "title": "<PERSON><PERSON>", "share-on": "Paylaş", "out-of-x-current-total-points": "Toplam Dağıtılan Puanlardan {{total}}", "secondRoundEnded": "🎉 Rabby Points ikinci turu sona erdi", "firstRoundEnded": "🎉 <PERSON><PERSON> Rabby <PERSON>ı turu sona erdi", "initialPointsClaimEnded": "<PERSON><PERSON> talep süresi sona erdi", "code-set-successfully": "Referans kodu başarıyla ayarlandı"}, "customTestnet": {"CustomTestnetForm": {"nativeTokenSymbolRequired": "Lütfen para birimi simgesini girin", "id": "<PERSON><PERSON><PERSON><PERSON>", "rpcUrlRequired": "Lütfen RPC URL'si giriniz", "name": "<PERSON><PERSON> adı", "nativeTokenSymbol": "Para birimi sembolü", "rpcUrl": "RPC URL", "nameRequired": "Lütfen ağ adını girin", "idRequired": "Lütfen zincir kimliğini girin", "blockExplorerUrl": "Blok gezgini URL'si (İsteğe bağlı)"}, "AddFromChainList": {"tips": {"added": "Bu zinciri zaten eklediniz", "supported": "<PERSON><PERSON><PERSON><PERSON> Rabby Wallet tarafından entegre edilmiştir."}, "empty": "<PERSON><PERSON><PERSON>r b<PERSON>namadı", "search": "Özel ağ adı veya ID ara", "title": "Chainlist'ten hızlı ekle"}, "signTx": {"title": "İşlem Verileri"}, "ConfirmModifyRpcModal": {"desc": "<PERSON><PERSON><PERSON>r zaten Rabby tarafından entegre edilmiştir. RPC URL'sini değiştirmek istiyor musunuz?"}, "title": "<PERSON><PERSON>", "add": "<PERSON>zel <PERSON>", "empty": "Özel Ağ Yok", "currency": "Para Birimi", "id": "ID", "desc": "<PERSON><PERSON>, <PERSON>zel ağların güvenliğini doğrulayamaz. Lütfen yalnızca güvenilir ağlar ekleyin."}, "addChain": {"desc": "Rabby özel ağların güvenliğini doğrulayamaz. Lütfen yalnızca güvenilir ağlar ekleyin.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sign": {"transactionSpeed": "İşlem Hızı"}, "ecology": {"sonic": {"home": {"earnBtn": "Çok yakında", "socialsTitle": "Katılın", "migrateTitle": "Taşınmak", "migrateDesc": "→", "arcadeBtn": "<PERSON><PERSON> o<PERSON>", "earnTitle": "Kazanın", "migrateBtn": "<PERSON><PERSON><PERSON><PERSON> geliyor", "airdrop": "Airdrop", "earnDesc": "$S'nizi stake edin", "airdropBtn": "<PERSON><PERSON>n", "airdropDesc": "Opera ve Sonic'teki k<PERSON> ~200 milyon S.", "arcadeDesc": "Ücretsiz oyunlar oynayarak S airdrop'u için puan kazanın."}, "points": {"today": "<PERSON><PERSON><PERSON><PERSON>", "referralCodeCopied": "Tavsiye kodu kopyalandı", "sonicArcadeBtn": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>", "shareOn": "Paylaş", "sonicArcade": "Sonic Arcade", "sonicPoints": "Sonic Points", "referralCode": "Referans kodu", "getReferralCode": "Tavsi<PERSON> kodunu alın", "errorTitle": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON>", "errorDesc": "Puanlarınız yüklenirken bir hata oluştu. Lütfen tekrar deneyin.", "pointsDashboardBtn": "<PERSON><PERSON> kazan<PERSON>ya ba<PERSON><PERSON><PERSON>n", "pointsDashboard": "<PERSON><PERSON><PERSON>"}}, "dbk": {"home": {"mintNFTDesc": "DBK Chain'in tanığı olun", "bridge": "DBK Zincirine <PERSON>", "mintNFTBtn": "<PERSON><PERSON><PERSON>", "bridgePoweredBy": "OP Superchain tarafından desteklenmektedir", "bridgeBtn": "Köprü", "mintNFT": "DBK Genesis NFT Bastır"}, "bridge": {"tabs": {"withdraw": "Çek<PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"toAddress": "<PERSON><PERSON>", "gasFee": "Gas fee", "completeTime": "<PERSON><PERSON><PERSON><PERSON>", "receiveOn": "{{chainName}} üzerinde alın"}, "error": {"notEnoughBalance": "<PERSON><PERSON><PERSON>"}, "ActivityPopup": {"status": {"readyToProve": "Kanıtlamaya hazır", "withdraw": "<PERSON><PERSON><PERSON>", "waitingToProve": "Durum kökü yayınlandı", "rootPublished": "Durum kökü yayınlandı", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "proved": "Kanıtlandı", "challengePeriod": "<PERSON><PERSON><PERSON>", "claimed": "<PERSON><PERSON> edildi", "readyToClaim": "Ödeme almaya hazır"}, "withdraw": "<PERSON><PERSON><PERSON>", "empty": "Henüz etkinlik yok", "claimBtn": "Talep Et", "title": "Aktiviteler", "proveBtn": "Kanıtla", "deposit": "Para Yatırma"}, "WithdrawConfirmPopup": {"step3": "Ethereum Üzerinde Talep Et", "btn": "<PERSON><PERSON><PERSON>", "step2": "Ethereum üzerinde kanıtla", "title": "DBK Chain Çekim işlemi ~7 gün sürer", "step1": "<PERSON><PERSON><PERSON><PERSON>", "question2": "Para çekme işlemi başlatıldıktan sonra hızlandırılamayacağını veya iptal edilemeyeceğini anlıyorum", "question1": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, <PERSON><PERSON>me işlemini kanıtladıktan sonra fonlarımın Ethereum'da talep edilebilir hale gelmesi yaklaşık 7 gün sürecek.", "tips": "<PERSON><PERSON><PERSON>, 1 DBK Chain işlemi ve 2 Ethereum işlemi gerektiren 3 aşamalı bir süreç içerir。", "question3": "Ağ ücretlerinin yaklaşık olduğunu ve değişeceğini anlıyorum"}, "labelTo": "<PERSON><PERSON>in", "labelFrom": "<PERSON><PERSON><PERSON><PERSON>"}, "minNFT": {"myBalance": "Bakiyem", "mintBtn": "<PERSON><PERSON><PERSON>", "minted": "Basıldı", "title": "DBK Genesis"}}}, "miniSignFooterBar": {"status": {"txCreated": "İşlem oluşturuldu", "txSending": "<PERSON><PERSON><PERSON> gönderiliyor", "txSigned": "İmzalandı. İşlem oluşturuluyor", "txSendings": "İmzalama isteği g<PERSON>r ({{current}}/{{total}})"}, "signWithLedger": "Ledger ile imzala"}, "gasAccount": {"history": {"noHistory": "Geçmiş yok"}, "loginInTip": {"login": "GasAccount ile giriş yap", "gotIt": "<PERSON><PERSON><PERSON><PERSON>", "desc": "Tüm Zincirlerde Gas Ücretlerini Ödeyin", "title": "USDC / USDT Yatırın"}, "loginConfirmModal": {"title": "Mevcut Adres ile Giri<PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sadece bu adrese yatırab<PERSON><PERSON><PERSON>z"}, "logoutConfirmModal": {"logout": "Çıkış yap", "title": "Geçerli GasAccount'tan çıkış yap", "desc": "<PERSON><PERSON><PERSON>, GasAccount'u devre dışı bırakır. Bu adresle giriş yaparak GasAccount'unuzu geri yükleyebilirsiniz."}, "depositPopup": {"selectToken": "Mevduat İçin Token Seçin", "token": "Token", "amount": "<PERSON><PERSON><PERSON>", "desc": "Rabby'nin DeBank L2 Hesabına ekstra ücret ödemeden para yatırın—istediğiniz zaman çekin.", "title": "Para Yatırma", "invalidAmount": "500'ün altında olmalı"}, "withdrawPopup": {"withdrawalLimit": "Para çekme limiti", "selectRecipientAddress": "Alıcı adresini seç", "selectDestinationChain": "<PERSON><PERSON><PERSON> se<PERSON>", "selectChain": "<PERSON><PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON>in", "title": "Para Çekme", "amount": "<PERSON><PERSON><PERSON>", "noEnoughGas": "<PERSON><PERSON><PERSON>, gas ücretlerini karşılamak için çok düşük", "desc": "GasAccount bakiyenizi DeBank L2 Cüzdanınıza çekebilirsiniz. Gerektiğinde desteklenen bir blokzincire para transferi yapmak için DeBank L2 Cüzdanınıza giriş yapın.", "riskMessageFromAddress": "Risk kontrol<PERSON>, ç<PERSON><PERSON> limiti bu adresin toplam yatırd<PERSON><PERSON><PERSON> miktara bağlıdır.", "noEnoughValuetBalance": "Kasadaki Bakiye yetersiz. <PERSON><PERSON><PERSON><PERSON> veya daha sonra tekrar deneyin.", "riskMessageFromChain": "Risk kontrolü <PERSON>, ç<PERSON><PERSON> limiti bu zincirden yatırılan toplam miktara bağlıdır.", "destinationChain": "<PERSON><PERSON><PERSON>", "selectAddr": "<PERSON><PERSON>", "recipientAddress": "Alıcı adresi", "noEligibleAddr": "Para çekim için u<PERSON>gun adres yok", "noEligibleChain": "<PERSON><PERSON><PERSON> i<PERSON> uygun zincir yok", "deductGasFees": "Alınan tutardan gas ücretleri düşülecektir"}, "withdrawConfirmModal": {"title": "DeBank L2 Cüzdanınıza aktarıldı", "button": "DeBank'te Görüntüle"}, "GasAccountDepositTipPopup": {"title": "GasAccount'u Aç ve Yatır", "gotIt": "<PERSON><PERSON><PERSON><PERSON>"}, "switchLoginAddressBeforeDeposit": {"desc": "Lütfen giriş adresinize geçin.", "title": "Para yatırmadan önce adresi değiştirin"}, "withdraw": "<PERSON><PERSON><PERSON>", "noBalance": "Bakiye yok", "title": "GasAccount", "safeAddressDepositTips": "Çoklu imza adresleri yatırımlar için desteklenmemektedir.", "gasExceed": "GasAccount bakiyesi $1000'ı aşamaz", "deposit": "Para Yatırma", "risk": "Mevcut adresiniz riskli olarak tespit edildiğinden bu özellik kullanılamıyor.", "logout": "Geçerli GasAccount'tan çıkış yap", "gasAccountList": {"gasAccountBalance": "Gas Bakiyesi", "address": "<PERSON><PERSON>"}, "switchAccount": "GasAccount <PERSON>", "withdrawDisabledIAP": "Para çekme işlemleri devre dışı bırakılmıştır çünkü bakiyeniz çekilemeyen fiat fonlarını içermektedir. Token bakiyenizi çekmek için destekle iletişime geçin."}, "safeMessageQueue": {"noData": "<PERSON><PERSON> yok", "loading": "<PERSON><PERSON><PERSON>"}, "newUserImport": {"guide": {"importAddress": "Zaten bir adresim var", "createNewAddress": "<PERSON><PERSON> bir ad<PERSON>", "title": "<PERSON><PERSON>", "desc": "Ethereum ve tüm EVM zincirleri için çığır açan cüzdan"}, "createNewAddress": {"showSeedPhrase": "Tohum İfadesini Göster", "title": "Başlamadan Önce", "tip2": "Kök ifadem yalnızca cihazımda saklanır. <PERSON><PERSON> buna er<PERSON>.", "desc": "Lütfen aşağıdaki güvenlik ipuçlarını okuyun ve aklınızda bulundurun.", "tip3": "Eğer seed phrase’imı yedeklemeden Rabby'yi kaldı<PERSON>, <PERSON><PERSON> tarafından kurtarı<PERSON>.", "tip1": "<PERSON><PERSON><PERSON> <PERSON><PERSON> cü<PERSON> kay<PERSON>er veya <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kalıcı olarak erişimimi ka<PERSON>."}, "importList": {"title": "İçe Aktarma Yöntemini Seç"}, "importPrivateKey": {"pasteCleared": "Yapıştırıldı ve pano temizlendi", "title": "Özel Anahtarı İçe Aktar"}, "PasswordCard": {"form": {"password": {"label": "Pa<PERSON><PERSON>", "min": "Parola en az 8 karakter uzunluğunda olmalıdır。", "required": "Lütfen Şifreyi Giriniz", "placeholder": "Şifre (en az 8 karakter)"}, "confirmPassword": {"notMatch": "<PERSON><PERSON><PERSON><PERSON>şmi<PERSON>r", "required": "Lütfen Parolayı Onaylayın", "label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}}, "title": "<PERSON><PERSON><PERSON>", "desc": "Cüzdanı açmak ve verileri şifrelemek için kullanılacaktır", "agree": "<1/> <2><PERSON><PERSON><PERSON><PERSON> Koşulları</2> ve <4>Gizlilik Politikası</4> kabul ediyorum"}, "successful": {"import": "Başarıyla İçe Aktarıldı", "addMoreAddr": "<PERSON><PERSON> Seed Phrase'ten daha fazla adres ekleyin", "addMoreFrom": "{{name}}'den daha fazla adres e<PERSON>in", "start": "Başlayın", "create": "Başarıyla Oluşturuldu"}, "readyToUse": {"pin": "<PERSON><PERSON>", "guides": {"step1": "Tarayıcı uzantısı simgesine tıklayın", "step2": "<PERSON><PERSON>"}, "desc": "<PERSON>bby <PERSON>ını Bul ve Sabitle", "extensionTip": "<PERSON><PERSON><PERSON> <1/> ve a<PERSON><PERSON><PERSON><PERSON> <3/>", "title": "Cüzdanınız hazır!"}, "importSeedPhrase": {"title": "Seed Phrase İçe Aktar"}, "importOneKey": {"title": "OneKey", "tip3": "3. Cihazınızı açın", "tip2": "2. One<PERSON>ey cihazınızı takın", "connect": "<PERSON><PERSON><PERSON> b<PERSON>", "tip1": "1. <1>OneKey Bridge<1/> <PERSON><PERSON><PERSON><PERSON>"}, "importTrezor": {"connect": "Trezor'a Bağlan", "title": "<PERSON><PERSON><PERSON>", "tip2": "2. Cihazınızı kilitleyin", "tip1": "1. <PERSON><PERSON><PERSON> cihazınızı takın"}, "ImportGridPlus": {"title": "GridPlus", "tip1": "1. GridPlus cihazınızı açın", "tip2": "2. <PERSON><PERSON><PERSON> Connector üzerinden bağlanın", "connect": "GridPlus'a Bağlan"}, "importLedger": {"connect": "<PERSON><PERSON>", "tip1": "Ledger cihazınızı takın.", "tip2": "PIN girerek kilidi a<PERSON>ın.", "tip3": "Ethereum uygulamasını açın.", "title": "Ledger"}, "importBitBox02": {"connect": "BitBox02 Bağla", "title": "BitBox02", "tip1": "1. <1>BitBoxBridge<1/>'i <PERSON><PERSON><PERSON><PERSON>", "tip2": "2. BitBox02 cihazınızı takın", "tip3": "3. Cihazınızı kilitleyin"}, "importKeystone": {"qrcode": {"desc": "Keystone donanım cüzdanındaki QR kodunu tarayın"}, "usb": {"tip2": "Şifrenizi girin ve kilidi açın", "tip3": "Bilgisayarınıza bağlantıyı onaylayın", "connect": "Keystone bağla", "desc": "Keystone 3 Pro'nuzun ana sayfada olduğundan emin olun", "tip1": "Keystone cihazınızı takın"}}, "importSafe": {"error": {"required": "Lütfen adres girin", "invalid": "Geçerli bir ad<PERSON>"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Bu ad<PERSON>in da<PERSON>ılmış zinciri aranıyor", "placeholder": "<PERSON><PERSON><PERSON><PERSON> adres girin"}}, "metamaskModeDapps": {"title": "İzin Verilen Dapp'le<PERSON>", "desc": "MetaMask Modu aşağıdaki <PERSON>ler için et<PERSON>ştiril<PERSON>. MetaMask seçeneğini seçerek Rabby bağlanabilirsiniz."}, "forgotPassword": {"home": {"button": "Sıfırlama İşlemine Başla", "buttonNoData": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "Rabby Wallet şifrenizi saklamaz ve size geri getirmede yardımcı olamaz. Yeni bir cüzdan kurmak için cüzdanınızı sıfırlayın.", "descriptionNoData": "<PERSON><PERSON>, şifrenizi saklamaz ve onu geri almanıza yardımcı olamaz. Unuttuysanız yeni bir şifre belirleyin."}, "reset": {"alert": {"privateKey": "<PERSON><PERSON>", "seed": "Seed Phrase", "title": "Veriler silinecek ve geri alınamaz:"}, "tip": {"records": "İmza <PERSON>ı", "title": "Veriler saklanacak:", "whitelist": "Whitelist Settings", "hardware": "İthal Edilen Donanım Cüzdanları", "safe": "İthal Edilen Safe Cüzdanlar", "watch": "<PERSON><PERSON><PERSON> ve <PERSON><PERSON>e <PERSON>"}, "title": "<PERSON><PERSON>i Sıfırla", "button": "Sıfırlamayı Onayla", "confirm": "Ku<PERSON>yu onaylamak ve devam etmek için <1>RESET</1> yazın"}, "tip": {"buttonNoData": "<PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "descriptionNoData": "Adresinizi ekleyerek başlayın", "description": "<PERSON>ni bir parola oluşturun ve devam edin", "title": "Ra<PERSON> Wallet Sıfırlama Tamamlandı"}, "success": {"button": "Tamamlandı", "title": "Şifre Başarıyla Ayarlandı", "description": "<PERSON><PERSON>et kullanmaya hazırsınız."}}, "eip7702": {"alert": "EIP-7702 hen<PERSON>z desteklenmiyor"}, "metamaskModeDappsGuide": {"toast": {"disabled": "Kılık değiştirme devre dışı. Dapp'i yenile.", "enabled": "Kılık etkinleştirildi. Yeniden bağlanmak için Dapp'i yenileyin."}, "step2": "Adım 2", "step1Desc": "<PERSON><PERSON>'nin mevcut Da<PERSON>'te MetaMask gibi görünmesine izin ver", "step1": "Adım 1", "title": "MetaMask gibi görünerek Rabby'yi <PERSON>la", "step2Desc": "MetaMask ile yenileyin ve bağlanın", "alert": "Bir Dapp'e bağlanamıyor musunuz çünkü Rabby Wallet bir seçenek olarak gözükmüyor mu?", "noDappFound": "Dapp bulunamadı", "manage": "İzin Verilen Dapp'le<PERSON>"}, "syncToMobile": {"downloadGooglePlay": "Google Play", "downloadAppleStore": "App Store", "clickToShowQr": "Adresi seçmek ve QR kodunu göstermek için tıklayın", "steps1": "1. <PERSON><PERSON>dir", "description": "Adres verileriniz tamamen çev<PERSON>ış<PERSON> kalı<PERSON>, şifrelenir ve güvenli bir şekilde bir QR kodu aracılığıyla aktarılır.", "steps2": "2. <PERSON><PERSON> il<PERSON>", "steps2Description": "QR kodunuz hassas veriler içerir. Gizli tutun ve asla kimseyle paylaşmayın.", "title": "<PERSON><PERSON>ından Mobil Cihaza Cüzdan Adresini Senkronize Et", "disableSelectAddress": "{{Type}} ad<PERSON><PERSON> i<PERSON> des<PERSON>nmeyen senkroniza<PERSON>on", "disableSelectAddressWithPassphrase": "<PERSON><PERSON><PERSON> ile {{type}} ad<PERSON>i i<PERSON><PERSON> des<PERSON>mi<PERSON> senk<PERSON>", "disableSelectAddressWithSlip39": "Slip39 ile {{type}} ad<PERSON>i i<PERSON><PERSON>", "selectedLenAddressesForSync_one": "Sync i<PERSON>in se<PERSON>n {{len}} adresi", "selectedLenAddressesForSync_other": "Sync i<PERSON>in se<PERSON>n {{len}} ad<PERSON><PERSON>i", "selectAddress": {"title": "Adresleri Senkronize Etmek İçin Seçin"}}, "search": {"sectionHeader": {"AllChains": "<PERSON><PERSON><PERSON>", "token": "Jetonlar", "NFT": "NFT", "Defi": "<PERSON><PERSON><PERSON>"}, "header": {"placeHolder": "Araştırma", "searchPlaceHolder": "Token Adı / Adres Ara"}, "tokenItem": {"FDV": "FDV", "gasToken": "Gaz Token", "scamWarningTips": "Bu düşük kaliteli bir token ve bir dolandırıcılık olabilir.", "verifyDangerTips": "Bu bir dolandırıcılık tokenıdır.", "listBy": "List by {{name}}", "Issuedby": "Tarafından verildi"}, "searchWeb": {"noResult": "Sonuç Yo<PERSON> için", "searchTips": "Web'de ara", "title": "<PERSON><PERSON><PERSON>", "noResults": "<PERSON><PERSON><PERSON>", "searching": "Sonuçlar için"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "Eşleşen adres yok", "AddressItem": {"whitelistedAddressTip": "Beyaz listeye alınmış adres"}}, "AccountSelectDrawer": {"btn": {"cancel": "İptal", "proceed": "<PERSON><PERSON> et"}}, "AddressList": {"AddressItem": {"addressTypeTip": "{{type}} tarafından içe aktarıldı"}}, "AuthenticationModal": {"passwordError": "yanlış şifre", "passwordRequired": "Lütfen şif<PERSON> girin", "passwordPlaceholder": "Onaylamak iç<PERSON>"}, "ConnectStatus": {"connecting": "Bağlanıyor...", "connect": "Bağlan", "gridPlusConnected": "GridPlus bağlandı", "gridPlusNotConnected": "GridPlus bağlanmadı", "ledgerNotConnected": "Ledger ba<PERSON><PERSON><PERSON><PERSON>", "ledgerConnected": "Ledger bağlandı", "keystoneNotConnected": "Keystone bağlı değil", "imKeyrNotConnected": "im<PERSON>ey bağlı değil", "imKeyConnected": "im<PERSON><PERSON> bağlı", "keystoneConnected": "Keystone bağlı"}, "Contact": {"AddressItem": {"notWhitelisted": "Bu adres beyaz listeye alınmamış", "whitelistedTip": "Beyaz listeye alınmış adres"}, "EditModal": {"title": "Edit address note"}, "EditWhitelist": {"backModalTitle": "Değişiklikleri İptal Et", "backModalContent": "Yaptığınız değişiklikler kaydedilmeyecek", "title": "Beyaz Listeyi <PERSON>ü<PERSON>le", "tip": "Beyaz listeye almak istediğiniz adresi seçin ve kaydedin.", "save": "<PERSON>az Listeye Kaydet ({{count}})"}, "ListModal": {"title": "<PERSON><PERSON>", "whitelistEnabled": "Beyaz liste etkin. Sadece beyaz listeye alınmış bir adrese varlık gönderebilirsiniz veya \"Ayarlar\"dan devre dışı bırakabilirsiniz.", "whitelistDisabled": "Beyaz liste devre dışı. Herhangi bir adrese varlık gönderebilirsiniz.", "editWhitelist": "Beyaz Listeyi <PERSON>ü<PERSON>le", "whitelistUpdated": "Beyaz Liste Güncellendi", "authModal": {"title": "Beyaz <PERSON>"}}}, "LoadingOverlay": {"loadingData": "<PERSON><PERSON>..."}, "MultiSelectAddressList": {"imported": "İçe Aktarıldı"}, "NFTNumberInput": {"erc1155Tips": "Bakiyeniz {{amount}}", "erc721Tips": "Tek seferde sadece bir ERC 721 NFT gönderilebilir"}, "TiledSelect": {"errMsg": "Seed Phrase sı<PERSON>ı yan<PERSON>, lütfen kontrol edin"}, "Uploader": {"placeholder": "Bir JSON dosyası seçin"}, "WalletConnectBridgeModal": {"title": "Köprü sunucu URL'si", "requiredMsg": "Lütfen köprü sunucu host'unuzu girin", "invalidMsg": "Lütfen host'u kontrol edin", "restore": "<PERSON><PERSON> ayarı geri <PERSON>"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "Entegre Ağ", "testnet": "Özel Network"}}, "ChainSelectorModal": {"searchPlaceholder": "Zincir ara", "noChains": "Zincir yok", "addTestnet": "<PERSON>zel <PERSON>"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "VARLIK / MİKTAR"}, "price": {"title": "FİYAT"}, "usdValue": {"title": "USD DEĞERİ"}}, "searchInput": {"placeholder": "İsme / Adrese göre ara"}, "header": {"title": "Bir <PERSON> seçin"}, "noTokens": "Token yok", "noMatch": "Eşleşme yok", "noMatchSuggestion": "{{ chainName }} üzerinde sözleşme adresini aramayı deneyin", "bridge": {"token": "Token", "high": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "low": "Düşük", "liquidity": "Likidite", "liquidityTips": "<PERSON><PERSON><PERSON> ticaret hacmi ne kadar yüksek<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> başarılı olma <PERSON>lığı o kadar yüksektir."}, "hot": "Sıcak", "common": "<PERSON><PERSON>", "recent": "<PERSON>", "chainNotSupport": "Bu zincir desteklenmiyor."}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "Koleksiyon", "Chain": "<PERSON>in<PERSON>r", "PurschaseDate": "<PERSON><PERSON><PERSON><PERSON>", "LastPrice": "<PERSON>"}}, "signPermissionCheckModal": {"title": "Bu Dapp'e sadece test ağlarında imza atmasına izin veriyorsunuz", "reconnect": "Dapp'ı Yeniden Bağlayın"}, "testnetCheckModal": {"title": "Test ağlarında imza atmadan önce \"Daha Fazla\" altında \"Test Ağlarını Etkinleştir\"i açın"}, "EcologyNavBar": {"providedBy": "Tara<PERSON>ı<PERSON>n <PERSON>lanmıştır {{chainName}}"}, "EcologyNoticeModal": {"notRemind": "Bir daha bana hatırlatma", "desc": "Aşağıdaki hizmetler doğrudan üçüncü taraf Ekosistem Ortağı tarafından sağlanacaktır. <PERSON><PERSON>, bu hizmet<PERSON>in güvenliğinden sorumlu değildir.", "title": "<PERSON><PERSON><PERSON>"}, "ReserveGasPopup": {"doNotReserve": "Gas ayırma", "title": "Rezerv Gaz", "normal": "Normal", "instant": "Anında", "fast": "Hızlı"}, "OpenExternalWebsiteModal": {"button": "<PERSON><PERSON>", "title": "<PERSON><PERSON>'ten Ayrılıyorsunuz", "content": "Harici bir web sitesini ziyaret etmek üzeresiniz. Ra<PERSON>, bu sitenin içeriğinden veya güvenliğinden sorumlu değildir."}, "TokenChart": {"holding": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>"}, "externalSwapBrideDappPopup": {"selectADapp": "Bir <PERSON> Seçin", "chainNotSupported": "Bu zincirde desteklenmiyor", "noDapp": "Mevcut Dapp yok", "thirdPartyDappToProceed": "Lütfen devam etmek için üçüncü taraf bir <PERSON> kullanın.", "noQuotesForChain": "Bu zincir için henüz <PERSON>hangi bir alıntı mevcut değil.", "help": "Lütfen destek için bu zincirin resmi ekibiyle iletişime geçin.", "viewDappOptions": "<PERSON><PERSON>", "bridgeOnDapp": "Köprü Dapp'ta\n", "swapOnDapp": "Dapp'ta Takas\n", "noDapps": "Bu zincirde kullanılabilir hiçbir Dapp yok.\n"}, "AccountSelectorModal": {"searchPlaceholder": "<PERSON><PERSON> ara\n", "title": "<PERSON><PERSON>\n"}}, "global": {"appName": "<PERSON><PERSON>", "appDescription": "Ethereum ve tüm EVM zincirleri için oyun değiştirici cüzdan", "copied": "Kopyalandı", "confirm": "<PERSON><PERSON><PERSON>", "next": "İleri", "back": "<PERSON><PERSON>", "ok": "<PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "failed": "Başarısız", "scamTx": "Sa<PERSON>e işlem", "gas": "Gas", "unknownNFT": "Bilinmeyen NFT", "copyAddress": "<PERSON><PERSON><PERSON>", "watchModeAddress": "<PERSON>zleme <PERSON> ad<PERSON>i", "assets": "varlık", "Confirm": "<PERSON><PERSON><PERSON>", "Cancel": "İptal", "Clear": "<PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON>", "confirmButton": "<PERSON><PERSON><PERSON>", "cancelButton": "İptal", "backButton": "<PERSON><PERSON>", "proceedButton": "<PERSON><PERSON>", "editButton": "<PERSON><PERSON><PERSON><PERSON>", "addButton": "<PERSON><PERSON>", "closeButton": "Ka<PERSON><PERSON>", "Deleted": "<PERSON><PERSON><PERSON>", "Loading": "Yükleniyor", "nonce": "nonce", "Balance": "Bakiye", "Done": "<PERSON><PERSON>", "Nonce": "<PERSON><PERSON>", "notSupportTesntnet": "Özel ağ için desteklenmiyor", "tryAgain": "<PERSON><PERSON><PERSON>"}, "background": {"error": {"noCurrentAccount": "Mevcut hesap yok", "invalidChainId": "Geçersiz zincir id'si", "notFindChain": "{{chain}} zinciri bulunamıyor", "unknownAbi": "bilinmeyen kontrat abi'ı", "invalidAddress": "Geçerli bir ad<PERSON>", "notFoundGnosisKeyring": "Gnosis keyring'i bulunamadı", "notFoundTxGnosisKeyring": "Gnosis keyring'inde işlem bulunamadı", "addKeyring404": "addKeyring başarıs<PERSON>z, keyring tanımlı değil", "emptyAccount": "mevcut hesap boş", "generateCacheAliasNames": "[GenerateCacheAliasNames]: en az bir adres gerekli", "invalidPrivateKey": "özel anahtar geçersiz", "invalidJson": "giriş dosyası geçersiz", "invalidMnemonic": "Seed Phrase ge<PERSON><PERSON><PERSON>, lütfen kontrol edin!", "notFoundKeyringByAddress": "<PERSON><PERSON><PERSON> göre keyring bulunamıyor", "txPushFailed": "İşlem gönderme başarısız", "unlock": "önce cüzdan kilidini açmalısınız", "duplicateAccount": "İçe aktarmaya çalıştığınız hesap zaten var", "canNotUnlock": "Önceki vault o<PERSON><PERSON> kilit a<PERSON>ı<PERSON>az"}, "transactionWatcher": {"submitted": "İşlem gönderildi", "more": "daha fazla bilgi i<PERSON>in tı<PERSON>n", "completed": "İşlem tamamlandı", "failed": "İşlem başarısız", "txFailedMoreContent": "{{chain}} #{{nonce}} başarısız oldu. Daha fazlasını görmek için tıklayın.", "txCompleteMoreContent": "{{chain}} #{{nonce}} tamamlandı. Daha fazlasını görmek için tıklayın."}, "alias": {"HdKeyring": "Seed Phrase", "simpleKeyring": "<PERSON><PERSON>", "watchAddressKeyring": "<PERSON><PERSON><PERSON>"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "Seed Phrase il<PERSON>", "SimpleKeyring": "Özel Anahtarla İçe Aktarıldı", "WatchAddressKeyring": "<PERSON><PERSON><PERSON>"}, "IMPORTED_HD_KEYRING": "Seed Phrase ile İçe Aktarıldı", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Ana Ağ & Test Ağı", "TESTNET": "Sadece Test Ağı"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "Tohum Cümlesi (Parola) ile İçe Aktarıldı"}}