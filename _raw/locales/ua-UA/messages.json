{"page": {"transactions": {"title": "Тран<PERSON><PERSON>к<PERSON><PERSON>ї", "empty": {"title": "Немає транзакцій", "desc": " Не знайдено транзакцій на <1>підтримуваних мережах</1>"}, "explain": {"approve": "Затвердити {{amount}} {{symbol}} для {{project}}", "unknown": "Взаємодія з контрактами", "cancel": "Скасувати незавершену транзакцію"}, "txHistory": {"tipInputData": "Транзакція містить повідомлення", "parseInputDataError": "Не вдалося розібрати повідомлення", "scamToolTip": "Ця транзакція ініційована шахраями для відправлення шахрайських токенів та NFT. Будь ласка, утримайтесь від взаємодії з нею."}, "modalViewMessage": {"title": "Переглянути повідомлення"}, "filterScam": {"title": "Сховати шахрайські транзакції", "loading": "Завантаження може зайняти певний час, можлива затримка даних", "btn": "Приховати шахрайські транзакції"}}, "chainList": {"title": "{{count}} підтримуваних мереж", "mainnet": "Mainnet", "testnet": "Testnets"}, "signTx": {"nftIn": "NFT отримано", "gasLimitNotEnough": "Ліміт газу менше 21000. Транзакція не може бути відправлена", "gasLimitLessThanExpect": "Ліміт газу низький. Існує 1% ймовірність, що транзакція може не пройти.", "gasLimitLessThanGasUsed": "Ліміт газу занадто низький. Існує 95% ймовірність, що транзакція може не пройти.", "nativeTokenNotEngouthForGas": "У вашому гаманці недостатньо коштів для оплати газу", "nonceLowerThanExpect": "<PERSON><PERSON> занадто низький, мінімум має бути {{0}}", "canOnlyUseImportedAddress": "Для підпису можна використовувати лише імпортовані адреси", "multiSigChainNotMatch": "Адреси з мультипідписом не входять до цієї мережі і не можуть ініціювати транзакції", "safeAddressNotSupportChain": "Поточна безпечна адреса не підтримується у мережі {{0}}", "noGasRequired": "Газ не потрібен", "gasSelectorTitle": "Газ", "failToFetchGasCost": "Не вдалося отримати вартість газу", "gasMoreButton": "Ще", "manuallySetGasLimitAlert": "Ви вручну встановили ліміт газу до", "gasNotRequireForSafeTransaction": "Плата за газ не потрібна для безпечних транзакцій", "gasPriceTitle": "Ціна газу (Gwei)", "maxPriorityFee": "Максимальна комісія за пріоритет (Gwei)", "eip1559Desc1": "У мережах, які підтримують EIP-1559, плата за пріоритет - це комісія для майнерів за обробку вашої транзакції. Ви можете заощадити кінцеву вартість газу, знизивши плату за першочергове обслуговування, що може коштувати більше часу на обробку транзакції.", "eip1559Desc2": "Тут, у Ra<PERSON>, плата за пріоритет (чайові) = максимальна плата - базова плата. Після того, як ви встановите максимальну плату за пріоритет, з неї буде вирахувано базову плату, а решту буде віддано на чай майнерам.", "hardwareSupport1559Alert": "Переконайтеся, що прошивка вашого апаратного гаманця оновлена до версії, яка підтримує EIP 1559", "gasLimitTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> газу", "recommendGasLimitTip": "Est. {{est}}. Поточний {{current}}x, рекомендовано", "nonceTitle": "<PERSON><PERSON>", "gasLimitModifyOnlyNecessaryAlert": "Змінювати лише за необхідності", "gasPriceMedian": "Медіана останніх 100 транзакцій у мережі: ", "myNativeTokenBalance": "<PERSON><PERSON><PERSON> Газ баланс: ", "gasLimitEmptyAlert": "Будь ласка, введіть ліміт газу", "gasLimitMinValueAlert": "Лі<PERSON><PERSON>т газу має бути більше 21000", "balanceChange": {"successTitle": "Результати моделювання транзакції", "failedTitle": "Симуляція транзакції завершилася невдало", "noBalanceChange": "Баланс не змінено", "tokenOut": "Токен знято", "tokenIn": "Токен отримано", "errorTitle": "Не вдалося отримати зміну балансу", "notSupport": "Симуляція транзакцій не підтримується", "nftOut": "NFT виведено"}, "enoughSafeSigCollected": "Зібрано достатньо підписів", "moreSafeSigNeeded": "{{0}} потрібно більше підтверджень", "safeAdminSigned": "Під<PERSON>и<PERSON>ано", "swap": {"title": "'Токен для обміну'", "payToken": "Оплатити", "receiveToken": "Отримати", "failLoadReceiveToken": "Не вдалося завантажити", "valueDiff": "Різниця значень", "simulationFailed": "Симуляція транзакції не вдалася", "simulationNotSupport": "Симуляція транзакції не підтримується у цій мережі", "minReceive": "Мінімальне отримання", "slippageFailToLoad": "Допуск проскальзування не вдасться завантажити", "slippageTolerance": "Допуск на Прослизання", "receiver": "Од<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notPaymentAddress": "Цє не адреса для оплати", "unknownAddress": "Невідома адреса"}, "crossChain": {"title": "Cross Chain"}, "swapAndCross": {"title": "Свап та Cross Chain"}, "wrapToken": "Обернути токен", "unwrap": "Розгорнути токен", "send": {"title": "Надіслати токен", "sendToken": "Надіслати токен", "sendTo": "Надіслати на", "receiverIsTokenAddress": "Адреса токена", "contractNotOnThisChain": "Адреса цього контракту ні у ції мережі", "notTopupAddress": "Це не адреса для поповнення", "tokenNotSupport": "{{0}} не підтримується", "onMyWhitelist": "У моєму білому списку", "notOnThisChain": "Не у цій мережі", "cexAddress": "CEX-адреса", "addressBalanceTitle": "Ба<PERSON><PERSON>нс адреси", "whitelistTitle": "<PERSON><PERSON><PERSON><PERSON> список", "notOnWhitelist": "Не в моєму білому списку", "fromMySeedPhrase": "З моєї seed-фрази", "scamAddress": "Шахрайська адреса", "fromMyPrivateKey": "З мого приватного ключа"}, "tokenApprove": {"title": "Схвалення токена", "approveToken": "Схвалити токен", "myBalance": "<PERSON><PERSON><PERSON> баланс", "approveTo": "Схвалити для", "eoaAddress": "Адреса EOA", "trustValueLessThan": "Цінність контракту ≤ {{value}}", "deployTimeLessThan": "Час розгортання < {{value}} днів", "amountPopupTitle": "Сума", "flagByRabby": "Позначен<PERSON> Rabby", "contractTrustValueTip": "Значення рівня безпеки відноситься до загальної суми токенів, схвалених і підданих ризику в цьому контракті. Низький рівень довіри вказує або на ризик, або на бездіяльність протягом 180 днів.", "amount": "Затвердити суму:", "exceed": "Перевищує ваш поточний баланс"}, "revokeTokenApprove": {"title": "Відкликати схвалення токена", "revokeFrom": "Відкликати з", "revokeToken": "Відкликати токен"}, "sendNFT": {"title": "Надіслати NFT", "nftNotSupport": "NFT не підтримується"}, "nftApprove": {"title": "Затвердження NFT", "approveNFT": "Затвердити NFT", "nftContractTrustValueTip": "Значення рівня безпеки відноситься до максимальної NFT, затвердженої та підданої ризику для цього контракту. Низьке значення довіри вказує або на ризик, або на бездіяльність протягом 180 днів."}, "revokeNFTApprove": {"title": "Відкликати схвалення NFT", "revokeNFT": "Відкликати NFT"}, "nftCollectionApprove": {"title": "Затвердження колекції NFT", "approveCollection": "Затвердити колекцію"}, "revokeNFTCollectionApprove": {"title": "Відкликати затвердження колекції NFT", "revokeCollection": "Відкликати колекцію"}, "deployContract": {"title": "Розгорнути контракт", "descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ви розгортаєте смарт-контракт"}, "cancelTx": {"title": "Скасувати відкладену транзакцію", "txToBeCanceled": "Транзакція, яку потрібно скасувати", "gasPriceAlert": "Встановіть поточну ціну газу більшу за {{value}} Gwei, щоб скасувати очікувану транзакцію"}, "submitMultisig": {"title": "Надіслати транзакцію Multisig", "multisigAddress": "Адреса Multisig"}, "contractCall": {"title": "Контрактна взаємодія", "operation": "Операція", "operationABIDesc": "Операція декодована з ABI", "operationCantDecode": "Операція не декодована", "payNativeToken": "Сплатити {{symbol}}", "receiver": "Адреса отримувача", "suspectedReceiver": "Виняткова адреса"}, "revokePermit2": {"title": "Відкликати схвалення токена Permit2"}, "unknownAction": "Невідомо", "interactContract": "Взаємодіяти з контрактом", "markAsTrust": "Позначити як довірений", "markAsBlock": "Позначити як заблокований", "interacted": "Взаємодіяли раніше", "neverInteracted": "Ніколи не взаємодіяли раніше", "transacted": "Здійснювали транзакції раніше", "neverTransacted": "Ніколи раніше не проводились транзакції", "fakeTokenAlert": "Це шахрайський токен, позначений Rabby", "scamTokenAlert": "Це потенційно неякісний та шахрайський токен, заснований на виявленні Rabby", "trusted": "Затвердити", "blocked": "Заблоковано", "noMark": "Без позначки", "markRemoved": "Мітку видалено", "speedUpTooltip": "Ця прискорена транзакція та оригінальна транзакція, лише одна з яких буде завершена в кінцевому підсумку", "signTransactionOnChain": "Підписати {{chain}} Транзакцію", "viewRaw": "Переглянути Raw", "unknownActionType": "Невідомий тип дії", "sigCantDecode": "Цей підпис не може бути розшифрований Rabby", "nftCollection": "NFT Колекція", "floorPrice": "Мін<PERSON>мальна ціна", "contractAddress": "Адреса контракту", "protocolTitle": "Протокол", "deployTimeTitle": "Час розгортання", "popularity": "Популярність", "contractPopularity": "№{{0}} в {{1}}", "addressNote": "Примітка до адреси", "myMarkWithContract": "Моя позначка з контрактом {{chainName}}", "myMark": "Моя позначка", "collectionTitle": "Колекція", "addressTypeTitle": "Ти<PERSON> адреси", "firstOnChain": "<PERSON>е<PERSON><PERSON><PERSON> у мережі", "trustValue": "Затверджене значення", "importedDelegatedAddress": "Імпортована делегована адреса", "noDelegatedAddress": "Немає імпортованої делегованої адреси", "coboSafeNotPermission": "Ця делегована адреса не має дозволу на ініціювання цієї транзакції", "gasAccount": {"totalCost": "Загальна вартість:", "sendGas": "Поточна транзакція здійснює передачу Gas на ваш рахунок:", "gasCost": "Вартість газу за переказ газу на вашу адресу:", "maxGas": "Максимальний Gas:", "currentTxCost": "Кількість Gas, відправлена на вашу адресу:", "estimatedGas": "Орієнтовна вартість газу: "}, "customRPCErrorModal": {"button": "Вимкнути Custom RPC", "title": "Настроювана помилка RPC", "content": "Ваша користувацька RPC наразі недоступна. Ви можете вимкнути її та продовжити підписання, використовуючи офіційну RPC Rabby."}, "transferOwner": {"description": "<PERSON><PERSON><PERSON><PERSON>", "title": "Передача права власності на активи", "transferTo": "Передати до"}, "swapLimitPay": {"title": "Обмеження на обмін токенів оплата", "maxPay": "Максимальна оплата"}, "batchRevokePermit2": {"title": "Пакетне відкликання дозволу Permit2"}, "revokePermit": {"title": "Скасувати схвалення дозволу токена"}, "assetOrder": {"listAsset": "Список активів", "title": "Активи Порядок", "receiveAsset": "Отримати asset"}, "BroadcastMode": {"instant": {"title": "Миттєво", "desc": "Транзакції будуть негайно трансляті на мережу"}, "lowGas": {"title": "Економія Gas", "desc": "Транзакції будуть транслюватися, коли мережева плата gas низька"}, "mev": {"title": "MEV Захищено", "desc": "Транзакції будуть надіслані на призначений MEV вузол"}, "tips": {"notSupported": "Не підтримується", "notSupportChain": "Не підтримується на цьому ланцюжку", "walletConnect": "Не підтримується WalletConnect", "customRPC": "Не підтримується при використанні кастомного RPC"}, "lowGasDeadline": {"label": "Тайм-аут", "24h": "24h", "1h": "1г.", "4h": "4г"}, "title": "Режим Broadcast"}, "SafeNonceSelector": {"explain": {"contractCall": "Виклик контракту", "unknown": "Невідома транзакція", "send": "Надіс<PERSON>а<PERSON><PERSON>"}, "optionGroup": {"recommendTitle": "Рекомендований nonce", "replaceTitle": "Замініть транзакцію в черзі"}, "option": {"new": "Нова транзакція"}, "error": {"pendingList": "Не вдалося завантажити операції, які очікують, <1/><2>Спробуйте ще раз</2>"}}, "coboSafeCreate": {"title": "Створити Cobo Safe", "descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "safeWalletTitle": "Safe{Wallet}"}, "coboSafeModificationRole": {"title": "Надіслати зміни ролі Safe", "descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "safeWalletTitle": "Безпечний{Wallet}"}, "coboSafeModificationDelegatedAddress": {"safeWalletTitle": "Безпечний{Wallet}", "descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "title": "Надіслати Зміну Довіреної Адреси"}, "coboSafeModificationTokenApproval": {"descriptionTitle": "<PERSON><PERSON><PERSON><PERSON>", "title": "Надіслати зміну затвердження токена", "safeWalletTitle": "Безпечний{Wallet}"}, "common": {"interactContract": "Взаємодіяти з контрактом", "description": "<PERSON><PERSON><PERSON><PERSON>", "descTipWarningPrivacy": "Підпис може підтвердити право власності на адресу", "descTipWarningAssets": "Підпис може спричинити зміну активів", "descTipWarningBoth": "Підпис може спричинити зміну активів і підтвердити володіння адресою", "descTipSafe": "Підпис не викликає зміну активів або перевірку права власності на адресу"}, "nativeTokenForGas": "Використовуйте токен {{tokenName}} на {{chainName}}, щоб заплатити за газ", "gasAccountForGas": "Використовувати USD з мого GasAccount для оплати gas", "importedAddress": "Імпортована адреса", "chain": "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "l2GasEstimateTooltip": "Оцінка gas для ланцюга L2 не включає плату за gas L1. Фактична плата буде вищою, ніж поточна оцінка.", "yes": "Так", "no": "Ні", "hasInteraction": "Раніше взаємодіяли", "protocol": "Протокол", "label": "Мітка", "amount": "Сума", "trustValueTitle": "Довіряйте цінності", "maxPriorityFeeDisabledAlert": "Будь ласка, спочатку встановіть Gas Price", "address": "Адреса", "contract": "Розумний контракт", "addressSource": "Дже<PERSON><PERSON>ло адреси", "typedDataMessage": "Підписати Типізовані Дані", "advancedSettings": "Розширені налаштування", "decodedTooltip": "Цей підпис розшифровано Rabby Wallet", "primaryType": "Первинний тип", "safeServiceNotAvailable": "Виба<PERSON><PERSON>е, служба Safe зараз недоступна, спробуйте пізніше.", "safeTx": {"selfHostConfirm": {"content": "API Safe недоступний. Переключіться на службу Safe, розгорну<PERSON><PERSON> Rabby, щоб ваш Safe залишався функціональним. <strong>Усі підписувачі Safe повинні використовувати Rabby Wallet для авторизації транзакцій.<strong>", "title": "Переключитися на службу безпеки Rabby", "button": "OK"}}}, "signFooterBar": {"requestFrom": "Запит від", "processRiskAlert": "Будь ласка, обробіть попередження перед підписанням", "ignoreAll": "Ігнорувати всі", "gridPlusConnected": "GridPlus підключено", "gridPlusNotConnected": "GridPlus не підключено", "connectButton": "Підключити", "connecting": "Підключення...", "ledgerNotConnected": "Ledger не підключено", "ledgerConnected": "Ledger підключен", "signAndSubmitButton": "Підписати та надіслати", "walletConnect": {"connectedButCantSign": "Підклю<PERSON><PERSON>н<PERSON>, але не може підписати.", "switchToCorrectAddress": "Будь ласка, перейдіть на правильну адресу в мобільному гаманці", "switchChainAlert": "Будь ласка, перейдіть на {{chain}} в мобільному гаманці", "notConnectToMobile": "Не підключено до {{brand}}", "connected": "Підключений і готовий до підписання", "howToSwitch": "Як переключитися", "wrongAddressAlert": "Ви перейшли на іншу адресу в мобільному гаманці. Будь ласка, перейдіть на правильну адресу в мобільному гаманці", "connectBeforeSign": "{{0}} не підключений до <PERSON>bby, будь ласка, підключіться перед підписанням", "chainSwitched": "Ви переключилися до іншої мережі на мобільному гаманці. Будь ласка, перейдіть на {{0}} в мобільному гаманці", "latency": "Затримка", "requestSuccessToast": "Запит успішно відправлено", "sendingRequest": "Відправлення запиту на підписання", "signOnYourMobileWallet": "Будь ласка, підпишіться у своєму мобільному гаманці.", "requestFailedToSend": "Запит на підписання не вдалося відправити"}, "beginSigning": "Почати процес підписання", "addressTip": {"onekey": "Адреса OneKey", "trezor": "Адреса Trezor", "bitbox": "Адреса BitBox02", "keystone": "Keystone адреса", "airgap": "Адреса AirGap", "coolwallet": "Адреса CoolWallet", "privateKey": "Прива<PERSON><PERSON><PERSON> ключ", "seedPhrase": "Сід фраза", "watchAddress": "Неможливо підписати за допомогою watch-only адреси", "safe": "Безпечна адреса", "coboSafe": "Адреса Cobo Argus", "seedPhraseWithPassphrase": "Ад<PERSON><PERSON>с Seed Phrase (Passphrase)"}, "qrcode": {"signWith": "Підписати з допомогою {{brand}}", "failedToGetExplain": "Не вдалося отримати опис", "txFailed": "Не вдалося надіслати", "sigReceived": "Під<PERSON>ис отримано", "sigCompleted": " Транзакцію відправлено", "getSig": "Отримати підпис", "qrcodeDesc": "Відскануйте своїм {{brand}}, щоб підписати<br></br>Після підписання натисніть кнопку нижче, щоб отримати підпис", "misMatchSignId": "Невідповідні дані транзакції. Перевірте, будь ласка, дані транзакції.", "unknownQRCode": "Помилка: Ми не змогли ідентифікувати цей QR-код", "afterSignDesc": "Після підписання розмістіть QR-код на {{brand}} перед камерою вашого ПК"}, "ledger": {"resent": "Resent", "signError": "Помилка підпису Ledger:", "notConnected": "Ваш гаманець не підключено. Будь ласка, підключіться повторно.", "siging": "Відправлення запиту на підпис", "txRejected": "Транзакцію відхилено", "unlockAlert": "Будь ласка, підключіть і розблокуйте ваш Ledger, відкрийте на ньому Ethereum", "updateFirmwareAlert": "Будь ласка, оновіть прошивку та додаток Ethereum на вашому гаманці", "txRejectedByLedger": "Транзакцію відхилено на вашому Ledger", "blindSigTutorial": "Навчальний посібник з накладання сліпого підпису в обліковому записі", "submitting": "Підписан<PERSON>. Відправляю транзакцію", "resubmitted": "Відправлено", "resubmited": "Переслано"}, "common": {"notSupport": "{{0}} не підтримується"}, "resend": "Повторити", "submitTx": "Надіслати транзакцію", "testnet": "Testnet", "mainnet": "Mainnet", "gasless": {"unavailable": "Ваш баланс Gas недостатній", "notEnough": "Баланс Gas недостатній", "watchUnavailableTip": "Перегляд тільки адреси не підтримується для Free Gas", "customRpcUnavailableTip": "Користувацькі RPC не підтримуються для Free Gas", "rabbyPayGas": "Ra<PERSON> покриє витрати на газ – просто підпишіть", "GetFreeGasToSign": "Отримайте безкоштовний Gas", "walletConnectUnavailableTip": "Мобільний гаманець, підключений через WalletConnect, не підтримується для Free Gas"}, "gasAccount": {"login": "Увійти", "WalletConnectTips": "WalletConnect не підтримується GasAccount", "loginFirst": "Будь ласка, увійдіть до GasAccount спочатку", "useGasAccount": "Використовуйте GasAccount", "deposit": "Депозит", "gotIt": "Зрозуміло", "chainNotSupported": "Цей ланцюг не підтримується GasAccount", "notEnough": "GasAccount недостатньо", "customRPC": "Не підтримується при використанні custom RPC", "depositTips": "Щоб завершити депозит GasAccount, ця транзакція буде відхилена. Вам потрібно буде створити її знову після депозиту.", "loginTips": "Щоб завершити вхід до GasAccount, ця транзакція буде вилучена. Вам потрібно буде створити її заново після входу."}, "keystone": {"unsupportedType": "Помилка: Тип транзакції не підтримується або невідомий.", "siging": "Відправлення запиту на підписання", "mismatchedWalletError": "Невідповідний гаманець", "txRejected": "Транзакцію відхилено", "hardwareRejectError": "Запит Keystone було скасовано. Щоб продовжити, будь ласка, повторно авторизуйтесь.", "shouldOpenKeystoneHomePageError": "Переконайтеся, що ваш Keystone 3 Pro знаходиться на домашній сторінці.", "shouldRetry": "Трапилась помилка. Будь ласка, спробуйте ще раз.", "signWith": "Переключитися на {{method}} для підпису", "verifyPasswordError": "Не вдалося підписати, будь ласка, спробуйте ще раз після розблокування", "misMatchSignId": "Невідповідні дані транзакції. Будь ласка, перевірте деталі транзакції.", "qrcodeDesc": "Сканувати для підпису. Після підписання натисніть нижче, щоб отримати підпис. Для <PERSON>, перепідключіть та авторизуйтеся, щоб почати процес підписання знову."}, "keystoneConnected": "Keystone підключено", "keystoneNotConnected": "Keystone не підключено", "cancelCurrentTransaction": "Скасувати поточну транзакцію", "detectedMultipleRequestsFromThisDapp": "Виявлено декілька запитів від цього Dapp", "imKeyNotConnected": "imKey не підключено", "cancelAll": "Скасувати всі {{count}} запити від Dapp", "cancelTransaction": "Скасувати транзакцію", "cancelConnection": "Скасувати з'єднання", "cancelCurrentConnection": "Скасувати поточне підключення", "imKeyConnected": "imKey підключено", "blockDappFromSendingRequests": "Заблокувати запит Dapp на 1 хвилину"}, "signTypedData": {"signTypeDataOnChain": "Підписати {{chain}} Типові дані", "safeCantSignText": "Це безпечна адреса, і вона не може бути використана для підписання тексту.", "permit": {"title": "Затвердження токену дозволу"}, "permit2": {"title": "Permit2 Схвалення токену дозволу", "sigExpireTimeTip": "Час, протягом якого цей підпис буде дійсним у мережі", "sigExpireTime": "Час закінчення терміну дії підпису", "approvalExpiretime": "<PERSON>ас закінчення терміну дії затвердження"}, "swapTokenOrder": {"title": "Swap Token Order"}, "sellNFT": {"title": "NFT Order", "receiveToken": "Отримати токен", "listNFT": "Залістити NFT", "specificBuyer": "Конкретний покупець"}, "signMultiSig": {"title": "Підтвердити транзакцію"}, "createKey": {"title": "Створити ключ"}, "verifyAddress": {"title": "Перевірити адресу"}, "buyNFT": {"payToken": "Токен для оплати", "receiveNFT": "Отримати NFT", "expireTime": "<PERSON>а<PERSON> закінчення терміну дії", "listOn": "Увімкнути список"}, "contractCall": {"operationDecoded": "Операція декодована з повідомлення"}, "safeCantSignTypedData": "Це адреса Safe, і вона підтримує лише підписування типізованих даних EIP-712 або рядків."}, "activities": {"title": "Ваші підписи", "signedTx": {"label": "Тран<PERSON><PERSON>к<PERSON><PERSON>ї", "empty": {"title": "Ще немає підписаних транзакцій", "desc": "Всі транза<PERSON><PERSON><PERSON><PERSON>, підписані чере<PERSON>, будуть перераховані тут."}, "common": {"unlimited": "без обмежень", "unknownProtocol": "Невідомий протокол", "unknown": "Невідомо", "speedUp": "Прискорити", "cancel": "Скасувати", "pendingDetail": "Очікує деталей"}, "tips": {"pendingDetail": "Буде завершена лише одна транзакція, і майже завжди це транзакція з найвищою ціною на газ", "canNotCancel": "Неможливо прискорити або скасувати: Не перший відкладений txn", "pendingBroadcastRetryBtn": "Повторна трансляція", "pendingBroadcastBtn": "Транслювати зараз", "pendingBroadcast": "Режим економії Gas: очікування знижених мережевих зборів. Максимум {{deadline}} годин очікування.", "pendingBroadcastRetry": "Трансляція не вдалася. Остання спроба: {{pushAt}}"}, "status": {"canceled": "Скасовано", "failed": "Не вдалося", "submitFailed": "Не вдалося відправити", "pending": "На розгляді", "pendingBroadcasted": "Очікує підтвердження: передано в ефір", "withdrawed": "Швидке скасування", "pendingBroadcastFailed": "Очікується: Передача не вдалася", "pendingBroadcast": "Очікується: буде транслюватися"}, "txType": {"initial": "Початковий tx", "cancel": "Скасувати tx", "speedUp": "Прискорити tx"}, "explain": {"unknown": "Невідома транзакція", "send": "Надіслати {{amount}} {{symbol}}", "cancel": "Скасувати {{token}} Затвердити для {{protocol}}", "approve": "Затвердити {{count}} {{token}} для {{protocol}}", "cancelNFTCollectionApproval": "Скасувати затвердження колекції NFT для {{protocol}}", "cancelSingleNFTApproval": "Скасувати єдине схвалення NFT для {{protocol}}", "singleNFTApproval": "Окреме схвалення NFT для {{protocol}}", "nftCollectionApproval": "Схвалення колекції NFT для {{protocol}}"}, "CancelTxPopup": {"options": {"removeLocalPendingTx": {"title": "Очистити очікування локально", "desc": "Видалити транзакцію, що очікує, з інтерфейсу"}, "quickCancel": {"title": "Швидке скасування", "tips": "Підтримується лише для транзакцій, які ще не транслювались", "desc": "Скасувати перед відправкою, без комісії за gas"}, "onChainCancel": {"desc": "Нова транзакція для скасування, потрібен газ", "title": "On-chain Cancel"}}, "removeLocalPendingTx": {"title": "Delete Transaction Locally", "desc": "This action will delete the pending transaction locally. The pending transaction may still be successfully submitted in the future."}, "title": "Скасувати транзакцію"}, "MempoolList": {"empty": "Не знайдено в жодному вузлі", "reBroadcastBtn": "Повторна трансляція", "title": "З'явився у {{count}} RPC вузлах"}, "message": {"reBroadcastSuccess": "Повторна трансляція", "broadcastSuccess": "Транслюється", "cancelSuccess": "Скасовано", "deleteSuccess": "Видалено успішно"}, "gas": {"noCost": "Без вартості Gas"}, "SkipNonceAlert": {"alert": "Пропущено <PERSON> #{{nonce}} у ланцюзі {{chainName}}. Це може спричинити очікування транзакцій попереду. <5></5> <6>Надішліть tx</6> <7></7> у ланцюзі для вирішення.", "clearPendingAlert": "Транзакція {{chainName}} ({{nonces}}) очікується понад 3 хвилини. Ви можете <5></5> <6>Очистити очікувані локально</6> <7></7> та повторно відправити транзакцію."}, "PredictTime": {"time": "Очікується, що буде упаковано в {{time}}", "failed": "Не вдалося передбачити час упаковки", "noTime": "Час упаковки прогнозується"}, "CancelTxConfirmPopup": {"title": "Очистити локальні очікування", "desc": "Це вилучить незавершену транзакцію з вашого інтерфейсу. Потім ви можете розпочати нову транзакцію.", "warning": "Видалена транзакція все ще може бути підтверджена в ланцюзі, якщо її не замінено."}}, "signedText": {"label": "Text", "empty": {"title": "Ще немає підписаних текстів", "desc": "Всі тексти, підписані чере<PERSON>, будуть перераховані тут."}}}, "receive": {"title": "Отримати {{token}} у {{chain}}", "watchModeAlert1": "Це адреса в режимі спостереження.", "watchModeAlert2": "Ви впевнені, що хочете використовувати її для отримання активів?"}, "sendToken": {"addressNotInContract": "Немає в списку адрес. <1></1><2>Додати в контакти</2>", "AddToContactsModal": {"addedAsContacts": "Додано як контакти", "editAddr": {"placeholder": "Введіть примітку до адреси", "validator__empty": "Будь ласка, введіть примітку до адреси"}, "editAddressNote": "Редагувати примітку до адреси", "error": "Не вдалося додати до контактів"}, "allowTransferModal": {"error": "Неправильний пароль", "placeholder": "Введіть пароль для підтвердження", "validator__empty": "Будь ласка, введіть пароль", "addWhitelist": "Додати до білого списку"}, "GasSelector": {"confirm": "Підтвердити", "level": {"$unknown": "Невідомо", "custom": "Власний", "fast": "Миттєво", "normal": "Швидко", "slow": "Звичайний"}, "popupDesc": "Вартість газу буде утримано з суми переказу на основі встановленої вами ціни на газ", "popupTitle": "Встановити ціну на газ (Gwei)"}, "header": {"title": "Надіслати"}, "modalConfirmAddToContacts": {"confirmText": "Підтвердити", "title": "Додати в контакти"}, "modalConfirmAllowTransferTo": {"cancelText": "Скасувати", "confirmText": "Підтвердити", "title": "Введіть пароль для підтвердження"}, "sectionBalance": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sectionChain": {"title": "Мережа"}, "sectionFrom": {"title": "<PERSON>ід"}, "sectionTo": {"addrValidator__empty": "Будь ласка, введіть адресу", "addrValidator__invalid": "Ця адреса є недійсною", "searchInputPlaceholder": "Введіть адресу або пошук", "title": "Кому"}, "sendButton": "Відправити", "tokenInfoFieldLabel": {"chain": "Мережа", "contract": "Адреса контракту"}, "tokenInfoPrice": "Ціна", "whitelistAlert__disabled": "Білий список відключено. Ви можете переказувати на будь-яку адресу.", "whitelistAlert__notWhitelisted": "Адреса не внесена до білого списку. <1 /> Я згоден надати тимчасовий дозвіл на перенесення.", "whitelistAlert__temporaryGranted": "Тимчасовий дозвіл надано", "whitelistAlert__whitelisted": "Адреса в білому списку", "balanceWarn": {"gasFeeReservation": "Потрібне резервування оплати за газ"}, "balanceError": {"insufficientBalance": "Недостатньо коштів на рахунку"}, "max": "MAX", "sectionMsgDataForEOA": {"placeholder": "Необов'язково", "title": "Повідомлення", "currentIsOriginal": "Поточний ввід - це є оригінальні дані. UTF-8:", "currentIsUTF8": "Поточний ввід - UTF-8. Вихідні дані:"}, "sectionMsgDataForContract": {"placeholder": "Необов'язково", "title": "Виклик контракту", "parseError": "Не вдалося розшифрувати виклик контракту", "simulation": "Симуляція виклику контракту:", "notHexData": "Підтримуються тільки шістнадцяткові дані"}, "blockedTransaction": "Заблокована Транзакція", "blockedTransactionCancelText": "<PERSON><PERSON>ю", "blockedTransactionContent": "Ця транзакція взаємодіє з адресою, яка знаходиться у списку санкцій OFAC."}, "sendTokenComponents": {"GasReserved": "Зарезервовано <1>0</1> {{ tokenName }} для вартості газу", "SwitchReserveGas": "Зарезервувати Gas <1 />"}, "sendNFT": {"header": {"title": "Відправити"}, "sectionChain": {"title": "Мережа"}, "sectionFrom": {"title": "<PERSON>ід"}, "sectionTo": {"title": "Кому", "addrValidator__empty": "Будь ласка, введіть адресу", "addrValidator__invalid": "Ця адреса є недійсною", "searchInputPlaceholder": "Введіть адресу або пошук"}, "nftInfoFieldLabel": {"Collection": "Колекція", "Контракт": "Контракт", "sendAmount": "Надіслати суму", "Contract": "Контракт"}, "sendButton": "Відправити", "whitelistAlert__disabled": "Білий список вимкнено. Можна переказувати на будь-яку адресу.", "whitelistAlert__whitelisted": "Адреса в білому списку", "whitelistAlert__temporaryGranted": "Тимчасовий дозвіл надано", "whitelistAlert__notWhitelisted": "Адреса не внесена до білого списку. <1 /> Я згоден надати тимчасовий дозвіл на перенесення.", "tipNotOnAddressList": "Немає в списку адрес.", "tipAddToContacts": "Додати в контакти", "confirmModal": {"title": "Введіть пароль для підтвердження"}}, "approvals": {"header": {"title": "Схвалення за {{ address }}"}, "tab-switch": {"contract": "За контрактами", "assets": "За активами"}, "component": {"table": {"bodyEmpty": {"loadingText": "Завантаження...", "noMatchText": "Немає збігів", "noDataText": "Без дозволів"}}, "ApprovalContractItem": {"ApprovalCount_one": "Затвердження", "ApprovalCount_other": "Схвалення"}, "RevokeButton": {"btnText_zero": "Відкликати", "btnText_one": "Відкликати ({{count}})", "btnText_other": "Відкликати ({{count}})", "permit2Batch": {"modalTitle_other": "Потрібно всього <2>{{count}}</2> підписів", "modalContent": "Схвалення з одного і того ж контракту Permit2 будуть згруповані разом під тим самим підписом.", "modalTitle_one": "Потр<PERSON><PERSON>н<PERSON> <2>{{count}}</2> підписів загалом"}}, "ViewMore": {"text": "Переглянути більше"}}, "search": {"placeholder": "Шукати {{ type }} за іменем/адресою"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "Контракт", "contractTrustValue": "Довіра контракту", "revokeTrends": "24-годинне відкликання трендів", "myApprovedAssets": "Мої схвалені активи", "myApprovalTime": "<PERSON><PERSON>й час схвалення"}, "columnTip": {"contractTrustValue": "Цінність відноситься до загальної вартості активів, затверджених і підданих ризику за цим контрактом. Низька цінність вказує або на ризик, або на бездіяльність протягом 180 днів.", "contractTrustValueWarning": "Цінність контракту < $100,000", "contractTrustValueDanger": "Цінність контракту < $10,000"}}, "byAssets": {"columnTitle": {"asset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Тип", "approvedAmount": "Схваленна сума", "approvedSpender": "Схвалений Витрачальник", "myApprovalTime": "Час на схвалення"}, "columnCell": {"approvedAmount": {"tipApprovedAmount": "Схваленна сума", "tipMyBalance": "<PERSON><PERSON><PERSON> баланс"}}}}, "RevokeApprovalModal": {"subTitleTokenAndNFT": "Затверджений токен і NFT", "subTitleContract": "Затверджено для наступних контрактів", "selectAll": "Вибрати все", "confirm": "Підтвердити {{ selectedCount }}", "title": "Схвалення", "unSelectAll": "Скасувати вибір всіх", "tooltipPermit2": "Це підтвердження схвалено через контракт Permit2:\n{{ permit2Id }}"}, "revokeModal": {"signAndStartRevoke": "Підписати та почати скасування", "revokeOneByOne": "Revoke One by One", "approvalCount_zero": "{{count}} схвалення", "approvalCount_other": "{{count}} підтверджень", "cancelTitle": "Скасувати решту відкликань", "approvalCount_one": "{{count}} підтвердження", "totalRevoked": "Загалом:", "confirmRevokePrivateKey": "Використовуючи seed phrase або приватний ключ, ви можете відкликати {{count}} затверджень одним натисканням.", "batchRevoke": "Масове відкликання", "revoked": "Відкликано:", "pause": "Пауза", "connectLedger": "Підклю<PERSON><PERSON><PERSON><PERSON>", "confirmTitle": "Одним натисканням: масове відкликання", "confirm": "Підтвердити", "defaultFailed": "Транзакція не вдалася", "confirmRevokeLedger": "Використовуючи адре<PERSON><PERSON> Ledger, ви можете одним натисканням скасувати {{count}} затверджень.", "gasNotEnough": "Недостатньо Gas для відправки", "useGasAccount": "Ваш газовий баланс низький. Ваш GasAccount покриє комісії за газ.", "ledgerSending": "Відправка запиту на підписання ({{current}}/{{total}})", "paused": "Призупинено", "stillRevoke": "Ще Revoke", "done": "Готово", "simulationFailed": "Симуляція не вдалася", "waitInQueue": "Зачекайте в черзі", "ledgerSended": "Будь ласка, підпишіть запит на Ledger ({{current}}/{{total}})", "ledgerAlert": "Будь ласка, відкрийте Ethereum App на вашому пристрої Ledger", "ledgerSigned": "Підписан<PERSON>. Створення транзакції ({{current}}/{{total}})", "submitTxFailed": "Не вдалося надіслати", "cancelBody": "Якщо ви закриєте цю сторінку, решта reвиконань не буде виконано.", "resume": "Продовжити", "revokeWithLedger": "Почніть відкликання за допомогою Ledger", "gasTooHigh": "Висока плата за Gas"}}, "gasTopUp": {"title": "Gas Top Up", "description": "Поповніть баланс, надіславши нам один із наявних токенів з вашого гаманця у іншій мережі. Цє будє миттєвий переказ, одразу після підтвердження платежу ви зможете отримати токени, не чекаючи, поки він стане незворотнім.", "topUpChain": "Мережа поповнення", "Amount": "Сума", "Continue": "Продовжити", "InsufficientBalance": "На контрактній адресі Rabby недостатньо коштів для поточної мережі. Будь ласка, повторіть спробу пізніше.", "hightGasFees": "Ця сума поповнення занадто мала, тому що у ції мережі високі тарифи на газ.", "No_Tokens": "Немає токенів", "InsufficientBalanceTips": "Недостатн<PERSON>й баланс", "payment": "Пла<PERSON><PERSON><PERSON> за газ", "Loading_Tokens": "Завантаження токенів...", "Including-service-fee": "У тому числі {{fee}} комісія за обслуговування", "service-fee-tip": "Надаючи послугу поповнення запасу газу, Rabby має нести втрати від коливання курсу токенів і плату за газ для поповнення запасу. Тому стягується комісія у розмірі 20%.", "Підтвердити": "Підтвердити", "Select-from-supported-tokens": "Виберіть з підтримуваних токенів", "Value": "Значення", "Payment-Token": "Платіжний токен", "Select-payment-token": "Виберіть токен який буде використовуватися для платежа", "Token": "Токен", "Balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm": "Підтвердити"}, "swap": {"title": "Свап", "pendingTip": "Tx надіслано. Якщо транзакція очікує на розгляд протягом тривалого часу, ви можете спробувати очистити очікування в налаштуваннях.", "Pending": "Очікує", "completedTip": "Транзакція у мережі, декодування даних для створення запису", "Completed": "Завершено", "slippage_tolerance": "Допуск на прослизання:", "actual-slippage": "Фактичне прослизання:", "gas-x-price": "Ціна газу: {{price}} Gwei.", "no-transaction-records": "Немає записів про транзакції", "swap-history": "Історія свапів", "InSufficientTip": "Недостатній баланс для симуляції транзакції та оцінки газу. Відображаються оригінальні котирування агрегатора", "testnet-is-not-supported": "Користувацька мережа не підтримується", "not-supported": "Не підтримується", "slippage-adjusted-refresh-quote": "Прослизання скориговано. Оновити пропозицію.", "price-expired-refresh-quote": "Термін дії ціни закінчився. Оновити котирування.", "approve-x-symbol": "Затвердити {{symbol}}", "swap-via-x": "Здійснити свап через {{name}}", "get-quotes": "Отримати котирування", "chain": "Мережа", "swap-from": "Свап з", "to": "До", "search-by-name-address": "Шукати за іменем / адресою", "amount-in": "Сума в {{symbol}}", "unlimited-allowance": "Необмежений ліміт", "insufficient-balance": "Недостатн<PERSON>й баланс", "rabby-fee": "<PERSON><PERSON> ком<PERSON>с<PERSON>я", "minimum-received": "Мінімум отримано", "there-is-no-fee-and-slippage-for-this-trade": "Для цієї угоди немає комісії та прослизання", "approve-tips": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> → 2.<PERSON>б<PERSON><PERSON><PERSON><PERSON><PERSON>и", "best": "Найкращий", "unable-to-fetch-the-price": "Неможливо отримати ціну", "fail-to-simulate-transaction": "Не вдалося зімітувати транзакцію", "security-verification-failed": "Не вдалося пройти перевірку безпеки", "need-to-approve-token-before-swap": "Потрібно схвалити токен перед свапом", "this-exchange-is-not-enabled-to-trade-by-you": "Ця біржа не увімкнена для торгівлі вами.", "enable-it": "Увімкніть її", "this-token-pair-is-not-supported": "Ця пара токенів не підтримується", "QuoteLessWarning": "Сума отримання розрахована на основі симуляції транзакції Rabby. Пропозиція, надана dex, становить {{receive}}. Ви отримаєте {{diff}} менше, ніж очікувана пропозиція.", "by-transaction-simulation-the-quote-is-valid": "При симуляції угоди котирування дійсне", "wrap-contract": "Контракт з обгортанням", "directlySwap": "Обернути токени {{symbol}} з допомогую смарт-контрактом", "rates-from-cex": "Кур<PERSON> з CEX", "edit": "Редагувати", "tradingSettingTips": "{{viewCount}} біржі пропонують котирування, а {{tradeCount}} дозволяють торгувати", "the-following-swap-rates-are-found": "Знайдено такі курси обмінів", "est-payment": "Est. О<PERSON>лата:", "est-receiving": "Est. Отри<PERSON>ання:", "est-difference": "Est. Різниця:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "Обрана пропозиція значно відрізняється від поточного курсу, може спричинити великі збитки", "rate": "<PERSON><PERSON><PERSON><PERSON>", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Низьке прослизання може спричинити невдалі транзакції через високу волатильність", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "Транзакція може бути першою через високу здатність до прослизання", "recommend-slippage": "Щоб запобігти випередженню, ми рекомендуємо прослизання <2>{{ slippage }}</2>%", "slippage-tolerance": "Допуск на прослизання", "select-token": "Вибрати токен", "enable-exchanges": "Увімкнути обміни", "exchanges": "Об<PERSON><PERSON><PERSON>и", "view-quotes": "Переглянути котирування", "trade": "Торгівля", "dex": "<PERSON>", "cex": "Cex", "enable-trading": "Увімкнути торгівлю", "i-understand-and-accept-it": "Я розумію і приймаю це", "confirm": "Підтверд<PERSON><PERSON>ю", "tradingSettingTip1": "1. після ввімкнення ви будете взаємодіяти з контрактом безпосередньо з біржі", "tradingSettingTip2": "2. <PERSON><PERSON> не несе відповідальності за будь-які ризики, що випливають з контракту біржі", "gas-fee": "GasFee: {{gasUsed}}", "estimate": "Розрахункова:", "actual": "Фактично:", "rabbyFee": {"rate": "Ко<PERSON><PERSON><PERSON><PERSON><PERSON>на ставка", "title": "<PERSON><PERSON> ком<PERSON>с<PERSON>я", "wallet": "Гама<PERSON><PERSON><PERSON>ь", "swapDesc": "Гаманець Rabby завжди знаходить найкращі можливі тарифи від провідних агрегаторів і перевіряє надійність їхніх пропозицій. Rabby стягує комісію 0.25% (0% за обгортання), яка автоматично включається у вартість.", "bridgeDesc": "Гаманець Rabby завжди знайде найкращий можливий курс від найкращих агрегаторів і перевірить надійність їхніх пропозицій. Rabby стягує комісію в розмірі 0,25%, яка автоматично включається у квоту.", "button": "Зрозуміло"}, "lowCreditModal": {"title": "Цей токен має низьке кредитне значення", "desc": "Низька кредитна вартість часто сигналізує про високий ризик, наприклад, токен-пастка або дуже низька ліквідність."}, "no-slippage-for-wrap": "Жодних прослизань для Wrap", "sort-with-gas": "Сортування з gas", "max": "МАКС", "approve-swap": "Схвалити та Swap", "hidden-no-quote-rates_one": "{{count}} курс недоступний", "Gas-fee-too-high": "Плата за Gas занадто висока", "approve-and-swap": "Затвердити та обміняти через {{name}}", "hidden-no-quote-rates_other": "{{count}} цін недоступно", "preferMEVTip": "Увімкніть функцію \"MEV Guarded\" для обмінів Ethereum, щоб знизити ризики атак типу \"бутерброд\". Примітка: ця функція не підтримується, якщо ви використовуєте користувацькі RPC або адресу підключення гаманця.", "no-fee-for-wrap": "Wrap без комісії Rabby", "no-quote-found": "Не знайдено котирування", "Auto": "Авто", "price-impact": "Ціновий вплив", "process-with-two-step-approve": "Перейдіть до двоетапного затвердження", "usd-after-fees": "≈ {{usd}}", "no-fees-for-wrap": "Без комісії Rabby за Wrap", "source": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetch-best-quote": "Отримання найкращої пропозиції", "No-available-quote": "Недоступна котирування", "two-step-approve-details": "Токен USDT вимагає 2 транзакції для зміни дозволу. Спочатку потрібно скинути дозвол до нуля, а потім встановити нове значення дозволу.", "two-step-approve": "Підпишіть 2 транзакції, щоб змінити allowance", "loss-tips": "Ви втрачаєте {{usd}}. Спробуйте меншу суму на маленькому ринку.", "preferMEV": "Віддати перевагу MEV Guarded", "from": "<PERSON>ід"}, "manageAddress": {"no-address": "Немає адреси", "no-match": "Немає збігів", "current-address": "Поточна адреса", "address-management": "Управління адресами", "update-balance-data": "Оновити дані балансу", "search": "По<PERSON><PERSON>к", "manage-address": "Керування адресою", "deleted": "Видалено", "whitelisted-address": "Адреса в білому списку", "addressTypeTip": "Імпортовано за допомогою {{type}}", "delete-desc": "Перш ніж видалити, візьміть до уваги наступні пункти, щоб зрозуміти, як захистити свої активи.", "delete-checklist-1": "Я розумію, що якщо я видалю цю адресу, відповідний приватний ключ та Seed Фраза цієї адреси буде видалено і Rabby НЕ зможе його відновити.", "delete-checklist-2": "Я підтверджую, що створив резервну копію приватного ключа або ключової фрази і готовий її видалити.", "confirm": "Підтвердити", "cancel": "Скасувати", "delete-private-key-modal-title_one": "Видалити адресу приватного ключа {{count}}", "delete-private-key-modal-title_other": "Видалити {{count}} адрес приватних ключів", "delete-seed-phrase-title_one": "Видалити ключову фразу та її адресу {{count}}", "delete-seed-phrase-title_other": "Видалити початкову фразу та її {{count}} адреси", "delete-title_one": "Видалити {{count}} {{brand}} адресу", "delete-title_other": "Видалити {{count}} {{brand}} адреси", "delete-empty-seed-phrase": "Видалити початкову фразу та її 0-адресу", "hd-path": "Шлях до HD:", "no-address-under-seed-phrase": "Ви не імпортували жодної адреси під цією ключовою фразою.", "add-address": "Додати адресу", "delete-seed-phrase": "Видалити ключову фразу", "confirm-delete": "Підтвердити видалення", "private-key": "Прива<PERSON><PERSON><PERSON> ключ", "seed-phrase": "Ключова фраза", "watch-address": "Адреса спостереження", "backup-seed-phrase": "Резервна ключова фраза", "delete-all-addresses-but-keep-the-seed-phrase": "Видалити всі адреси, але зберегти ключову фразу", "delete-all-addresses-and-the-seed-phrase": "Видалити всі адреси та ключову фразу", "seed-phrase-delete-title": "Видалити ключову фразу?", "sort-address": "Сортувати адресу", "passphraseError": "Фраза-пароль недійсна", "sort-by-balance": "Сортувати за балансом", "enterThePassphrase": "Введіть ключову фразу", "enterPassphraseTitle": "Введіть парольну фразу, щоб підписати", "addNewAddress": "Додати нову адресу", "sort-by-address-note": "Сортувати за адресною приміткою", "sort-by-address-type": "Сортувати за типом адреси", "CurrentDappAddress": {"desc": "Змінити адресу Dapp\n"}}, "dashboard": {"home": {"offline": "Немає підключення до мережі", "panel": {"swap": "Свап", "send": "Відправити", "receive": "Отримати", "gasTopUp": "Gas Top Up", "queue": "Черга", "transactions": "Тран<PERSON><PERSON>к<PERSON><PERSON>ї", "approvals": "Схвалення", "feedback": "Відгуки", "more": "Більше", "manageAddress": "Керувати адресою", "nft": "NFT", "bridge": "Міст", "ecology": "Екосистема", "rabbyPoints": "<PERSON><PERSON>", "mobile": "Mobile Sync"}, "comingSoon": "Скоро", "soon": "Незабаром", "refreshTheWebPageToTakeEffect": "Оновити веб-сторінку, щоб зміни набули чинності", "rabbyIsInUseAndMetamaskIsBanned": "<PERSON><PERSON> використовується, а Metamask заборонений", "flip": "Перевернути", "metamaskIsInUseAndRabbyIsBanned": "Metamask використовується, а Rabby заборонений", "transactionNeedsToSign": "транзакцію потрібно підписати", "transactionsNeedToSign": "транзакції потрібно підписати", "view": "Перегляд", "viewFirstOne": "Переглянути першу", "rejectAll": "Від<PERSON>илити всі", "pendingCount": "1 Очікує", "pendingCountPlural": "{{countStr}} Незавершені", "queue": {"title": "Черга", "count": "{{count}} у"}, "whatsNew": "Що нового", "importType": "Імпортовано за допомогою {{type}}", "chainEnd": "ланцюг", "missingDataTooltip": "Через поточні проблеми з мережею {{text}} баланс може не оновлюватись.", "chain": "лан<PERSON><PERSON><PERSON>,"}, "recentConnection": {"disconnected": "Відключено", "rpcUnavailable": "Кастомний RPC недоступний", "metamaskTooltip": "Ви віддаєте перевагу використанню MetaMask з цим dapp. Оновіть ці налаштування будь-коли в Налаштуваннях > MetaMask Preferred Dapps", "connected": "Підключено", "notConnected": "Не підключено", "connectedDapp": "Rabby не підключено до поточного Dapp'а. Щоб підключитися, знайдіть і натисніть кнопку підключитися на сторінці Dapp'а.", "noDappFound": "Dapp не знайдено", "disconnectAll": "Від'єднати всі", "disconnectRecentlyUsed": {"title": "Від'єднати нещодавно використані <strong>{{count}}</strong> DApps", "description": "Закріплені DApps залишаться підключеними", "title_one": "Відключити <strong>{{count}}</strong> підключену Dapp", "title_other": "Відключити <strong>{{count}}</strong> підключених Dapps"}, "title": "Підключено до Dapp", "pinned": "Закріплено", "noPinnedDapps": "Немає закріплених Dapps", "dragToSort": "Перетягнути для сортування", "recentlyConnected": "Нещодавно підключено", "noRecentlyConnectedDapps": "Немає нещодавно підключених Dapps", "dapps": "<PERSON><PERSON>", "noConnectedDapps": "Немає підключених Dapps", "metamaskModeTooltip": "Не вдається підключити Rabby на цьому Dapp? Спробуйте увімкнути <1>режим MetaMask</1>", "metamaskModeTooltipNew": "Гамане<PERSON><PERSON> Rabby підключиться, коли ви виберете \"MetaMask\" у Dapp. Ви можете керувати цим у Більше > Підключити Ra<PERSON>, маскуючись під MetaMask"}, "feedback": {"directMessage": {"content": "Пряме повідомлення", "description": "Поспілкуйтеся з представником Rabby Wallet на DeBank"}, "proposal": {"content": "Пропозиція", "description": "Подати пропозицію для Rabby Wallet на DeBank"}, "title": "Зворотній зв'язок"}, "nft": {"empty": "У підтримуваних колекціях не знайдено NFT", "collectionList": {"collections": {"label": "Колекції"}, "all_nfts": {"label": "Всі NFT"}}, "listEmpty": "Ви ще не отримали жодного NFT", "modal": {"collection": "Колекція", "chain": "Мережа", "lastPrice": "Остання ціна", "purchaseDate": "Дата покупки", "sendTooltip": "Наразі підтримуються лише ERC-721 та ERC-1155 NFT", "send": "Відправити"}}, "rabbyBadge": {"imageLabel": "<PERSON><PERSON>", "title": "Вимагайте Rabby Badge для", "enterClaimCode": "Введіть код вимоги", "swapTip": "Спочатку вам потрібно виконати свап через DEX у гаманці Rabby Wallet.", "goToSwap": "Перейти до свапу", "claim": "Вимагати", "viewYourClaimCode": "Переглянути код вашої заявки", "noCode": "Ви не активували код претензії для цієї адреси", "learnMoreOnDebank": "Дізнатися більше на DeBank", "rabbyValuedUserNo": "<PERSON><PERSON> Valued User No.{{num}}", "claimSuccess": "Успіх заявки", "viewOnDebank": "Переглянути у DeBank", "learnMore": "Дізнатися більше", "rabbyFreeGasUserNo": "Rabby Free Gas Користувач №{{num}}", "freeGasTitle": "Отримайте безкоштовний Gas Badge для", "freeGasTip": "Будь ласка, підпишіть транзакцію, використовуючи Free Gas. Кнопка 'Free Gas' з'явиться автоматично, коли вам не вистачає газу.", "freeGasNoCode": "Будь ласка, натисніть кнопку нижче, щоб відвідати DeBank і спочатку отримати код запиту, використовуючи вашу поточну адресу."}, "contacts": {"noDataLabel": "немає даних", "noData": "Немає даних", "oldContactList": "Старий список контактів", "oldContactListDescription": "У зв'язку зі злиттям контактів та відстежувальних адрес, старі контакти будуть збережені для вас тут, а через деякий час ми видалимо список. Будь ласка, додайте вчасно, якщо ви продовжуєте користуватися."}, "security": {"tokenApproval": "Затвердження токена", "nftApproval": "Схвалення NFT", "comingSoon": "Більше функцій незабаром", "title": "Безпека"}, "settings": {"lock": {"never": "Ніколи"}, "7Days": "7 днів", "1Day": "1 день", "4Hours": "4 години", "1Hour": "1 година", "10Minutes": "10 хвилин", "backendServiceUrl": "URL-адреса внутрішньої служби", "inputOpenapiHost": "Будь ласка, введіть хост openapi", "pleaseCheckYourHost": "Будь ласка, перевірте ваш хост", "host": "Хо<PERSON>т", "reset": "Відновити початкові налаштування", "save": "Зберегти", "pendingTransactionCleared": "Незавершена транзакція очищена", "clearPending": "Очистити локально очікуючі", "clearPendingTip1": "Ця дія видаляє транзакцію в очікуванні з вашого інтерфейсу, допомагаючи вирішити проблеми, викликані тривалими періодами очікування в мережі.", "clearPendingTip2": "Це не впливає на баланс ваших облікових записів і не вимагає повторного введення вашої seed phrase. Усі активи та дані облікового запису залишаються в безпеці.", "autoLockTime": "Час автоматичного блокування", "claimRabbyBadge": "Отримати Rabby Badge!", "cancel": "Скасувати", "enableWhitelist": "Увімкнути білий список", "disableWhitelist": "Вимкнути білий список", "enableWhitelistTip": "Після увімкнення ви зможете надсилати ресурси лише на адреси з білого списку за допомогою Rabby.", "disableWhitelistTip": "Після вимкнення ви можете надсилати ресурси на будь-які адреси", "warning": "Попередження", "clearWatchAddressContent": "Ви хочете видалити всі адреси Режиму спостереження?", "updateVersion": {"content": "Доступне нове оновлення для Rabby Wallet. Натисніть, щоб перевірити, як оновити вручну.", "okText": "Дивіться інструкцію", "successTip": "Ви використовуєте останню версію", "title": "Доступно оновлення"}, "features": {"label": "Можливості", "lockWallet": "Заблокувати гаманець", "signatureRecord": "Ваші підписи", "manageAddress": "Керувати адресою", "connectedDapp": "Ваші підключення до dApp", "rabbyPoints": "<PERSON><PERSON>", "searchDapps": "<PERSON><PERSON><PERSON><PERSON><PERSON> Dapps", "gasTopUp": "Поповнення Gas"}, "settings": {"label": "Налаштування", "enableWhitelistForSendingAssets": "Увімкнути білий список для відправки активів", "customRpc": "Кастомний RPC", "metamaskPreferredDapps": "Додатки для використання виключно з Metamask", "currentLanguage": "Поточна мова", "enableTestnets": "Увімкнути тестові мережі", "toggleThemeMode": "Тема режиму", "customTestnet": "Додати власну мережу", "themeMode": "Тема режиму", "metamaskMode": "Під<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, замаскував<PERSON>и<PERSON>ь під Meta<PERSON>", "enableDappAccount": "Незалежно змінюйте адресу Dapp\n"}, "aboutUs": "Про нас", "currentVersion": "Поточна версія", "updateAvailable": "Доступне оновлення", "supportedChains": "Підтримувані мережі", "followUs": "Слідкуйте за нами", "testnetBackendServiceUrl": "URL-адреса внутрішньої служби Testnet", "clearWatchMode": "Очистити режим спостереження", "requestDeBankTestnetGasToken": "Запросити тестові токени для DeBank Testnet", "clearPendingWarningTip": "Вилучена транзакція може все ще бути підтверджена в ланцюжку, якщо її не замінити.", "DappAccount": {"button": "Увімкнути\n", "title": "Перемикайте адресу Dapp незалежно\n", "desc": "Увімкнувши, ви можете самостійно вибрати адресу підключення до кожного Dapp. Зміна основної адреси не вплине на адресу, підключену до кожного Dapp.\n"}}, "tokenDetail": {"blockedTip": "Заблокований токен не буде показано у списку токенів", "blocked": "Заблоковано", "selectedCustom": "Токен не внесено до списку Rabby. Ви додали його до списку токенів за власним бажанням.", "notSelectedCustom": "Токен не внесено до списку Rabby. Його буде додано до списку токенів, якщо ви його увімкнете.", "customized": "Кастомізований", "scamTx": "Шахрайський tx", "txFailed": "Не вдалося", "notSupported": "Токен у цій мережі не підтримується", "swap": "Свап", "send": "Відправити", "receive": "Отримати", "noTransactions": "Без транзакцій", "customizedButton": "налаштована", "blockedButton": "заблоковано", "customizedButtons": "кастомні токени", "blockedButtons": "заблоковані токени", "ContractAddress": "Адрес контракту", "customizedListTitle": "кастомний токен", "NoListedBy": "Немає інформації про список", "blockedListTitle": "заблокований токен", "customizedListTitles": "кастомні токени", "verifyScamTips": "Це токен-шахрайство", "SupportedExchanges": "Підтримувані біржі", "OriginalToken": "Оригінальний токен", "blockedListTitles": "заблоковані токени", "customizedHasAddedTips": "Токен не внесений до списку Rabby. Ви додали його до списку токенів вручну.", "blockedTips": "Заблоковані токени не будуть відображатися в списку токенів.", "TokenName": "Назва токена", "myBalance": "<PERSON><PERSON><PERSON> баланс", "IssuerWebsite": "Веб-сайт емітента", "Chain": "Цепочка", "NoSupportedExchanges": "Немає доступних підтримуваних бірж", "maybeScamTips": "Це токен низької якості і може бути шахрайством", "noIssuer": "Немає інформації про емітента", "OriginIssue": "Нативно випущено на цьому блокчейні", "BridgeIssue": "То<PERSON><PERSON>н, що перебуває в мості, від третьої сторони", "BridgeProvider": "Постачальник моста", "ListedBy": "Пер<PERSON><PERSON><PERSON><PERSON> від", "fdvTips": "Капіталізація ринку, якщо максимальна пропозиція була в обігу. Повна розведена оцінка (FDV) = Ціна x Максимальна пропозиція. Якщо максимальна пропозиція є null, FDV = Ціна x Загальна пропозиція. Якщо жодна з максимальних або загальних пропозицій не визначена або є безкінечною, FDV не відображається.", "AddToMyTokenList": "Додати до мого списку токенів"}, "assets": {"usdValue": "ВАРТІСТЬ У USD", "amount": "СУМА", "portfolio": {"nftTips": "Розраховано на основі мінімальної ціни, визнаної цим протоколом.", "fractionTips": "Розраховується на основі ціни прив'язаного токена ERC20."}, "tokenButton": {"subTitle": "Токен з цього списку не буде додано до загального балансу"}, "table": {"assetAmount": "Актив / Сума", "price": "Ціна", "useValue": "Вартість у USD", "healthRate": "Рівень здоров'я", "debtRatio": "Коефіцієнт боргу", "unlockAt": "Розблокувати в", "lentAgainst": "ПОЗИЧЕНО ПРОТИ", "type": "Тип", "strikePrice": "Ціна виконання", "exerciseEnd": "Кінець виконання", "tradePair": "Торгова пара", "side": "Сторона", "leverage": "Кредитне плече", "PL": "P&L", "unsupportedPoolType": "Непідтримуваний тип пулу", "claimable": "Можна вимагати", "endAt": "Кінець в", "dailyUnlock": "Щоденне розблокування", "pool": "ПУЛ", "token": "Токен", "balanceValue": "Баланс / Вартість", "percent": "Відсоток", "summaryTips": "Вартість активів, поділена на загальну чисту вартість", "summaryDescription": "Всі активи в протоколах (наприклад, токени LP) прирівнюються до базових активів для статистичних розрахунків", "noMatch": "Немає збігів", "lowValueDescription": "Тут будуть показані активи з низькою вартістю", "lowValueAssets": "{{count}} малоцінні активи", "lowValueAssets_0": "{{count}} токен з низькою вартістю", "lowValueAssets_one": "{{count}} токенів низької вартості", "lowValueAssets_other": "{{count}} низьковартісних токенів"}, "noAssets": "Немає активів", "blockLinkText": "Адреса для пошуку токена блоку", "blockDescription": "Тут буде показано заблокований вами токен", "unfoldChain": "Розгорнути 1 мережу", "unfoldChainPlural": "Розгорнути {{moreLen}} мереж", "customLinkText": "Шукайте адресу для додавання кастомного токена", "customDescription": "Тут буде показано доданий вами кастомний токен", "comingSoon": "Незабаром...", "searchPlaceholder": "Токени", "AddMainnetToken": {"notFound": "Токен не знайдено", "isBuiltInToken": "Токен уже підтримується", "searching": "Пошук токена", "tokenAddress": "Токен Адреса", "title": "Додати власний Token", "selectChain": "Виберіть chain", "tokenAddressPlaceholder": "Токен-адреса"}, "AddTestnetToken": {"tokenAddressPlaceholder": "Токенна адреса", "searching": "Пошук Token", "tokenAddress": "Токенна адреса", "notFound": "Токен не знайдено", "title": "Додати токен користувацької мережі", "selectChain": "Вибрати ланцюг"}, "TestnetAssetListContainer": {"add": "Токен", "addTestnet": "Мережа"}, "customButtonText": "Додати користувацький токен", "addTokenEntryText": "Токен", "noTestnetAssets": "Немає активів у користувацькій мережі"}, "hd": {"howToConnectLedger": "Як підклю<PERSON>и<PERSON><PERSON> Ledger", "userRejectedTheRequest": "Користу<PERSON><PERSON><PERSON> відхилив запит.", "ledger": {"doc1": "Підклю<PERSON><PERSON><PERSON>ь од<PERSON><PERSON>", "doc2": "Введіть пін-код для розблокування", "doc3": "Відкрийте додаток Ethereum", "перепідключіться": "Якщо це не спрацює, спробуйте <1>підключитися з самого початку.</1>", "connected": "Ledger підключено", "reconnect": "Якщо це не працює, спробуйте <1>повторно підключити з самого початку.</1>"}, "howToSwitch": "Як переключитися", "keystone": {"doc1": "Підключіть оди<PERSON>tone", "doc2": "Введіть пароль, щоб розблокувати", "title": "Переконайтеся, що ваш Keystone 3 Pro знаходиться на головній сторінці", "reconnect": "Якщо це не працює, спробуйте <1>підключити знову з самого початку.</1>", "doc3": "Підтвердити підключення до комп'ютера"}, "imkey": {"doc1": "Підключити один <PERSON>ey", "doc2": "Введіть пін-код, щоб розблокувати"}, "howToConnectImKey": "Як підключити imKey", "ledgerIsDisconnected": "<PERSON>аш Ledger не підключено", "howToConnectKeystone": "Як підключи<PERSON><PERSON> Keystone"}, "GnosisWrongChainAlertBar": {"warning": "Safe address does not support {{chain}}", "notDeployed": "Ваша Safe адреса не розгорнута на цьому ланцюжку"}, "echologyPopup": {"title": "Екосистема"}, "MetamaskModePopup": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enableDesc": "Увімкніть, якщо Dapp працює лише з MetaMask", "toastSuccess": "Увімкнено. Оновіть сторінку, щоб повторно підключитися.", "footerText": "Додати більше Dapps до MetaMask Mode в Більше > MetaMask Mode", "desc": "Якщо ви не можете підключити Rabby на Dapp, увімкніть MetaMask Mode та підключіться, вибравши опцію MetaMask."}, "offlineChain": {"chain": "{{chain}} згодом не буде інтегровано.", "tips": "{{chain}} Chain не буде інтегровано на {{date}}. Ваші активи не будуть порушені, але не будуть включені в ваш загальний баланс. Щоб отримати до них доступ, ви можете додати його як користувацьку мережу в \"Більше\"."}, "recentConnectionGuide": {"title": "Тут змініть адресу для підключення до Dapp\n", "button": "Отримано\n"}}, "nft": {"floorPrice": "/ Ринкова Ціна:", "title": "NFT", "all": "Усі", "starred": "Виділено ({{count}})", "empty": {"title": "Немає позначених NFT", "description": "Ви можете вибрати NFT з \"Усіх\" і додати до \"Позначених\""}, "noNft": "Немає NFT"}, "newAddress": {"title": "Додати адресу", "importSeedPhrase": "Імпортувати ключову фразу", "importPrivateKey": "Імпортувати приватний ключ", "importMyMetamaskAccount": "Імпортувати мій обліковий запис MetaMask", "addContacts": {"content": "Додати контакти", "description": "Ви також можете використовувати його як адресу тільки для перегляду", "required": "Будь ласка, введіть адресу", "notAValidAddress": "Не дійсна адреса", "scanViaMobileWallet": "Відсканувати через мобільний гаманець", "scanViaPcCamera": "Відсканувати через камеру ПК", "scanQRCode": "Сканувати QR-коди за допомогою гаманців, сумісних з WalletConnect", "walletConnect": "Підключити гаманець", "walletConnectVPN": "WalletConnect буде працювати нестабільно, якщо ви використовуєте VPN.", "cameraTitle": "Будь ласка, відскануйте QR-код своєю камерою", "addressEns": "Адреса / ENS"}, "unableToImport": {"title": "Неможливо імпортувати", "description": "Імпорт декількох апаратних гаманців на основі QR не підтримується. Будь ласка, видаліть усі адреси з {{0}} перед імпортом іншого пристрою."}, "connectHardwareWallets": "Підключити апаратні гаманці", "connectMobileWalletApps": "Підключити додатки мобільних гаманців", "connectInstitutionalWallets": "Підключити інституційні гаманці", "createNewSeedPhrase": "Створити нову ключову фразу", "importKeystore": "Імпортувати сховище ключів", "selectImportMethod": "Вибрати метод імпорту", "theSeedPhraseIsInvalidPleaseCheck": "Ключова фраза є недійсною, будь ласка, перевірте!", "seedPhrase": {"importTips": "Ви можете вставити всю вашу секретну фразу відновлення в 1-е поле", "whatIsASeedPhrase": {"question": "Що таке ключова фраза?", "answer": "Фраза з 12, 18 або 24 слів, яка використовується для контролю ваших активів."}, "isSafeToImportItInRabby": {"question": "Чи безпечно імпортувати його в Rabby?", "answer": "Так, він буде зберігатися локально у вашому браузері і буде доступний лише вам"}, "importError": "[CreateMnemonics] неочікуваний крок {{0}}", "importQuestion4": "Якщо я видал<PERSON>, не створивши резервну копію ключової фрази, <PERSON><PERSON> не зможе її відновити.", "riskTips": "Перш ніж почати, будь ласка, прочитайте і пам'ятайте про наступні поради щодо безпеки.", "showSeedPhrase": "Показати ключову фразу", "backup": "Створити резервну копію ключової фрази", "backupTips": "Переконайтеся, що ніхто не дивиться на ваш екран, коли ви створюєте резервну копію ключової фрази", "copy": "Скопіювати ключову фразу", "saved": "Я зберіг фразу", "pleaseSelectWords": "Будь ласка, виберіть слова", "verificationFailed": "Верифікація не пройшла", "createdSuccessfully": "Створено успішно", "verifySeedPhrase": "Перевірити ключову фразу", "fillInTheBackupSeedPhraseInOrder": "Заповнити резервну фразу по порядку", "wordPhrase": "У мене є <1>{{count}}</1>-словосполучення", "clearAll": "Очистити все", "isItSafeToImportItInRabby": {"question": "Чи безпечно імпортувати його в Rabby?", "answer": "Так, він буде збережений локально у вашому браузері і доступний лише вам."}, "slip39SeedPhrasePlaceholder_few": "Введіть вашу {{count}}-у частку seed фрази тут", "slip39SeedPhraseWithPassphrase": "У мене є <0>{{SLIP39}}</0> Початкова фраза з Паролем", "slip39SeedPhrase": "У мене є <0>{{SLIP39}}</0> Seed Phrase", "pastedAndClear": "Вставлено та буфер обміну очищено", "slip39SeedPhrasePlaceholder_one": "Введіть вашу {{count}}-шу частину seed-фрази тут", "inputInvalidCount_other": "{{count}} введень не відповідають нормам Seed Phrase, перевірте, будь ласка.", "slip39SeedPhrasePlaceholder_two": "Введіть тут {{count}}-у частину посівної фрази", "invalidContent": "Недійсний вміст", "passphrase": "Парольна фраза", "wordPhraseAndPassphrase": "У мене є фраза з <1>{{count}}</1> словами з Passphrase", "slip39SeedPhrasePlaceholder_other": "Введіть тут {{count}}-й частину вашої seed фрази", "inputInvalidCount_one": "Вхідні дані не відповідають нормам Seed Phrase, будь ласка, перевірте.", "importQuestion2": "Моя семантична фраза зберігається лише на моєму пристрої. Rabby не може отримати до неї доступ.", "importQuestion3": "Якщо я видалю Rabby без резервного копіювання своєї фрази відновлення, її не можна буде відновити через Rabby.", "importQuestion1": "Якщо я втрачу або поділюся своєю seed-фразою, я втрачу доступ до своїх активів назавжди."}, "metamask": {"step1": " Експортуйте сід фразу або приватний ключ з Metamask<br /> <1> Натисніть, щоб переглянути підручник <1/></1>", "step2": " Імпортуйте сід фразу або приватний ключ до Rabby", "step3": "Імпорт завершено, і всі ваші активи <br /> з'являться автоматично", "how": "Як імпортувати мій обліковий запис MetaMask?", "step": "Крок", "importSeedPhrase": "Імпортувати ключову фразу або приватний ключ", "importSeedPhraseTips": "Це буде збережено лише локально у браузері. Rabby ніколи не матиме доступу до вашої приватної інформації.", "tips": "Підказки:", "tipsDesc": "Ваша сід фраза/приватний ключ не належить MetaMask або якомусь конкретному гаманцю; він належить тільки вам."}, "privateKey": {"required": "Будь ласка, введіть приватний ключ", "placeholder": "Введіт ваш приватний ключ", "whatIsAPrivateKey": {"question": "Що таке приватний ключ?", "answer": "Рядок букв і цифр, який використовується для контролю ваших активів."}, "isSafeToImportItInRabby": {"question": "Чи безпечно імпортувати його в Rabby?", "answer": "Так, він буде зберігатися локально у вашому браузері і буде доступний лише вам"}, "isItPossibleToImportKeystore": {"question": "Чи можна імпортувати KeyStore?", "answer": "Так, ви можете <1> імпортувати KeyStore </1> тут."}, "notAValidPrivateKey": "Не дійсний приватний ключ", "repeatImportTips": {"question": "Ви хочете переключитися на цю адресу?", "desc": "Ця адреса вже була імпортована."}, "isItSafeToImportItInRabby": {"question": "Чи безпечно імпортувати це в Ra<PERSON>?", "answer": "Так, він буде зберігатися локально у вашому браузері і буде доступний тільки вам."}}, "importedSuccessfully": "Імпортовано успішно", "ledger": {"title": "Підклю<PERSON><PERSON><PERSON><PERSON>", "cameraPermissionTitle": "Дозволити Rabby доступ до камери", "cameraPermission1": "Дозволити Rabby доступ до камери у спливаючому вікні браузера", "allowRabbyPermissionsTitle": "Дозволити Ra<PERSON> доступ до:", "ledgerPermission1": "Підключення до HID-пристрою", "ledgerPermissionTip": "Будь ласка, натисніть \\\"Дозволити\\\" нижче та авторизуйте доступ до вашого Ledger у наступному спливаючому вікні.", "permissionsAuthorized": "Дозволи авторизовано", "nowYouCanReInitiateYourTransaction": "Тепер ви можете повторно ініціювати вашу транзакцію.", "allow": "Дозволити", "error": {"ethereum_app_not_installed_error": "Будь ласка, встановіть Ethereum застосунок на вашому пристрої Ledger.", "ethereum_app_open_error": "Будь ласка, встановіть/прийміть Ethereum app на вашому Ledger пристрої.", "running_app_close_error": "Не вдалося закрити запущену програму на вашому пристрої Ledger.", "ethereum_app_unconfirmed_error": "Ви відхилили запит на відкриття додатку Ethereum."}}, "walletConnect": {"connectYour": "Підключіть свій", "viaWalletConnect": "через Wallet Connect", "connectedSuccessfully": "Підключено успішно", "qrCodeError": "Будь ласка, перевірте вашу мережу або оновіть QR-код", "qrCode": "QR-код", "url": "URL", "changeBridgeServer": "Змінити сервер моста", "status": {"received": "Сканування виконано успішно. Очікує на підтвердження", "rejected": "З'єднання скасовано. Будь ласка, відскануйте QR-код для повторної спроби.", "brandError": "Неправильний додаток гаманця.", "brandErrorDesc": "Будь ласка, використовуйте {{brandName}} для підключення", "accountError": "Адреса не збігається.", "accountErrorDesc": "Будь ласка, змініть адресу в мобільному гаманці", "connected": "Підключено", "duplicate": "Адреса, яку ви намагаєтеся імпортувати, дублюється", "default": "Сканувати за допомогою {{brand}}"}, "title": "З'єднатися з {{brandName}}", "disconnected": "Від'єднано", "accountError": {}, "tip": {"accountError": {"tip1": "Підклю<PERSON><PERSON>н<PERSON>, але не може підписати.", "tip2": "Будь ласка, перейдіть на правильну адресу в мобільному гаманці"}, "disconnected": {"tip": "Не підключено до {{brandName}}"}, "connected": {"tip": "Підключено до {{brandName}}"}}, "button": {"disconnect": "Від'єднати", "connect": "Підключити", "howToSwitch": "Як переключити"}}, "hd": {"tooltip": {"removed": "Адреса вилучена з Rabby", "added": "Адреса додана до Rabby", "connectError": "Підключення зупинено. Будь ласка, оновіть сторінку для повторного підключення.", "disconnected": "Не вдалося підключитися до апаратного гаманця. Будь ласка, спробуйте підключитися ще раз."}, "waiting": "Очікує", "clickToGetInfo": "Натисніть, щоб отримати інформацію у мережі", "addToRabby": "Додати в Rabby", "basicInformation": "Основна інформація", "addresses": "Адреси", "loadingAddress": "Завантаження адрес {{0}}/{{1}}", "notes": "Примітки", "getOnChainInformation": "Отримати інформацію про мережі", "hideOnChainInformation": "Приховати інформацію про мережі", "usedChains": "Використані мережі", "firstTransactionTime": "Час першої транзакції", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: О<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> HD-шлях до Ledger. У перших 3 адресах вказані адреси, що використовуються у мережі.", "bip44": "Стандарт BIP44: HD-шлях, визначений протоколом BIP44. У перших 3 адресах вказано адреси, що використовуються у мережі.", "legacy": "Legacy: <PERSON>-<PERSON>л<PERSON><PERSON>, що використовується MEW / Mycrypto. У перших 3 адресах вказані адреси, що використовуються у мережі."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: О<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ий HD-шлях до Ledger. У перших 3 адресах немає адрес, що використовуються у мережі.", "bip44": "BIP44 Standard: HD-шлях, визначений протоколом BIP44. У перших 3 адресах немає адрес, що використовуються у мережі.", "legacy": "Legacy: <PERSON>-<PERSON>л<PERSON><PERSON>, що використовується MEW / Mycrypto. У перших 3 адресах немає адрес, що використовуються у мережі."}}, "trezor": {"hdPathType": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44.", "ledgerLive": "Ledger Live: офі<PERSON><PERSON><PERSON><PERSON><PERSON> шлях HD від Ledger.", "legacy": "Спадщина: <PERSON> шлях, який використовується MEW / Mycrypto."}, "hdPathTypeNoChain": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44.", "ledgerLive": "Ledger Live: Ledger official HD path.", "legacy": "Спадщина: HD шлях, що використовується MEW / Mycrypto."}, "message": {"disconnected": "{{0}}Підключення зупинено. Будь ласка, оновіть сторінку, щоб підключитися знову."}}, "onekey": {"hdPathType": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44."}}, "mnemonic": {"hdPathType": {"default": "За замовчуванням: Використовується типовий HD-шлях для імпорту початкової фрази.", "legacy": "Легендарний: HD шлях, що використовується MEW / Mycrypto.", "bip44": "Стандарт BIP44: HDpath, визначений протоколом BIP44.", "ledgerLive": "Ledger Live: Ledger офіційний HD шлях."}, "hdPathTypeNoChain": {"default": "За замовчуванням: Використовується типовий HD-шлях для імпорту початкової фрази."}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: О<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> HD-шлях до Ledger. У перших 3 адресах вказані адреси, що використовуються у мережі.", "bip44": "Стандарт BIP44: HD-шлях, визначений протоколом BIP44. У перших 3 адресах вказано адреси, що використовуються у мережі.", "legacy": "Legacy: <PERSON>-<PERSON>л<PERSON><PERSON>, що використовується MEW / Mycrypto. У перших 3 адресах вказані адреси, що використовуються у мережі."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: О<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ий HD-шлях до Ledger. У перших 3 адресах немає адрес, що використовуються у мережі.", "bip44": "BIP44 Standard: HD-шлях, визначений протоколом BIP44. У перших 3 адресах немає адрес, що використовуються у мережі.", "legacy": "Legacy: <PERSON>-<PERSON>л<PERSON><PERSON>, що використовується MEW / Mycrypto. У перших 3 адресах немає адрес, що використовуються у мережі."}, "switch": {"title": "Переключитися на новий пристрій GridPlus", "content": "Імпорт декількох пристроїв GridPlus не підтримується Якщо ви переключитесь на новий пристрій GridPlus, список адрес поточного пристрою буде видалено перед початком процесу імпорту."}, "switchToAnotherGridplus": "Переключитися на інший GridPlus"}, "keystone": {"hdPathType": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44.", "ledgerLive": "Ledger Live: Ledger official HD path. Ви можете управляти лише 10 адресами з шляхом Ledger Live.", "legacy": "Спадщина: шлях HD, який використовується MEW / Mycrypto."}, "hdPathTypeNochain": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44.", "ledgerLive": "Ledger Live: Ledger official HD path. Ви можете керувати лише 10 адресами з Ledger Live path.", "legacy": "Спадщина: <PERSON> шлях, використовуваний MEW / Mycrypto."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HD-<PERSON>л<PERSON><PERSON>, визначений протоколом BIP44."}, "disconnected": "Не вдається підключитися до BitBox02. Будь ласка, оновіть сторінку для повторного підключення. Причина: {{0}}"}, "selectHdPath": "Виберіть HD-шлях:", "selectIndexTip": "Виберіть порядковий номер адреси, з якої потрібно почати:", "manageAddressFrom": "Керувати адресою від {{0}} до {{1}}", "advancedSettings": "Додаткові налаштування", "customAddressHdPath": "Спеціальний шлях до адреси HD", "connectedToLedger": "Підключено до Ledger", "connectedToTrezor": "Підключено до Trezor", "connectedToOnekey": "Підключено до OneKey", "manageSeedPhrase": "Керування ключовою фразою", "manageGridplus": "Керувати GridPlus", "manageKeystone": "Керування ключовим елементом", "manageAirgap": "Керувати AirGap", "manageCoolwallet": "Керування CoolWallet", "manageBitbox02": "Керування BitBox02", "manageNgraveZero": "Керування NGRAVE ZERO", "done": "Виконано", "addressesIn": "Адреси в {{0}}", "addressesInRabby": "Адрес<PERSON> в Rabby{{0}}", "qrCode": {"switch": {"title": "Переключитися на новий пристрій {{0}}", "content": "Не підтримується імпорт декількох пристроїв {{0}} Якщо ви перемкнетеся на новий пристрій {{0}}, список адрес поточного пристрою буде вилучено перед початком процесу імпорту."}, "switchAnother": "Переключитися на інший {{0}}"}, "manageImKey": "Керувати imKey", "manageImtokenOffline": "Керування imToken", "importBtn": "Імпорт ({{count}})"}, "importYourKeystore": "Імпортувати ваш сховище ключів", "incorrectPassword": "неправильний пароль", "keystore": {"description": "Виберіть файл сховища ключів, який потрібно імпортувати, і введіть відповідний пароль", "password": {"required": "Будь ласка, введіть пароль", "placeholder": "Пароль"}}, "coboSafe": {"inputSafeModuleAddress": "Адреса вхідного безпечного модуля", "invalidAddress": "Невірна адреса", "whichChainIsYourCoboAddressOn": "В якої мережі знаходиться ваша cobo-адреса", "addCoboArgusAddress": "Додати адресу Cobo Argus", "findTheAssociatedSafeAddress": "Знайти асоційовану безпечну адресу", "import": "Імпортувати"}, "imkey": {"title": "Підключити imKey", "imkeyPermissionTip": "Будь ласка, натисніть \"Allow\" нижче та авторизуйте доступ до вашого imKey у наступному спливаючому вікні."}, "keystone": {"title": "Підключи<PERSON><PERSON> Keystone", "exportAddressJustAllowedOnHomePage": "Експорт адреси дозволено лише на домашній сторінці", "deviceIsLockedError": "Введіть пароль для розблокування", "noDeviceFoundError": "Підключіть оди<PERSON>tone", "keystonePermission1": "Підключіться до USB пристрою", "allowRabbyPermissionsTitle": "Дозволити Rabby дозволи на:", "deviceRejectedExportAddress": "Підтвердити підключення до Rabby", "deviceIsBusy": "Пристрій зайнятий", "unknowError": "Невідома помилка, будь ласка, спробуйте ще раз", "keystonePermissionTip": "Будь ласка, натисніть \"Дозволити\" нижче, щоб авторизувати доступ до вашого Keystone у наступному спливаючому вікні, та переконайтесь, що ваш Keystone 3 Pro знаходиться на головній сторінці."}, "firefoxLedgerDisableTips": "Ledger не сумісний із Firefox", "addFromCurrentSeedPhrase": "Додати з поточної Seed Phrase"}, "unlock": {"btn": {"unlock": "Unlock"}, "password": {"required": "Введіть пароль для розблокування", "placeholder": "Введіть пароль для розблокування", "error": "неправильний пароль"}, "btnForgotPassword": "Забули пароль?", "title": "<PERSON><PERSON>", "description": "Гамевий гаманець для Ethereum та всіх EVM ланцюгів"}, "addToken": {"noTokenFound": "Токен не знайдено", "tokenSupported": "Токен підтримується на Rabby", "tokenCustomized": "Поточний токен вже додано до налаштованих", "tokenNotFound": "Токен не знайдено за цією контрактною адресою", "title": "Додати кастомний токен до Rabby", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokenOnMultiChains": "Адреса токена на декількох ланцюжках. Будь ласка, виберіть один", "noTokenFoundOnThisChain": "У цієї мережі токенів не знайдено", "hasAdded": "Вам був доданий цей токен."}, "switchChain": {"title": "Перемикання на {{chain}}", "chainNotSupport": "Запитуваний ланцюжок ще не підтримується Rabby", "testnetTip": "Будь ласка, увімкніть \"Увімкнути тестові мережі\" у розділі \"Більше\" перед підключенням до тестових мереж", "addChain": "Додати <PERSON>net", "chainNotSupportYet": "Запитаний ланцюжок ще не підтримується Rabby.", "unknownChain": "Невідома мережа", "requestsReceived": "Отримано 1 запит", "requestRabbyToSupport": "Запросити підтримку Rabby", "desc": "Запитувана мережа ще не інтегрована Rabby. Ви можете додати її як користувацьку мережу вручну.", "requestsReceivedPlural": "{{count}} запитів отримано", "chainNotSupportAddChain": "Запитаний ланцюг ще не інтегрований Rabby. Ви можете додати його як Custom Testnet.", "chainId": "Chain ID:"}, "signText": {"title": "Текст підпису", "message": "Повідомлення", "createKey": {"interactDapp": "Взаємодіяти з Dapp", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "sameSafeMessageAlert": "Таке ж повідомлення підтверджено; додатковий підпис не потрібен."}, "securityEngine": {"yes": "Так", "no": "Ні", "whenTheValueIs": "коли значенням є {{value}}", "currentValueIs": "Поточне значення - {{value}}", "viewRules": "Переглянути правила безпеки", "undo": "Скасувати", "riskProcessed": "Оброблено ризик", "ignoreAlert": "Ігнорувати попередження", "ruleDisabled": "Правила безпеки було вимкнено. Для вашої безпеки ви можете увімкнути їх будь-коли.", "unknownResult": "Невідомий результат, оскільки правило безпеки недоступне", "alertTriggerReason": "Причина спрацювання сповіщення:", "understandRisk": "Я розумію та приймаю відповідальність за будь-які збитки", "forbiddenCantIgnore": "Знайдено заборонений ризик, який не можна ігнорувати.", "ruleDetailTitle": "Деталь правила", "enableRule": "Увімкнути правило", "viewRiskLevel": "Переглянути рівень ризику"}, "connect": {"listedBy": "Перераховано по", "sitePopularity": "Популярність сайту", "myMark": "Моя позначка", "flagByRabby": "Позначен<PERSON> Rabby", "flagByMM": "Позначено Metamask", "flagByScamSniffer": "Познач<PERSON><PERSON><PERSON>amSniffer", "verifiedByRabby": "Перев<PERSON><PERSON><PERSON><PERSON><PERSON> Rabby", "foundForbiddenRisk": "Знайдено заборонені ризики. З'єднання заблоковано.", "markAsTrustToast": "Позначити як \"Довірені\"", "markAsBlockToast": "Позначити як \"Заблоковані\"", "markRemovedToast": "Позначку вилучено", "title": "Підключитися до Dapp", "selectChainToConnect": "Виберіть ланцюжок для підключення", "markRuleText": "Моя позначка", "connectBtn": "Підключити", "noWebsite": "Немає", "popularLevelHigh": "Високий", "popularLevelMedium": "Середній", "popularLevelLow": "Низький", "popularLevelVeryLow": "Дуже низький", "noMark": "Без позначки", "blocked": "Заблоковано", "trusted": "Затвердити", "addedToWhitelist": "Додано до вашого білого списку", "addedToBlacklist": "Додано до вашого чорного списку", "removedFromAll": "Видалено з усіх списків", "notOnAnyList": "Немає в жодному списку", "onYourBlacklist": "У вашому чорному списку", "onYourWhitelist": "У вашому білому списку", "manageWhiteBlackList": "Керування білим/чорним списком", "SignTestnetPermission": {"title": "Дозвіл на підпис"}, "ignoreAll": "Ігнорувати всі", "SelectWallet": {"desc": "Виберіть з гаманців, які ви встановили", "title": "Виберіть гаманець для підключення"}, "otherWalletBtn": "Підключитися до іншого гаманця", "connectAddress": "Підключити адресу\n"}, "addressDetail": {"add-to-whitelist": "Додати до білого списку", "remove-from-whitelist": "Видалити з білого списку", "address-detail": "Детальна адреса", "backup-private-key": "Резервна копія приватного ключа", "backup-seed-phrase": "Резервна кодова фраза", "delete-address": "Видалити адресу", "delete-desc": "Перш ніж видалити, візьміть до уваги наступні моменти, щоб зрозуміти, як захистити свої активи.", "direct-delete-desc": "Ця адреса є адресою {{renderBrand}}, <PERSON><PERSON> не зберігає приватний ключ або ключову фразу для цієї адреси, ви можете просто видалити її", "admins": "Admins", "tx-requires": "Будь-яка транзакція вимагає <2>{{num}}</2> підтверджень", "edit-memo-title": "Редагувати адресну примітку", "please-input-address-note": "Будь ласка, введіть примітку до адреси", "address": "Адреса", "address-note": "Примітка до адреси", "assets": "Активи", "qr-code": "QR-код", "source": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hd-path": "Ш<PERSON><PERSON><PERSON> HD", "manage-seed-phrase": "Керування ключовою фразою", "manage-addresses-under-this-seed-phrase": "Керування адресами під цією ключовою фразою", "safeModuleAddress": "Безпечна адреса модуля", "coboSafeErrorModule": "Термін дії адреси закінчився, видаліть та імпортуйте адресу знову", "importedDelegatedAddress": "Імпортована делегована адреса", "manage-addresses-under": "Керуйте адресами під цим {{brand}}"}, "preferMetamaskDapps": {"title": "Додатки які використовуются лише з Metamask", "desc": "Наступні dapps залишаться підключеними через MetaMask, незалежно від того, на який гаманець ви перейшли", "howToAdd": "Як додати", "howToAddDesc": "Клацніть правою кнопкою миші на сайті та знайдіть цю опцію", "empty": "Немає додатків"}, "customRpc": {"opened": "Відкрито", "closed": "Закрито", "empty": "Немає спеціальних RPC URL", "title": "Кастомний RPC", "desc": "Увімкнення кастомного RPC замінить Rabby як вузол за замовчуванням. Щоб продовжити використання Rabby, будь ласка, вимкніть або видаліть кастомний RPC.", "add": "Додати RPC", "EditRPCModal": {"invalidRPCUrl": "Невірна URL-адреса RPC", "invalidChainId": "Недійсний ідентифікатор ланцюжка", "rpcAuthFailed": "Не вдалося виконати аутентифікацію RPC", "title": "Редагувати RPC", "rpcUrl": "RPC URL", "rpcUrlPlaceholder": "Введіть URL-адресу RPC"}, "EditCustomTestnetModal": {"title": "Додати власну мережу", "quickAdd": "Швидке додавання з Chainlist"}}, "requestDebankTestnetGasToken": {"title": "Запросити тестові токени для DeBank Testnet", "mintedTip": "Власники бейд<PERSON><PERSON><PERSON> Rabby можуть запитувати один раз на день", "notMintedTip": "Запит доступний лише для власників бейджів Rabby", "claimBadgeBtn": "Отримати бейдж Rabby", "time": "За день", "requested": "Ви запросили сьогодні", "requestBtn": "Запит"}, "safeQueue": {"title": "Черга", "sameNonceWarning": "Ці транзакції конфліктують, оскільки використовують один і той самий nonce.                   Виконання однієї з них автоматично замінить іншу(і).", "loading": "Завантаження транзакцій, що очікують на виконання", "noData": "Немає очікуваних транзакцій", "loadingFaild": "Через нестабільність сервера Safe дані недоступні, перевірте ще раз через 5 хвилин", "accountSelectTitle": "Ви можете відправити цю транзакцію, використовуючи будь-яку адресу", "LowerNonceError": "Транзакція з nonce {{nonce}} має бути виконана першою", "submitBtn": "Відправити транзакцію", "unknownTx": "Невідома транзакція", "cancelExplain": "Скасувати {{token}} Схвалити для {{protocol}}", "unknownProtocol": "Невідомий протокол", "approvalExplain": "Затвердити {{count}} {{token}} для {{protocol}}", "unlimited": "необмежено", "action": {"send": "Відправити", "cancel": "Скасувати Очікуючу Транзакцію"}, "viewBtn": "Перегляд", "ReplacePopup": {"options": {"send": "Надіс<PERSON>а<PERSON><PERSON>", "reject": "Відхилити транзакцію"}, "desc": "Підписану транзакцію не можна видалити, але її можна замінити новою транзакцією з тим самим nonce.", "title": "Виберіть, як замінити цю транзакцію"}, "replaceBtn": "Замінити"}, "importSuccess": {"title": "Успішно імпортовано ", "addressCount": "{{count}} адрес", "gnosisChainDesc": "Цю адресу було знайдено розгорнутою у {{count}} мережах"}, "backupSeedPhrase": {"title": "Резервна ключова фраза", "alert": "Ця ключова фраза - це ключ до ваших активів. НЕ втрачайте її і не розголошуйте іншим, інакше ви можете втратити свої активи назавжди.         Будь ласка, переглядайте його в безпечному середовищі і зберігайте обережно.", "clickToShow": "Натисніть, щоб показати Seed Phrase", "copySeedPhrase": "Скопіювати ключову фразу", "showQrCode": "Показати QR код", "qrCodePopupTips": "Ніколи не діліться QR-кодом з seed фразою з іншими. Будь ласка, переглядайте його в безпечному середовищі та зберігайте його обережно.", "qrCodePopupTitle": "QR Code"}, "backupPrivateKey": {"title": "Резервна копія приватного ключа", "alert": "Цей приватний ключ є обліковим записом до ваших активів. НЕ втрачайте його і не розголошуйте іншим, інакше ви можете втратити свої активи назавжди.         Будь ласка, перегляньте його в безпечному середовищі і зберігайте його обережно.", "clickToShow": "Натисніть, щоб показати приватний ключ", "clickToShowQr": "Натисніть, щоб показати QR-код приватного ключа"}, "ethSign": {"alert": "Підписання за допомогою 'eth_sign' може призвести до втрати активів. Задля вашої безпеки Rabby не підтримує цей метод."}, "createPassword": {"title": "Встановити пароль", "passwordRequired": "Будь ласка, введіть пароль", "passwordMin": "Пароль має бути щонайменше 8 символів", "passwordPlaceholder": "Пароль повинен містити щонайменше 8 символів", "confirmRequired": "Будь ласка, підтвердіть пароль", "confirmError": "Паролі не збігаються", "confirmPlaceholder": "Підтвердіть пароль", "agree": "Я прочитав та погоджуюсь з <1/> <2>Умовами використання</2>"}, "welcome": {"step1": {"title": "Доступ до всіх додатків", "desc": "Ra<PERSON> підключається до всі<PERSON>, які підтримує MetaMask"}, "step2": {"title": "Self-custodial", "desc": "Закриті ключі зберігаються локально, доступ до них маєте лише ви", "btnText": "Почати"}}, "importSafe": {"title": "Додати безпечну адресу", "placeholder": "Будь ласка, введіть адресу", "error": {"invalid": "Не дійсна адреса", "required": "Будь ласка, введіть адресу"}, "loading": "Пошук у розгорнутої мережі за цією адресою", "gnosisChainDesc": "Цю адресу знайдено розгорнутою у {{count}} ланцюжках"}, "importQrBase": {"desc": "Відскануйте QR-код на апаратному гаманці {{brandName}}", "btnText": "Спробуйте ще раз"}, "bridge": {"showMore": {"title": "Показати більше", "source": "Міст Д<PERSON><PERSON><PERSON><PERSON><PERSON>о"}, "settingModal": {"confirmModal": {"i-understand-and-accept-it": "Я розумію і приймаю це", "title": "Увімкніть торгівлю з цим агрегатором", "tip2": "2. <PERSON><PERSON> не несе відповідальності за будь-які ризики, що виникають через контракт цього агрегатора.", "tip1": "Щойно ввімкнено, ви будете взаємодіяти безпосередньо з контрактом через цей агрегатор."}, "title": "Увімкніть Bridge Aggregators для торгівлі", "SupportedBridge": "Підтримувані мосты:", "confirm": "Підтвердити"}, "tokenPairDrawer": {"tokenPair": "<PERSON><PERSON><PERSON><PERSON>н Pair", "balance": "Балансова вартість", "noData": "Не підтримується пара токенів", "title": "Виберіть із підтримуваних пар токенів"}, "Select": "Вибрати", "the-following-bridge-route-are-found": "Знайдено наступний маршрут", "To": "Щоб", "From": "<PERSON>ід", "Balance": "Баланс: ", "title": "Міст", "history": "Історія Bridge", "no-quote": "Без котирування", "no-quote-found": "Котирування не знайдено. Спробуйте інші пари токенів.", "Amount": "Кількість", "best": "Найкращий", "Completed": "Завершено", "approve-and-bridge": "Затвердити та Міст", "estimate": "Оцінка:", "Pending": "В очікуванні", "price-expired-refresh-route": "Ціна застаріла. Оновіть маршрут.", "BridgeTokenPair": "Мост Token Pair", "insufficient-balance": "Недостатн<PERSON>й баланс", "duration": "{{duration}} хвилин", "approve-x-symbol": "Схвалити {{symbol}}", "rabby-fee": "<PERSON><PERSON> ком<PERSON>с<PERSON>я", "via-bridge": "через {{bridge}}", "gas-x-price": "Ціна Gas: {{price}} Gwei.", "tokenPairPlaceholder": "Виберіть пару токенів", "gas-fee": "Плата за газ: {{gasUsed}}", "bridge-cost": "Міст вартість", "no-route-found": "Маршрут не знайдено", "no-transaction-records": "Жодних записів транзакцій", "completedTip": "Транзакція на ланцюзі, декодування даних для створення запису", "actual": "Актуальний:", "bridge-via-x": "Міст на {{name}}", "detail-tx": "Деталь", "need-to-approve-token-before-bridge": "Потрібно схвалити токен перед мостом", "bridgeTo": "Міст до", "getRoutes": "Отримати маршрути", "unlimited-allowance": "Необмежений дозвіл", "estimated-value": "≈ {{value}}", "slippage-adjusted-refresh-quote": "Прослизання скориговано. Оновити маршрут.", "est-payment": "Оцінка платежу:", "est-receiving": "Очікується отримання:", "enable-it": "Увімкнути це", "loss-tips": "Ви втрачаєте {{usd}}. Спробуйте іншу суму.", "est-difference": "Орієнтовна різниця:", "max-tips": "Це значення обчислюється шляхом віднімання вартості gas для bridge.", "price-impact": "Ціновий вплив", "recommendFromToken": "Міст з <1></1> для доступної котирування", "aggregator-not-enabled": "Цей агрегатор не увімкнений вами для торгівлі.", "select-chain": "Вибрати Chain", "pendingTip": "Tx відправлено. Якщо Tx очікує на підтвердження протягом тривалого часу, ви можете спробувати очистити очікуючі в налаштуваннях."}, "pendingDetail": {"Header": {"predictTime": "Прогнозується, що буде запаковано в"}, "TxStatus": {"reBroadcastBtn": "Повторна трансляція", "pendingBroadcast": "Очікується: Має бути відправлено", "pendingBroadcasted": "Очікується: Транслюється", "completed": "Завершено"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"broadcastedCount_ordinal_few": "{{count}}-я трансляція", "broadcastedCount_ordinal_other": "{{count}}-та трансляція", "broadcastedCount_ordinal_one": "{{count}}-а трансляція", "pending": "Перевірка статусу...", "broadcasted": "Нещодавно транслювалося", "created": "Транзакцію створено", "broadcastedCount_ordinal_two": "{{count}}-й модуль мовлення"}, "MempoolList": {"col": {"nodeOperator": "Оператор вузла", "txStatus": "Статус транзакції", "nodeName": "Ім'я вузла"}, "txStatus": {"appeared": "З'явився", "appearedOnce": "З'явилося один раз", "notFound": "Не знайдено"}, "title": "З'явився в {{count}} RPC вузлах"}, "PendingTxList": {"filterBaseFee": {"label": "Відповідає лише вимогам Base fee", "tooltip": "Показувати тільки ті транзакції, у яких Gas Price відповідає вимогам базової комісії блоку"}, "col": {"actionType": "Ти<PERSON> дії", "gasPrice": "Ціна Gas", "interact": "Взаємодіяти з", "balanceChange": "Ба<PERSON>а<PERSON><PERSON> змінено", "action": "Дія транзакції"}, "titleNotFound": "Усі очікувані транзакції без рангу", "title": "GasPrice займає {{rank}} місце серед усіх очікуючих транзакцій.", "titleSame": "GasPrice займає {{rank}} місце в Same as Current", "titleSameNotFound": "Такий самий ранг, як і поточний"}, "Empty": {"noData": "Дані не знайдено"}, "PrePackInfo": {"col": {"prePackContent": "Попередньо упакований вміст", "prePackResults": "Попередньо упакувати результати", "expectations": "Очікування", "difference": "Перевірити результати"}, "type": {"receive": "Отримати", "pay": "Платити"}, "title": "Попередня перевірка пакунку", "error": "{{count}} помилок знайдено", "noLoss": "Втрата не виявлена", "noError": "Помилок не виявлено", "desc": "Симуляція виконана в останньому блоці, оновлено {{time}}", "loss": "Знайдено {{lossCount}} втрат"}, "Predict": {"completed": "Транзакція завершена", "predictFailed": "Не вдалося передбачити час пакування", "skipNonce": "Ваша адреса пропустил<PERSON>ce у ланцюзі Ethereum, що спричиняє неможливість завершення поточної транзакції."}}, "dappSearch": {"searchResult": {"foundDapps": "Знайдено <2>{{count}}</2> Dapps", "totalDapps": "Усього <2>{{count}}</2> Dapps"}, "expand": "Розгорнути", "emptySearch": "Жодного Dapp не знайдено", "favorite": "Обране", "selectChain": "Вибрати Chain", "listBy": "Dapp було зазначено за", "emptyFavorite": "Немає улюбленого Dapp"}, "rabbyPoints": {"claimItem": {"go": "Перейти", "disabledTip": "Зараз немає балів для отримання", "claim": "Вимагати", "earnTip": "Ліміт раз на день. Будь ласка, заробляйте бали після 00:00 UTC+0", "claimed": "Заявлено"}, "claimModal": {"walletBalance": "Б<PERSON><PERSON><PERSON><PERSON>с Wallet", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "snapshotTime": "<PERSON>а<PERSON> знімка: {{time}}", "rabbyUser": "Активний користувач Rabby", "MetaMaskSwap": "MetaMask Swap", "referral-code": "Реферальний код", "invalid-code": "недійсний код", "season2": "Сезон 2", "title": "Отримати початкові бали", "activeStats": "Активний статус", "rabbyValuedUserBadge": "Значок поцінованого користувач<PERSON> Rabby", "claim": "Вимога", "addressBalance": "Бала<PERSON><PERSON> гаманця", "cantUseOwnCode": "Ви не можете використовувати власний реферальний код.", "placeholder": "Введіть реферальний код для отримання додаткових балів (необов'язково)"}, "referralCode": {"verifyAddressModal": {"cancel": "Скасувати", "verify-address": "Підтвердити адресу", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "Підпишіть це текстове повідомлення, щоб підтвердити, що ви є власником цієї адреси", "sign": "Підписати"}, "referral-code-available": "Доступний реферальний код", "my-referral-code": "Мій реферальний код", "referral-code-cannot-be-empty": "Реферальний код не може бути порожнім", "confirm": "Підтвердити", "refer-a-new-user-to-get-50-points": "Запросіть нового користувача, щоб отримати 50 балів", "set-my-referral-code": "Встановити мій реферальний код", "referral-code-already-exists": "Реферальний код вже існує", "referral-code-cannot-exceed-15-characters": "Реферальний код не може перевищувати 15 символів", "max-15-characters-use-numbers-and-letters-only": "Максимум 15 символів, використовуйте лише цифри та літери.", "once-set-this-referral-code-is-permanent-and-cannot-change": "Після встановлення цього коду запрошення його не можна змінити.", "set-my-code": "Встановити мій код"}, "top-100": "Топ 100", "title": "<PERSON><PERSON>", "referral-code-copied": "Реферальний код скопійовано", "earn-points": "Заробляйте бали", "out-of-x-current-total-points": "З {{total}} усього розподілених балів", "share-on": "Поділитися на", "code-set-successfully": "Код запрошення встановлено успішно", "initialPointsClaimEnded": "Ініціальне нарахування балів завершено", "secondRoundEnded": "🎉 Завершився другий раунд Rabby Points", "firstRoundEnded": "🎉 Перше коло Rabby Points завершено"}, "customTestnet": {"CustomTestnetForm": {"idRequired": "Будь ласка, введіть ідентифікатор ланцюга", "rpcUrlRequired": "Будь ласка, введіть RPC URL", "rpcUrl": "RPC URL", "nativeTokenSymbolRequired": "Будь ласка, введіть символ валюти", "blockExplorerUrl": "URL block explorer (Необов'язково)", "nameRequired": "Будь ласка, введіть назву мережі", "name": "Назва мережі", "id": "Chain ID", "nativeTokenSymbol": "Символ валюти"}, "AddFromChainList": {"tips": {"supported": "Ланцюг вже інтегровано Rabby Wallet", "added": "Ви вже додали цей ланцюг"}, "empty": "Ланцюгів не знайдено", "search": "Пошук за назвою або ID користувацької мережі", "title": "Швидке додавання з Chainlist"}, "signTx": {"title": "Дані транзакції"}, "ConfirmModifyRpcModal": {"desc": "Ланцюг уже інтегрований Rabby. Потрібно змінити його URL RPC?"}, "id": "ІD", "currency": "Валюта", "title": "<PERSON><PERSON><PERSON>сна мережа", "desc": "Rabby не може перевірити безпеку користувацьких мереж. Будь ласка, додавайте тільки довірені мережі.", "add": "Додати власну мережу", "empty": "Немає спеціальної мережі"}, "addChain": {"desc": "Rabby не може перевірити безпеку користувацьких мереж. Будь ласка, додавайте лише надійні мережі.", "title": "Додати користувацьку мережу до Rabby"}, "sign": {"transactionSpeed": "Швидкість Transaction"}, "ecology": {"sonic": {"home": {"airdrop": "Е<PERSON>р<PERSON>р<PERSON><PERSON>", "arcadeBtn": "<PERSON>ра<PERSON>и зараз", "arcadeDesc": "Грайте в безкоштовні ігри, щоб заробити бали для аirdrop S.", "airdropBtn": "Заробляйте бали", "migrateBtn": "Незабаром", "migrateTitle": "Мігра<PERSON><PERSON>я", "earnTitle": "Заробіток", "socialsTitle": "Долучайтеся", "migrateDesc": "→", "airdropDesc": "Приблизно 200 мільйонів S для користувачів на Opera та Sonic.", "earnBtn": "Скоро з'явиться", "earnDesc": "Ставте ваші $S"}, "points": {"shareOn": "Поділитися на", "sonicPoints": "Сonic Points", "getReferralCode": "Отримати реферальний код", "referralCode": "Реферальний код", "sonicArcadeBtn": "Почати гру", "retry": "Повторити", "sonicArcade": "Сonic Arcade", "today": "Сьогодні", "pointsDashboardBtn": "Почніть заробляти очки", "errorTitle": "Неможливо завантажити бали", "pointsDashboard": "Панель управління Points", "errorDesc": "Сталася помилка при завантаженні ваших балів. Будь ласка, спробуйте ще раз.", "referralCodeCopied": "Реферальний код скопійовано"}}, "dbk": {"home": {"bridgeBtn": "Міст", "bridge": "Міст до DBK Chain", "mintNFT": "Mint DBK Genesis NFT", "bridgePoweredBy": "На базі OP Superchain", "mintNFTDesc": "Станьте свідком DBK Chain", "mintNFTBtn": "Карбуй"}, "bridge": {"tabs": {"withdraw": "Виведення", "deposit": "Депозит"}, "info": {"completeTime": "<PERSON>а<PERSON> завершення", "gasFee": "Gas fee", "toAddress": "Щоб адресувати", "receiveOn": "Отримати на {{chainName}}"}, "error": {"notEnoughBalance": "Недостатн<PERSON>й баланс"}, "ActivityPopup": {"status": {"challengePeriod": "Період оскарження", "deposit": "Депозит", "readyToProve": "Готові довести", "withdraw": "Вивести", "claimed": "Заявлено", "proved": "Доведено", "waitingToProve": "Стан кореня опубліковано", "readyToClaim": "Готові вимагати", "rootPublished": "Державний корінь опубліковано"}, "empty": "Поки немає активності", "title": "Дії", "claimBtn": "Вимога", "withdraw": "Виведення", "proveBtn": "Доведіть", "deposit": "Депозит"}, "WithdrawConfirmPopup": {"question3": "Я розумію, що мережеві комісії є приблизними і можуть змінюватися", "question2": "Я розумію, що після початку виведення швидкість операції не може бути збільшена або скасована.", "tips": "Зняття коштів передбачає процес з 3 кроків, який вимагає 1 транзакцію DBK Chain та 2 транзакції Ethereum.", "question1": "Я розумію, що після підтвердження мого виведення коштів на Ethereum пройде ~7 днів, перш ніж мої кошти можуть бути заявлені.", "title": "DBK Chain зняття займає ~7 днів", "btn": "Виведення", "step2": "Довести на Ethereum", "step3": "Вимога на Ethereum", "step1": "Розпочати зняття"}, "labelTo": "Щоб", "labelFrom": "<PERSON>ід"}, "minNFT": {"minted": "Карбується", "myBalance": "<PERSON><PERSON><PERSON> баланс", "title": "DBK Генезис", "mintBtn": "Чеканити"}}}, "miniSignFooterBar": {"status": {"txSigned": "Підписа<PERSON><PERSON>. Створення транзакції", "txCreated": "Транзакцію створено", "txSendings": "Надсилання запиту на підпис({{current}}/{{total}})", "txSending": "Надсилання запиту на підписання"}, "signWithLedger": "Увійти за допомогою Ledger"}, "gasAccount": {"history": {"noHistory": "Немає історії"}, "loginInTip": {"login": "Увійти в GasAccount", "gotIt": "Зрозуміло", "desc": "Сплачуйте комісії Gas на всіх ланцюжках", "title": "Депозит USDC / USDT"}, "loginConfirmModal": {"desc": "Після підтвердження ви можете депозитити тільки на цю адресу", "title": "Увійти з поточною адресою"}, "logoutConfirmModal": {"title": "Вийти з поточного GasAccount", "desc": "Вихід з системи вимикає GasAccount. Ви можете відновити свій GasAccount, увійшовши за цією адресою.", "logout": "Вийти"}, "depositPopup": {"amount": "Сума", "token": "Токен", "title": "Депозит", "selectToken": "Виберіть токен для депозиту", "invalidAmount": "Має бути менше 500", "desc": "Здійсніть депозит на рахунок Rabby's DeBank L2 без додаткових зборів — виводьте кошти будь-коли."}, "withdrawPopup": {"to": "Щоб", "amount": "Сума", "withdrawalLimit": "Ліміт зняття коштів", "selectRecipientAddress": "Виберіть адресу одержувача", "title": "Вивести кошти", "noEligibleChain": "Немає відповідного ланцюга для зняття", "recipientAddress": "Адреса отримувача", "noEnoughValuetBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> недостатній. Перемкніть ланцюг або спробуйте пізніше.", "noEligibleAddr": "Немає відповідної адреси для зняття коштів", "destinationChain": "Цільовий ланцюг", "selectDestinationChain": "Виберіть цільовий ланцюг", "noEnoughGas": "Сума занадто мала для покриття плати за газ", "selectChain": "Виберіть Chain", "riskMessageFromAddress": "Через контроль ризиків ліміт на зняття коштів залежить від загальної суми, яку цей адрес депонував.", "selectAddr": "Вибрати адресу", "deductGasFees": "Отримана сума буде зменшена на розмір комісії за газ", "riskMessageFromChain": "Через контроль ризиків, ліміт виведення залежить від загальної суми, внесеної з цього ланцюжка.", "desc": "Ви можете зняти баланс вашого GasAccount на ваш гаманець DeBank L2. Увійдіть у ваш гаманець DeBank L2, щоб перевести кошти на підтримуваний блокчейн за потреби."}, "withdrawConfirmModal": {"button": "Переглянути на DeBank", "title": "Переведено до вашого DeBank L2 Wallet"}, "GasAccountDepositTipPopup": {"title": "Відкрити GasAccount і внести депозит", "gotIt": "Зрозуміло"}, "switchLoginAddressBeforeDeposit": {"title": "Перемкніть адресу перед депозитом", "desc": "Будь ласка, перейдіть на вашу адресу для входу."}, "deposit": "Депозит", "noBalance": "Немає балансу", "logout": "Вийти з поточного GasAccount", "risk": "Вашу поточну адресу було визначено як ризиковану, тому ця функція недоступна.", "safeAddressDepositTips": "Мультипідписи для депозитів не підтримуються.", "withdraw": "Вивести кошти", "gasExceed": "Баланс GasAccount не може перевищувати $1000", "title": "GasAccount", "gasAccountList": {"address": "Адреса", "gasAccountBalance": "Газ<PERSON>ий баланс"}, "switchAccount": "Перемкнути GasAccount", "withdrawDisabledIAP": "Виведення коштів неможливе, оскільки ваш баланс містить фіатні кошти, які не можна вивести. Зверніться до служби підтримки, щоб вивести баланс ваших токенів"}, "safeMessageQueue": {"noData": "Немає повідомлень", "loading": "Завантаження повідомлень"}, "newUserImport": {"guide": {"desc": "Гамовеідний гаманець для Ethereum та всіх EVM ланцюгів", "createNewAddress": "Створити нову адресу", "title": "Ласкаво просимо до Rabby Wallet", "importAddress": "У мене вже є адреса"}, "createNewAddress": {"showSeedPhrase": "Показа<PERSON>и Seed Phrase", "title": "Перед тим як почати", "tip1": "Якщо я втрачу або поділюсь своїм seed phrase, я назавжди втратю доступ до своїх активів.", "tip2": "Моя сімейна фраза зберігається лише на моєму пристрої. Rabby не може отримати до неї доступ.", "desc": "Будь ласка, ознайомтеся з наведеними нижче порадами щодо безпеки та тримайте їх у пам'яті.", "tip3": "Якщо я видалю Rabby, не зробивши резервну копію своєї seed-фрази, її не можна буде відновити за допомогою Rabby."}, "importList": {"title": "Виберіть метод імпорту"}, "importPrivateKey": {"title": "Імпорт приватного ключа", "pasteCleared": "Вставлено, буфер обміну очищено"}, "PasswordCard": {"form": {"password": {"label": "Пароль", "required": "Будь ласка, введіть пароль", "min": "Пароль повинен містити принаймні 8 символів", "placeholder": "Пароль (мінімум 8 символів)"}, "confirmPassword": {"notMatch": "Паролі не співпадають", "label": "Підтвердьте Пароль", "required": "Будь ласка, підтвердіть пароль", "placeholder": "Підтвердьте пароль"}}, "title": "Встановити пароль", "desc": "Це буде використовуватись для розблокування гаманця та шифрування даних", "agree": "Я погоджуюсь з<1/> <2>Terms of Use</2> та <4>Privacy Policy</4>"}, "successful": {"create": "Успішно створено", "start": "Почати", "import": "Імпортовано успішно", "addMoreAddr": "Додати більше адрес із цієї Seed Phrase", "addMoreFrom": "Додати більше адрес з {{name}}"}, "readyToUse": {"pin": "Закріпі<PERSON><PERSON> Rabby Wallet", "guides": {"step2": "Закрі<PERSON>ит<PERSON> Ra<PERSON>", "step1": "Натисніть на піктограму розширення браузера"}, "title": "Ваш Rabby Wallet готовий!", "extensionTip": "Натисніть <1/> а потім <3/>", "desc": "Знайдіть Rabby Wallet і закріпіть його"}, "importSeedPhrase": {"title": "Імпорт Seed Phrase"}, "importOneKey": {"connect": "Підключити OneKey", "title": "OneKey", "tip3": "3. Розблокуйте ваш пристрій", "tip2": "2. Під<PERSON><PERSON>ючіть ваш пристрій OneKey", "tip1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ити <1>OneKey Bridge<1/>"}, "importTrezor": {"title": "<PERSON><PERSON><PERSON>", "tip1": "1. Під<PERSON><PERSON>ю<PERSON><PERSON>ть ваш пристр<PERSON><PERSON> Trezor", "tip2": "2. Розблокуйте ваш пристрій", "connect": "Підклю<PERSON><PERSON><PERSON><PERSON>"}, "ImportGridPlus": {"connect": "Підключити GridPlus", "title": "GridPlus", "tip2": "2. Підключитися через Lattice Connector", "tip1": "Відкрийте ваш пристрій GridPlus"}, "importLedger": {"tip3": "Відкрийте додаток Ethereum.", "tip2": "Введіть ваш PIN-код для розблокування.", "tip1": "Підключ<PERSON>ть ваш пристр<PERSON><PERSON> Ledger.", "title": "Ledger", "connect": "Підклю<PERSON><PERSON><PERSON><PERSON>"}, "importBitBox02": {"connect": "Підключити BitBox02", "title": "BitBox02", "tip2": "2. Під<PERSON><PERSON><PERSON><PERSON><PERSON>ть ваш BitBox02", "tip3": "3. Розблокуйте ваш пристрій", "tip1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>овіть <1>BitBoxBridge<1/>"}, "importKeystone": {"qrcode": {"desc": "Скануйте QR-код на апаратному гаманці Keystone"}, "usb": {"connect": "Підключи<PERSON><PERSON> Keystone", "tip2": "Введіть свій пароль, щоб розблокувати", "tip3": "Затвердити підключення до вашого комп'ютера", "desc": "Переконайтесь, що ваш Keystone 3 Pro знаходиться на домашній сторінці", "tip1": "Підключіть ваш Keystone пристрій"}}, "importSafe": {"error": {"required": "Будь ласка, введіть адресу", "invalid": "Невірна адреса"}, "title": "Додати Safe адресу", "loading": "Пошук розгорнутого ланцюга цієї адреси", "placeholder": "Введіть безпечну адресу"}}, "metamaskModeDapps": {"title": "Керування дозволеними Dapps", "desc": "Увімкнено режим MetaMask для наступних Dapps. Ви можете підключити Ra<PERSON>, вибравши опцію MetaMask."}, "forgotPassword": {"home": {"buttonNoData": "Встановити пароль", "description": "Rabby Wallet не зберігає ваш пароль і не може допомогти вам його відновити. Скиньте гаманець, щоб налаштувати новий.", "descriptionNoData": "Rabby Wallet не зберігає ваш пароль і не може допомогти вам його відновити. Встановіть новий пароль, якщо ви його забули.", "button": "Почати процес скидання", "title": "Забули пароль"}, "reset": {"alert": {"privateKey": "Приватний Key", "seed": "Сид фраза", "title": "Дані буде видалено і вони не підлягатимуть відновленню:"}, "tip": {"records": "Під<PERSON>иси", "title": "Дані будуть збережені:", "watch": "Контакти та адреси лише для перегляду", "whitelist": "Налаштування Whitelist", "hardware": "Імпортовані апаратні гаманці", "safe": "Імпортовані Safe гаманці"}, "title": "Скинути <PERSON>", "button": "Підтвердити Скидання", "confirm": "Введіть <1>RESET</1> у полі, щоб підтвердити і продовжити."}, "tip": {"button": "Встановити пароль", "buttonNoData": "Додати адресу", "description": "Створіть новий пароль, щоб продовжити", "title": "Скидання Rabby Wallet завершено", "descriptionNoData": "Додайте вашу адресу, щоб почати"}, "success": {"title": "Пароль успішно встановлено", "button": "Завершено", "description": "Ви готові використовувати Rabby Wallet"}}, "eip7702": {"alert": "EIP-7702 ще не підтримується"}, "metamaskModeDappsGuide": {"toast": {"enabled": "Маскування ввімкнено. Оновіть Dapp для повторного підключення.", "disabled": "Маскування вимкнено. Оновіть Dapp."}, "title": "Під<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, замаскував<PERSON>и<PERSON>ь під Meta<PERSON>", "step1": "Крок 1", "manage": "Керувати дозволеними Dapps", "step2": "Крок 2", "step2Desc": "Оновіть і підключіться через MetaMask", "alert": "Не можу підключитися до Dapp, тому що вона не відображає Rabby Wallet як опцію?", "noDappFound": "Жодного Dapp не знайдено", "step1Desc": "Дозволити Rabby маскуватися під MetaMask на поточному Dapp"}, "syncToMobile": {"steps2": "2. Сканувати за допомогою Rabby Mobile", "clickToShowQr": "Click to Select Address  and Show QR Code", "downloadAppleStore": "App Store", "steps1": "Завантажте Rabby Mobile", "title": "Синхронізуйте адресу гаманця з розширення Rabby на мобільний пристрій", "description": "Ваші дані адреси залишаються повністю офлайн, зашифрованими та безпечно передаються через QR-код.", "downloadGooglePlay": "Google Play", "steps2Description": "Ваш QR-код містить конфіденційні дані. Тримайте його в таємниці та ніколи не діліться ним із будь-ким.", "disableSelectAddress": "Sync not supported for {{type}} address", "disableSelectAddressWithPassphrase": "Sync not supported for {{type}} address with passphrase", "disableSelectAddressWithSlip39": "Sync not supported for {{type}} address with slip39", "selectedLenAddressesForSync_one": "Selected {{len}} address for sync", "selectedLenAddressesForSync_other": "Selected {{len}} addresses for sync", "selectAddress": {"title": "Виберіть адреси для синхронізації"}}, "search": {"sectionHeader": {"Defi": "<PERSON><PERSON><PERSON>", "NFT": "NFT", "AllChains": "Усі ланцюги", "token": "Токени"}, "header": {"placeHolder": "По<PERSON><PERSON>к", "searchPlaceHolder": "Шукати назву токена / адресу"}, "tokenItem": {"FDV": "FDV", "Issuedby": "Випущено від", "gasToken": "Токен Gas", "listBy": "Список за {{name}}", "scamWarningTips": "Це токен низької якості і може бути шахрайством.", "verifyDangerTips": "Це токен-шахрайство"}, "searchWeb": {"title": "Всі результати", "noResults": "Немає результатів", "searchTips": "Шукати в мережі", "noResult": "Немає результатів для", "searching": "Результати для"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "Немає збігів адреси", "AddressItem": {"whitelistedAddressTip": "Адреса в білому списку"}}, "AccountSelectDrawer": {"btn": {"cancel": "Скасувати", "proceed": "Продовжити"}}, "AddressList": {"AddressItem": {"addressTypeTip": "Імпортовано за допомогою {{type}}"}}, "AuthenticationModal": {"passwordError": "неправильний пароль", "passwordRequired": "Будь ласка, введіть пароль", "passwordPlaceholder": "Введіть пароль для підтвердження"}, "ConnectStatus": {"connecting": "Підключення...", "connect": "Підключення", "gridPlusConnected": "GridPlus підключено", "gridPlusNotConnected": "GridPlus не підключено", "ledgerNotConnected": "Ledger не підключено", "ledgerConnected": "Ledger підключено", "imKeyrNotConnected": "imKey не підключено", "keystoneConnected": "Keystone підключено", "imKeyConnected": "imKey підключений", "keystoneNotConnected": "Keystone не підключено"}, "Contact": {"AddressItem": {"notWhitelisted": "Ця адреса не внесена до білого списку", "whitelistedTip": "Адреса в білому списку"}, "EditModal": {"title": "Редагувати примітку до адреси"}, "EditWhitelist": {"backModalTitle": "Відкинути зміни", "backModalContent": "<PERSON><PERSON><PERSON><PERSON><PERSON>, які ви зробили, не будуть збережені", "title": "Редагувати білий список", "tip": "Виберіть адресу, яку ви хочете додати до білого списку та збережіть.", "save": "Зберегти до білого списку ({{count}})"}, "ListModal": {"title": "Виберіть адресу", "whitelistEnabled": "Білий список увімкнено. Ви можете надсилати активи лише на адресу з білого списку або вимкнути його в \"Налаштуваннях\"", "whitelistDisabled": "Білий список вимкнено. Ви можете надсилати ресурси на будь-яку адресу", "editWhitelist": "Редагувати білий список", "whitelistUpdated": "Оновлений білий список", "authModal": {"title": "Зберегти в білий список"}}}, "LoadingOverlay": {"loadingData": "Завантаження даних..."}, "MultiSelectAddressList": {"imported": "Імпортовано"}, "NFTNumberInput": {"erc1155Tips": "Ваш баланс становить {{amount}}", "erc721Tips": "За один раз можна відправити лише один NFT ERC 721"}, "TiledSelect": {"errMsg": "Неправильний порядок початкових фраз, будь ласка, перевірте"}, "Uploader": {"placeholder": "Виберіть файл JSON"}, "WalletConnectBridgeModal": {"title": "URL сервера моста", "requiredMsg": "Будь ласка, введіть хост сервера моста", "invalidMsg": "Будь ласка, перевірте свій хост", "restore": "Відновити початкові налаштування"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "Mainnets", "testnet": "Testnets"}}, "ChainSelectorModal": {"searchPlaceholder": "Пошук мереж", "noChains": "Без ланцюжків", "addTestnet": "Додати власну мережу"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "АКТИВ / СУМА"}, "price": {"title": "ЦІНА"}, "usdValue": {"title": "USD VALUE"}}, "searchInput": {"placeholder": "Пошук за іменем / адресою"}, "header": {"title": "Виберіть токен"}, "noTokens": "Немає токенів", "noMatch": "Немає збігів", "noMatchSuggestion": "Спробуйте пошукати адресу контракту в {{ chainName }}", "bridge": {"token": "Токен", "liquidity": "Ліквідність", "low": "Низький", "high": "Висока", "liquidityTips": "Чим вищий історичний обсяг торгівлі, тим більша ймовірність успіху мосту.", "value": "Значення"}, "common": "Загальне", "hot": "Гар<PERSON><PERSON>е", "recent": "Останн<PERSON>", "chainNotSupport": "Цей ланцюг не підтримується"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "Колекція", "Chain": "Мережа", "PurschaseDate": "Дата покупки", "LastPrice": "Остання ціна"}}, "signPermissionCheckModal": {"title": "Ви дозволяєте цьому Dapp підписуватись лише в тестових мережах", "reconnect": "Перепідключити Dapp"}, "testnetCheckModal": {"title": "Будь ласка, увімкніть \"Увімкнути тестові мережі\" у розділі \"Більше\" перед тим, як реєструватися у тестових мережах"}, "EcologyNavBar": {"providedBy": "Надано {{chainName}}"}, "EcologyNoticeModal": {"notRemind": "Не нагадувати мені знову", "title": "Сповіщення", "desc": "Наступні послуги будуть надані безпосередньо стороннім партнером екосистеми. Rabby Wallet не несе відповідальності за безпеку цих послуг."}, "ReserveGasPopup": {"normal": "Звичайний", "fast": "Швидко", "title": "Резерв Gas", "instant": "Миттєвий", "doNotReserve": "Не резервуйте Gas"}, "OpenExternalWebsiteModal": {"button": "Продовжити", "title": "Ви залишаєте <PERSON><PERSON>", "content": "Ви збираєтесь відвідати зовнішній вебсайт. Ra<PERSON> Wallet не несе відповідальності за вміст або безпеку цього сайту."}, "TokenChart": {"holding": "Тримання вартості", "price": "Ціна"}, "externalSwapBrideDappPopup": {"noDapp": "Немає доступних Dapps", "selectADapp": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pp", "help": "Будь ласка, зв'яжіться з офіційною командою цієї ланцюга для отримання підтримки.", "thirdPartyDappToProceed": "Будь ласка, використайте сторонній Dapp для продовження.", "chainNotSupported": "Не підтримується в цій мережі", "viewDappOptions": "Переглянути параметри Dapp", "noQuotesForChain": "Цієї ланцюга поки що немає доступних котирувань", "swapOnDapp": "На Dapp міняти\n", "bridgeOnDapp": "Міст на Dapp\n", "noDapps": "На цій ланцюжку немає доступних додатків Dapp\n"}, "AccountSelectorModal": {"title": "Виберіть адресу\n", "searchPlaceholder": "Пошук адреси\n"}}, "global": {"appName": "<PERSON><PERSON>", "appDescription": "Гаманець, що змінює правила гри для Ethereum і всіх EVM-мереж", "copied": "Скопійовано", "confirm": "Підтвердити", "next": "<PERSON><PERSON><PERSON><PERSON>", "back": "Назад", "ok": "ГАРАЗД", "refresh": "Оновити", "failed": "Не вдалося", "scamTx": " Скам tx", "gas": "Gas", "unknownNFT": "Невідомий NFT", "copyAddress": "Адреса копіювання", "watchModeAddress": "Адреса режиму перегляду", "assets": "активи", "Підтвердити": "Підтвердити", "Скасувати": "Скасувати", "Очистити": "Очистити", "Зберегти": "Зберегти", "confirmButton": "Підтвердити", "cancelButton": "Скасувати", "backButton": "Назад", "proceedButton": "Продовжити", "editButton": "Редагувати", "addButton": "Додати", "closeButton": "Закрити", "Видалено": "Видалено", "Завантаження": "Завантаження", "nonce": "nonce", "Баланс": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Готово": "Готово", "tryAgain": "Спробуйте ще раз", "Loading": "Завантаження", "Confirm": "Підтвердити", "Clear": "Очистити", "Nonce": "Нонц", "Cancel": "Скасувати", "Balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notSupportTesntnet": "Не підтримується для користувацької мережі", "Deleted": "Видалити", "Save": "Зберегти", "Done": "Готово"}, "background": {"error": {"noCurrentAccount": "Немає поточного рахунку", "invalidChainId": "Недійсний ідентифікатор ланцюжка", "notFindChain": "Не вдається знайти ланцюжок {{chain}}", "unknownAbi": "невідомий абі контракту", "invalidAddress": "Не дійсна адреса", "notFoundGnosisKeyring": "Брелок Gnosis не знайдено", "notFoundTxGnosisKeyring": "Не знайдено жодної транзакції у кільці ключів Gnosis", "addKeyring404": "не вдалося додати зв'язку, зв'язка не визначена", "emptyAccount": "поточний акаунт порожній", "generateCacheAliasNames": "[GenerateCacheAliasNames]: потрібна принаймні одна адреса", "invalidPrivateKey": "приватний ключ недійсний", "invalidJson": "вхідний файл є недійсним", "invalidMnemonic": "ключова фраза невірна, будь ласка, перевірте!", "notFoundKeyringByAddress": "Не вдалося знайти брелок за адресою", "txPushFailed": "Не вдалося відправити транзакцію", "unlock": "спочатку потрібно розблокувати гаманець", "duplicateAccount": "Обліковий запис, який ви намагаєтеся імпортувати, є дублікатом", "canNotUnlock": "Неможливо розблокувати без попереднього сховища"}, "transactionWatcher": {"submitted": "Транзакцію надіслано", "more": "натисніть, щоб переглянути додаткову інформацію", "completed": "Транзакція завершена", "failed": "Транзакція не виконана", "txFailedMoreContent": "{{chain}} #{{nonce}} не вдалося. Натисніть, щоб переглянути деталі.", "txCompleteMoreContent": "{{chain}} #{{nonce}} завершено. Натисніть, щоб дізнатися більше."}, "alias": {"HdKeyring": "Початкова фраза", "simpleKeyring": "Прива<PERSON><PERSON><PERSON> ключ", "watchAddressKeyring": "Кон<PERSON><PERSON><PERSON>т"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "Створено за ключовою фразою", "SimpleKeyring": "Імпортовано за допомогою приватного ключа", "WatchAddressKeyring": "Contact"}, "IMPORTED_HD_KEYRING": "Імпортовано за ключовою фразою", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Mainnet & Testnet", "TESTNET": "Тільки тестова мережа"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "Імпортовано за допомогою Seed Phrase (Passphrase)"}}