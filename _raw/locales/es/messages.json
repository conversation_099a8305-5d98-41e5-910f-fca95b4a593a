{"page": {"transactions": {"title": "Transacciones", "empty": {"title": "No hay transacciones", "desc": " No se encontraron transacciones en <1>cadenas soportadas</1>"}, "explain": {"approve": "Aprobar {{amount}} {{symbol}} para {{project}}", "unknown": "Interacción con el contrato", "cancel": "Cancelar una transacción pendiente"}, "txHistory": {"parseInputDataError": "Error al analizar el mensaje", "tipInputData": "La transacción incluye un mensaje", "scamToolTip": "Esta transacción es iniciada por estafadores para enviar tokens y NFT fraudulentos. Por favor, absténgase de interactuar con ella."}, "modalViewMessage": {"title": "<PERSON><PERSON>"}, "filterScam": {"loading": "La carga puede tardar un momento y es posible que haya retrasos en los datos.", "title": "Ocultar transacciones fraudulentas", "btn": "Ocultar transacciones de estafa"}}, "chainList": {"title": "{{count}} chains Integrated", "mainnet": "Mainnets", "testnet": "Testnets"}, "signTx": {"nftIn": "NFT entrante", "gasLimitNotEnough": "El límite de gas es inferior a 21000. No se puede enviar la transacción", "gasLimitLessThanExpect": "El límite de gas es bajo. Hay un 1% de probabilidad de que la transacción falle.", "gasLimitLessThanGasUsed": "El límite de gas es demasiado bajo. Hay un 95% de probabilidad de que la transacción falle.", "nativeTokenNotEngouthForGas": "No tienes suficiente gas en tu billetera", "nonceLowerThanExpect": "El nonce es demasiado bajo, el mínimo debería ser {{0}}", "canOnlyUseImportedAddress": "Solo puedes usar direcciones importadas para firmar", "multiSigChainNotMatch": "Las direcciones multi-firma no están en esta cadena y no pueden iniciar transacciones", "safeAddressNotSupportChain": "La dirección segura actual no es compatible en la cadena {{0}}", "noGasRequired": "No se requiere gas", "gasSelectorTitle": "Gas", "failToFetchGasCost": "Error al obtener el costo de gas", "gasMoreButton": "Más", "manuallySetGasLimitAlert": "Has configurado manualmente el límite de gas a", "gasNotRequireForSafeTransaction": "La tarifa de gas no es necesaria para las transacciones seguras", "gasPriceTitle": "Precio de Gas (Gwei)", "maxPriorityFee": "Tarifa Máxima de Prioridad (Gwei)", "eip1559Desc1": "En cadenas que admiten EIP-1559, la Tarifa de Prioridad es la propina para los mineros para procesar tu transacción. Puedes ahorrar en el costo de gas final reduciendo la Tarifa de Prioridad, lo que puede aumentar el tiempo necesario para procesar la transacción.", "eip1559Desc2": "Aquí en Rabby, la Tarifa de Prioridad (Propina) = Tarifa <PERSON> - Tarifa Base. Después de configurar la Tarifa Máxima de Prioridad, se restará la Tarifa Base y el resto se dará como propina a los mineros.", "hardwareSupport1559Alert": "Asegúrate de que el firmware de tu billetera de hardware se haya actualizado a la versión que admita EIP 1559", "gasLimitTitle": "Límite de Gas", "recommendGasLimitTip": "Est. {{est}}. Actual {{current}}x, recomendado ", "nonceTitle": "<PERSON><PERSON>", "gasLimitModifyOnlyNecessaryAlert": "Modifica solo cuando sea necesario", "gasPriceMedian": "Mediana de las últimas 100 transacciones en cadena: ", "myNativeTokenBalance": "<PERSON> saldo de Gas: ", "gasLimitEmptyAlert": "Por favor, ingresa el límite de gas", "gasLimitMinValueAlert": "El límite de gas debe ser mayor que 21000", "balanceChange": {"successTitle": "Resultados de Simulación de Transacción", "failedTitle": "Fallo en la Simulación de Transacción", "noBalanceChange": "Sin cambio de saldo", "tokenOut": "Token enviado", "tokenIn": "Token recibido", "errorTitle": "Error al obtener el cambio de saldo", "notSupport": "Simulación de Transacción No Soportada", "nftOut": "NFT enviado"}, "enoughSafeSigCollected": "Suficientes firmas recopiladas", "moreSafeSigNeeded": "Se necesitan {{0}} confirmaciones adicionales", "safeAdminSigned": "<PERSON><PERSON><PERSON>", "swap": {"title": "Intercambiar Token", "payToken": "<PERSON><PERSON>", "receiveToken": "Recibir", "failLoadReceiveToken": "Error al cargar", "valueDiff": "Diferencia de Valor", "simulationFailed": "Fallo en la simulación de transacción", "simulationNotSupport": "La simulación de transacción no está disponible en esta cadena", "minReceive": "<PERSON><PERSON><PERSON> a recibir", "slippageFailToLoad": "no se pudo cargar el Slippage", "slippageTolerance": "Tolerancia de Slippage", "receiver": "Destinatario", "notPaymentAddress": "No es la dirección de pago", "unknownAddress": "Dirección desconocida"}, "crossChain": {"title": "Cambiar de cadena"}, "swapAndCross": {"title": "Intercambiar Token y cambiar cadena"}, "wrapToken": "Envolver Token", "unwrap": "Desenvolver Token", "send": {"title": "<PERSON><PERSON><PERSON>", "sendToken": "Enviar token", "sendTo": "Enviar a", "receiverIsTokenAddress": "Dirección del token", "contractNotOnThisChain": "La dirección del contrato no está en esta cadena", "notTopupAddress": "No es una dirección de recarga", "tokenNotSupport": "{{0}} no es compatible", "onMyWhitelist": "En mi lista blanca", "notOnThisChain": "No está en esta cadena", "cexAddress": "Dirección CEX", "addressBalanceTitle": "Saldo de la dirección", "whitelistTitle": "Lista blanca", "notOnWhitelist": "No está en mi lista blanca", "fromMyPrivateKey": "Desde mi clave privada", "fromMySeedPhrase": "De mi frase semilla", "scamAddress": "Dirección de estafa"}, "tokenApprove": {"title": "Aprobación de Token", "approveToken": "Aprobar token", "myBalance": "Mi saldo", "approveTo": "Aprobar a", "eoaAddress": "Dirección EOA", "trustValueLessThan": "Valor de confianza ≤ {{value}}", "deployTimeLessThan": "Tie<PERSON> desplegado < {{value}} días", "amountPopupTitle": "Cantidad", "flagByRabby": "<PERSON><PERSON> por <PERSON>", "contractTrustValueTip": "El valor de confianza se refiere al total de tokens aprobados y expuestos a este contrato. Un valor de confianza bajo indica riesgo o inactividad durante 180 días.", "exceed": "Supera tu saldo actual", "amount": "Aprobar cantidad:"}, "revokeTokenApprove": {"title": "Revocar Aprobación de Token", "revokeFrom": "<PERSON><PERSON><PERSON> <PERSON>", "revokeToken": "Revocar token"}, "sendNFT": {"title": "Enviar NFT", "nftNotSupport": "NFT no compatible"}, "nftApprove": {"title": "Aprobación de NFT", "approveNFT": "Aprobar NFT", "nftContractTrustValueTip": "El valor de confianza se refiere al NFT principal aprobado y expuesto a este contrato. Un valor de confianza bajo indica riesgo o inactividad durante 180 días."}, "revokeNFTApprove": {"title": "Revocar Aprobación de NFT", "revokeNFT": "Revocar NFT"}, "nftCollectionApprove": {"title": "Aprobación de Colección de NFT", "approveCollection": "<PERSON><PERSON><PERSON>"}, "revokeNFTCollectionApprove": {"title": "Revocar Aprobación de Colección de NFT", "revokeCollection": "<PERSON><PERSON><PERSON>"}, "deployContract": {"title": "Desplegar un Contrato", "descriptionTitle": "Descripción", "description": "Estás desplegando un contrato inteligente"}, "cancelTx": {"title": "Cancelar Transacción Pendiente", "txToBeCanceled": "Transacción a ser cancelada", "gasPriceAlert": "Establece un precio de gas actual mayor a {{value}} Gwei para cancelar la transacción pendiente"}, "submitMultisig": {"title": "Enviar Transacción Multifirma", "multisigAddress": "Dirección Multifirma"}, "contractCall": {"title": "Llamada a Contrato", "operation": "Operación", "operationABIDesc": "La operación está decodificada desde ABI", "operationCantDecode": "La operación no está decodificada", "payNativeToken": "Pagar {{symbol}}", "suspectedReceiver": "Dirección de excepción", "receiver": "Dirección del receptor"}, "revokePermit2": {"title": "Revocar Aprobación de Permit2 para Token"}, "unknownAction": "Desconocido", "interactContract": "Interactuar con el contrato", "markAsTrust": "Marcado como confiable", "markAsBlock": "Marcado como bloqueado", "interacted": "Interactuado previamente", "neverInteracted": "Nunca <PERSON>uado antes", "transacted": "Transacción previa", "neverTransacted": "Nunca transaccionado antes", "fakeTokenAlert": "Este es un token falso marcado por Rabby", "scamTokenAlert": "Este es potencialmente un token de baja calidad y estafa según la detección de Rabby", "trusted": "Confiable", "blocked": "Bloqueado", "noMark": "Sin marca", "markRemoved": "Marca eliminada", "speedUpTooltip": "Esta es una transacción acelerada y la transacción original, solo una de ellas se completará eventualmente", "signTransactionOnChain": "Firmar Transacción en {{chain}}", "viewRaw": "Ver en crudo", "unknownActionType": "Tipo de acción desconocido", "sigCantDecode": "Esta firma no puede ser decodificada por Rabby", "nftCollection": "Colección de NFT", "floorPrice": "<PERSON><PERSON>", "contractAddress": "Dirección del contrato", "protocolTitle": "Protocolo", "deployTimeTitle": "Tiempo de despliegue", "popularity": "Popularidad", "contractPopularity": "N.°{{0}} en {{1}}", "addressNote": "Nota de dirección", "myMarkWithContract": "Mi marca en el contrato de {{chainName}}", "myMark": "Mi marca", "collectionTitle": "Colección", "addressTypeTitle": "Tipo de dirección", "firstOnChain": "Primer on-chain", "trustValue": "Valor de confianza", "gasAccount": {"estimatedGas": "Gas estimado: ", "currentTxCost": "Cantidad de Gas enviada a tu dirección:", "gasCost": "Costo de gas por transferir gas a tu dirección:", "totalCost": "Costo total:", "maxGas": "Gas máximo:", "sendGas": "El Gas transferido a ti para la transacción actual:"}, "customRPCErrorModal": {"button": "Desactivar Custom RPC", "title": "Error RPC personalizado", "content": "Tu RPC personalizado no está disponible ahora. Puedes desactivarlo y continuar firmando usando el RPC oficial de Rabby."}, "transferOwner": {"title": "Transferir la propiedad de activos", "description": "Descripción", "transferTo": "Transferir a"}, "swapLimitPay": {"maxPay": "<PERSON><PERSON>", "title": "Intercambiar Límite de Pago de Token"}, "batchRevokePermit2": {"title": "Revocar en lote la Aprobación Permit2"}, "revokePermit": {"title": "Revocar permiso de aprobación de token"}, "assetOrder": {"receiveAsset": "Recibir asset", "title": "<PERSON><PERSON>", "listAsset": "List asset"}, "BroadcastMode": {"instant": {"title": "Instant", "desc": "Las transacciones se transmitirán inmediatamente a la red"}, "lowGas": {"title": "Ahorro de Gas", "desc": "Las transacciones se emitirán cuando el gas de la red sea bajo"}, "mev": {"title": "MEV Protegido", "desc": "Las transacciones se transmitirán al nodo MEV designado"}, "tips": {"notSupported": "No compatible", "walletConnect": "No compatible con WalletConnect", "notSupportChain": "No compatible con esta cadena", "customRPC": "No compatible cuando se usa custom RPC"}, "lowGasDeadline": {"4h": "4h", "label": "Tiempo de espera", "24h": "24h", "1h": "1h"}, "title": "Modo Broadcast"}, "SafeNonceSelector": {"explain": {"send": "<PERSON><PERSON><PERSON>", "contractCall": "Llamada al contrato", "unknown": "Transacción desconocida"}, "optionGroup": {"replaceTitle": "Reemplazar la transacción en la Cola", "recommendTitle": "<PERSON><PERSON> recomendado"}, "option": {"new": "Nueva Transacción"}, "error": {"pendingList": "Error al cargar las transacciones pendientes, <1/><2>Reintentar</2>"}}, "coboSafeCreate": {"descriptionTitle": "Descripción", "safeWalletTitle": "<PERSON><PERSON><PERSON>{Wallet}", "title": "<PERSON><PERSON>r Cobo Safe"}, "coboSafeModificationRole": {"safeWalletTitle": "Safe{Wallet}", "descriptionTitle": "Descripción", "title": "Enviar modificación de Safe Role"}, "coboSafeModificationDelegatedAddress": {"safeWalletTitle": "<PERSON><PERSON><PERSON>{Wallet}", "descriptionTitle": "Descripción", "title": "Enviar modificación de dirección delegada"}, "coboSafeModificationTokenApproval": {"descriptionTitle": "Descripción", "safeWalletTitle": "<PERSON><PERSON><PERSON>{Wallet}", "title": "Enviar modificación de aprobación de Token"}, "common": {"description": "Descripción", "descTipSafe": "La firma no causa cambio de activos ni verifica la propiedad de la dirección", "descTipWarningAssets": "La firma puede causar un cambio en los activos", "descTipWarningPrivacy": "La firma puede verificar la propiedad de la dirección", "descTipWarningBoth": "La firma puede causar un cambio de activos y verificar la propiedad de la dirección", "interactContract": "Interactuar con el contrato"}, "gasAccountForGas": "Usar USD de mi GasAccount para pagar el gas", "nativeTokenForGas": "Usa el token {{tokenName}} en {{chainName}} para pagar el gas", "importedAddress": "Dirección importada", "importedDelegatedAddress": "Dirección delegada importada", "coboSafeNotPermission": "Esta dirección delegada no tiene permiso para iniciar esta transacción", "noDelegatedAddress": "No hay dirección delegada importada", "l2GasEstimateTooltip": "La estimación de gas para la cadena L2 no incluye la tarifa de gas de L1. La tarifa real será más alta que la estimación actual.", "chain": "Cadena", "decodedTooltip": "Esta firma es decodificada por Rabby Wallet", "amount": "Cantidad", "protocol": "Protocolo", "yes": "Sí", "hasInteraction": "Interactuado antes", "no": "No", "advancedSettings": "Configuración avanzada", "address": "Dirección", "addressSource": "Fuente de Dirección", "contract": "Contrato inteligente", "label": "Etiqueta", "maxPriorityFeeDisabledAlert": "Por favor, establece primero el Gas Price.", "typedDataMessage": "<PERSON><PERSON><PERSON>", "trustValueTitle": "Confianza en el valor", "primaryType": "Tipo principal", "safeServiceNotAvailable": "El servicio Safe no está disponible ahora, por favor intente más tarde.", "safeTx": {"selfHostConfirm": {"content": "La API de Safe no está disponible. Cambia al servicio de Safe desplegado por Rabby para mantener tu Safe funcional. <strong>Todos los firmantes de Safe deben usar Rabby Wallet para autorizar transacciones.<strong>", "title": "Cambiar al servicio seguro <PERSON>", "button": "OK"}}}, "signFooterBar": {"requestFrom": "Solicitud de", "processRiskAlert": "Por favor, procesa la alerta antes de firmar", "ignoreAll": "<PERSON><PERSON><PERSON> todo", "gridPlusConnected": "GridPlus está conectado", "gridPlusNotConnected": "GridPlus no está conectado", "connectButton": "Conectar", "connecting": "Conectando...", "ledgerNotConnected": "Ledger no está conectado", "ledgerConnected": "Ledger está conectado", "signAndSubmitButton": "<PERSON><PERSON>r y Enviar", "walletConnect": {"connectedButCantSign": "Conectado pero no se puede firmar.", "switchToCorrectAddress": "Por favor, cambia a la dirección correcta en la billetera móvil.", "switchChainAlert": "Por favor, cambia a {{chain}} en la billetera móvil.", "notConnectToMobile": "No conectado a {{brand}}.", "connected": "Conectado y listo para firmar.", "howToSwitch": "¿Cómo cambiar?", "wrongAddressAlert": "Has cambiado a una dirección diferente en la billetera móvil. Por favor, cambia a la dirección correcta en la billetera móvil.", "connectBeforeSign": "{{0}} no está conectado a Rabby, por favor conéctate antes de firmar.", "chainSwitched": "Has cambiado a una cadena diferente en la billetera móvil. Por favor, cambia a {{0}} en la billetera móvil.", "latency": "Latencia", "requestSuccessToast": "Solicitud enviada exitosamente.", "sendingRequest": "<PERSON><PERSON><PERSON> solicitud de firma", "signOnYourMobileWallet": "Por favor, firma en tu billetera móvil.", "requestFailedToSend": "No se pudo enviar la solicitud de firma."}, "beginSigning": "Iniciar proceso de firma", "addressTip": {"onekey": "Dirección OneKey", "trezor": "Dirección Trezor", "bitbox": "Dirección BitBox02", "keystone": "Dirección Keystone", "airgap": "Dirección AirGap", "coolwallet": "Dirección CoolWallet", "privateKey": "Dirección de Clave Privada", "seedPhrase": "Dirección de Frase Semilla", "watchAddress": "No es posible firmar con una dirección de solo observación", "safe": "Dirección Safe", "coboSafe": "Cobo Argus Address", "seedPhraseWithPassphrase": "Seed Phrase address (Passphrase)"}, "qrcode": {"signWith": "Firmar con {{brand}}", "failedToGetExplain": "Error al obtener explicación", "txFailed": "La transacción falló", "sigReceived": "<PERSON>rma recibida", "sigCompleted": "<PERSON>rma completada", "getSig": "Obtener firma", "qrcodeDesc": "Escanea con tu {{brand}} para firmar<br></br><PERSON><PERSON><PERSON> de firmar, haz clic en el botón de abajo para recibir la firma", "misMatchSignId": "Datos de transacción incongruentes. Por favor, verifica los detalles de la transacción.", "unknownQRCode": "Error: No pudimos identificar ese código QR", "afterSignDesc": "Después de firmar, coloca el código QR en tu {{brand}} frente a la cámara de tu PC"}, "ledger": {"resent": "Reenviar", "signError": "Error de firma en Ledger:", "notConnected": "Tu billetera no está conectada. Por favor, vuelve a conectar.", "siging": "Enviando solicitud de firma...", "txRejected": "Transacción rechazada", "unlockAlert": "Por favor, conecta y desbloquea tu Ledger, abre Ethereum en él", "updateFirmwareAlert": "Por favor, actualiza el firmware y la aplicación Ethereum en tu Ledger", "txRejectedByLedger": "La transacción ha sido rechazada en tu Ledger", "blindSigTutorial": "Tutorial de Firma Ciega de Ledger", "resubmited": "Enviado nuevamente", "submitting": "Firmado. Creando transacción"}, "common": {"notSupport": "{{0}} no está soportada"}, "resend": "Reenviar", "submitTx": "Enviar transacción", "testnet": "Testnet", "mainnet": "Mainnet", "gasless": {"notEnough": "El balance de Gas no es suficiente", "GetFreeGasToSign": "Obtén Gas Gratis", "walletConnectUnavailableTip": "La billetera móvil conectada a través de WalletConnect no es compatible con Free Gas.", "rabbyPayGas": "<PERSON><PERSON> paga<PERSON> el gas necesario, solo firma", "unavailable": "Tu saldo de Gas no es suficiente", "watchUnavailableTip": "La dirección de solo observación no es compatible con Free Gas", "customRpcUnavailableTip": "Las RPC personalizadas no son compatibles con Free Gas"}, "gasAccount": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "chainNotSupported": "Esta cadena no es compatible con GasAccount", "loginFirst": "Por favor, inicia sesión en GasAccount primero.", "notEnough": "GasAccount is not enough", "login": "In<PERSON><PERSON>", "useGasAccount": "<PERSON><PERSON>unt", "gotIt": "Entendido", "customRPC": "No compatible cuando se utiliza RPC personalizado", "WalletConnectTips": "GasAccount no admite WalletConnect", "loginTips": "Para completar el inicio de sesión de GasAccount, esta transacción será descartada. Tendrás que rehacerla después del inicio de sesión.", "depositTips": "Para completar el depósito de GasAccount, esta transacción será descartada. Tendrás que rehacerla después del depósito."}, "keystone": {"signWith": "Cambia a {{method}} para firmar", "unsupportedType": "Error: El tipo de transacción no es compatible o es desconocido.", "qrcodeDesc": "Escanea para firmar. Después de firmar, haz clic abajo para obtener la firma. Para USB, vuelve a conectar y autoriza para comenzar el proceso de firma nuevamente.", "misMatchSignId": "Datos de transacción incongruentes. Por favor, verifica los detalles de la transacción.", "txRejected": "Transacción rechazada", "hardwareRejectError": "La solicitud de Keystone fue cancelada. Para continuar, por favor, reautoriza.", "verifyPasswordError": "Error de firma, inténtelo de nuevo después de desbloquear", "mismatchedWalletError": "<PERSON>a no coincidente", "shouldRetry": "Ocurrió un error. <PERSON><PERSON> favor, inténtalo de nuevo.", "siging": "<PERSON><PERSON><PERSON> solicitud de firma", "shouldOpenKeystoneHomePageError": "Asegúrate de que tu Keystone 3 Pro esté en la página de inicio"}, "keystoneConnected": "Keystone está conectado", "keystoneNotConnected": "Keystone no está conectado", "imKeyNotConnected": "imKey no está conectado", "cancelCurrentConnection": "Cancelar la conexión actual", "blockDappFromSendingRequests": "Bloquear Dapp para que no envíe solicitudes durante 1 min", "cancelCurrentTransaction": "Cancelar la transacción actual", "imKeyConnected": "imKey está conectado", "cancelAll": "<PERSON><PERSON>ar todas las {{count}} solicitudes de <PERSON>", "cancelConnection": "<PERSON><PERSON><PERSON>", "cancelTransaction": "Cancelar Transacción", "detectedMultipleRequestsFromThisDapp": "Detectamos múltiples solicitudes de esta Dapp"}, "signTypedData": {"signTypeDataOnChain": "<PERSON><PERSON><PERSON> en {{chain}}", "safeCantSignText": "Esta es una dirección Safe y no puede usarse para firmar texto.", "permit": {"title": "Aprobación de Permit para Token"}, "permit2": {"title": "Aprobación de Permit2 para Token", "sigExpireTimeTip": "La duración de esta firma para que sea válida en la cadena", "sigExpireTime": "Tiempo de vencimiento de la firma", "approvalExpiretime": "Tiempo de vencimiento de la aprobación"}, "swapTokenOrder": {"title": "<PERSON><PERSON>"}, "sellNFT": {"title": "Orden de NFT", "receiveToken": "Recibir token", "listNFT": "Listar NFT", "specificBuyer": "Comprador específico"}, "signMultiSig": {"title": "Confirmar Transacción"}, "createKey": {"title": "<PERSON><PERSON><PERSON>"}, "verifyAddress": {"title": "Verificar Dirección"}, "buyNFT": {"payToken": "Pagar token", "receiveNFT": "Recibir NFT", "expireTime": "Tiempo de vencimiento", "listOn": "Listar en"}, "contractCall": {"operationDecoded": "La operación está decodificada del mensaje"}, "safeCantSignTypedData": "Esta es una dirección Safe, y solo admite firmar datos tipados EIP-712 o cadenas."}, "activities": {"title": "Registro de Firmas", "signedTx": {"label": "Transacciones", "empty": {"title": "Aún no hay transacciones firmadas", "desc": "Todas las transacciones firmadas a través de Rabby se listarán aquí."}, "common": {"unlimited": "ilimitado", "unknownProtocol": "Protocolo desconocido", "unknown": "Desconocido", "speedUp": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "pendingDetail": "Detalle pendiente"}, "tips": {"pendingDetail": "Solo una transacción se completará, y casi siempre es aquella con el gas price más alto", "canNotCancel": "No se puede acelerar o cancelar: No es la primera transacción pendiente", "pendingBroadcastBtn": "Transmite ahora", "pendingBroadcast": "Modo de ahorro de Gas: esperando tarifas de red más bajas. Máximo {{deadline}}h de espera.", "pendingBroadcastRetryBtn": "Re-broadcast", "pendingBroadcastRetry": "Transmisión fallida. Último intento: {{pushAt}}"}, "status": {"canceled": "Cancelada", "failed": "Fallida", "submitFailed": "Fallo al enviar", "pending": "Pendiente", "pendingBroadcasted": "Pendiente: transmitido", "withdrawed": "Cancelación rápida", "pendingBroadcastFailed": "Pendiente: <PERSON><PERSON><PERSON> al transmitir", "pendingBroadcast": "Pendiente: por transmitir"}, "txType": {"initial": "Tx inicial", "cancel": "Tx de cancelación", "speedUp": "Tx de aceleración"}, "explain": {"unknown": "Transacción desconocida", "send": "Enviar {{amount}} {{symbol}}", "cancel": "Cancelar Aprobación de {{token}} para {{protocol}}", "approve": "Aprobar {{count}} {{token}} para {{protocol}}", "cancelNFTCollectionApproval": "Cancelar Aprobación de Colección de NFT para {{protocol}}", "cancelSingleNFTApproval": "Cancelar Aprobación Individual de NFT para {{protocol}}", "singleNFTApproval": "Aprobación Individual de NFT para {{protocol}}", "nftCollectionApproval": "Aprobación de Colección de NFT para {{protocol}}"}, "CancelTxPopup": {"options": {"quickCancel": {"tips": "Only supported for transactions that haven't broadcast", "title": "Cancelación rápida", "desc": "Cancelar antes de transmitir, sin comisión de gas"}, "removeLocalPendingTx": {"title": "Borrar pendiente localmente", "desc": "Eliminar la transacción pendiente de la interfaz"}, "onChainCancel": {"title": "Cancelación en cadena", "desc": "Nueva transacción para cancelar, requiere gas"}}, "removeLocalPendingTx": {"title": "Eliminar transacción localmente", "desc": "Esta acción eliminará la transacción pendiente localmente. \nEs posible que la transacción pendiente aún se envíe correctamente en el futuro."}, "title": "Cancelar transacción"}, "MempoolList": {"reBroadcastBtn": "Re-broadcast", "empty": "No encontrado en ningún nodo", "title": "Apareció en {{count}} nodos RPC"}, "message": {"broadcastSuccess": "Transmitido", "cancelSuccess": "Cancelado", "reBroadcastSuccess": "Retransmitido", "deleteSuccess": "Eliminado con éxito"}, "gas": {"noCost": "Sin costo de Gas"}, "SkipNonceAlert": {"clearPendingAlert": "La transacción de {{chainName}} ({{nonces}}) ha estado pendiente por más de 3 minutos. Puedes <5></5> <6>Borrar pendiente localmente</6> <7></7> y volver a enviar la transacción.", "alert": "Nonce #{{nonce}} omitido en la cadena {{chainName}}. Esto puede causar transacciones pendientes. <5></5> <6>Enviar una tx</6> <7></7> en la cadena para resolver."}, "PredictTime": {"failed": "Falló la predicción del tiempo de empaquetado", "time": "Se prevé que esté completo en {{time}}", "noTime": "El tiempo de empaquetado está siendo predicho"}, "CancelTxConfirmPopup": {"warning": "La transacción eliminada aún puede confirmarse en la cadena a menos que sea reemplazada.", "title": "Borrar pendiente localmente", "desc": "Esto eliminará la transacción pendiente de su interfaz. <PERSON><PERSON> puede iniciar una nueva transacción."}}, "signedText": {"label": "Texto", "empty": {"title": "Aún no hay textos firmados", "desc": "Todos los textos firmados a través de Rabby se listarán aquí."}}}, "receive": {"title": "Recibir {{token}} en {{chain}}", "watchModeAlert1": "Esta es una dirección en Modo de Observación.", "watchModeAlert2": "¿Estás seguro de que quieres usarla para recibir activos?"}, "sendToken": {"addressNotInContract": "No en la lista de direcciones. <1></1><2>Agregar a contactos</2>", "AddToContactsModal": {"addedAsContacts": "Agregado como contacto", "editAddr": {"placeholder": "Ingresa Nota de Dirección", "validator__empty": "Por favor ingresa una nota de dirección"}, "editAddressNote": "Editar nota de dirección", "error": "Error al agregar a contactos"}, "allowTransferModal": {"error": "Contrase<PERSON>", "placeholder": "Ingresa la Contraseña para Confirmar", "validator__empty": "Por favor ingresa la contraseña", "addWhitelist": "Agregar a la lista blanca"}, "GasSelector": {"confirm": "Confirmar", "level": {"$unknown": "Desconocido", "custom": "Personalizado", "fast": "Instantáneo", "normal": "<PERSON><PERSON><PERSON><PERSON>", "slow": "<PERSON><PERSON><PERSON><PERSON>"}, "popupDesc": "El costo de gas se reservará del monto de la transferencia en función del precio de gas que establezcas", "popupTitle": "<PERSON><PERSON><PERSON> (Gwei)"}, "header": {"title": "Enviar"}, "modalConfirmAddToContacts": {"confirmText": "Confirmar", "title": "Agregar a contactos"}, "modalConfirmAllowTransferTo": {"cancelText": "<PERSON><PERSON><PERSON>", "confirmText": "Confirmar", "title": "Ingresa la Contraseña para Confirmar"}, "sectionBalance": {"title": "<PERSON><PERSON>"}, "sectionChain": {"title": "Cadena"}, "sectionFrom": {"title": "De"}, "sectionTo": {"addrValidator__empty": "Por favor ingresa la dirección", "addrValidator__invalid": "Esta dirección no es válida", "searchInputPlaceholder": "Ingresa la dirección o busca", "title": "Para"}, "sendButton": "Enviar", "tokenInfoFieldLabel": {"chain": "Cadena", "contract": "Dirección del Contrato"}, "tokenInfoPrice": "Precio", "whitelistAlert__disabled": "Lista blanca desactivada. Puedes transferir a cualquier dirección.", "whitelistAlert__notWhitelisted": "La dirección no está en la lista blanca. <1 /> Acepto otorgar permiso temporal para la transferencia.", "whitelistAlert__temporaryGranted": "Permiso temporal otorgado", "whitelistAlert__whitelisted": "La dirección está en la lista blanca", "balanceWarn": {"gasFeeReservation": "Se requiere reserva de tarifa de gas"}, "balanceError": {"insufficientBalance": "<PERSON><PERSON> insuficiente"}, "max": "MÁX", "sectionMsgDataForEOA": {"currentIsOriginal": "El valor actual es Datos originales. UTF-8 es:", "title": "Men<PERSON><PERSON>", "currentIsUTF8": "La entrada actual es UTF-8. Los datos originales son:", "placeholder": "Opcional"}, "sectionMsgDataForContract": {"title": "Llamada de contrato", "parseError": "Error al decodificar la llamada del contrato", "placeholder": "Opcional", "simulation": "Simulación de llamada de contrato:", "notHexData": "Solo se admite datos hexadecimales"}, "blockedTransactionCancelText": "Lo s<PERSON>", "blockedTransactionContent": "Esta transacción interactúa con una dirección que está en la lista de sanciones de OFAC.", "blockedTransaction": "Transacción Bloqueada"}, "sendTokenComponents": {"GasReserved": "Reservados <1>0</1> {{ tokenName }} para costo de gas", "SwitchReserveGas": "Reserva Gas <1 />"}, "sendNFT": {"header": {"title": "Enviar"}, "sectionChain": {"title": "Cadena"}, "sectionFrom": {"title": "<PERSON><PERSON>"}, "sectionTo": {"title": "Para", "addrValidator__empty": "Por favor ingresa la dirección", "addrValidator__invalid": "Esta dirección no es válida", "searchInputPlaceholder": "Ingresa la dirección o busca"}, "nftInfoFieldLabel": {"Collection": "Colección", "Contract": "Contrato", "sendAmount": "Enviar Cantidad"}, "sendButton": "Enviar", "whitelistAlert__disabled": "Lista blanca desactivada. Puedes transferir a cualquier dirección.", "whitelistAlert__whitelisted": "La dirección está en la lista blanca", "whitelistAlert__temporaryGranted": "Permiso temporal otorgado", "whitelistAlert__notWhitelisted": "La dirección no está en la lista blanca. <1 /> Acepto otorgar permiso temporal para la transferencia.", "tipNotOnAddressList": "No está en la lista de direcciones.", "tipAddToContacts": "Agregar a contactos", "confirmModal": {"title": "Ingresa la contraseña para confirmar"}}, "approvals": {"header": {"title": "Aprobaciones en {{ address }}"}, "tab-switch": {"contract": "Por Contratos", "assets": "Por Activos"}, "component": {"table": {"bodyEmpty": {"loadingText": "Cargando...", "noMatchText": "Sin Coincidencias", "noDataText": "Sin Aprobaciones"}}, "ApprovalContractItem": {"ApprovalCount_one": "Aprobación", "ApprovalCount_other": "Aprobaciones"}, "RevokeButton": {"btnText_zero": "Revocar", "btnText_one": "Revocar ({{count}})", "btnText_other": "Revocar ({{count}})", "permit2Batch": {"modalTitle_other": "Se requiere un total de <2>{{count}}</2> firmas", "modalTitle_one": "Se requiere un total de <2>{{count}}</2> firmas", "modalContent": "Las aprobaciones del mismo contrato Permit2 se empaquetarían juntas bajo la misma firma."}}, "ViewMore": {"text": "<PERSON>er más"}}, "search": {"placeholder": "Buscar {{ type }} por nombre/dirección"}, "tableConfig": {"byContracts": {"columnTitle": {"contract": "Contrato", "contractTrustValue": "Valor de Confianza del Contrato", "revokeTrends": "Tendencias de Revocación en 24h", "myApprovedAssets": "Mis Activos Aprobados", "myApprovalTime": "Mi Hora de Aprobación"}, "columnTip": {"contractTrustValue": "El valor de confianza se refiere al valor total de activos aprobados y expuestos a este contrato. Un valor de confianza bajo indica riesgo o inactividad durante 180 días.", "contractTrustValueWarning": "El valor de confianza del contrato < $100,000", "contractTrustValueDanger": "El valor de confianza del contrato < $10,000"}}, "byAssets": {"columnTitle": {"asset": "Activo", "type": "Tipo", "approvedAmount": "Cantidad Aprobada", "approvedSpender": "<PERSON><PERSON><PERSON>", "myApprovalTime": "Mi Hora de Aprobación"}, "columnCell": {"approvedAmount": {"tipMyBalance": "Mi Balance", "tipApprovedAmount": "<PERSON><PERSON>"}}}}, "RevokeApprovalModal": {"subTitleTokenAndNFT": "Tokens y NFTs Aprobados", "subTitleContract": "Aprobado para los siguientes Contratos", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar {{ selectedCount }}", "title": "Aprobaciones", "unSelectAll": "<PERSON>elecci<PERSON><PERSON> todo", "tooltipPermit2": "Esta aprobación se aprueba a través del contrato Permit2:\n{{ permit2Id }}"}, "revokeModal": {"paused": "En pausa", "approvalCount_zero": "{{count}} approval", "approvalCount_one": "{{count}} aprobación", "resume": "<PERSON><PERSON><PERSON><PERSON>", "submitTxFailed": "Error al enviar", "revokeOneByOne": "Revocar uno por uno", "confirm": "Confirmar", "waitInQueue": "Espera en la cola", "revokeWithLedger": "Iniciar <PERSON><PERSON><PERSON>", "totalRevoked": "Total:", "pause": "Pausa", "gasTooHigh": "La tarifa de Gas es alta", "revoked": "Revoked:", "defaultFailed": "Transacción fallida", "stillRevoke": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "approvalCount_other": "{{count}} aprobaciones", "gasNotEnough": "Gas insuficiente para enviar", "confirmRevokePrivateKey": "Usando una frase semilla o dirección de clave privada, puedes revocar {{count}} aprobaciones en lote con 1 clic.", "signAndStartRevoke": "<PERSON><PERSON><PERSON> y Comenzar Rev<PERSON>ar", "connectLedger": "Conectar Ledger", "cancelBody": "Si cierra esta página, las revocaciones restantes no se ejecutarán.", "useGasAccount": "Tu saldo de gas es bajo. Tu GasAccount cubrirá las tarifas de gas.", "batchRevoke": "Revocación por lote", "cancelTitle": "Cancelar las Revocaciones Restantes", "ledgerAlert": "Por favor, abre la aplicación de Ethereum en tu dispositivo Ledger", "confirmRevokeLedger": "Usando una dirección de Ledger, puedes revocar por lotes {{count}} aprobaciones con un solo clic.", "simulationFailed": "Simulación fallida", "confirmTitle": "Revocación por lotes con un clic", "ledgerSended": "Por favor, firme la solicitud en Ledger ({{current}}/{{total}})", "ledgerSigned": "Firmado. Creando transacción ({{current}}/{{total}})", "ledgerSending": "<PERSON><PERSON><PERSON> solicitud de firma ({{current}}/{{total}})"}}, "gasTopUp": {"title": "Recarga Instantánea de Gas", "description": "Recarga gas enviándonos tokens disponibles en otra cadena. Transferencia instantánea tan pronto como se confirme tu pago, sin esperar a que sea irreversible.", "topUpChain": "Cadena para Recarga", "Amount": "Cantidad", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "InsufficientBalance": "No hay suficiente saldo en la dirección del contrato de Rabby para la cadena actual. Por favor, intenta de nuevo más tarde.", "hightGasFees": "Esta cantidad de recarga es demasiado pequeña porque la red de destino requiere altas tarifas de gas.", "No_Tokens": "Sin <PERSON>", "InsufficientBalanceTips": "<PERSON><PERSON> insuficiente", "payment": "Pago de Recarga de Gas", "Loading_Tokens": "Cargando <PERSON>...", "Including-service-fee": "Incluyendo tarifa de servicio de {{fee}}", "service-fee-tip": "Al proporcionar el servicio de Recarga de Gas, <PERSON><PERSON> tiene que asumir la pérdida de fluctuación de tokens y la tarifa de gas para la recarga. Por lo tanto, se cobra una tarifa de servicio del 20%.", "Confirm": "Confirmar", "Select-from-supported-tokens": "Seleccionar entre los tokens soportados", "Value": "Valor", "Payment-Token": "Token de Pago", "Select-payment-token": "Seleccionar token de pago", "Token": "Token", "Balance": "<PERSON><PERSON>"}, "swap": {"title": "Intercambio", "pendingTip": "Tx enviada. Si la tx está pendiente durante muchas horas, puedes intentar eliminarla en la configuración.", "Pending": "Pendiente", "completedTip": "Transacción en la cadena, decodificando datos para generar el registro", "Completed": "Completado", "slippage_tolerance": "Tolerancia de Slippage:", "actual-slippage": "Slippage Actual:", "gas-x-price": "Precio del gas: {{price}} Gwei.", "no-transaction-records": "No hay registros de transacciones", "swap-history": "Historial de Intercambio", "InSufficientTip": "Saldo insuficiente para realizar la simulación de transacción y la estimación de gas. Se muestran las cotizaciones originales del agregador.", "testnet-is-not-supported": "La red personalizada no es compatible", "not-supported": "No es compatible", "slippage-adjusted-refresh-quote": "Slippage ajustado. Actualizar cotización.", "price-expired-refresh-quote": "Precio expirado. Actualizar cotización.", "approve-x-symbol": "Aprobar {{symbol}}", "swap-via-x": "Intercambiar a través de {{name}}", "get-quotes": "Obtener cotizaciones", "chain": "Cadena", "swap-from": "Intercambiar desde", "to": "Hacia", "search-by-name-address": "Buscar por Nombre / Dirección", "amount-in": "Cantidad en {{symbol}}", "unlimited-allowance": "<PERSON><PERSON>o ilimitado", "insufficient-balance": "<PERSON><PERSON> insuficiente", "rabby-fee": "<PERSON><PERSON><PERSON>", "minimum-received": "<PERSON><PERSON><PERSON>", "there-is-no-fee-and-slippage-for-this-trade": "No hay tarifa ni slippage para este intercambio", "approve-tips": "1. <PERSON><PERSON><PERSON> → 2. Intercambiar", "best": "Mejor", "unable-to-fetch-the-price": "No se puede obtener el precio", "fail-to-simulate-transaction": "Error al simular la transacción", "security-verification-failed": "Verificación de seguridad fallida", "need-to-approve-token-before-swap": "Necesitas aprobar el token antes del intercambio", "this-exchange-is-not-enabled-to-trade-by-you": "Este intercambio no está habilitado para el comercio por ti.", "enable-it": "Habilitarlo", "this-token-pair-is-not-supported": "Este par de tokens no es compatible", "QuoteLessWarning": "La cantidad recibida se estima a partir de la simulación de transacción de Rabby. La oferta proporcionada por el exchange es {{receive}}. Recibirás {{diff}} menos de la oferta esperada.", "by-transaction-simulation-the-quote-is-valid": "Por la simulación de transacción, la cotización es válida", "wrap-contract": "Contrato de Envoltura", "directlySwap": "Envolver tokens {{symbol}} directamente con el contrato inteligente", "rates-from-cex": "Tarifas de los Exchange Centralizados (CEX)", "edit": "<PERSON><PERSON>", "tradingSettingTips": "{{viewCount}} exchanges ofrecen cotizaciones y {{tradeCount}} permiten el intercambio", "the-following-swap-rates-are-found": "Se encontraron las siguientes tasas de intercambio", "est-payment": "<PERSON><PERSON> estimado:", "est-receiving": "Recepción estimada:", "est-difference": "Diferencia estimada:", "selected-offer-differs-greatly-from-current-rate-may-cause-big-losses": "La oferta seleccionada difiere mucho de la tasa actual, lo que puede causar grandes pérdidas", "rate": "<PERSON><PERSON>", "low-slippage-may-cause-failed-transactions-due-to-high-volatility": "Un slippage bajo puede causar transacciones fallidas debido a la alta volatilidad", "transaction-might-be-frontrun-because-of-high-slippage-tolerance": "La transacción podría ser adelantada debido a una alta tolerancia al slippage", "recommend-slippage": "Para evitar el adelantamiento, recomendamos un slippage de <2>{{slippage}}</2>%", "slippage-tolerance": "Tolerancia al slippage", "select-token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable-exchanges": "Habilitar Exchanges", "exchanges": "Exchanges", "view-quotes": "Ver cotizaciones", "trade": "Comerciar", "dex": "DEX", "cex": "CEX", "enable-trading": "Habilitar Comercio", "i-understand-and-accept-it": "Entiendo y acepto", "confirm": "Confirmar", "tradingSettingTip1": "1. <PERSON> vez habilitado, interactuarás directamente con el contrato del exchange.", "tradingSettingTip2": "2. Rabby no se hace responsable de los riesgos derivados del contrato de los exchanges.", "gas-fee": "Tarifa de Gas: {{gasUsed}}", "estimate": "Estimado:", "actual": "Real:", "rabbyFee": {"title": "<PERSON><PERSON><PERSON>", "swapDesc": "Rabby Wallet siempre encontrará la mejor tarifa posible de los principales agregadores y verificará la fiabilidad de sus ofertas. Rabby cobra una comisión del 0.25% (0% para wrapping), que se incluye automáticamente en la cotización.", "wallet": "Bill<PERSON>a", "button": "Entendido", "rate": "<PERSON><PERSON> de <PERSON>ri<PERSON>", "bridgeDesc": "Rabby Wallet siempre encontrará la mejor tasa posible de los principales agregadores y verificará la fiabilidad de sus ofertas. Rabby cobra una tarifa del 0,25%, que se incluye automáticamente en la cotización."}, "lowCreditModal": {"title": "Este token tiene un bajo valor de crédito", "desc": "Un valor de crédito bajo a menudo indica un alto riesgo, como un token trampa o una liquidez muy baja."}, "No-available-quote": "No available quote", "source": "Fuente", "hidden-no-quote-rates_one": "{{count}} tarifa no disponible", "max": "MAX", "Auto": "Auto", "approve-swap": "Aprobar y Intercambiar", "Gas-fee-too-high": "Tarifa de gas demasiado alta", "fetch-best-quote": "Obteniendo la mejor cotización", "from": "<PERSON><PERSON>", "approve-and-swap": "Aprobar y Intercambiar a través de {{name}}", "no-fees-for-wrap": "Sin tarifa de Rabby para Wrap", "hidden-no-quote-rates_other": "{{count}} rates no disponibles", "no-slippage-for-wrap": "Sin deslizamiento para Wrap", "no-fee-for-wrap": "Sin tarifa de Rabby por Wrap", "two-step-approve-details": "El token USDT requiere 2 transacciones para cambiar el allowance. <PERSON>ro, tendría que restablecer el allowance a cero, y solo entonces establecer el nuevo valor del allowance.", "sort-with-gas": "Ordenar con gas", "loss-tips": "Estás perdiendo {{usd}}. Prueba con una cantidad menor en un mercado pequeño.", "preferMEV": "Prefiere MEV Guarded", "process-with-two-step-approve": "Proceder con la aprobación en dos pasos", "two-step-approve": "Firma 2 transacciones para cambiar la asignación", "preferMEVTip": "Habilita la función \"MEV Guarded\" para intercambios de Ethereum y reducir los riesgos de ataques sandwich. Nota: esta función no es compatible si utilizas una dirección RPC personalizada o de conexión de wallet.", "price-impact": "Impacto de Precio", "no-quote-found": "No quote found", "usd-after-fees": "≈ {{usd}}"}, "manageAddress": {"no-address": "Sin dirección", "no-match": "Sin coincidencias", "current-address": "Dirección actual", "address-management": "Gestión de direcciones", "update-balance-data": "Actualizar da<PERSON> de saldo", "search": "Buscar", "manage-address": "Administrar <PERSON>", "deleted": "Eliminado", "whitelisted-address": "Dirección en lista blanca", "addressTypeTip": "Importado por {{type}}", "delete-desc": "Antes de eliminar, ten en cuenta los siguientes puntos para entender cómo proteger tus activos.", "delete-checklist-1": "Entiendo que si elimino esta dirección, la Clave Privada y la Frase de Recuperación correspondientes a esta dirección se eliminarán y Rabby NO podrá recuperarla.", "delete-checklist-2": "Confirmo que he respaldado la clave privada o la Frase de Recuperación y estoy listo para eliminarla ahora.", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "delete-private-key-modal-title_one": "Eliminar dirección de clave privada {{count}}", "delete-private-key-modal-title_other": "Eliminar direcciones de clave privada {{count}}", "delete-seed-phrase-title_one": "Eliminar frase de recuperación y su dirección {{count}}", "delete-seed-phrase-title_other": "Eliminar frase de recuperación y sus direcciones {{count}}", "delete-title_one": "Eliminar dirección de {{brand}} {{count}}", "delete-title_other": "Eliminar direcciones de {{brand}} {{count}}", "delete-empty-seed-phrase": "Eliminar frase de recuperación y sus 0 direcciones", "hd-path": "HDPath:", "no-address-under-seed-phrase": "No has importado ninguna dirección bajo esta frase de recuperación.", "add-address": "Agregar <PERSON>", "delete-seed-phrase": "Eliminar frase de recuperación", "confirm-delete": "Confirmar eliminación", "private-key": "Clave Privada", "seed-phrase": "Frase de Recuperación", "watch-address": "Dirección de observación", "backup-seed-phrase": "Respaldo de la Frase de Recuperación", "delete-all-addresses-but-keep-the-seed-phrase": "Eliminar todas las direcciones, pero conservar la Frase de Recuperación", "delete-all-addresses-and-the-seed-phrase": "Eliminar todas las direcciones y la Frase de Recuperación", "seed-phrase-delete-title": "¿Eliminar frase de recuperación?", "enterThePassphrase": "Introduce la frase de contraseña", "addNewAddress": "Agregar nueva dirección", "passphraseError": "Frase de contraseña inválida", "sort-by-address-type": "Ordenar por tipo de dirección", "sort-by-balance": "Ordenar por balance", "enterPassphraseTitle": "Ingrese la frase de contraseña para firmar", "sort-by-address-note": "Ordenar por nota de dirección", "sort-address": "Ordenar dirección", "CurrentDappAddress": {"desc": "Cambiar dirección <PERSON>pp"}}, "dashboard": {"home": {"offline": "La red está desconectada y no se obtienen datos", "panel": {"swap": "Intercambiar", "send": "Enviar", "receive": "Recibir", "gasTopUp": "Recarga de Gas", "queue": "Cola", "transactions": "Transacciones", "approvals": "Aprobaciones", "feedback": "Comentarios", "more": "Más", "manageAddress": "Administra<PERSON>", "nft": "NFT", "ecology": "Ecosystem", "bridge": "Puente", "rabbyPoints": "<PERSON><PERSON>", "mobile": "Mobile Sync"}, "comingSoon": "Próximamente", "soon": "Pronto", "refreshTheWebPageToTakeEffect": "Actualiza la página web para que surtan efecto los cambios", "rabbyIsInUseAndMetamaskIsBanned": "Rabby está en uso y MetaMask está prohibido", "flip": "Cambiar", "metamaskIsInUseAndRabbyIsBanned": "MetaMask está en uso y Rabby está prohibido", "transactionNeedsToSign": "se necesita firmar la transacción", "transactionsNeedToSign": "se necesitan firmar las transacciones", "view": "<PERSON>er", "viewFirstOne": "Ver la primera", "rejectAll": "<PERSON><PERSON><PERSON>", "pendingCount": "1 pendiente", "pendingCountPlural": "{{countStr}} pendientes", "queue": {"title": "Cola", "count": "{{count}} en"}, "whatsNew": "Novedades", "importType": "Importado por {{type}}", "missingDataTooltip": "El saldo puede no estar actualizado debido a problemas actuales de la red con {{text}}.", "chainEnd": "cadena", "chain": "cadena,"}, "recentConnection": {"disconnected": "Desconectado", "rpcUnavailable": "El RPC personalizado no está disponible", "metamaskTooltip": "Prefieres usar MetaMask con esta aplicación. Actualiza esta configuración en cualquier momento en Configuración > Aplicaciones preferidas de MetaMask", "connected": "Conectado", "notConnected": "No conectado", "connectedDapp": "Rabby no está conectado a la aplicación actual. Para conectarte, busca y haz clic en el botón de conexión en la página web de la aplicación.", "noDappFound": "No se encontró ninguna aplicación", "disconnectAll": "Desconectar todo", "disconnectRecentlyUsed": {"title": "Desconectar las <strong>{{count}}</strong> aplicaciones utilizadas recientemente", "description": "Las aplicaciones ancladas seguirán conectadas", "title_one": "Desconectar <strong>{{count}}</strong> Dapp conectada", "title_other": "Desconectar <strong>{{count}}</strong> Dapps conectados"}, "title": "Aplicación Conectada", "pinned": "Anclado", "noPinnedDapps": "No hay aplicaciones ancladas", "dragToSort": "Arrastra para ordenar", "recentlyConnected": "Conectadas recientemente", "noRecentlyConnectedDapps": "No hay aplicaciones conectadas recientemente", "noConnectedDapps": "No connected Dapps", "dapps": "<PERSON><PERSON>", "metamaskModeTooltip": "¿No puedes conectar Rabby en esta Dapp? Intenta habilitar el <1>Modo MetaMask</1>", "metamaskModeTooltipNew": "<PERSON><PERSON>et se conectará cuando selecciones \"MetaMask\" en el Dapp. Puedes gestionar esto en Más > Conectar <PERSON>bby disfrazándose como MetaMask"}, "feedback": {"directMessage": {"content": "<PERSON><PERSON>je <PERSON>", "description": "Chatea con <PERSON><PERSON> Wallet Oficial en DeBank"}, "proposal": {"content": "Propuesta", "description": "Envía una propuesta para <PERSON><PERSON>et en DeBank"}, "title": "Comentarios"}, "nft": {"empty": "No se encontraron NFT en Colecciones soportadas", "collectionList": {"collections": {"label": "Colecciones"}, "all_nfts": {"label": "Todos los NFT"}}, "listEmpty": "Aún no tienes ningún NFT", "modal": {"collection": "Colección", "chain": "Cadena", "lastPrice": "<PERSON><PERSON><PERSON>", "purchaseDate": "<PERSON><PERSON>", "sendTooltip": "<PERSON><PERSON> ah<PERSON>, solo se admiten NFT ERC 721 y ERC 1155", "send": "Enviar"}}, "rabbyBadge": {"imageLabel": "distintivo de Rabby", "title": "Reclama el distintivo de Rabby para", "enterClaimCode": "Ingresa el código de reclamo", "swapTip": "Primero necesitas completar un intercambio en un notable dex dentro de <PERSON>.", "goToSwap": "<PERSON><PERSON> <PERSON>", "claim": "<PERSON><PERSON><PERSON><PERSON>", "viewYourClaimCode": "Ver tu código de reclamo", "noCode": "Aún no has activado el código de reclamo para esta dirección", "learnMoreOnDebank": "Más información en DeBank", "rabbyValuedUserNo": "<PERSON><PERSON><PERSON>bby No.{{num}}", "claimSuccess": "<PERSON><PERSON><PERSON><PERSON>", "viewOnDebank": "Ver en DeBank", "learnMore": "Aprender más", "freeGasTip": "Por favor, firme una transacción usando Free Gas. El botón 'Free Gas' aparecerá automáticamente cuando su gas no sea suficiente.", "rabbyFreeGasUserNo": "Rabby Free Gas Usuario No.{{num}}", "freeGasNoCode": "Haga clic en el botón de abajo para visitar DeBank y obtener el código de reclamación utilizando su dirección actual primero.", "freeGasTitle": "Reclamar Distintivo de Gas Gratis para"}, "contacts": {"noDataLabel": "sin datos", "noData": "Sin datos", "oldContactList": "Lista de Contactos Antigua", "oldContactListDescription": "Debido a la fusión de contactos y direcciones de modelo de observación, los antiguos contactos se respaldarán aquí y después de un tiempo eliminaremos la lista. Por favor, agrega en tiempo si continúas utilizando."}, "security": {"tokenApproval": "Aprobación de Tokens", "nftApproval": "Aprobación de NFT", "comingSoon": "Próximamente más funciones", "title": "Seguridad"}, "settings": {"lock": {"never": "Nunca"}, "7Days": "7 días", "1Day": "1 día", "4Hours": "4 horas", "1Hour": "1 hora", "10Minutes": "10 minutos", "backendServiceUrl": "URL del Servicio de Backend", "inputOpenapiHost": "Por favor, ingresa el host de OpenAPI", "pleaseCheckYourHost": "Por favor, verifica tu host", "host": "Host", "reset": "Restaurar configuración inicial", "save": "Guardar", "pendingTransactionCleared": "Transacciones pendientes eliminadas", "clearPending": "Borrar pendiente localmente", "clearPendingTip1": "Esta acción elimina la transacción pendiente de tu interfaz, ayudando a resolver problemas causados por largas duraciones de espera en la red.", "clearPendingTip2": "No afecta los saldos de su cuenta ni requiere volver a ingresar su seed phrase. Todos los activos y los detalles de la cuenta permanecen seguros.", "autoLockTime": "Tiempo de Bloqueo Automático", "claimRabbyBadge": "¡Reclama la Distinción Rabby!", "cancel": "<PERSON><PERSON><PERSON>", "enableWhitelist": "Habilitar Lista Blanca", "disableWhitelist": "Deshabilitar Lista Blanca", "enableWhitelistTip": "Una vez habilitada, solo podrás enviar activos a las direcciones en la lista blanca usando <PERSON>.", "disableWhitelistTip": "Podrás enviar activos a cualquier dirección una vez deshabilitada", "warning": "Advertencia", "clearWatchAddressContent": "¿Estás seguro de que deseas eliminar todas las direcciones en modo de observación?", "updateVersion": {"content": "Hay una nueva actualización para <PERSON><PERSON> Wallet disponible. Haz clic para ver cómo actualizar manualmente.", "okText": "Ver Tutorial", "successTip": "Estás usando la última versión", "title": "Actualización disponible"}, "features": {"label": "Funciones", "lockWallet": "Bloquear Billetera", "signatureRecord": "Registro de Firmas", "manageAddress": "Administrar <PERSON>", "connectedDapp": "<PERSON><PERSON>", "searchDapps": "Buscar Dapps", "rabbyPoints": "<PERSON><PERSON>", "gasTopUp": "Recarga de Gas"}, "settings": {"label": "Configuración", "enableWhitelistForSendingAssets": "Habilitar Lista Blanca para Enviar Activos", "customRpc": "Modificar URL de RPC", "metamaskPreferredDapps": "Dapps Preferidas de MetaMask", "currentLanguage": "Idioma Actual", "enableTestnets": "Habilitar Testnets", "customTestnet": "Agregar red personalizada", "toggleThemeMode": "<PERSON><PERSON> de <PERSON>", "themeMode": "<PERSON><PERSON> de <PERSON>", "metamaskMode": "Conectar Rabby disfrazándolo como MetaMask", "enableDappAccount": "Cambiar la dirección del Dapp de forma independiente\n"}, "aboutUs": "Acerca de Nosotros", "currentVersion": "Versión Actual", "updateAvailable": "Actualización Disponible", "supportedChains": "Cadenas integradas", "followUs": "Síguenos", "testnetBackendServiceUrl": "URL de Servicio de Backend de Testnet", "clearWatchMode": "Limpiar Modo de Observación", "requestDeBankTestnetGasToken": "Solicitar Token de Gas de Testnet DeBank", "clearPendingWarningTip": "La transacción eliminada aún puede confirmarse en la cadena a menos que sea reemplazada.", "DappAccount": {"button": "Habilitar\n", "title": "Cambiar la dirección del Dapp de forma independiente\n", "desc": "Una vez habilitado, puede elegir qué dirección conectar a cada DApp de forma independiente. Cambiar su dirección principal no afectará la dirección conectada a cada DApp.\n"}}, "tokenDetail": {"blockedTip": "Los tokens bloqueados no se mostrarán en la lista de tokens", "blocked": "Bloqueado", "selectedCustom": "El token no está en la lista de Rabby. <PERSON> has añadido a la lista de tokens de forma personalizada.", "notSelectedCustom": "El token no está en la lista de Rabby. Se añadirá a la lista de tokens si lo activas.", "customized": "Personalizado", "scamTx": "Tx de estafa", "txFailed": "Fallido", "notSupported": "El token en esta cadena no está soportado", "swap": "Intercambio", "send": "Enviar", "receive": "Recibir", "noTransactions": "No hay transacciones", "customizedButton": "personalizado", "blockedButton": "bloqueado", "blockedButtons": "tokens bloqueados", "customizedButtons": "tokens personalizados", "verifyScamTips": "Esto es un token de estafa", "TokenName": "Nombre del token", "NoSupportedExchanges": "No hay intercambios compatibles disponibles", "NoListedBy": "No hay información de listado disponible", "customizedListTitles": "tokens personalizados", "OriginIssue": "Emitido de manera nativa en esta blockchain", "Chain": "Cadena", "myBalance": "<PERSON>", "customizedListTitle": "token personalizado", "IssuerWebsite": "Sitio web del emisor", "maybeScamTips": "Este es un token de baja calidad y puede ser una estafa.", "SupportedExchanges": "Exchanges soportados", "blockedListTitles": "tokens bloqueados", "noIssuer": "No hay información del emisor disponible", "AddToMyTokenList": "Agregar a mi lista de tokens", "ContractAddress": "Dirección del contrato", "BridgeIssue": "Token puenteado por un tercero", "customizedHasAddedTips": "El token no está listado por Rabby. Lo has añadido a la lista de tokens manualmente.", "BridgeProvider": "<PERSON><PERSON><PERSON><PERSON>", "blockedTips": "Los tokens bloqueados no se mostrarán en la lista de tokens.", "blockedListTitle": "token bloqueado", "ListedBy": "Listado por", "OriginalToken": "Token Original", "fdvTips": "La capitalización de mercado si la oferta máxima estuviera en circulación. Valoración Total Diluida (FDV) = Precio x Oferta Máxima. Si la Oferta Máxima es nula, FDV = Precio x Oferta Total. Si no se define ni la Oferta Máxima ni la Oferta Total o son infinitas, no se muestra FDV."}, "assets": {"usdValue": "VALOR EN USD", "amount": "CANTIDAD", "portfolio": {"nftTips": "Calculado en función del precio mínimo reconocido por este protocolo.", "fractionTips": "Calculado en función del precio del token ERC20 vinculado."}, "tokenButton": {"subTitle": "El token en esta lista no se agregará al saldo total"}, "table": {"assetAmount": "Activo / Cantidad", "price": "Precio", "useValue": "Valor en USD", "healthRate": "<PERSON><PERSON> de <PERSON>", "debtRatio": "Proporción de deuda", "unlockAt": "Desbloquear en", "lentAgainst": "PRESTADO CONTRA", "type": "Tipo", "strikePrice": "<PERSON>cio de ejercicio", "exerciseEnd": "Final de ejercicio", "tradePair": "<PERSON>r de comercio", "side": "Lado", "leverage": "Apalancamiento", "PL": "P&L", "unsupportedPoolType": "Tipo de pool no admitido", "claimable": "Reclamable", "endAt": "Finaliza en", "dailyUnlock": "Desbloqueo diario", "pool": "POOL", "token": "Token", "balanceValue": "Saldo / Valor", "percent": "Po<PERSON>entaj<PERSON>", "summaryTips": "Valor del activo dividido por el valor neto total", "summaryDescription": "Todos los activos en protocolos (por ejemplo, tokens LP) se resuelven en los activos subyacentes para cálculos estadísticos", "noMatch": "Sin coincidencias", "lowValueDescription": "Los activos de bajo valor se mostrarán aquí", "lowValueAssets": "{{count}} activos de bajo valor", "lowValueAssets_other": "{{count}} low value tokens", "lowValueAssets_0": "{{count}} token de bajo valor", "lowValueAssets_one": "{{count}} low value token"}, "noAssets": "No hay activos", "blockLinkText": "Buscar dirección para bloquear token", "blockDescription": "Los tokens bloqueados por ti se mostrarán aquí", "unfoldChain": "Desplegar 1 cadena", "unfoldChainPlural": "Desplegar {{moreLen}} cadenas", "customLinkText": "Buscar dirección para agregar token personalizado", "customDescription": "Los tokens personalizados agregados por ti se mostrarán aquí", "comingSoon": "Próximamente...", "searchPlaceholder": "Tokens", "AddMainnetToken": {"tokenAddress": "Dirección del Token", "title": "Agregar token personalizado", "tokenAddressPlaceholder": "Dirección del Token", "selectChain": "Seleccionar cadena", "isBuiltInToken": "Token ya compatible", "searching": "<PERSON><PERSON><PERSON>", "notFound": "Token no encontrado"}, "AddTestnetToken": {"searching": "<PERSON><PERSON><PERSON>", "tokenAddress": "Dirección del Token", "title": "Agregar token de red personalizada", "tokenAddressPlaceholder": "Dirección del Token", "selectChain": "Seleccionar cadena", "notFound": "Token no encontrado"}, "TestnetAssetListContainer": {"add": "Token", "addTestnet": "Red\u001b"}, "customButtonText": "Agregar token personalizado", "noTestnetAssets": "Sin activos de red personalizados", "addTokenEntryText": "Token"}, "hd": {"howToConnectLedger": "¿Cómo Conectar Ledger?", "userRejectedTheRequest": "El usuario rechazó la solicitud.", "ledger": {"doc1": "Conecta un solo Ledger", "doc2": "Ingresa el PIN para desbloquear", "doc3": "Abre la aplicación Ethereum", "reconnect": "Si no funciona, por favor intenta <1>reconectar desde el principio.</1>", "connected": "Ledger conectado"}, "howToSwitch": "¿Cómo cambiar?", "keystone": {"doc3": "Aprobar conexión con la computadora", "doc1": "Conecta un único Keystone", "doc2": "Ingrese la contraseña para desbloquear", "reconnect": "Si no funciona, por favor intenta <1>reconectar desde el principio.</1>", "title": "Asegúrate de que tu Keystone 3 Pro esté en la página de inicio"}, "imkey": {"doc2": "Ingrese pin para desbloquear", "doc1": "Conectar un único imKey"}, "ledgerIsDisconnected": "Tu Ledger no está conectado", "howToConnectKeystone": "Cómo conectar Keystone", "howToConnectImKey": "Cómo conectar imKey"}, "GnosisWrongChainAlertBar": {"warning": "La dirección segura no es compatible con {{chain}}", "notDeployed": "Tu dirección Safe no está desplegada en esta cadena"}, "echologyPopup": {"title": "Ecosistema"}, "MetamaskModePopup": {"enableDesc": "Habilitar si <PERSON> solo funciona con MetaMask", "footerText": "Agregar más Dapps al modo MetaMask en Más > Modo MetaMask", "title": "<PERSON><PERSON>", "toastSuccess": "Habilitado. Actualiza la página para reconectar.", "desc": "Si no puedes conectar Rabby en una Dapp, habilita el Modo MetaMask y conéctate seleccionando la opción MetaMask."}, "offlineChain": {"chain": "{{chain}} pronto no estará integrado.", "tips": "La cadena {{chain}} no se integrará el {{date}}. Tus activos no se verán afectados, pero no se incluirán en tu saldo total. Para acceder a ellos, puedes agregarla como una red personalizada en \"Más\"."}, "recentConnectionGuide": {"button": "Entendido", "title": "Cambia la dirección para la conexión Dapp aquí"}}, "nft": {"floorPrice": "/ <PERSON><PERSON>:", "title": "NFT", "all": "Todos", "starred": "<PERSON><PERSON><PERSON> ({{count}})", "empty": {"title": "Ningún NFT Marcado", "description": "Puedes seleccionar NFT de \"Todos\" y añadir a \"Marcados\""}, "noNft": "No hay NFT"}, "newAddress": {"title": "Agregar una Dirección", "importSeedPhrase": "Importar Frase <PERSON>", "importPrivateKey": "Importar Clave Privada", "importMyMetamaskAccount": "Importar Mi Cuenta de MetaMask", "addContacts": {"content": "Agregar <PERSON>", "description": "También puedes usarlo como una dirección de solo observación", "required": "Por favor ingrese una dirección", "notAValidAddress": "No es una dirección válida", "scanViaMobileWallet": "Escanear a través de billetera móvil", "scanViaPcCamera": "Escanear a través de la cámara de la PC", "scanQRCode": "Escanear códigos QR con billeteras compatibles con WalletConnect", "walletConnect": "WalletConnect", "walletConnectVPN": "WalletConnect será inestable si utilizas una VPN.", "cameraTitle": "Por favor escanea el código QR con tu cámara", "addressEns": "Dirección / ENS"}, "unableToImport": {"title": "No se puede importar", "description": "No se admite la importación de múltiples billeteras de hardware basadas en códigos QR. Por favor, elimina todas las direcciones de {{0}} antes de importar otro dispositivo."}, "connectHardwareWallets": "Conectar Billeteras de Hardware", "connectMobileWalletApps": "Conectar Aplicaciones de Billeteras Móviles", "connectInstitutionalWallets": "Conectar Billeteras Institucionales", "createNewSeedPhrase": "<PERSON><PERSON><PERSON> Frase <PERSON>", "importKeystore": "Importar Archivo Keystore", "selectImportMethod": "Seleccionar Método de Importación", "theSeedPhraseIsInvalidPleaseCheck": "La frase de semilla no es válida, ¡por favor verifícala!", "seedPhrase": {"importTips": "<PERSON><PERSON><PERSON> pegar tu frase de recuperación secreta completa en el primer campo", "whatIsASeedPhrase": {"question": "¿Qué es una Frase de Semilla?", "answer": "Una frase de 12, 18 o 24 palabras que se utiliza para controlar tus activos."}, "isItSafeToImportItInRabby": {"question": "¿Es seguro importarla en Rabby?", "answer": "S<PERSON>, se almacenará localmente en tu navegador y solo será accesible para ti."}, "importError": "[CreateMnemonics] paso inesperado {{0}}", "importQuestion4": "Si desinstalo Rabby sin hacer una copia de respaldo de la frase de semilla, <PERSON><PERSON> no podrá recuperarla para mí.", "riskTips": "<PERSON>tes de comenzar, por favor lee y ten en cuenta los siguientes consejos de seguridad.", "showSeedPhrase": "<PERSON><PERSON>", "backup": "<PERSON><PERSON> Respaldo de la Frase de Semilla", "backupTips": "Asegúrate de que nadie más esté viendo tu pantalla cuando haces una copia de respaldo de la frase de semilla", "copy": "<PERSON><PERSON>r frase de semilla", "saved": "He Guardado la Frase", "pleaseSelectWords": "Por favor selecciona palabras", "verificationFailed": "Verificación fallida", "createdSuccessfully": "Creado Exitosamente", "verifySeedPhrase": "Verificar Frase de Semilla", "fillInTheBackupSeedPhraseInOrder": "Completa la frase de semilla de respaldo en orden", "wordPhrase": "Tengo una frase de <1>{{count}}</1> palabras", "clearAll": "Limpia<PERSON>", "slip39SeedPhrasePlaceholder_two": "Ingrese sus {{count}}as comparticiones de la frase semilla aquí", "pastedAndClear": "Pegado y portapapeles borrado", "slip39SeedPhrasePlaceholder_other": "Ingrese su {{count}}ª parte de la frase semilla aquí", "slip39SeedPhrasePlaceholder_one": "Ingrese aquí su {{count}}.ª parte de la frase semilla", "slip39SeedPhrase": "Tengo una <0>{{SLIP39}}</0> Seed Phrase", "wordPhraseAndPassphrase": "Tengo una frase de <1>{{count}}</1> palabras con Passphrase", "slip39SeedPhraseWithPassphrase": "Tengo una <0>{{SLIP39}}</0> Seed Phrase con Fr<PERSON> de <PERSON>", "slip39SeedPhrasePlaceholder_few": "Introduce tus {{count}}rd partes de la frase semilla aquí", "passphrase": "Passphrase", "invalidContent": "Contenido no válido", "inputInvalidCount_one": "1 entrada no cumple con las normas de la Seed Phrase, por favor revise.", "inputInvalidCount_other": "{{count}} inputs do not conform to Seed Phrase norms, please check.\n", "importQuestion2": "Mi frase semilla solo se almacena en mi dispositivo. Rabby no puede acceder a ella.", "importQuestion3": "Si desinstalo Rabby sin hacer una copia de seguridad de mi frase semilla, no se podrá recuperar mediante <PERSON>.", "importQuestion1": "Si pierdo o comparto mi frase semilla, perderé el acceso a mis activos de forma permanente."}, "metamask": {"step1": "Exporta la frase de semilla o la clave privada desde MetaMask <br/><1>Guía <1/></1>", "step2": "Importa la frase de semilla o la clave privada en Rabby", "step3": "La importación se ha completado y todos tus activos se mostrarán <br /> automáticamente", "how": "¿Cómo importar mi cuenta de MetaMask?", "step": "Paso", "importSeedPhrase": "Importa la frase de semilla o la clave privada", "importSeedPhraseTips": "Solo se almacenará localmente en el navegador. Ra<PERSON> nunca tendrá acceso a tu información privada.", "tips": "Consejos:", "tipsDesc": "Tu frase de recuperación/clave privada no pertenece a MetaMask ni a ninguna billetera específica; solo te pertenece a ti."}, "privateKey": {"required": "Por favor, ingresa la clave privada", "placeholder": "Ingresa tu clave privada", "whatIsAPrivateKey": {"question": "¿Qué es una clave privada?", "answer": "Una cadena de letras y números utilizada para controlar tus activos."}, "isItSafeToImportItInRabby": {"question": "¿Es seguro importarla en Rabby?", "answer": "S<PERSON>, se almacenará localmente en tu navegador y solo será accesible para ti."}, "isItPossibleToImportKeystore": {"question": "¿Es posible importar un archivo Keystore?", "answer": "<PERSON><PERSON>, puedes <1>importar un archivo Keystore</1> aquí."}, "notAValidPrivateKey": "No es una clave privada válida", "repeatImportTips": {"question": "¿Quieres cambiar a esta dirección?", "desc": "Esta dirección ha sido importada."}}, "importedSuccessfully": "Importado exitosamente", "ledger": {"title": "Conectar Ledger", "cameraPermissionTitle": "<PERSON><PERSON><PERSON> que <PERSON> acceda a la cámara", "cameraPermission1": "<PERSON><PERSON><PERSON> que <PERSON> acceda a la cámara en la ventana emergente del navegador", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> permis<PERSON> a <PERSON> para:", "ledgerPermission1": "Conectar a un dispositivo HID", "ledgerPermissionTip": "Por favor, haz clic en \\\"Permitir\\\" a continuación y autoriza el acceso a tu Ledger en la siguiente ventana emergente.", "permissionsAuthorized": "Permisos Autorizados", "nowYouCanReInitiateYourTransaction": "Ahora puedes volver a iniciar tu transacción.", "allow": "<PERSON><PERSON><PERSON>", "error": {"ethereum_app_not_installed_error": "Por favor, instala la aplicación de Ethereum en tu dispositivo Ledger.", "ethereum_app_open_error": "Por favor, instala/acepta la aplicación de Ethereum en tu dispositivo Ledger.", "ethereum_app_unconfirmed_error": "Has negado la solicitud de abrir la aplicación Ethereum.", "running_app_close_error": "No se pudo cerrar la aplicación en ejecución en su dispositivo Ledger."}}, "walletConnect": {"connectYour": "Conectar tu", "viaWalletConnect": "a través de Wallet Connect", "connectedSuccessfully": "Conectado exitosamente", "qrCodeError": "Por favor, verifica tu red o actualiza el código QR", "qrCode": "Código QR", "url": "URL", "changeBridgeServer": "Cambiar servidor de puente", "status": {"received": "Escaneo exitoso. Esperando confirmación", "rejected": "Conexión cancelada. Por favor, escanea el código QR nuevamente para intentarlo.", "brandError": "Error de aplicación de billetera incorrecta.", "brandErrorDesc": "Por favor, utiliza {{brandName}} para conectarte", "accountError": "<PERSON><PERSON><PERSON> de direcci<PERSON>.", "accountErrorDesc": "Por favor, cambia la dirección en tu billetera móvil", "connected": "Conectado", "duplicate": "La dirección que intentas importar ya existe", "default": "Escanear con tu {{brand}}"}, "title": "Conectar con {{brandName}}", "disconnected": "Desconectado", "accountError": {}, "tip": {"accountError": {"tip1": "Conectado pero no es posible firmar.", "tip2": "Por favor, cambia a la dirección correcta en la billetera móvil"}, "disconnected": {"tip": "No conectado a {{brandName}}"}, "connected": {"tip": "Conectado a {{brandName}}"}}, "button": {"disconnect": "Desconectar", "connect": "Conectar", "howToSwitch": "¿Cómo cambiar?"}}, "hd": {"tooltip": {"removed": "La dirección ha sido eliminada de <PERSON>bby", "added": "La dirección ha sido agregada a Rabby", "connectError": "La conexión se ha detenido. Por favor, actualiza la página para conectarte nuevamente.", "disconnected": "No es posible conectar con la billetera de hardware. Por favor, intenta reconectar."}, "waiting": "<PERSON><PERSON><PERSON><PERSON>", "clickToGetInfo": "Haz clic para obtener la información en cadena", "addToRabby": "Ag<PERSON><PERSON> a <PERSON>bby", "basicInformation": "Información básica", "addresses": "Direcciones", "loadingAddress": "Cargando {{0}}/{{1}} direcciones", "notes": "Notas", "getOnChainInformation": "Obtener información en cadena", "hideOnChainInformation": "Ocultar información en cadena", "usedChains": "Cadenas utilizadas", "firstTransactionTime": "Hora de la primera transacción", "balance": "<PERSON><PERSON>", "ledger": {"hdPathType": {"ledgerLive": "Ledger Live: HDPath oficial de Ledger. En las primeras 3 direcciones, hay direcciones utilizadas en cadena.", "bip44": "Estándar BIP44: HDPath definida por el protocolo BIP44. En las primeras 3 direcciones, hay direcciones utilizadas en cadena.", "legacy": "Legacy: HDPath utilizada por MEW / Mycrypto. En las primeras 3 direcciones, hay direcciones utilizadas en cadena."}, "hdPathTypeNoChain": {"ledgerLive": "Ledger Live: HDPath oficial de Ledger. En las primeras 3 direcciones, no hay direcciones utilizadas en cadena.", "bip44": "Estándar BIP44: HDPath definida por el protocolo BIP44. En las primeras 3 direcciones, no hay direcciones utilizadas en cadena.", "legacy": "Legacy: HDPath utilizada por MEW / Mycrypto. En las primeras 3 direcciones, no hay direcciones utilizadas en cadena."}}, "trezor": {"hdPathType": {"bip44": "BIP44: HDPath definida por el protocolo BIP44.", "ledgerLive": "Ledger Live: ruta <PERSON> oficial de Ledger.", "legacy": "Legacy: Ruta HD utilizada por MEW / Mycrypto."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDPath definida por el protocolo BIP44.", "ledgerLive": "Ledger Live: ruta <PERSON> oficial de Ledger.", "legacy": "Legacy: ruta HD utilizada por MEW / Mycrypto."}, "message": {"disconnected": "{{0}}La conexión se ha detenido. Por favor, actualiza la página para conectar nuevamente."}}, "onekey": {"hdPathType": {"bip44": "BIP44: HDPath definida por el protocolo BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDPath definida por el protocolo BIP44."}}, "mnemonic": {"hdPathType": {"default": "Predeterminado: Se utiliza la HDPath predeterminada para importar una frase semilla.", "ledgerLive": "Ledger Live: ruta <PERSON> oficial de Ledger.", "bip44": "Estándar BIP44: HDpath definido por el protocolo BIP44.", "legacy": "Legacy: Ruta HD utilizada por MEW / Mycrypto."}, "hdPathTypeNoChain": {"default": "Predeterminado: Se utiliza la HDPath predeterminada para importar una frase semilla."}}, "gridplus": {"hdPathType": {"ledgerLive": "Ledger Live: HDPath oficial de Ledger. En las primeras 3 direcciones, hay direcciones utilizadas en la cadena.", "bip44": "Estándar BIP44: HDPath definida por el protocolo BIP44. En las primeras 3 direcciones, hay direcciones utilizadas en la cadena.", "legacy": "Legacy: HDPath utilizada por MEW / Mycrypto. En las primeras 3 direcciones, hay direcciones utilizadas en la cadena."}, "hdPathTypeNochain": {"ledgerLive": "Ledger Live: HPathD oficial de Ledger. En las primeras 3 direcciones, no hay direcciones utilizadas en la cadena.", "bip44": "Estándar BIP44: HDPath definida por el protocolo BIP44. En las primeras 3 direcciones, no hay direcciones utilizadas en la cadena.", "legacy": "Legacy: HDPath utilizada por MEW / Mycrypto. En las primeras 3 direcciones, no hay direcciones utilizadas en la cadena."}, "switch": {"title": "Cambiar a un nuevo dispositivo GridPlus", "content": "No es compatible importar múltiples dispositivos GridPlus. Si cambias a un nuevo dispositivo GridPlus, la lista de direcciones del dispositivo actual se eliminará antes de iniciar el proceso de importación."}, "switchToAnotherGridplus": "Cambiar a otro dispositivo GridPlus"}, "keystone": {"hdPathType": {"bip44": "BIP44: HDPath definida por el protocolo BIP44.", "legacy": "Legacy: ruta HD utilizada por MEW / Mycrypto.", "ledgerLive": "Ledger Live: <PERSON>uta HD oficial de Ledger. Solo puedes gestionar 10 direcciones con la ruta de Ledger Live."}, "hdPathTypeNochain": {"bip44": "BIP44: HDPath definida por el protocolo BIP44.", "ledgerLive": "Ledger Live: <PERSON>uta HD oficial de Ledger. Solo puedes gestionar 10 direcciones con la ruta de Ledger Live.", "legacy": "Legado: Ruta HD utilizada por MEW / Mycrypto."}}, "bitbox02": {"hdPathType": {"bip44": "BIP44: HDPath definida por el protocolo BIP44."}, "hdPathTypeNoChain": {"bip44": "BIP44: HDPath definida por el protocolo BIP44."}, "disconnected": "No se puede conectar a BitBox02. Por favor, actualiza la página para conectar nuevamente. Razón: {{0}}"}, "selectHdPath": "Seleccionar HDPath:", "selectIndexTip": "Selecciona el número de serie de las direcciones desde donde comenzar:", "manageAddressFrom": "Administrar direcci<PERSON> desde {{0}} hasta {{1}}", "advancedSettings": "Configuración avanzada", "customAddressHdPath": "HDPath de dirección personalizada", "connectedToLedger": "Conectado a Ledger", "connectedToTrezor": "Conectado a Trezor", "connectedToOnekey": "Conectado a OneKey", "manageSeedPhrase": "Administrar frase de recuperación", "manageGridplus": "Administrar GridPlus", "manageKeystone": "Administrar <PERSON>", "manageAirgap": "Administrar AirGap", "manageCoolwallet": "Admini<PERSON><PERSON>", "manageBitbox02": "Administrar BitBox02", "manageNgraveZero": "Administrar NGRAVE ZERO", "done": "<PERSON><PERSON>", "addressesIn": "Direcciones en {{0}}", "addressesInRabby": "Direcciones en Rabby {{0}}", "qrCode": {"switch": {"title": "Cambiar a un nuevo dispositivo {{0}}", "content": "No es compatible importar múltiples dispositivos {{0}}. Si cambias a un nuevo dispositivo {{0}}, la lista de direcciones del dispositivo actual se eliminará antes de iniciar el proceso de importación."}, "switchAnother": "Cambiar a otro {{0}}"}, "importBtn": "Importar ({{count}})", "manageImtokenOffline": "Administrar imToken", "manageImKey": "<PERSON><PERSON><PERSON><PERSON>"}, "importYourKeystore": "Importar su KeyStore", "incorrectPassword": "Contrase<PERSON>", "keystore": {"description": "Seleccione el archivo de keystore que desea importar e ingrese la contraseña correspondiente", "password": {"required": "Por favor, ingrese la contraseña", "placeholder": "Contraseña"}}, "imkey": {"title": "Conectar imKey", "imkeyPermissionTip": "Por favor, haz clic en \"Permitir\" abajo y autoriza el acceso a tu imKey en la ventana emergente siguiente."}, "keystone": {"title": "Conectar Keystone", "deviceIsBusy": "El dispositivo está ocupado", "deviceIsLockedError": "Introduzca la contraseña para desbloquear", "keystonePermission1": "Conectar a un dispositivo USB", "noDeviceFoundError": "Conectar un solo Keystone", "deviceRejectedExportAddress": "Aprobar la conexión a Rabby", "unknowError": "<PERSON><PERSON><PERSON> des<PERSON>, por favor intente de nuevo", "exportAddressJustAllowedOnHomePage": "Export address just allowed on home page", "allowRabbyPermissionsTitle": "<PERSON><PERSON><PERSON> para:", "keystonePermissionTip": "Haga clic en \"Permitir\" a continuación para autorizar el acceso a su Keystone en la siguiente ventana emergente, y asegúrese de que su Keystone 3 Pro esté en la página de inicio."}, "coboSafe": {"findTheAssociatedSafeAddress": "Encuentra la dirección segura asociada", "whichChainIsYourCoboAddressOn": "¿En qué cadena está tu dirección de cobo?", "addCoboArgusAddress": "Agregar dirección de Cobo Argus", "invalidAddress": "Dirección no válida", "import": "Importar", "inputSafeModuleAddress": "Introducir dirección del módulo Safe"}, "firefoxLedgerDisableTips": "Ledger no es compatible con Firefox", "addFromCurrentSeedPhrase": "Añadir desde la frase de recuperación actual"}, "unlock": {"btn": {"unlock": "Desb<PERSON>que<PERSON>"}, "password": {"required": "Ingrese la contraseña para desbloquear", "placeholder": "Ingrese la contraseña para desbloquear", "error": "Contrase<PERSON>"}, "title": "<PERSON><PERSON>", "description": "El wallet revolucionario para Ethereum y todas las cadenas EVM", "btnForgotPassword": "¿Olvidaste la contraseña?"}, "addToken": {"noTokenFound": "No se encontró ningún token", "tokenSupported": "El token ha sido soportado en Rabby", "tokenCustomized": "El token actual ya ha sido agregado como personalizado", "tokenNotFound": "Token no encontrado en esta dirección de contrato", "title": "Agregar token personalizado a Rabby", "balance": "<PERSON><PERSON>", "tokenOnMultiChains": "Dirección del token en varias cadenas. Por favor, elija una", "noTokenFoundOnThisChain": "No se encontró ningún token en esta cadena", "hasAdded": "Has añadido este token."}, "switchChain": {"title": "Agregar red personalizada a Rabby", "chainNotSupport": "La cadena solicitada aún no es compatible con Rabby", "testnetTip": "Por favor, active \"Habilitar Testnets\" en \"Más\" antes de conectarse a las testnets", "chainId": "ID de cadena:", "addChain": "Agregar Testnet", "requestsReceivedPlural": "{{count}} solicitudes recibidas", "chainNotSupportYet": "La cadena solicitada aún no es compatible con Rabby.", "unknownChain": "Cadena desconocida", "requestsReceived": "1 solicitud recibida", "chainNotSupportAddChain": "La cadena solicitada aún no está integrada por Rabby. Puedes añadirla como una Testnet personalizada.", "desc": "La red solicitada aún no está integrada por Rabby. Puedes añadirla manualmente como una red personalizada.", "requestRabbyToSupport": "Solicitar a Rabby que apoye"}, "signText": {"title": "<PERSON><PERSON><PERSON>", "message": "Men<PERSON><PERSON>", "createKey": {"interactDapp": "Interactuar con <PERSON>", "description": "Descripción"}, "sameSafeMessageAlert": "Se confirma el mismo mensaje; no se requiere una firma adicional."}, "securityEngine": {"yes": "Sí", "no": "No", "whenTheValueIs": "cuando el valor es {{value}}", "currentValueIs": "El valor actual es {{value}}", "viewRules": "Ver reglas de seguridad", "undo": "<PERSON><PERSON><PERSON>", "riskProcessed": "Riesgo procesado", "ignoreAlert": "Ignorar la alerta", "ruleDisabled": "Las reglas de seguridad han sido desactivadas. Para tu seguridad, puedes activarlas en cualquier momento.", "unknownResult": "Resultado desconocido porque la regla de seguridad no está disponible", "alertTriggerReason": "Motivo de activación de la alerta:", "understandRisk": "Entiendo y acepto la responsabilidad por cualquier pérdida", "forbiddenCantIgnore": "Se encontró un riesgo prohibido que no se puede ignorar.", "ruleDetailTitle": "Detalle de la regla", "enableRule": "Habilitar la regla", "viewRiskLevel": "Ver nivel de riesgo"}, "connect": {"listedBy": "Listado por", "sitePopularity": "Popularidad del sitio", "myMark": "<PERSON>", "flagByRabby": "<PERSON><PERSON> por <PERSON>", "flagByMM": "Marcado por MetaMask", "flagByScamSniffer": "<PERSON><PERSON> por ScamSniffer", "verifiedByRabby": "Verificado por <PERSON>", "foundForbiddenRisk": "Se encontraron riesgos prohibidos. La conexión está bloqueada.", "markAsTrustToast": "Marcar como \"Confiable\"", "markAsBlockToast": "Marcar como \"Bloqueado\"", "markRemovedToast": "Marc<PERSON> removida", "title": "Conectar a Dapp", "selectChainToConnect": "Selecciona una cadena para conectarte", "markRuleText": "Mi marca", "connectBtn": "Conectar", "noWebsite": "<PERSON><PERSON><PERSON>", "popularLevelHigh": "Alto", "popularLevelMedium": "Medio", "popularLevelLow": "<PERSON><PERSON>", "popularLevelVeryLow": "<PERSON><PERSON>", "noMark": "Sin marca", "blocked": "Bloqueado", "trusted": "Confiable", "addedToWhitelist": "Añadido a tu lista blanca", "addedToBlacklist": "Añadido a tu lista negra", "removedFromAll": "Removido de todas las listas", "notOnAnyList": "No está en ninguna lista", "onYourBlacklist": "En tu lista negra", "onYourWhitelist": "En tu lista blanca", "manageWhiteBlackList": "Administrar lista blanca/negra", "SignTestnetPermission": {"title": "Permiso de firma en testnet"}, "ignoreAll": "<PERSON><PERSON><PERSON> todo", "SelectWallet": {"desc": "Elige entre las billeteras que has instalado", "title": "Seleccione una billetera para conectar"}, "otherWalletBtn": "Conectar con Otra Wallet", "connectAddress": "Conectar Dirección"}, "addressDetail": {"add-to-whitelist": "Agregar a lista blanca", "remove-from-whitelist": "Eliminar de lista blanca", "address-detail": "Detalle de dirección", "backup-private-key": "Re<PERSON>aldar clave privada", "backup-seed-phrase": "Respaldar frase de recuperación", "delete-address": "Eliminar dirección", "delete-desc": "Antes de eliminar, ten en cuenta los siguientes puntos para entender cómo proteger tus activos.", "direct-delete-desc": "Esta dirección es una dirección {{renderBrand}}, <PERSON><PERSON> no almacena la clave privada ni la frase de recuperación de esta dirección, simplemente puedes eliminarla", "admins": "Administradores", "tx-requires": "Cualquier transacción requiere <2>{{num}}</2> confirmaciones", "edit-memo-title": "Editar nota de dirección", "please-input-address-note": "Por favor ingresa una nota de dirección", "address": "Dirección", "address-note": "Nota de dirección", "assets": "Activos", "qr-code": "Código QR", "source": "Origen", "hd-path": "HDPath", "manage-seed-phrase": "Administrar frase de recuperación", "manage-addresses-under-this-seed-phrase": "Administrar direcciones bajo esta frase de recuperación", "safeModuleAddress": "Dirección del Módulo Safe", "manage-addresses-under": "Administra direcciones bajo este {{brand}}", "coboSafeErrorModule": "La dirección ha caducado, por favor elimínala e importa la dirección de nuevo.", "importedDelegatedAddress": "Dirección delegada importada"}, "preferMetamaskDapps": {"title": "Dapps preferidos de MetaMask", "desc": "Las siguientes dapps seguirán conectadas a través de MetaMask, independientemente de la billetera que hayas seleccionado.", "howToAdd": "<PERSON><PERSON><PERSON> a<PERSON>", "howToAddDesc": "Haz clic derecho en el sitio web y encuentra esta opción", "empty": "No hay dapps"}, "customRpc": {"opened": "<PERSON>bie<PERSON>o", "closed": "<PERSON><PERSON><PERSON>", "empty": "No custom RPC URL", "title": "Modificar URL de RPC", "desc": "Una vez modificado, el RPC personalizado reemplazará el nodo de Rabby. Para continuar usando el nodo de <PERSON><PERSON>, elimine el RPC personalizado.", "add": "Modificar URL de RPC", "EditRPCModal": {"invalidRPCUrl": "URL de RPC inválido", "invalidChainId": "ID de cadena inválido", "rpcAuthFailed": "Autenticación RPC fallida", "title": "Modificar URL RPC", "rpcUrl": "URL de RPC", "rpcUrlPlaceholder": "Ingresa la URL de RPC"}, "EditCustomTestnetModal": {"quickAdd": "<PERSON><PERSON><PERSON> desde Chainlist", "title": "Agregar red personalizada"}}, "requestDebankTestnetGasToken": {"title": "Solicitar Token de Gas de DeBank en Testnet", "mintedTip": "Los poseedores del Badge de Rabby pueden solicitar una vez al día", "notMintedTip": "La solicitud está disponible solo para los poseedores del Badge de Rabby", "claimBadgeBtn": "Obtener Badge de Rabby", "time": "Por día", "requested": "Has realizado una solicitud hoy", "requestBtn": "Solicitar"}, "safeQueue": {"title": "Cola", "sameNonceWarning": "Estas transacciones entran en conflicto ya que utilizan el mismo nonce. Ejecutar una automáticamente reemplazará a la(s) otra(s).", "loading": "Cargando transacciones pendientes", "noData": "No hay transacciones pendientes", "loadingFaild": "Debido a la inestabilidad del servidor Safe, los datos no están disponibles, por favor verifica de nuevo después de 5 minutos", "accountSelectTitle": "Puedes enviar esta transacción usando cualquier dirección", "LowerNonceError": "La transacción con nonce {{nonce}} debe ejecutarse primero", "submitBtn": "Enviar transacción", "unknownTx": "Transacción desconocida", "cancelExplain": "Cancelar Aprobación de {{token}} para {{protocol}}", "unknownProtocol": "Protocolo desconocido", "approvalExplain": "Aprobar {{count}} {{token}} para {{protocol}}", "unlimited": "ilimitado", "action": {"send": "Enviar", "cancel": "Cancelar transacción pendiente"}, "viewBtn": "<PERSON>er", "ReplacePopup": {"options": {"send": "<PERSON><PERSON><PERSON>", "reject": "<PERSON>chazar <PERSON>"}, "title": "Seleccione cómo reemplazar esta transacción", "desc": "Una transacción firmada no puede ser eliminada, pero puede ser reemplazada con una nueva transacción con el mismo nonce."}, "replaceBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "importSuccess": {"title": "Importado Exitosamente", "addressCount": "{{count}} direcciones", "gnosisChainDesc": "Esta dirección se encontró desplegada en {{count}} cadenas"}, "backupSeedPhrase": {"title": "Respaldar Frase de Recuperación", "alert": "Esta Frase de Recuperación es la credencial para tus activos. NO la pierdas ni la reveles a otros, de lo contrario podrías perder tus activos para siempre. Por favor, revísala en un entorno seguro y guárdala cuidadosamente.", "clickToShow": "Haz clic para mostrar la Frase de Recuperación", "copySeedPhrase": "Copiar frase de recuperación", "showQrCode": "Mostrar código QR", "qrCodePopupTips": "Nunca compartas el código QR de la seed phrase con nadie más. Por favor, revísalo en un entorno seguro y guárdalo cuidadosamente.", "qrCodePopupTitle": "Código QR"}, "backupPrivateKey": {"title": "Respaldar Clave Privada", "alert": "Esta Clave Privada es la credencial para tus activos. NO la pierdas ni la reveles a otros, de lo contrario podrías perder tus activos para siempre. Por favor, revísala en un entorno seguro y guárdala cuidadosamente.", "clickToShow": "Haz clic para mostrar la Clave Privada", "clickToShowQr": "Haz clic para mostrar el código QR de la Clave Privada"}, "ethSign": {"alert": "Firmar con 'eth_sign' puede llevar a la pérdida de activos. Por tu seguridad, <PERSON><PERSON> no admite este método."}, "createPassword": {"title": "<PERSON><PERSON><PERSON>", "passwordRequired": "Por favor ingresa una contraseña", "passwordMin": "La contraseña debe tener al menos 8 caracteres", "passwordPlaceholder": "La contraseña debe tener al menos 8 caracteres", "confirmRequired": "Por favor confirma la contraseña", "confirmError": "Las contraseñas no coinciden", "confirmPlaceholder": "Confirmar con<PERSON>", "agree": "He leído y acepto los <1/> <2>T<PERSON>rm<PERSON>s de Uso</2>"}, "welcome": {"step1": {"title": "Acceso a Todas las Dapps", "desc": "<PERSON>bby se conecta a todas las Dapps que MetaMask admite"}, "step2": {"title": "Auto custodia", "desc": "Las claves privadas se almacenan localmente con acceso exclusivo para ti", "btnText": "Comenzar"}}, "importSafe": {"title": "Agregar dirección Safe", "placeholder": "Por favor ingresa la dirección", "error": {"invalid": "No es una dirección válida", "required": "Por favor ingresa la dirección"}, "loading": "Buscando la cadena en la que se desplegó esta dirección", "gnosisChainDesc": "Esta dirección se encontró desplegada en {{count}} cadenas"}, "importQrBase": {"desc": "Escanee el código QR en la billetera de hardware {{brandName}}", "btnText": "Intentar de nuevo"}, "bridge": {"showMore": {"title": "Mostrar más", "source": "Fuente del Bridge"}, "settingModal": {"confirmModal": {"i-understand-and-accept-it": "Entiendo y lo acepto", "tip2": "2. Rabby no es responsable de ningún riesgo derivado del contrato de este agregador", "tip1": "Una vez habilitado, interactuarás directamente con el contrato desde este aggregator.", "title": "Habilitar el comercio con este Agregador"}, "confirm": "Confirmar", "SupportedBridge": "Puente admitido:", "title": "Habilitar Bridge Aggregators para comerciar"}, "tokenPairDrawer": {"title": "Seleccione de los pares de tokens compatibles", "noData": "No Supported Token Pair", "balance": "Valor del Balance", "tokenPair": "<PERSON><PERSON> <PERSON>"}, "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "history": "<PERSON><PERSON> de Bridge", "Completed": "Completado", "To": "Para", "From": "<PERSON><PERSON>", "Balance": "Saldo:", "no-quote-found": "No se encontró cotización. Por favor, prueba con otros pares de tokens.", "unlimited-allowance": "<PERSON><PERSON>o ilimitado", "title": "Bridge", "BridgeTokenPair": "<PERSON><PERSON> <PERSON>", "the-following-bridge-route-are-found": "Encontrada la siguiente ruta", "gas-x-price": "Precio del gas: {{price}} Gwei.", "select-chain": "Seleccionar Chain", "no-quote": "Sin cotización", "insufficient-balance": "<PERSON><PERSON> insuficiente", "gas-fee": "GasFee: {{gasUsed}}", "no-transaction-records": "Sin registros de transacciones", "Pending": "Pendiente", "bridgeTo": "Puente a", "actual": "Actual:", "completedTip": "Transacción en cadena, decodificando datos para generar registro", "estimate": "Estimación:", "detail-tx": "Detalle", "est-receiving": "Est. Receiving:", "pendingTip": "Transacción enviada. Si la transacción está pendiente durante muchas horas, puedes intentar borrar pendiente en la configuración.", "est-difference": "Dif. Est.:", "estimated-value": "≈ {{value}}", "price-impact": "Impacto en el precio", "enable-it": "Habilitarlo", "via-bridge": "a través de {{bridge}}", "recommendFromToken": "Puente desde <1></1> para una cotización disponible", "est-payment": "Est. Payment:", "aggregator-not-enabled": "Este agregador no está habilitado para intercambiar por usted.", "no-route-found": "No se encontró ruta", "best": "Mejor", "slippage-adjusted-refresh-quote": "Deslizamiento ajustado. Actualizar ruta.", "bridge-via-x": "Bridge on {{name}}", "rabby-fee": "Rabby fee", "bridge-cost": "Costo del Bridge", "approve-x-symbol": "Aprobar {{symbol}}", "max-tips": "Este valor se calcula restando el costo de gas para el puente.", "duration": "{{duration}} min", "price-expired-refresh-route": "El precio ha expirado. Actualizar ruta.", "approve-and-bridge": "<PERSON><PERSON><PERSON> y Puente", "need-to-approve-token-before-bridge": "Necesita aprobar el token antes del puente", "getRoutes": "Obtener rutas", "Amount": "Monto", "loss-tips": "Está perdiendo {{usd}}. Intente con una cantidad diferente.", "tokenPairPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pendingDetail": {"Header": {"predictTime": "Predicted to be packed in"}, "TxStatus": {"pendingBroadcast": "Pendiente: Para ser transmitido", "reBroadcastBtn": "Re-broadcast", "completed": "Completado", "pendingBroadcasted": "Pendiente: Transmitido"}, "TxHash": {"hash": "Tx Hash"}, "TxTimeline": {"created": "Transacción creada", "broadcastedCount_ordinal_one": "{{count}}ª emisión", "broadcastedCount_ordinal_two": "{{count}}.ª emisión", "broadcastedCount_ordinal_few": "{{count}}ra emisión", "pending": "Comprobando el estado...", "broadcastedCount_ordinal_other": "{{count}}a transmisión", "broadcasted": "Recientemente transmitido"}, "MempoolList": {"col": {"nodeName": "Nombre del Nodo", "nodeOperator": "Operador de nodo", "txStatus": "Estado de la transacción"}, "txStatus": {"appearedOnce": "Apareció una vez", "appeared": "Apareció", "notFound": "No encontrado"}, "title": "Apareció en {{count}} nodos RPC"}, "PendingTxList": {"filterBaseFee": {"label": "Solo cumple con el requisito de tarifa Base", "tooltip": "Mostrar solo las transacciones cuyo Gas Price cumpla con los requisitos de tarifa base del bloque"}, "col": {"interact": "Interactuar con", "actionType": "Tipo de acción", "gasPrice": "Gas Price", "action": "Acción de transacción", "balanceChange": "Cambio de balance"}, "titleSameNotFound": "Sin clasificación en Igual que Actual", "title": "GasPrice ocupa el puesto n.º {{rank}} entre todas las transacciones pendientes.", "titleSame": "GasPrice Ranks #{{rank}} en Same as Current", "titleNotFound": "Sin clasificación en todas las Txs pendientes"}, "Empty": {"noData": "No se encontraron datos"}, "PrePackInfo": {"col": {"prePackContent": "Contenido preempaquetado", "expectations": "Expectations", "difference": "Verificar resultados", "prePackResults": "Resultados preempaquetados"}, "type": {"receive": "Recibir", "pay": "<PERSON><PERSON>"}, "loss": "{{lossCount}} p<PERSON>rdida encontrada", "noLoss": "No se encontró pérdida", "noError": "No se encontraron errores", "error": "{{count}} error encontrado", "title": "Verificación previa al embalaje", "desc": "Simulación ejecutada en el último bloque, actualizada {{time}}"}, "Predict": {"completed": "Transacción completada", "predictFailed": "Error en la predicción del tiempo de empaquetado", "skipNonce": "Su dirección ha omitido Nonce en la cadena de Ethereum, lo que provoca que la transacción actual no se pueda completar."}}, "dappSearch": {"searchResult": {"foundDapps": "Se encontraron <2>{{count}}</2> Dapps", "totalDapps": "Total <2>{{count}}</2> Dapps"}, "expand": "Expand", "listBy": "Dapp ha sido listado por", "emptySearch": "No Dapp Found", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "selectChain": "Seleccionar Chain", "emptyFavorite": "Sin Dapp favorita"}, "rabbyPoints": {"claimItem": {"claimed": "Reclamado", "go": "<PERSON>r", "claim": "<PERSON><PERSON><PERSON><PERSON>", "disabledTip": "No se pueden reclamar puntos ahora", "earnTip": "Una vez al día, lí<PERSON>. <PERSON><PERSON> <PERSON>, gana puntos después de las 00:00 UTC+0"}, "claimModal": {"claim": "<PERSON><PERSON><PERSON><PERSON>", "addressBalance": "<PERSON><PERSON>", "rabbyUser": "Usuario activo de Rabby", "MetaMaskSwap": "MetaMask Swap", "placeholder": "Introduce el código de referido para puntos extra (opcional)", "title": "<PERSON><PERSON><PERSON>ar Puntos Iniciales", "snapshotTime": "<PERSON><PERSON> de la instantánea: {{time}}", "invalid-code": "c<PERSON><PERSON> in<PERSON>", "referral-code": "Código de Referencia", "activeStats": "Estado Activo", "season2": "Temporada 2", "cantUseOwnCode": "No puedes usar tu propio código de referido.", "walletBalance": "<PERSON><PERSON>", "rabbyDesktopGenesisNft": "Rabby Desktop Genesis NFT", "rabbyValuedUserBadge": "Insignia de Usuario <PERSON>"}, "referralCode": {"verifyAddressModal": {"sign": "<PERSON><PERSON><PERSON>", "please-sign-this-text-message-to-verify-that-you-are-the-owner-of-this-address": "Por favor, firma este mensaje de texto para verificar que eres el propietario de esta dirección.", "verify-address": "Verificar dirección", "cancel": "<PERSON><PERSON><PERSON>"}, "confirm": "Confirmar", "my-referral-code": "Mi código de referencia", "set-my-code": "Establecer mi código", "referral-code-already-exists": "El código de referencia ya existe", "referral-code-available": "Código de referido disponible", "referral-code-cannot-be-empty": "El código de referencia no puede estar vacío", "max-15-characters-use-numbers-and-letters-only": "Máximo 15 caracteres, usa solo números y letras.", "refer-a-new-user-to-get-50-points": "Refiere a un nuevo usuario para obtener 50 puntos", "referral-code-cannot-exceed-15-characters": "El código de referencia no puede exceder los 15 caracteres。", "once-set-this-referral-code-is-permanent-and-cannot-change": "Una vez establecido, este código de referido es permanente y no se puede cambiar.", "set-my-referral-code": "Establecer mi código de referencia"}, "title": "<PERSON><PERSON>", "earn-points": "<PERSON><PERSON><PERSON>", "top-100": "Top 100", "share-on": "Compartir en", "out-of-x-current-total-points": "Fuera de {{total}} Total de Puntos Distribuidos", "code-set-successfully": "Código de referencia configurado con éxito", "initialPointsClaimEnded": "La reclamación de Initial Points finalizó", "secondRoundEnded": "🎉 La segunda ronda de Rabby Points ha finalizado", "firstRoundEnded": "🎉 La primera ronda de Rabby Points ha terminado", "referral-code-copied": "Código de referencia copiado"}, "customTestnet": {"CustomTestnetForm": {"blockExplorerUrl": "URL del explorador de bloques (Opcional)", "nameRequired": "Por favor, introduzca el nombre de la red", "nativeTokenSymbol": "Símbolo de moneda", "nativeTokenSymbolRequired": "Por favor, introduzca el símbolo de la moneda", "rpcUrlRequired": "Por favor, introduzca la URL RPC", "rpcUrl": "RPC URL", "id": "Chain ID", "name": "Nombre de la red", "idRequired": "Por favor, introduzca el id de la cadena"}, "AddFromChainList": {"tips": {"added": "<PERSON> has añadido esta cadena", "supported": "Cadena ya integrada por <PERSON><PERSON>"}, "empty": "No chains found", "search": "Buscar nombre o ID de red personalizada", "title": "Agregar rápidamente desde Chainlist"}, "signTx": {"title": "Datos de Transacción"}, "ConfirmModifyRpcModal": {"desc": "La cadena ya está integrada por Rabby. ¿Necesitas modificar su URL de RPC?"}, "title": "Red de red personalizada", "desc": "Rabby no puede verificar la seguridad de las redes personalizadas. Por favor, añade solo redes de confianza.", "add": "Agregar red personalizada", "empty": "Sin red personalizada", "currency": "Moneda", "id": "ID"}, "addChain": {"title": "Agregar red personalizada a Rabby", "desc": "Rabby no puede verificar la seguridad de las redes personalizadas. Por favor, añade solo redes de confianza."}, "sign": {"transactionSpeed": "Velocidad de transacción"}, "ecology": {"sonic": {"home": {"migrateTitle": "<PERSON><PERSON><PERSON>", "migrateBtn": "Próximamente", "arcadeBtn": "<PERSON><PERSON> ah<PERSON>", "airdropBtn": "<PERSON><PERSON>", "airdrop": "Airdrop", "migrateDesc": "→", "arcadeDesc": "Juega a juegos gratis para ganar puntos para el airdrop de S.", "earnDesc": "Apuesta tus $S", "earnBtn": "Próximamente", "earnTitle": "G<PERSON><PERSON>", "airdropDesc": "~200 millones S a usuarios en Opera y Sonic.", "socialsTitle": "Involúcrate"}, "points": {"pointsDashboard": "Panel de puntos", "sonicArcade": "Sonic Arcade", "sonicArcadeBtn": "Comienza a jugar", "sonicPoints": "Puntos Sonic", "retry": "Reintentar", "referralCodeCopied": "Código de referido copiado", "today": "Hoy", "referralCode": "Código de referencia", "getReferralCode": "Obtener el código de referido", "errorTitle": "No se pueden cargar los puntos", "errorDesc": "Hubo un error al cargar tus puntos. Por favor, inténtalo de nuevo.", "shareOn": "Compartir en", "pointsDashboardBtn": "Comienza a ganar puntos"}}, "dbk": {"home": {"mintNFTDesc": "Sé testigo de DBK Chain", "mintNFTBtn": "<PERSON><PERSON><PERSON><PERSON>", "mintNFT": "Acuñar DBK Genesis NFT", "bridgeBtn": "Bridge", "bridge": "Puente a DBK Chain", "bridgePoweredBy": "Impulsado por OP Superchain"}, "bridge": {"tabs": {"withdraw": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"completeTime": "Tiempo de finalización", "toAddress": "Para abordar", "receiveOn": "Recibir en {{chainName}}", "gasFee": "Gas fee"}, "error": {"notEnoughBalance": "<PERSON><PERSON> insuficiente"}, "ActivityPopup": {"status": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "rootPublished": "Estado raíz publicado", "readyToProve": "Listo para demostrar", "withdraw": "<PERSON><PERSON><PERSON>", "proved": "Probado", "readyToClaim": "Listo para reclamar", "claimed": "Reclamado", "challengePeriod": "<PERSON><PERSON><PERSON> de challenge", "waitingToProve": "Estado root publicado"}, "title": "Actividades", "proveBtn": "Probar", "claimBtn": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "empty": "Aún no hay actividades"}, "WithdrawConfirmPopup": {"title": "La retirada de DBK Chain tarda ~7 días", "step3": "Reclamar en Ethereum", "step1": "Iniciar retiro", "step2": "Prove on Ethereum", "question1": "Entiendo que tardará aproximadamente 7 días hasta que mis fondos se puedan reclamar en Ethereum después de que demuestre mi retiro", "question3": "Entiendo que las tarifas de red son aproximadas y cambiarán", "tips": "Retirar implica un proceso de 3 pasos, que requiere 1 transacción de DBK Chain y 2 transacciones de Ethereum", "question2": "Entiendo que una vez que se inicia un retiro, no puede acelerarse ni cancelarse", "btn": "<PERSON><PERSON><PERSON>"}, "labelTo": "A", "labelFrom": "<PERSON><PERSON>"}, "minNFT": {"myBalance": "Mi Balance", "minted": "Minted", "mintBtn": "<PERSON><PERSON><PERSON><PERSON>", "title": "DBK Genesis"}}}, "miniSignFooterBar": {"status": {"txSending": "<PERSON><PERSON><PERSON> solicitud de firma", "txSigned": "Firmado. Creando transacción", "txCreated": "Transacción creada", "txSendings": "<PERSON><PERSON><PERSON> solicitud de firma ({{current}}/{{total}})"}, "signWithLedger": "Firmar con Ledger"}, "gasAccount": {"history": {"noHistory": "Sin historial"}, "loginInTip": {"login": "Iniciar sesi<PERSON> en GasAccount", "title": "Depósito USDC / USDT", "desc": "Paga tarifas de gas en todas las cadenas", "gotIt": "Entendido"}, "loginConfirmModal": {"title": "Iniciar sesión con la dirección actual", "desc": "Una vez confirmado, solo puedes depositarlo en esta dirección"}, "logoutConfirmModal": {"title": "Cerrar sesi<PERSON> en la GasAccount actual", "logout": "<PERSON><PERSON><PERSON>", "desc": "Cerrar sesión desactiva GasAccount. Puedes restaurar tu GasAccount iniciando sesión con esta dirección."}, "depositPopup": {"amount": "Cantidad", "selectToken": "Seleccione Token para Depositar", "title": "<PERSON><PERSON><PERSON><PERSON>", "invalidAmount": "Debe ser menos de 500", "desc": "Haz un depósito en la cuenta L2 de DeBank de Rabby sin tarifas adicionales: retira en cualquier momento.", "token": "Token"}, "withdrawPopup": {"noEnoughValuetBalance": "<PERSON><PERSON> insuficiente. Cambia de cadena o inténtalo de nuevo más tarde.", "noEligibleAddr": "Ninguna dirección elegible para retiro", "recipientAddress": "Dirección del destinatario", "title": "<PERSON><PERSON><PERSON>", "withdrawalLimit": "Límite de retiro", "noEligibleChain": "No hay cadena elegible para retiro", "selectRecipientAddress": "Seleccionar dirección del destinatario", "selectDestinationChain": "Seleccione la cadena de destino", "noEnoughGas": "Cantidad demasiado baja para cubrir las tarifas de gas", "selectChain": "Seleccionar cadena", "to": "Para", "selectAddr": "Seleccionar <PERSON>", "amount": "Cantidad", "destinationChain": "Cadena de destino", "deductGasFees": "El monto recibido deducirá las tarifas de gas", "riskMessageFromAddress": "Debido al control de riesgos, el límite de retiro depende del monto total que esta dirección ha depositado.", "riskMessageFromChain": "Debido al control de riesgos, el límite de retiro depende del monto total depositado desde esta cadena.", "desc": "Puede retirar el saldo de su GasAccount a su billetera DeBank L2. Inicie sesión en su billetera DeBank L2 para transferir los fondos a una blockchain compatible según sea necesario."}, "withdrawConfirmModal": {"button": "Ver en DeBank", "title": "Transferido a tu billetera DeBank L2"}, "GasAccountDepositTipPopup": {"title": "Abrir GasAccount y Depositar", "gotIt": "Entendido"}, "switchLoginAddressBeforeDeposit": {"desc": "Por favor, cambia a tu dirección de inicio de sesión.", "title": "Cambiar dirección antes del depósito"}, "noBalance": "Sin saldo", "safeAddressDepositTips": "No se admiten direcciones Multisig para depósitos.", "logout": "Cerrar sesi<PERSON> en la GasAccount actual", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "title": "GasAccount", "risk": "Se ha detectado que su dirección actual es de riesgo, por lo que esta función no está disponible.", "gasExceed": "El saldo de GasAccount no puede exceder $1000", "withdraw": "<PERSON><PERSON><PERSON>", "gasAccountList": {"address": "Dirección", "gasAccountBalance": "Gas Balance"}, "switchAccount": "Cambiar GasAccount", "withdrawDisabledIAP": "Los retiros están deshabilitados porque tu saldo incluye fondos fiduciarios, los cuales no pueden ser retirados. Contacta con el soporte para retirar el saldo de tus tokens."}, "safeMessageQueue": {"noData": "<PERSON> mensajes", "loading": "<PERSON><PERSON><PERSON> men<PERSON>"}, "newUserImport": {"guide": {"createNewAddress": "Crea una nueva dirección", "importAddress": "Ya tengo una dirección", "desc": "La billetera revolucionaria para Ethereum y todas las cadenas EVM", "title": "Bienvenido a Rabby Wallet"}, "createNewAddress": {"showSeedPhrase": "<PERSON><PERSON> Seed Phrase", "title": "<PERSON><PERSON> em<PERSON>zar", "desc": "Por favor, lee y ten en cuenta los siguientes consejos de seguridad.", "tip2": "Mi frase semilla se almacena únicamente en mi dispositivo. Rabby no puede acceder a ella.", "tip1": "Si pierdo o comparto mi frase semilla, perderé el acceso a mis activos de forma permanente.", "tip3": "Si desinstalo Rabby sin hacer una copia de seguridad de mi frase semilla, no se puede recuperar mediante <PERSON>."}, "importList": {"title": "Seleccionar método de importación"}, "importPrivateKey": {"title": "Importar Private Key", "pasteCleared": "Pegado y portapapeles borrado"}, "PasswordCard": {"form": {"password": {"label": "Contraseña", "min": "La contraseña debe tener al menos 8 caracteres de longitud", "required": "Por favor, ingresa la contraseña", "placeholder": "Contraseña (mínimo 8 caracteres)"}, "confirmPassword": {"label": "Confirmar Password", "notMatch": "Las contraseñas no coinciden", "required": "Por favor, confirma la contraseña", "placeholder": "Confirmar con<PERSON>"}}, "agree": "Estoy de acuerdo con los<1/> <2>Términos de uso</2> y la <4>Política de privacidad</4>", "title": "<PERSON><PERSON><PERSON> contras<PERSON>", "desc": "Se utilizará para desbloquear la wallet y cifrar los datos"}, "successful": {"start": "Comenzar", "import": "Importado correctamente", "create": "Creado con éxito", "addMoreAddr": "Agregar más direcciones desde esta Seed Phrase", "addMoreFrom": "Agregar más direcciones de {{name}}"}, "readyToUse": {"pin": "<PERSON><PERSON>", "guides": {"step2": "<PERSON><PERSON><PERSON>", "step1": "Haz clic en el ícono de la extensión del navegador."}, "desc": "Encuentra Rabby <PERSON> y fíjalo", "extensionTip": "Haz clic en <1/> y luego en <3/>", "title": "¡Tu billetera Rabby está lista!"}, "importSeedPhrase": {"title": "Importar Seed Phrase"}, "importOneKey": {"title": "OneKey", "connect": "Conectar OneKey", "tip3": "3. Desbloquea tu dispositivo", "tip1": "1. <PERSON><PERSON><PERSON> <1>OneKey Bridge<1/>", "tip2": "2. Conecta tu dispositivo OneKey"}, "importTrezor": {"connect": "Conectar <PERSON>", "tip2": "2. Desbloquee su dispositivo", "title": "<PERSON><PERSON><PERSON>", "tip1": "1. Conecta tu dispositivo Trezor"}, "ImportGridPlus": {"title": "GridPlus", "connect": "Conectar GridPlus", "tip1": "1. Abre tu dispositivo GridPlus", "tip2": "2. Conectar a través de Lattice Connector"}, "importLedger": {"connect": "Conectar Ledger", "tip2": "Introduce tu PIN para desbloquear.", "title": "Ledger", "tip3": "Abre la aplicación de Ethereum.", "tip1": "Conecta tu dispositivo Ledger."}, "importBitBox02": {"title": "BitBox02", "tip3": "3. Desbloquea tu dispositivo", "connect": "Conectar BitBox02", "tip1": "1. Instale el <1>BitBoxBridge<1/>", "tip2": "2. Conecta tu BitBox02"}, "importKeystone": {"qrcode": {"desc": "Escanee el código QR en el monedero de hardware Keystone"}, "usb": {"connect": "Conectar Keystone", "tip1": "Conecte su dispositivo Keystone", "tip3": "Aprobar la conexión a su computadora", "tip2": "Ingrese su contraseña para desbloquear", "desc": "Asegúrate de que tu Keystone 3 Pro esté en la página de inicio"}}, "importSafe": {"error": {"required": "Por favor, ingrese la dirección", "invalid": "No es una dirección válida"}, "title": "Agregar dirección Safe", "loading": "Buscando la cadena desplegada de esta dirección", "placeholder": "Introduzca la dirección segura"}}, "metamaskModeDapps": {"title": "Administrar <PERSON>idas", "desc": "Modo MetaMask habilitado para las siguientes Dapps. Puedes conectar Rabby seleccionando la opción de MetaMask."}, "forgotPassword": {"home": {"title": "O<PERSON>vidó la contraseña", "buttonNoData": "<PERSON><PERSON><PERSON>", "descriptionNoData": "<PERSON>bby <PERSON> no almacena tu contraseña y no puede ayudarte a recuperarla. Establece una nueva contraseña si la olvidaste.", "button": "Iniciar proceso de restablecimiento", "description": "Rabby <PERSON>et no almacena su contraseña y no puede ayudarle a recuperarla. Restablezca su wallet para configurar una nueva."}, "reset": {"alert": {"title": "Los datos serán eliminados y no se podrán recuperar:", "seed": "<PERSON><PERSON> de <PERSON>", "privateKey": "Clave Privada"}, "tip": {"records": "Registros de Signature", "watch": "Contactos y Direcciones Solo de Lectura", "title": "Los datos se mantendrán:", "whitelist": "Configuración de Whitelist", "hardware": "Carteras de Hardware Importadas", "safe": "Carteras de seguridad importadas"}, "button": "Confirmar rest<PERSON><PERSON><PERSON><PERSON>", "title": "Restablecer <PERSON>", "confirm": "Escriba <1>RESET</1> en el cuadro para confirmar y continuar"}, "tip": {"button": "<PERSON><PERSON><PERSON>", "descriptionNoData": "Agregue su dirección para comenzar", "title": "<PERSON><PERSON> Reset <PERSON>o", "description": "Cree una nueva contraseña para continuar", "buttonNoData": "Agregar <PERSON>"}, "success": {"description": "Ya estás listo para usar <PERSON><PERSON>", "button": "<PERSON><PERSON>", "title": "Contraseña establecida con éxito"}}, "eip7702": {"alert": "EIP-7702 aún no es compatible"}, "metamaskModeDappsGuide": {"toast": {"disabled": "Disguise desactivado. Actualiza el Dapp.", "enabled": "Disfraz habilitado. Actualiza la Dapp para reconectar."}, "step2Desc": "Actualizar y conectar a través de MetaMask", "noDappFound": "No Dapp encontrado", "manage": "Gestionar Dapps Permitidas", "title": "Conectar Rabby disfrazándose de MetaMask", "step1Desc": "<PERSON><PERSON><PERSON> que <PERSON> se disfrace de MetaMask en el Dapp actual", "step1": "Paso 1", "alert": "¿No puedes conectarte a una Dapp porque no muestra Rabby Wallet como una opción?", "step2": "Paso 2"}, "syncToMobile": {"title": "Sincronizar la dirección de Wallet desde la extensión de Rabby al móvil", "steps2": "2. <PERSON><PERSON><PERSON> con <PERSON><PERSON>", "downloadGooglePlay": "Google Play", "description": "Tus datos de dirección permanecen totalmente fuera de línea, cifrados y se transfieren de manera segura a través de un código QR.", "clickToShowQr": "Haga clic para seleccionar la dirección y mostrar el código QR", "steps1": "1. <PERSON><PERSON>ga Rabby Mobile", "downloadAppleStore": "App Store", "steps2Description": "*Tu código QR contiene datos sensibles. Manténlo privado y nunca lo compartas con nadie.", "disableSelectAddress": "Sincronización no es compatible para la dirección {{type}}", "disableSelectAddressWithPassphrase": "Sincronización no es compatible con la dirección {{type}} con frase", "disableSelectAddressWithSlip39": "Sincronización no es compatible con la dirección {{type}} con slip39", "selectedLenAddressesForSync_one": "Dirección seleccionada {{len}} para sincronización", "selectedLenAddressesForSync_other": "Direcciones seleccionadas {{len}} para sincronización", "selectAddress": {"title": "Seleccionar direcciones para sincronizar"}}, "search": {"sectionHeader": {"token": "Tokens", "NFT": "NFT", "AllChains": "Todas las cadenas", "Defi": "<PERSON><PERSON><PERSON>"}, "header": {"placeHolder": "Buscar", "searchPlaceHolder": "Buscar nombre de token / dirección"}, "tokenItem": {"Issuedby": "Emitido por", "verifyDangerTips": "Este es un token de estafa", "scamWarningTips": "Este es un token de baja calidad y puede ser una estafa.", "FDV": "FDV", "listBy": "Listar por {{name}}", "gasToken": "Token de Gas"}, "searchWeb": {"noResults": "Sin resultados", "title": "Todos los resultados", "searching": "Resultados para", "searchTips": "Buscar en la web", "noResult": "Sin resultados para"}}}, "component": {"AccountSearchInput": {"noMatchAddress": "No hay coincidencia de dirección", "AddressItem": {"whitelistedAddressTip": "Dirección en lista blanca"}}, "AccountSelectDrawer": {"btn": {"cancel": "<PERSON><PERSON><PERSON>", "proceed": "<PERSON><PERSON><PERSON><PERSON>"}}, "AddressList": {"AddressItem": {"addressTypeTip": "Importada por {{type}}"}}, "AuthenticationModal": {"passwordError": "Contrase<PERSON>", "passwordRequired": "Por favor ingresa la contraseña", "passwordPlaceholder": "Ingresa la contraseña para confirmar"}, "ConnectStatus": {"connecting": "Conectando...", "connect": "Conectar", "gridPlusConnected": "GridPlus está conectado", "gridPlusNotConnected": "GridPlus no está conectado", "ledgerNotConnected": "Ledger no está conectado", "ledgerConnected": "Ledger está conectado", "imKeyConnected": "imKey está conectado", "keystoneNotConnected": "Keystone no está conectado", "imKeyrNotConnected": "imKey no está conectado", "keystoneConnected": "Keystone está conectado"}, "Contact": {"AddressItem": {"notWhitelisted": "Esta dirección no está en lista blanca", "whitelistedTip": "Dirección en lista blanca"}, "EditModal": {"title": "Editar nota de la dirección"}, "EditWhitelist": {"backModalTitle": "Descartar cambios", "backModalContent": "Los cambios que realizaste no se guardarán", "title": "<PERSON>ar <PERSON>", "tip": "Selecciona la dirección que deseas poner en lista blanca y guarda los cambios.", "save": "Guardar en Lista Blanca ({{count}})"}, "ListModal": {"title": "Se<PERSON><PERSON><PERSON><PERSON>", "whitelistEnabled": "La lista blanca está habilitada. Solo puedes enviar activos a una dirección en lista blanca o puedes deshabilitarla en \"Configuración\"", "whitelistDisabled": "La lista blanca está deshabilitada. Puedes enviar activos a cualquier dirección", "editWhitelist": "<PERSON>ar <PERSON>", "whitelistUpdated": "Lista Blanca Actualizada", "authModal": {"title": "Guardar en Lista Blanca"}}}, "LoadingOverlay": {"loadingData": "Cargando datos..."}, "MultiSelectAddressList": {"imported": "Importada"}, "NFTNumberInput": {"erc1155Tips": "Tu saldo es {{amount}}", "erc721Tips": "Solo se puede enviar un NFT de ERC 721 a la vez"}, "TiledSelect": {"errMsg": "El orden de las frases mnemónicas es incorrecto, por favor verifica"}, "Uploader": {"placeholder": "Seleccionar un archivo JSON"}, "WalletConnectBridgeModal": {"title": "URL del servidor del puente", "requiredMsg": "Por favor ingresa el host del servidor del puente", "invalidMsg": "Por favor verifica tu host", "restore": "Restaurar configuración inicial"}, "PillsSwitch": {"NetSwitchTabs": {"mainnet": "Red de Integración", "testnet": "Red de Custom"}}, "ChainSelectorModal": {"searchPlaceholder": "Buscar cadena", "noChains": "No hay cadenas", "addTestnet": "Agregar red personalizada"}, "TokenSelector": {"listTableHead": {"assetAmount": {"title": "ACTIVO / CANTIDAD"}, "price": {"title": "PRECIO"}, "usdValue": {"title": "VALOR USD"}}, "searchInput": {"placeholder": "Buscar por Nombre / Dirección"}, "header": {"title": "Seleccionar un token"}, "noTokens": "Sin <PERSON>", "noMatch": "Sin Coincidencias", "noMatchSuggestion": "Intenta buscar la dirección del contrato en {{ chainName }}", "bridge": {"value": "Valor", "liquidity": "Liquidez", "high": "Alto", "low": "<PERSON><PERSON>", "token": "Token", "liquidityTips": "Cuanto mayor sea el volumen de comercio histó<PERSON>, más probable es que el puente tenga éxito."}, "recent": "Reciente", "common": "Común", "hot": "Caliente", "chainNotSupport": "Esta cadena no es compatible"}, "ModalPreviewNFTItem": {"FieldLabel": {"Collection": "Colección", "Chain": "Cadena", "PurschaseDate": "<PERSON><PERSON>", "LastPrice": "<PERSON><PERSON><PERSON>"}}, "signPermissionCheckModal": {"title": "Solo permites que esta Dapp firme en testnets", "reconnect": "Reconectar Dapp"}, "testnetCheckModal": {"title": "Por favor, activa \"Habilitar Testnets\" en \"Más\" antes de firmar en testnets"}, "EcologyNavBar": {"providedBy": "Proporcionado por {{chainName}}"}, "EcologyNoticeModal": {"notRemind": "No me lo recuerdes de nuevo", "title": "Aviso", "desc": "Los siguientes servicios serán proporcionados directamente por el Socio del Ecosistema de terceros. <PERSON><PERSON> no asume responsabilidad por la seguridad de estos servicios."}, "ReserveGasPopup": {"title": "Reservar Gas", "instant": "Instant", "normal": "Normal", "fast": "<PERSON><PERSON><PERSON><PERSON>", "doNotReserve": "No reserves de Gas"}, "OpenExternalWebsiteModal": {"button": "<PERSON><PERSON><PERSON><PERSON>", "title": "Est<PERSON> saliendo de <PERSON>", "content": "Está a punto de visitar un sitio web externo. Ra<PERSON> no es responsable del contenido o la seguridad de este sitio."}, "TokenChart": {"price": "Precio", "holding": "Sosteniendo Valor"}, "externalSwapBrideDappPopup": {"selectADapp": "Selecciona un Dapp", "noQuotesForChain": "No hay cotizaciones disponibles para esta cadena aún.", "thirdPartyDappToProceed": "Por favor, utiliza un Dapp de terceros para continuar.", "help": "Por favor, contacta al equipo oficial de esta cadena para obtener soporte.", "noDapp": "No hay Dapps disponibles", "viewDappOptions": "Ver opciones de Dapp", "chainNotSupported": "No soportado en esta cadena", "noDapps": "No hay Dapp disponible en esta cadena", "bridgeOnDapp": "Puente en Dapp", "swapOnDapp": "Intercambiar en Dapp\n"}, "AccountSelectorModal": {"searchPlaceholder": "Buscar dirección", "title": "Seleccionar <PERSON>"}}, "global": {"appName": "<PERSON><PERSON>", "appDescription": "La billetera revolucionaria para Ethereum y todas las cadenas EVM", "copied": "Copiado", "confirm": "Confirmar", "next": "Siguient<PERSON>", "back": "Atrás", "ok": "Aceptar", "refresh": "Actualizar", "failed": "<PERSON><PERSON>", "scamTx": "Tx fraudulenta", "gas": "Gas", "unknownNFT": "NFT desconocido", "copyAddress": "<PERSON><PERSON><PERSON>", "watchModeAddress": "Dirección en modo observación", "assets": "activos", "Confirm": "Confirmar", "Cancel": "<PERSON><PERSON><PERSON>", "Clear": "Limpiar", "Save": "Guardar", "confirmButton": "Confirmar", "cancelButton": "<PERSON><PERSON><PERSON>", "backButton": "Atrás", "proceedButton": "<PERSON><PERSON><PERSON><PERSON>", "editButton": "<PERSON><PERSON>", "addButton": "Agregar", "closeButton": "<PERSON><PERSON><PERSON>", "Deleted": "Eliminado", "Loading": "Cargando", "nonce": "nonce", "Balance": "<PERSON><PERSON>", "Done": "<PERSON><PERSON>", "notSupportTesntnet": "No compatible con red personalizada", "tryAgain": "Inténtalo de nuevo", "Nonce": "<PERSON><PERSON>"}, "background": {"error": {"noCurrentAccount": "No hay cuenta actual", "invalidChainId": "ID de cadena no válido", "notFindChain": "No se puede encontrar la cadena {{chain}}", "unknownAbi": "ABI del contrato desconocido", "invalidAddress": "No es una dirección válida", "notFoundGnosisKeyring": "No se encontró el llavero de Gnosis", "notFoundTxGnosisKeyring": "No se encontraron transacciones en el llavero de Gnosis", "addKeyring404": "Error al agregar el llavero, el llavero no está definido", "emptyAccount": "La cuenta actual está vacía", "generateCacheAliasNames": "[GenerateCacheAliasNames]: se necesita al menos una dirección", "invalidPrivateKey": "La clave privada no es válida", "invalidJson": "El archivo de entrada no es válido", "invalidMnemonic": "La frase semilla no es válida, ¡por favor verifica!", "notFoundKeyringByAddress": "No se puede encontrar el llavero por dirección", "txPushFailed": "Fallo al enviar la transacción", "unlock": "Debes desbloquear la billetera primero", "duplicateAccount": "La cuenta que estás intentando importar ya existe", "canNotUnlock": "No se puede desbloquear sin un almacén de seguridad anterior"}, "transactionWatcher": {"submitted": "Transacción enviada", "more": "haz clic para ver más información", "completed": "Transacción completada", "failed": "La transacción falló", "txCompleteMoreContent": "{{chain}} #{{nonce}} completado. Haz clic para ver más.", "txFailedMoreContent": "{{chain}} #{{nonce}} falló. Haz clic para ver más."}, "alias": {"HdKeyring": "<PERSON><PERSON>lla", "simpleKeyring": "Clave privada", "watchAddressKeyring": "Contacto"}}, "constant": {"KEYRING_TYPE_TEXT": {"HdKeyring": "<PERSON><PERSON><PERSON> por <PERSON>", "SimpleKeyring": "Importado por Clave Privada", "WatchAddressKeyring": "Contacto"}, "IMPORTED_HD_KEYRING": "Importado por Fr<PERSON>", "SIGN_PERMISSION_OPTIONS": {"MAINNET_AND_TESTNET": "Mainnet y Testnet", "TESTNET": "Solo Testnets"}, "IMPORTED_HD_KEYRING_NEED_PASSPHRASE": "<PERSON>mportado por Seed Phrase (Passphrase)"}}