/*!
 * Matomo - free/libre analytics platform
 *
 * JavaScript tracking client
 *
 * @link https://piwik.org
 * @source https://github.com/matomo-org/matomo/blob/master/js/piwik.js
 * @license https://piwik.org/free-software/bsd/ BSD-3 Clause (also in js/LICENSE.txt)
 * @license magnet:?xt=urn:btih:c80d50af7d3db9be66a4d0a86db0286e4fd33292&dn=bsd-3-clause.txt BSD-3-Clause
 */
"object"!=typeof _paq&&(_paq=[]),"object"!=typeof window.Matomo&&(window.Matomo=window.Piwik=function(){"use strict";var expireDateTime,plugins={},eventHandlers={},documentAlias=document,navigatorAlias=navigator,screenAlias=screen,windowAlias=window,performanceAlias=windowAlias.performance||windowAlias.mozPerformance||windowAlias.msPerformance||windowAlias.webkitPerformance,encodeWrapper=windowAlias.encodeURIComponent,decodeWrapper=windowAlias.decodeURIComponent,urldecode=unescape,asyncTrackers=[],iterator,Matomo,missedPluginTrackerCalls=[],coreConsentCounter=0,coreHeartBeatCounter=0,trackerIdCounter=0,isPageUnloading=!1;function safeDecodeWrapper(e){try{return decodeWrapper(e)}catch(t){return unescape(e)}}function isDefined(e){return"undefined"!==typeof e}function isFunction(e){return"function"==typeof e}function isObject(e){return"object"==typeof e}function isString(e){return"string"==typeof e||e instanceof String}function isNumber(e){return"number"==typeof e||e instanceof Number}function isNumberOrHasLength(e){return isDefined(e)&&(isNumber(e)||isString(e)&&e.length)}function isObjectEmpty(e){if(!e)return!0;var t;for(t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}function logConsoleError(e){"undefined"!==typeof console&&console&&console.error&&console.error(e)}function apply(){var e,t,n,i,r;for(e=0;e<arguments.length;e+=1){var o,a;if(r=null,arguments[e]&&arguments[e].slice&&(r=arguments[e].slice()),isString(n=(i=arguments[e]).shift())&&n.indexOf("::")>0)a=(o=n.split("::"))[0],n=o[1],"object"==typeof Matomo[a]&&"function"==typeof Matomo[a][n]?Matomo[a][n].apply(Matomo[a],i):r&&missedPluginTrackerCalls.push(r);else for(t=0;t<asyncTrackers.length;t++)if(isString(n)){a=asyncTrackers[t];var s=n.indexOf(".")>0;if(s)if(o=n.split("."),a&&"object"==typeof a[o[0]])a=a[o[0]],n=o[1];else if(r){missedPluginTrackerCalls.push(r);break}if(a[n])a[n].apply(a,i);else{var c="The method '"+n+'\' was not found in "_paq" variable.  Please have a look at the Matomo tracker documentation: https://developer.matomo.org/api-reference/tracking-javascript';if(logConsoleError(c),!s)throw new TypeError(c)}if("addTracker"===n)break;if("setTrackerUrl"===n||"setSiteId"===n)break}else n.apply(asyncTrackers[t],i)}}function addEventListener(e,t,n,i){return e.addEventListener?(e.addEventListener(t,n,i),!0):e.attachEvent?e.attachEvent("on"+t,n):void(e["on"+t]=n)}function trackCallbackOnLoad(e){"complete"===documentAlias.readyState?e():windowAlias.addEventListener?windowAlias.addEventListener("load",e,!1):windowAlias.attachEvent&&windowAlias.attachEvent("onload",e)}function trackCallbackOnReady(e){var t=!1;(t=documentAlias.attachEvent?"complete"===documentAlias.readyState:"loading"!==documentAlias.readyState)?e():(documentAlias.addEventListener?addEventListener(documentAlias,"DOMContentLoaded",function n(){documentAlias.removeEventListener("DOMContentLoaded",n,!1),t||(t=!0,e())}):documentAlias.attachEvent&&(documentAlias.attachEvent("onreadystatechange",function n(){"complete"===documentAlias.readyState&&(documentAlias.detachEvent("onreadystatechange",n),t||(t=!0,e()))}),documentAlias.documentElement.doScroll&&windowAlias===windowAlias.top&&function n(){if(!t){try{documentAlias.documentElement.doScroll("left")}catch(e){return void setTimeout(n,0)}t=!0,e()}}()),addEventListener(windowAlias,"load",function(){t||(t=!0,e())},!1))}function executePluginMethod(e,t,n){if(!e)return"";var i,r,o="";for(i in plugins)Object.prototype.hasOwnProperty.call(plugins,i)&&(plugins[i]&&"function"==typeof plugins[i][e])&&(r=(0,plugins[i][e])(t||{},n))&&(o+=r);return o}function beforeUnloadHandler(e){var t;isPageUnloading=!0,executePluginMethod("unload");var n=(t=new Date).getTimeAlias();if(expireDateTime-n>3e3&&(expireDateTime=n+3e3),expireDateTime)do{t=new Date}while(t.getTimeAlias()<expireDateTime)}function loadScript(e,t){var n=documentAlias.createElement("script");n.type="text/javascript",n.src=e,n.readyState?n.onreadystatechange=function(){var e=this.readyState;"loaded"!==e&&"complete"!==e||(n.onreadystatechange=null,t())}:n.onload=t,documentAlias.getElementsByTagName("head")[0].appendChild(n)}function getReferrer(){var e="";try{e=windowAlias.top.document.referrer}catch(t){if(windowAlias.parent)try{e=windowAlias.parent.document.referrer}catch(t){e=""}}return""===e&&(e=documentAlias.referrer),e}function getProtocolScheme(e){var t=new RegExp("^([a-z]+):").exec(e);return t?t[1]:null}function getHostName(e){var t=new RegExp("^(?:(?:https?|ftp):)/*(?:[^@]+@)?([^:/#]+)").exec(e);return t?t[1]:e}function isPositiveNumberString(e){return/^[0-9][0-9]*(\.[0-9]+)?$/.test(e)}function filterIn(e,t){var n,i={};for(n in e)e.hasOwnProperty(n)&&t(e[n])&&(i[n]=e[n]);return i}function onlyPositiveIntegers(e){var t,n={};for(t in e)if(e.hasOwnProperty(t)){if(!isPositiveNumberString(e[t]))throw new Error('Parameter "'+t+'" provided value "'+e[t]+'" is not valid. Please provide a numeric value.');n[t]=Math.round(e[t])}return n}function queryStringify(e){var t,n="";for(t in e)e.hasOwnProperty(t)&&(n+="&"+encodeWrapper(t)+"="+encodeWrapper(e[t]));return n}function stringStartsWith(e,t){return 0===(e=String(e)).lastIndexOf(t,0)}function stringEndsWith(e,t){return-1!==(e=String(e)).indexOf(t,e.length-t.length)}function stringContains(e,t){return-1!==(e=String(e)).indexOf(t)}function removeCharactersFromEndOfString(e,t){return(e=String(e)).substr(0,e.length-t)}function addUrlParameter(e,t,n){n||(n="");var i=(e=String(e)).indexOf("#"),r=e.length;-1===i&&(i=r);var o=e.substr(0,i),a=e.substr(i,r-i);return-1===o.indexOf("?")?o+="?":stringEndsWith(o,"?")||(o+="&"),o+encodeWrapper(t)+"="+encodeWrapper(n)+a}function removeUrlParameter(e,t){if(-1===(e=String(e)).indexOf("?"+t+"=")&&-1===e.indexOf("&"+t+"="))return e;var n=e.indexOf("?");if(-1===n)return e;var i=e.substr(n+1),r=e.substr(0,n);if(i){var o="",a=i.indexOf("#");-1!==a&&(o=i.substr(a+1),i=i.substr(0,a));for(var s=i.split("&"),c=s.length-1;c>=0;c--)s[c].split("=")[0]===t&&s.splice(c,1);var u=s.join("&");u&&(r=r+"?"+u),o&&(r+="#"+o)}return r}function getUrlParameter(e,t){var n=new RegExp("[\\?&#]"+t+"=([^&#]*)").exec(e);return n?safeDecodeWrapper(n[1]):""}function trim(e){return e&&String(e)===e?e.replace(/^\s+|\s+$/g,""):e}function utf8_encode(e){return unescape(encodeWrapper(e))}function sha1(e){var t,n,i,r,o,a,s,c,u,l,f=function(e,t){return e<<t|e>>>32-t},d=function(e){var t,n="";for(t=7;t>=0;t--)n+=(e>>>4*t&15).toString(16);return n},g=[],m=1732584193,h=4023233417,p=2562383102,C=271733878,k=3285377520,T=[];for(l=(e=utf8_encode(e)).length,n=0;n<l-3;n+=4)i=e.charCodeAt(n)<<24|e.charCodeAt(n+1)<<16|e.charCodeAt(n+2)<<8|e.charCodeAt(n+3),T.push(i);switch(3&l){case 0:n=2147483648;break;case 1:n=e.charCodeAt(l-1)<<24|8388608;break;case 2:n=e.charCodeAt(l-2)<<24|e.charCodeAt(l-1)<<16|32768;break;case 3:n=e.charCodeAt(l-3)<<24|e.charCodeAt(l-2)<<16|e.charCodeAt(l-1)<<8|128}for(T.push(n);14!=(15&T.length);)T.push(0);for(T.push(l>>>29),T.push(l<<3&4294967295),t=0;t<T.length;t+=16){for(n=0;n<16;n++)g[n]=T[t+n];for(n=16;n<=79;n++)g[n]=f(g[n-3]^g[n-8]^g[n-14]^g[n-16],1);for(r=m,o=h,a=p,s=C,c=k,n=0;n<=19;n++)u=f(r,5)+(o&a|~o&s)+c+g[n]+1518500249&4294967295,c=s,s=a,a=f(o,30),o=r,r=u;for(n=20;n<=39;n++)u=f(r,5)+(o^a^s)+c+g[n]+1859775393&4294967295,c=s,s=a,a=f(o,30),o=r,r=u;for(n=40;n<=59;n++)u=f(r,5)+(o&a|o&s|a&s)+c+g[n]+2400959708&4294967295,c=s,s=a,a=f(o,30),o=r,r=u;for(n=60;n<=79;n++)u=f(r,5)+(o^a^s)+c+g[n]+3395469782&4294967295,c=s,s=a,a=f(o,30),o=r,r=u;m=m+r&4294967295,h=h+o&4294967295,p=p+a&4294967295,C=C+s&4294967295,k=k+c&4294967295}return(u=d(m)+d(h)+d(p)+d(C)+d(k)).toLowerCase()}function urlFixup(e,t,n){return e||(e=""),t||(t=""),"translate.googleusercontent.com"===e?(""===n&&(n=t),e=getHostName(t=getUrlParameter(t,"u"))):"cc.bingj.com"!==e&&"webcache.googleusercontent.com"!==e&&"74.6."!==e.slice(0,5)||(e=getHostName(t=documentAlias.links[0].href)),[e,t,n]}function domainFixup(e){var t=e.length;return"."===e.charAt(--t)&&(e=e.slice(0,t)),"*."===e.slice(0,2)&&(e=e.slice(1)),-1!==e.indexOf("/")&&(e=e.substr(0,e.indexOf("/"))),e}function titleFixup(e){if(!isString(e=e&&e.text?e.text:e)){var t=documentAlias.getElementsByTagName("title");t&&isDefined(t[0])&&(e=t[0].text)}return e}function getChildrenFromNode(e){return e?!isDefined(e.children)&&isDefined(e.childNodes)?e.children:isDefined(e.children)?e.children:[]:[]}function containsNodeElement(e,t){return!(!e||!t)&&(e.contains?e.contains(t):e===t||!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t)))}function indexOfArray(e,t){if(e&&e.indexOf)return e.indexOf(t);if(!isDefined(e)||null===e)return-1;if(!e.length)return-1;var n=e.length;if(0===n)return-1;for(var i=0;i<n;){if(e[i]===t)return i;i++}return-1}function isVisible(e){if(!e)return!1;function t(e,t){return windowAlias.getComputedStyle?documentAlias.defaultView.getComputedStyle(e,null)[t]:e.currentStyle?e.currentStyle[t]:void 0}return function n(i,r,o,a,s,c,u){var l=i.parentNode;return!!function(e){for(e=e.parentNode;e;){if(e===documentAlias)return!0;e=e.parentNode}return!1}(i)&&(9===l.nodeType||"0"!==t(i,"opacity")&&"none"!==t(i,"display")&&"hidden"!==t(i,"visibility")&&(isDefined(r)&&isDefined(o)&&isDefined(a)&&isDefined(s)&&isDefined(c)&&isDefined(u)||(r=i.offsetTop,s=i.offsetLeft,a=r+i.offsetHeight,o=s+i.offsetWidth,c=i.offsetWidth,u=i.offsetHeight),(e!==i||0!==u&&0!==c||"hidden"!==t(i,"overflow"))&&(!l||("hidden"!==t(l,"overflow")&&"scroll"!==t(l,"overflow")||!(s+1>l.offsetWidth+l.scrollLeft||s+c-1<l.scrollLeft||r+1>l.offsetHeight+l.scrollTop||r+u-1<l.scrollTop))&&(i.offsetParent===l&&(s+=l.offsetLeft,r+=l.offsetTop),n(l,r,o,a,s,c,u)))))}(e)}var query={htmlCollectionToArray:function(e){var t,n=[];if(!e||!e.length)return n;for(t=0;t<e.length;t++)n.push(e[t]);return n},find:function(e){if(!document.querySelectorAll||!e)return[];var t=document.querySelectorAll(e);return this.htmlCollectionToArray(t)},findMultiple:function(e){if(!e||!e.length)return[];var t,n,i=[];for(t=0;t<e.length;t++)n=this.find(e[t]),i=i.concat(n);return i=this.makeNodesUnique(i)},findNodesByTagName:function(e,t){if(!e||!t||!e.getElementsByTagName)return[];var n=e.getElementsByTagName(t);return this.htmlCollectionToArray(n)},makeNodesUnique:function(e){var t=[].concat(e);if(e.sort(function(e,n){if(e===n)return 0;var i=indexOfArray(t,e),r=indexOfArray(t,n);return i===r?0:i>r?-1:1}),e.length<=1)return e;var n,i=0,r=0,o=[];for(n=e[i++];n;)n===e[i]&&(r=o.push(i)),n=e[i++]||null;for(;r--;)e.splice(o[r],1);return e},getAttributeValueFromNode:function(e,t){if(this.hasNodeAttribute(e,t)){if(e&&e.getAttribute)return e.getAttribute(t);if(e&&e.attributes)if("undefined"!==typeof e.attributes[t]){if(e.attributes[t].value)return e.attributes[t].value;if(e.attributes[t].nodeValue)return e.attributes[t].nodeValue;var n,i=e.attributes;if(i){for(n=0;n<i.length;n++)if(i[n].nodeName===t)return i[n].nodeValue;return null}}}},hasNodeAttributeWithValue:function(e,t){return!!this.getAttributeValueFromNode(e,t)},hasNodeAttribute:function(e,t){return e&&e.hasAttribute?e.hasAttribute(t):!(!e||!e.attributes)&&"undefined"!==typeof e.attributes[t]},hasNodeCssClass:function(e,t){if(e&&t&&e.className&&-1!==indexOfArray("string"==typeof e.className?e.className.split(" "):[],t))return!0;return!1},findNodesHavingAttribute:function(e,t,n){if(n||(n=[]),!e||!t)return n;var i,r,o=getChildrenFromNode(e);if(!o||!o.length)return n;for(i=0;i<o.length;i++)r=o[i],this.hasNodeAttribute(r,t)&&n.push(r),n=this.findNodesHavingAttribute(r,t,n);return n},findFirstNodeHavingAttribute:function(e,t){if(e&&t){if(this.hasNodeAttribute(e,t))return e;var n=this.findNodesHavingAttribute(e,t);return n&&n.length?n[0]:void 0}},findFirstNodeHavingAttributeWithValue:function(e,t){if(e&&t){if(this.hasNodeAttributeWithValue(e,t))return e;var n,i=this.findNodesHavingAttribute(e,t);if(i&&i.length)for(n=0;n<i.length;n++)if(this.getAttributeValueFromNode(i[n],t))return i[n]}},findNodesHavingCssClass:function(e,t,n){if(n||(n=[]),!e||!t)return n;if(e.getElementsByClassName){var i=e.getElementsByClassName(t);return this.htmlCollectionToArray(i)}var r,o,a=getChildrenFromNode(e);if(!a||!a.length)return[];for(r=0;r<a.length;r++)o=a[r],this.hasNodeCssClass(o,t)&&n.push(o),n=this.findNodesHavingCssClass(o,t,n);return n},findFirstNodeHavingClass:function(e,t){if(e&&t){if(this.hasNodeCssClass(e,t))return e;var n=this.findNodesHavingCssClass(e,t);return n&&n.length?n[0]:void 0}},isLinkElement:function(e){if(!e)return!1;return-1!==indexOfArray(["a","area"],String(e.nodeName).toLowerCase())},setAnyAttribute:function(e,t,n){e&&t&&(e.setAttribute?e.setAttribute(t,n):e[t]=n)}},content={CONTENT_ATTR:"data-track-content",CONTENT_CLASS:"matomoTrackContent",LEGACY_CONTENT_CLASS:"piwikTrackContent",CONTENT_NAME_ATTR:"data-content-name",CONTENT_PIECE_ATTR:"data-content-piece",CONTENT_PIECE_CLASS:"matomoContentPiece",LEGACY_CONTENT_PIECE_CLASS:"piwikContentPiece",CONTENT_TARGET_ATTR:"data-content-target",CONTENT_TARGET_CLASS:"matomoContentTarget",LEGACY_CONTENT_TARGET_CLASS:"piwikContentTarget",CONTENT_IGNOREINTERACTION_ATTR:"data-content-ignoreinteraction",CONTENT_IGNOREINTERACTION_CLASS:"matomoContentIgnoreInteraction",LEGACY_CONTENT_IGNOREINTERACTION_CLASS:"piwikContentIgnoreInteraction",location:void 0,findContentNodes:function(){var e="."+this.CONTENT_CLASS,t="."+this.LEGACY_CONTENT_CLASS,n="["+this.CONTENT_ATTR+"]";return query.findMultiple([e,t,n])},findContentNodesWithinNode:function(e){if(!e)return[];var t=query.findNodesHavingCssClass(e,this.CONTENT_CLASS);t=query.findNodesHavingCssClass(e,this.LEGACY_CONTENT_CLASS,t);var n,i=query.findNodesHavingAttribute(e,this.CONTENT_ATTR);if(i&&i.length)for(n=0;n<i.length;n++)t.push(i[n]);return query.hasNodeAttribute(e,this.CONTENT_ATTR)?t.push(e):query.hasNodeCssClass(e,this.CONTENT_CLASS)?t.push(e):query.hasNodeCssClass(e,this.LEGACY_CONTENT_CLASS)&&t.push(e),t=query.makeNodesUnique(t)},findParentContentNode:function(e){if(e)for(var t=e,n=0;t&&t!==documentAlias&&t.parentNode;){if(query.hasNodeAttribute(t,this.CONTENT_ATTR))return t;if(query.hasNodeCssClass(t,this.CONTENT_CLASS))return t;if(query.hasNodeCssClass(t,this.LEGACY_CONTENT_CLASS))return t;if(t=t.parentNode,n>1e3)break;n++}},findPieceNode:function(e){var t;return(t=query.findFirstNodeHavingAttribute(e,this.CONTENT_PIECE_ATTR))||(t=query.findFirstNodeHavingClass(e,this.CONTENT_PIECE_CLASS)),t||(t=query.findFirstNodeHavingClass(e,this.LEGACY_CONTENT_PIECE_CLASS)),t||e},findTargetNodeNoDefault:function(e){if(e){var t=query.findFirstNodeHavingAttributeWithValue(e,this.CONTENT_TARGET_ATTR);return t||((t=query.findFirstNodeHavingAttribute(e,this.CONTENT_TARGET_ATTR))?t:(t=query.findFirstNodeHavingClass(e,this.CONTENT_TARGET_CLASS))?t:(t=query.findFirstNodeHavingClass(e,this.LEGACY_CONTENT_TARGET_CLASS))||void 0)}},findTargetNode:function(e){var t=this.findTargetNodeNoDefault(e);return t||e},findContentName:function(e){if(e){var t=query.findFirstNodeHavingAttributeWithValue(e,this.CONTENT_NAME_ATTR);if(t)return query.getAttributeValueFromNode(t,this.CONTENT_NAME_ATTR);var n=this.findContentPiece(e);if(n)return this.removeDomainIfIsInLink(n);if(query.hasNodeAttributeWithValue(e,"title"))return query.getAttributeValueFromNode(e,"title");var i=this.findPieceNode(e);if(query.hasNodeAttributeWithValue(i,"title"))return query.getAttributeValueFromNode(i,"title");var r=this.findTargetNode(e);return query.hasNodeAttributeWithValue(r,"title")?query.getAttributeValueFromNode(r,"title"):void 0}},findContentPiece:function(e){if(e){var t=query.findFirstNodeHavingAttributeWithValue(e,this.CONTENT_PIECE_ATTR);if(t)return query.getAttributeValueFromNode(t,this.CONTENT_PIECE_ATTR);var n=this.findPieceNode(e),i=this.findMediaUrlInNode(n);return i?this.toAbsoluteUrl(i):void 0}},findContentTarget:function(e){if(e){var t,n=this.findTargetNode(e);if(query.hasNodeAttributeWithValue(n,this.CONTENT_TARGET_ATTR))return query.getAttributeValueFromNode(n,this.CONTENT_TARGET_ATTR);if(query.hasNodeAttributeWithValue(n,"href"))return t=query.getAttributeValueFromNode(n,"href"),this.toAbsoluteUrl(t);var i=this.findPieceNode(e);return query.hasNodeAttributeWithValue(i,"href")?(t=query.getAttributeValueFromNode(i,"href"),this.toAbsoluteUrl(t)):void 0}},isSameDomain:function(e){if(!e||!e.indexOf)return!1;if(0===e.indexOf(this.getLocation().origin))return!0;var t=e.indexOf(this.getLocation().host);return 8>=t&&0<=t},removeDomainIfIsInLink:function(e){return e&&e.search&&-1!==e.search(new RegExp("^https?://[^/]+"))&&this.isSameDomain(e)&&((e=e.replace(new RegExp("^.*//[^/]+"),""))||(e="/")),e},findMediaUrlInNode:function(e){if(e){var t=e.nodeName.toLowerCase();if(-1!==indexOfArray(["img","embed","video","audio"],t)&&query.findFirstNodeHavingAttributeWithValue(e,"src")){var n=query.findFirstNodeHavingAttributeWithValue(e,"src");return query.getAttributeValueFromNode(n,"src")}if("object"===t&&query.hasNodeAttributeWithValue(e,"data"))return query.getAttributeValueFromNode(e,"data");if("object"===t){var i,r=query.findNodesByTagName(e,"param");if(r&&r.length)for(i=0;i<r.length;i++)if("movie"===query.getAttributeValueFromNode(r[i],"name")&&query.hasNodeAttributeWithValue(r[i],"value"))return query.getAttributeValueFromNode(r[i],"value");var o=query.findNodesByTagName(e,"embed");if(o&&o.length)return this.findMediaUrlInNode(o[0])}}},trim:function(e){return trim(e)},isOrWasNodeInViewport:function(e){if(!e||!e.getBoundingClientRect||1!==e.nodeType)return!0;var t=e.getBoundingClientRect(),n=documentAlias.documentElement||{},i=t.top<0;i&&e.offsetTop&&(i=e.offsetTop+t.height>0);var r=n.clientWidth;windowAlias.innerWidth&&r>windowAlias.innerWidth&&(r=windowAlias.innerWidth);var o=n.clientHeight;return windowAlias.innerHeight&&o>windowAlias.innerHeight&&(o=windowAlias.innerHeight),(t.bottom>0||i)&&t.right>0&&t.left<r&&(t.top<o||i)},isNodeVisible:function(e){var t=isVisible(e),n=this.isOrWasNodeInViewport(e);return t&&n},buildInteractionRequestParams:function(e,t,n,i){var r="";return e&&(r+="c_i="+encodeWrapper(e)),t&&(r&&(r+="&"),r+="c_n="+encodeWrapper(t)),n&&(r&&(r+="&"),r+="c_p="+encodeWrapper(n)),i&&(r&&(r+="&"),r+="c_t="+encodeWrapper(i)),r&&(r+="&ca=1"),r},buildImpressionRequestParams:function(e,t,n){var i="c_n="+encodeWrapper(e)+"&c_p="+encodeWrapper(t);return n&&(i+="&c_t="+encodeWrapper(n)),i&&(i+="&ca=1"),i},buildContentBlock:function(e){if(e){var t=this.findContentName(e),n=this.findContentPiece(e),i=this.findContentTarget(e);return{name:(t=this.trim(t))||"Unknown",piece:(n=this.trim(n))||"Unknown",target:(i=this.trim(i))||""}}},collectContent:function(e){if(!e||!e.length)return[];var t,n,i=[];for(t=0;t<e.length;t++)isDefined(n=this.buildContentBlock(e[t]))&&i.push(n);return i},setLocation:function(e){this.location=e},getLocation:function(){var e=this.location||windowAlias.location;return e.origin||(e.origin=e.protocol+"//"+e.hostname+(e.port?":"+e.port:"")),e},toAbsoluteUrl:function(e){if((!e||String(e)!==e)&&""!==e)return e;if(""===e)return this.getLocation().href;if(-1!==e.search(/^\/\//))return this.getLocation().protocol+e;if(-1!==e.search(/:\/\//))return e;if(0===e.indexOf("#"))return this.getLocation().origin+this.getLocation().pathname+e;if(0===e.indexOf("?"))return this.getLocation().origin+this.getLocation().pathname+e;if(0===e.search("^[a-zA-Z]{2,11}:"))return e;if(-1!==e.search(/^\//))return this.getLocation().origin+e;return this.getLocation().origin+this.getLocation().pathname.match(new RegExp("(.*/)"))[0]+e},isUrlToCurrentDomain:function(e){var t=this.toAbsoluteUrl(e);if(!t)return!1;var n=this.getLocation().origin;return n===t||0===String(t).indexOf(n)&&":"!==String(t).substr(n.length,1)},setHrefAttribute:function(e,t){e&&t&&query.setAnyAttribute(e,"href",t)},shouldIgnoreInteraction:function(e){return!!query.hasNodeAttribute(e,this.CONTENT_IGNOREINTERACTION_ATTR)||(!!query.hasNodeCssClass(e,this.CONTENT_IGNOREINTERACTION_CLASS)||!!query.hasNodeCssClass(e,this.LEGACY_CONTENT_IGNOREINTERACTION_CLASS))}};function getMatomoUrlForOverlay(e,t){if(t)return t;if(stringContains(e=content.toAbsoluteUrl(e),"?")){var n=e.indexOf("?");e=e.slice(0,n)}if(stringEndsWith(e,"matomo.php"))e=removeCharactersFromEndOfString(e,"matomo.php".length);else if(stringEndsWith(e,"piwik.php"))e=removeCharactersFromEndOfString(e,"piwik.php".length);else if(stringEndsWith(e,".php")){var i=e.lastIndexOf("/");e=e.slice(0,i+1)}return stringEndsWith(e,"/js/")&&(e=removeCharactersFromEndOfString(e,"js/".length)),e}function isOverlaySession(e){var t=new RegExp("index\\.php\\?module=Overlay&action=startOverlaySession&idSite=([0-9]+)&period=([^&]+)&date=([^&]+)(&segment=[^&]*)?").exec(documentAlias.referrer);if(t){if(t[1]!==String(e))return!1;var n=t[2],i=t[3],r=t[4];r?0===r.indexOf("&segment=")&&(r=r.substr("&segment=".length)):r="",windowAlias.name="Matomo_Overlay###"+n+"###"+i+"###"+r}var o=windowAlias.name.split("###");return 4===o.length&&"Matomo_Overlay"===o[0]}function injectOverlayScripts(e,t,n){var i=windowAlias.name.split("###"),r=i[1],o=i[2],a=i[3],s=getMatomoUrlForOverlay(e,t);loadScript(s+"plugins/Overlay/client/client.js?v=1",function(){Matomo_Overlay_Client.initialize(s,n,r,o,a)})}function isInsideAnIframe(){var e;try{e=windowAlias.frameElement}catch(e){return!0}if(isDefined(e))return!(!e||"iframe"!==String(e.nodeName).toLowerCase());try{return windowAlias.self!==windowAlias.top}catch(e){return!0}}function Tracker(trackerUrl,siteId){var registeredHooks={},trackerInstance=this,CONSENT_COOKIE_NAME="mtm_consent",COOKIE_CONSENT_COOKIE_NAME="mtm_cookie_consent",CONSENT_REMOVED_COOKIE_NAME="mtm_consent_removed",locationArray=urlFixup(documentAlias.domain,windowAlias.location.href,getReferrer()),domainAlias=domainFixup(locationArray[0]),locationHrefAlias=safeDecodeWrapper(locationArray[1]),configReferrerUrl=safeDecodeWrapper(locationArray[2]),enableJSErrorTracking=!1,defaultRequestMethod="GET",configRequestMethod=defaultRequestMethod,defaultRequestContentType="application/x-www-form-urlencoded; charset=UTF-8",configRequestContentType=defaultRequestContentType,configTrackerUrl=trackerUrl||"",configApiUrl="",configAppendToTrackingUrl="",customPagePerformanceTiming="",configTrackerSiteId=siteId||"",configUserId="",visitorUUID="",configCustomUrl,configTitle="",configDownloadExtensions=["7z","aac","apk","arc","arj","asf","asx","avi","azw3","bin","csv","deb","dmg","doc","docx","epub","exe","flv","gif","gz","gzip","hqx","ibooks","jar","jpg","jpeg","js","mobi","mp2","mp3","mp4","mpg","mpeg","mov","movie","msi","msp","odb","odf","odg","ods","odt","ogg","ogv","pdf","phps","png","ppt","pptx","qt","qtm","ra","ram","rar","rpm","rtf","sea","sit","tar","tbz","tbz2","bz","bz2","tgz","torrent","txt","wav","wma","wmv","wpd","xls","xlsx","xml","z","zip"],configHostsAlias=[domainAlias],configIgnoreClasses=[],configExcludedReferrers=[".paypal.com"],configExcludedQueryParams=[],configDownloadClasses=[],configLinkClasses=[],configTrackerPause=500,configAlwaysUseSendBeacon=!0,configMinimumVisitTime,configHeartBeatDelay,heartBeatPingIfActivityAlias,configDiscardHashTag,configCustomData,configCampaignNameParameters=["pk_campaign","mtm_campaign","piwik_campaign","matomo_campaign","utm_campaign","utm_source","utm_medium"],configCampaignKeywordParameters=["pk_kwd","mtm_kwd","piwik_kwd","matomo_kwd","utm_term"],configCookieNamePrefix="_pk_",configVisitorIdUrlParameter="pk_vid",configVisitorIdUrlParameterTimeoutInSeconds=180,configCookieDomain,configCookiePath,configCookieIsSecure=!1,configCookieSameSite="Lax",configCookiesDisabled=!1,configDoNotTrack,configCountPreRendered,configConversionAttributionFirstReferrer,configVisitorCookieTimeout=339552e5,configSessionCookieTimeout=18e5,configReferralCookieTimeout=15768e6,configPerformanceTrackingEnabled=!0,performanceAvailable=!1,performanceTracked=!1,configStoreCustomVariablesInCookie=!1,customVariables=!1,configCustomRequestContentProcessing,customVariablesPage={},customVariablesEvent={},customDimensions={},customVariableMaximumLength=200,ecommerceProductView={},ecommerceItems={},browserFeatures={},clientHints={},clientHintsRequestQueue=[],clientHintsResolved=!1,trackedContentImpressions=[],isTrackOnlyVisibleContentEnabled=!1,timeNextTrackingRequestCanBeExecutedImmediately=!1,linkTrackingInstalled=!1,linkTrackingEnabled=!1,crossDomainTrackingEnabled=!1,heartBeatSetUp=!1,hadWindowFocusAtLeastOnce=isInsideAnIframe(),timeWindowLastFocused=null,lastTrackerRequestTime=null,lastButton,lastTarget,hash=sha1,domainHash,configIdPageView,configIdPageViewSetManually=!1,numTrackedPageviews=0,configCookiesToDelete=["id","ses","cvar","ref"],configConsentRequired=!1,configHasConsent=null,consentRequestsQueue=[],javaScriptErrors=[],uniqueTrackerId=trackerIdCounter++,hasSentTrackingRequestYet=!1,configBrowserFeatureDetection=!0;try{configTitle=documentAlias.title}catch(e){configTitle=""}function getCookie(e){if(configCookiesDisabled&&e!==CONSENT_REMOVED_COOKIE_NAME)return 0;var t=new RegExp("(^|;)[ ]*"+e+"=([^;]*)").exec(documentAlias.cookie);return t?decodeWrapper(t[2]):0}function setCookie(e,t,n,i,r,o,a){var s;configCookiesDisabled&&e!==CONSENT_REMOVED_COOKIE_NAME||(n&&(s=new Date).setTime(s.getTime()+n),a||(a="Lax"),documentAlias.cookie=e+"="+encodeWrapper(t)+(n?";expires="+s.toGMTString():"")+";path="+(i||"/")+(r?";domain="+r:"")+(o?";secure":"")+";SameSite="+a,(!n||n>=0)&&getCookie(e)!==String(t)&&logConsoleError("There was an error setting cookie `"+e+"`. Please check domain and path."))}function purify(e){var t,n;for(e=removeUrlParameter(e=removeUrlParameter(e=removeUrlParameter(e,configVisitorIdUrlParameter),"ignore_referrer"),"ignore_referer"),n=0;n<configExcludedQueryParams.length;n++)e=removeUrlParameter(e,configExcludedQueryParams[n]);return configDiscardHashTag?(t=new RegExp("#.*"),e.replace(t,"")):e}function resolveRelativeReference(e,t){var n;return getProtocolScheme(t)?t:((n=(e=purify(e)).indexOf("?"))>=0&&(e=e.slice(0,n)),(n=e.lastIndexOf("/"))!==e.length-1&&(e=e.slice(0,n+1)),`https://${location.host}.com`+t)}function isSameHost(e,t){var n;if((e=String(e).toLowerCase())===(t=String(t).toLowerCase()))return!0;if("."===t.slice(0,1)){if(e===t.slice(1))return!0;if((n=e.length-t.length)>0&&e.slice(n)===t)return!0}return!1}function getPathName(e){var t=document.createElement("a");return 0!==e.indexOf("//")&&0!==e.indexOf("http")&&(0===e.indexOf("*")&&(e=e.substr(1)),0===e.indexOf(".")&&(e=e.substr(1)),e="http://"+e),t.href=content.toAbsoluteUrl(e),t.pathname?t.pathname:""}function isSitePath(e,t){stringStartsWith(t,"/")||(t="/"+t),stringStartsWith(e,"/")||(e="/"+e);var n="/"===t||"/*"===t;return!!n||(e===t||(t=String(t).toLowerCase(),e=String(e).toLowerCase(),stringEndsWith(t,"*")?!!(n=!(t=t.slice(0,-1))||"/"===t)||(e===t||0===e.indexOf(t)):(stringEndsWith(e,"/")||(e+="/"),stringEndsWith(t,"/")||(t+="/"),0===e.indexOf(t))))}function isSiteHostPath(e,t){var n,i,r;for(n=0;n<configHostsAlias.length;n++)if(i=domainFixup(configHostsAlias[n]),r=getPathName(configHostsAlias[n]),isSameHost(e,i)&&isSitePath(t,r))return!0;return!1}function isSiteHostName(e){var t,n,i;for(t=0;t<configHostsAlias.length;t++){if(e===(n=domainFixup(configHostsAlias[t].toLowerCase())))return!0;if("."===n.slice(0,1)){if(e===n.slice(1))return!0;if((i=e.length-n.length)>0&&e.slice(i)===n)return!0}}return!1}function isReferrerExcluded(e){var t,n,i,r,o;if(!e.length||!configExcludedReferrers.length)return!1;for(n=getHostName(e),i=getPathName(e),0===n.indexOf("www.")&&(n=n.substr(4)),t=0;t<configExcludedReferrers.length;t++)if(r=domainFixup(configExcludedReferrers[t]),o=getPathName(configExcludedReferrers[t]),0===r.indexOf("www.")&&(r=r.substr(4)),isSameHost(n,r)&&isSitePath(i,o))return!0;return!1}function getImage(e,t){e=e.replace("send_image=0","send_image=1");var n=new Image(1,1);n.onload=function(){iterator=0,"function"==typeof t&&t({request:e,trackerUrl:configTrackerUrl,success:!0})},n.onerror=function(){"function"==typeof t&&t({request:e,trackerUrl:configTrackerUrl,success:!1})},n.src=configTrackerUrl+(configTrackerUrl.indexOf("?")<0?"?":"&")+e}function shouldForcePost(e){return"POST"===configRequestMethod||e&&(e.length>2e3||0===e.indexOf('{"requests"'))}function supportsSendBeacon(){return"object"==typeof navigatorAlias&&"function"==typeof navigatorAlias.sendBeacon&&"function"==typeof Blob}function sendPostRequestViaSendBeacon(e,t,n){if(!supportsSendBeacon())return!1;var i={type:"application/x-www-form-urlencoded; charset=UTF-8"},r=!1,o=configTrackerUrl;try{var a=new Blob([e],i);n&&!shouldForcePost(e)&&(a=new Blob([],i),o=o+(o.indexOf("?")<0?"?":"&")+e),r=navigatorAlias.sendBeacon(o,a)}catch(e){return!1}return r&&"function"==typeof t&&t({request:e,trackerUrl:configTrackerUrl,success:!0,isSendBeacon:!0}),r}function sendXmlHttpRequest(e,t,n){isDefined(n)&&null!==n||(n=!0),isPageUnloading&&sendPostRequestViaSendBeacon(e,t,n)||setTimeout(function(){if(!isPageUnloading||!sendPostRequestViaSendBeacon(e,t,n))try{var i=windowAlias.XMLHttpRequest?new windowAlias.XMLHttpRequest:windowAlias.ActiveXObject?new ActiveXObject("Microsoft.XMLHTTP"):null;i.open("POST",configTrackerUrl,!0),i.onreadystatechange=function(){4!==this.readyState||this.status>=200&&this.status<300?4===this.readyState&&"function"==typeof t&&t({request:e,trackerUrl:configTrackerUrl,success:!0,xhr:this}):!(isPageUnloading&&sendPostRequestViaSendBeacon(e,t,n))&&n?getImage(e,t):"function"==typeof t&&t({request:e,trackerUrl:configTrackerUrl,success:!1,xhr:this})},i.setRequestHeader("Content-Type",configRequestContentType),i.withCredentials=!0,i.send(e)}catch(i){!(isPageUnloading&&sendPostRequestViaSendBeacon(e,t,n))&&n?getImage(e,t):"function"==typeof t&&t({request:e,trackerUrl:configTrackerUrl,success:!1})}},50)}function setExpireDateTime(e){var t=(new Date).getTime()+e;(!expireDateTime||t>expireDateTime)&&(expireDateTime=t)}function heartBeatOnFocus(){hadWindowFocusAtLeastOnce=!0,timeWindowLastFocused=(new Date).getTime()}function hadWindowMinimalFocusToConsiderViewed(){var e=(new Date).getTime();return!timeWindowLastFocused||e-timeWindowLastFocused>configHeartBeatDelay}function heartBeatOnBlur(){hadWindowMinimalFocusToConsiderViewed()&&heartBeatPingIfActivityAlias()}function heartBeatOnVisible(){"hidden"===documentAlias.visibilityState&&hadWindowMinimalFocusToConsiderViewed()?heartBeatPingIfActivityAlias():"visible"===documentAlias.visibilityState&&(timeWindowLastFocused=(new Date).getTime())}function setUpHeartBeat(){!heartBeatSetUp&&configHeartBeatDelay&&(heartBeatSetUp=!0,addEventListener(windowAlias,"focus",heartBeatOnFocus),addEventListener(windowAlias,"blur",heartBeatOnBlur),addEventListener(windowAlias,"visibilitychange",heartBeatOnVisible),coreHeartBeatCounter++,Matomo.addPlugin("HeartBeat"+coreHeartBeatCounter,{unload:function(){heartBeatSetUp&&hadWindowMinimalFocusToConsiderViewed()&&heartBeatPingIfActivityAlias()}}))}function makeSureThereIsAGapAfterFirstTrackingRequestToPreventMultipleVisitorCreation(e){var t=(new Date).getTime();if(lastTrackerRequestTime=t,timeNextTrackingRequestCanBeExecutedImmediately&&t<timeNextTrackingRequestCanBeExecutedImmediately){var n=timeNextTrackingRequestCanBeExecutedImmediately-t;return setTimeout(e,n),setExpireDateTime(n+50),void(timeNextTrackingRequestCanBeExecutedImmediately+=50)}if(!1===timeNextTrackingRequestCanBeExecutedImmediately){timeNextTrackingRequestCanBeExecutedImmediately=t+800}e()}function refreshConsentStatus(){getCookie(CONSENT_REMOVED_COOKIE_NAME)?configHasConsent=!1:getCookie(CONSENT_COOKIE_NAME)&&(configHasConsent=!0)}function injectClientHints(e){if(!clientHints)return e;var t,n="&uadata="+encodeWrapper(windowAlias.JSON.stringify(clientHints));if(e instanceof Array)for(t=0;t<e.length;t++)e[t]+=n;else e+=n;return e}function detectClientHints(e){configBrowserFeatureDetection&&isDefined(navigatorAlias.userAgentData)&&isFunction(navigatorAlias.userAgentData.getHighEntropyValues)?(clientHints={brands:navigatorAlias.userAgentData.brands,platform:navigatorAlias.userAgentData.platform},navigatorAlias.userAgentData.getHighEntropyValues(["brands","model","platform","platformVersion","uaFullVersion","fullVersionList"]).then(function(t){t.fullVersionList&&(delete t.brands,delete t.uaFullVersion),clientHints=t,e()},function(t){e()})):e()}function sendRequest(e,t,n){clientHintsResolved?(refreshConsentStatus(),configHasConsent?(hasSentTrackingRequestYet=!0,!configDoNotTrack&&e&&(configConsentRequired&&configHasConsent&&(e+="&consent=1"),e=injectClientHints(e),makeSureThereIsAGapAfterFirstTrackingRequestToPreventMultipleVisitorCreation(function(){configAlwaysUseSendBeacon&&sendPostRequestViaSendBeacon(e,n,!0)?setExpireDateTime(100):(shouldForcePost(e)?sendXmlHttpRequest(e,n):getImage(e,n),setExpireDateTime(t))})),heartBeatSetUp||setUpHeartBeat()):consentRequestsQueue.push(e)):clientHintsRequestQueue.push(e)}function canSendBulkRequest(e){return!configDoNotTrack&&(e&&e.length)}function arrayChunk(e,t){if(!t||t>=e.length)return[e];for(var n=0,i=e.length,r=[];n<i;n+=t)r.push(e.slice(n,n+t));return r}function sendBulkRequest(e,t){canSendBulkRequest(e)&&(clientHintsResolved?configHasConsent?(hasSentTrackingRequestYet=!0,makeSureThereIsAGapAfterFirstTrackingRequestToPreventMultipleVisitorCreation(function(){for(var n,i=arrayChunk(e,50),r=0;r<i.length;r++)n='{"requests":["?'+injectClientHints(i[r]).join('","?')+'"],"send_image":0}',configAlwaysUseSendBeacon&&sendPostRequestViaSendBeacon(n,null,!1)?setExpireDateTime(100):sendXmlHttpRequest(n,null,!1);setExpireDateTime(t)})):consentRequestsQueue.push(e):clientHintsRequestQueue.push(e))}function getCookieName(e){return configCookieNamePrefix+e+"."+configTrackerSiteId+"."+domainHash}function deleteCookie(e,t,n){setCookie(e,"",-1296e5,t,n)}function hasCookies(){if(configCookiesDisabled)return"0";if(!isDefined(windowAlias.showModalDialog)&&isDefined(navigatorAlias.cookieEnabled))return navigatorAlias.cookieEnabled?"1":"0";var e=configCookieNamePrefix+"testcookie";setCookie(e,"1",void 0,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite);var t="1"===getCookie(e)?"1":"0";return deleteCookie(e),t}function updateDomainHash(){domainHash=hash((configCookieDomain||domainAlias)+(configCookiePath||"/")).slice(0,4)}function detectBrowserFeatures(){if(detectClientHints(function(){var e,t;for(clientHintsResolved=!0,e=0;e<clientHintsRequestQueue.length;e++)"string"===(t=typeof clientHintsRequestQueue[e])?sendRequest(clientHintsRequestQueue[e],configTrackerPause):"object"===t&&sendBulkRequest(clientHintsRequestQueue[e],configTrackerPause);clientHintsRequestQueue=[]}),!configBrowserFeatureDetection)return{};if(isDefined(browserFeatures.res))return browserFeatures;var e,t,n={pdf:"application/pdf",qt:"video/quicktime",realp:"audio/x-pn-realaudio-plugin",wma:"application/x-mplayer2",fla:"application/x-shockwave-flash",java:"application/x-java-vm",ag:"application/x-silverlight"};if(!new RegExp("MSIE").test(navigatorAlias.userAgent)){if(navigatorAlias.mimeTypes&&navigatorAlias.mimeTypes.length)for(e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t=navigatorAlias.mimeTypes[n[e]],browserFeatures[e]=t&&t.enabledPlugin?"1":"0");!new RegExp("Edge[ /](\\d+[\\.\\d]+)").test(navigatorAlias.userAgent)&&"unknown"!=typeof navigator.javaEnabled&&isDefined(navigatorAlias.javaEnabled)&&navigatorAlias.javaEnabled()&&(browserFeatures.java="1"),!isDefined(windowAlias.showModalDialog)&&isDefined(navigatorAlias.cookieEnabled)?browserFeatures.cookie=navigatorAlias.cookieEnabled?"1":"0":browserFeatures.cookie=hasCookies()}var i=parseInt(screenAlias.width,10),r=parseInt(screenAlias.height,10);return browserFeatures.res=parseInt(i,10)+"x"+parseInt(r,10),browserFeatures}function getCustomVariablesFromCookie(){var e=getCookie(getCookieName("cvar"));return e&&e.length&&isObject(e=windowAlias.JSON.parse(e))?e:{}}function loadCustomVariables(){!1===customVariables&&(customVariables=getCustomVariablesFromCookie())}function generateRandomUuid(){var e=detectBrowserFeatures();return hash((navigatorAlias.userAgent||"")+(navigatorAlias.platform||"")+windowAlias.JSON.stringify(e)+(new Date).getTime()+Math.random()).slice(0,16)}function generateBrowserSpecificId(){var e=detectBrowserFeatures();return hash((navigatorAlias.userAgent||"")+(navigatorAlias.platform||"")+windowAlias.JSON.stringify(e)).slice(0,6)}function getCurrentTimestampInSeconds(){return Math.floor((new Date).getTime()/1e3)}function makeCrossDomainDeviceId(){var e=getCurrentTimestampInSeconds(),t=generateBrowserSpecificId();return String(e)+t}function isSameCrossDomainDevice(e){e=String(e);var t=generateBrowserSpecificId(),n=t.length,i=e.substr(-1*n,n),r=parseInt(e.substr(0,e.length-n),10);if(r&&i&&i===t){var o=getCurrentTimestampInSeconds();if(configVisitorIdUrlParameterTimeoutInSeconds<=0)return!0;if(o>=r&&o<=r+configVisitorIdUrlParameterTimeoutInSeconds)return!0}return!1}function getVisitorIdFromUrl(e){if(!crossDomainTrackingEnabled)return"";var t=getUrlParameter(e,configVisitorIdUrlParameter);if(!t)return"";t=String(t);var n=new RegExp("^[a-zA-Z0-9]+$");if(32===t.length&&n.test(t)&&isSameCrossDomainDevice(t.substr(16,32)))return t.substr(0,16);return""}function loadVisitorIdCookie(){visitorUUID||(visitorUUID=getVisitorIdFromUrl(locationHrefAlias));var e,t=new Date,n=Math.round(t.getTime()/1e3),i=getCookie(getCookieName("id"));return i?((e=i.split(".")).unshift("0"),visitorUUID.length&&(e[1]=visitorUUID),e):e=["1",visitorUUID.length?visitorUUID:"0"===hasCookies()?"":generateRandomUuid(),n]}function getValuesFromVisitorIdCookie(){var e=loadVisitorIdCookie();return{newVisitor:e[0],uuid:e[1],createTs:e[2]}}function getRemainingVisitorCookieTimeout(){var e=(new Date).getTime(),t=getValuesFromVisitorIdCookie().createTs;return 1e3*parseInt(t,10)+configVisitorCookieTimeout-e}function setVisitorIdCookie(e){if(configTrackerSiteId){var t=new Date;Math.round(t.getTime()/1e3);isDefined(e)||(e=getValuesFromVisitorIdCookie());var n=e.uuid+"."+e.createTs+".";setCookie(getCookieName("id"),n,getRemainingVisitorCookieTimeout(),configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite)}}function loadReferrerAttributionCookie(){var e=getCookie(getCookieName("ref"));if(e.length)try{if(isObject(e=windowAlias.JSON.parse(e)))return e}catch(e){}return["","",0,""]}function isPossibleToSetCookieOnDomain(e){var t=configCookieNamePrefix+"testcookie_domain";return setCookie(t,"testvalue",1e4,null,e,configCookieIsSecure,configCookieSameSite),"testvalue"===getCookie(t)&&(deleteCookie(t,null,e),!0)}function deleteCookies(){var e,t,n=configCookiesDisabled;for(configCookiesDisabled=!1,e=0;e<configCookiesToDelete.length;e++)(t=getCookieName(configCookiesToDelete[e]))!==CONSENT_REMOVED_COOKIE_NAME&&t!==CONSENT_COOKIE_NAME&&0!==getCookie(t)&&deleteCookie(t,configCookiePath,configCookieDomain);configCookiesDisabled=n}function setSiteId(e){configTrackerSiteId=e}function sortObjectByKeys(e){if(e&&isObject(e)){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.push(t);var i={};n.sort();var r,o=n.length;for(r=0;r<o;r++)i[n[r]]=e[n[r]];return i}}function setSessionCookie(){setCookie(getCookieName("ses"),"1",configSessionCookieTimeout,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite)}function generateUniqueId(){var e,t="",n="abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",i=n.length;for(e=0;e<6;e++)t+=n.charAt(Math.floor(Math.random()*i));return t}function appendAvailablePerformanceMetrics(e){if(""!==customPagePerformanceTiming)return performanceTracked=!0,e+=customPagePerformanceTiming;if(!performanceAlias)return e;var t="object"==typeof performanceAlias.timing&&performanceAlias.timing?performanceAlias.timing:void 0;if(t||(t="function"==typeof performanceAlias.getEntriesByType&&performanceAlias.getEntriesByType("navigation")?performanceAlias.getEntriesByType("navigation")[0]:void 0),!t)return e;var n="";if(t.connectEnd&&t.fetchStart){if(t.connectEnd<t.fetchStart)return e;n+="&pf_net="+Math.round(t.connectEnd-t.fetchStart)}if(t.responseStart&&t.requestStart){if(t.responseStart<t.requestStart)return e;n+="&pf_srv="+Math.round(t.responseStart-t.requestStart)}if(t.responseStart&&t.responseEnd){if(t.responseEnd<t.responseStart)return e;n+="&pf_tfr="+Math.round(t.responseEnd-t.responseStart)}if(isDefined(t.domLoading)){if(t.domInteractive&&t.domLoading){if(t.domInteractive<t.domLoading)return e;n+="&pf_dm1="+Math.round(t.domInteractive-t.domLoading)}}else if(t.domInteractive&&t.responseEnd){if(t.domInteractive<t.responseEnd)return e;n+="&pf_dm1="+Math.round(t.domInteractive-t.responseEnd)}if(t.domComplete&&t.domInteractive){if(t.domComplete<t.domInteractive)return e;n+="&pf_dm2="+Math.round(t.domComplete-t.domInteractive)}if(t.loadEventEnd&&t.loadEventStart){if(t.loadEventEnd<t.loadEventStart)return e;n+="&pf_onl="+Math.round(t.loadEventEnd-t.loadEventStart)}return e+n}function hasIgnoreReferrerParameter(e){return"1"===getUrlParameter(e,"ignore_referrer")||"1"===getUrlParameter(e,"ignore_referer")}function detectReferrerAttribution(){var e,t,n,i,r,o,a,s=new Date,c=Math.round(s.getTime()/1e3),u=getCookieName("ses"),l=getCookieName("ref"),f=getCookie(u),d=loadReferrerAttributionCookie(),g=configCustomUrl||locationHrefAlias,m={};if(o=d[0],a=d[1],t=d[2],n=d[3],!hasIgnoreReferrerParameter(g)&&!f){if(!configConversionAttributionFirstReferrer||!o.length){for(e in configCampaignNameParameters)if(Object.prototype.hasOwnProperty.call(configCampaignNameParameters,e)&&(o=getUrlParameter(g,configCampaignNameParameters[e])).length)break;for(e in configCampaignKeywordParameters)if(Object.prototype.hasOwnProperty.call(configCampaignKeywordParameters,e)&&(a=getUrlParameter(g,configCampaignKeywordParameters[e])).length)break}i=getHostName(configReferrerUrl),r=n.length?getHostName(n):"",!i.length||isSiteHostName(i)||isReferrerExcluded(configReferrerUrl)||configConversionAttributionFirstReferrer&&r.length&&!isSiteHostName(r)&&!isReferrerExcluded(n)||(n=configReferrerUrl),(n.length||o.length)&&(d=[o,a,t=c,purify(n.slice(0,1024))],setCookie(l,windowAlias.JSON.stringify(d),configReferralCookieTimeout,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite))}return o.length&&(m._rcn=encodeWrapper(o)),a.length&&(m._rck=encodeWrapper(a)),m._refts=t,String(n).length&&(m._ref=encodeWrapper(purify(n.slice(0,1024)))),m}function getRequest(e,t,n){var i,r=new Date,o=customVariables,a=getCookieName("cvar"),s=configCustomUrl||locationHrefAlias,c=hasIgnoreReferrerParameter(s);if(configCookiesDisabled&&deleteCookies(),configDoNotTrack)return"";var u=getValuesFromVisitorIdCookie(),l=documentAlias.characterSet||documentAlias.charset;l&&"utf-8"!==l.toLowerCase()||(l=null),e+="&idsite="+configTrackerSiteId+"&rec=1&r="+String(Math.random()).slice(2,8)+"&h="+r.getHours()+"&m="+r.getMinutes()+"&s="+r.getSeconds()+"&url="+encodeWrapper(purify(s))+(!configReferrerUrl.length||isReferrerExcluded(configReferrerUrl)||c?"":"&urlref="+encodeWrapper(purify(configReferrerUrl)))+(isNumberOrHasLength(configUserId)?"&uid="+encodeWrapper(configUserId):"")+"&_id="+u.uuid+"&_idn="+u.newVisitor+(l?"&cs="+encodeWrapper(l):"")+"&send_image=0";var f=detectReferrerAttribution();for(i in f)Object.prototype.hasOwnProperty.call(f,i)&&(e+="&"+i+"="+f[i]);var d=detectBrowserFeatures();for(i in d)Object.prototype.hasOwnProperty.call(d,i)&&(e+="&"+i+"="+d[i]);var g=[];if(t)for(i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&/^dimension\d+$/.test(i)){var m=i.replace("dimension","");g.push(parseInt(m,10)),g.push(String(m)),e+="&"+i+"="+encodeWrapper(t[i]),delete t[i]}for(i in t&&isObjectEmpty(t)&&(t=null),ecommerceProductView)Object.prototype.hasOwnProperty.call(ecommerceProductView,i)&&(e+="&"+i+"="+encodeWrapper(ecommerceProductView[i]));for(i in customDimensions){if(Object.prototype.hasOwnProperty.call(customDimensions,i))-1===indexOfArray(g,i)&&(e+="&dimension"+i+"="+encodeWrapper(customDimensions[i]))}function h(e,t){var n=windowAlias.JSON.stringify(e);return n.length>2?"&"+t+"="+encodeWrapper(n):""}t?e+="&data="+encodeWrapper(windowAlias.JSON.stringify(t)):configCustomData&&(e+="&data="+encodeWrapper(windowAlias.JSON.stringify(configCustomData)));var p=sortObjectByKeys(customVariablesPage),C=sortObjectByKeys(customVariablesEvent);if(e+=h(p,"cvar"),e+=h(C,"e_cvar"),customVariables){for(i in e+=h(customVariables,"_cvar"),o)Object.prototype.hasOwnProperty.call(o,i)&&(""!==customVariables[i][0]&&""!==customVariables[i][1]||delete customVariables[i]);configStoreCustomVariablesInCookie&&setCookie(a,windowAlias.JSON.stringify(customVariables),configSessionCookieTimeout,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite)}return configPerformanceTrackingEnabled&&performanceAvailable&&!performanceTracked&&(e=appendAvailablePerformanceMetrics(e),performanceTracked=!0),configIdPageView&&(e+="&pv_id="+configIdPageView),setVisitorIdCookie(u),setSessionCookie(),e+=executePluginMethod(n,{tracker:trackerInstance,request:e}),configAppendToTrackingUrl.length&&(e+="&"+configAppendToTrackingUrl),isFunction(configCustomRequestContentProcessing)&&(e=configCustomRequestContentProcessing(e)),e}function logEcommerce(e,t,n,i,r,o){var a,s="idgoal=0",c=(new Date,[]),u=String(e).length;if(u&&(s+="&ec_id="+encodeWrapper(e)),s+="&revenue="+t,String(n).length&&(s+="&ec_st="+n),String(i).length&&(s+="&ec_tx="+i),String(r).length&&(s+="&ec_sh="+r),String(o).length&&(s+="&ec_dt="+o),ecommerceItems){for(a in ecommerceItems)Object.prototype.hasOwnProperty.call(ecommerceItems,a)&&(isDefined(ecommerceItems[a][1])||(ecommerceItems[a][1]=""),isDefined(ecommerceItems[a][2])||(ecommerceItems[a][2]=""),isDefined(ecommerceItems[a][3])&&0!==String(ecommerceItems[a][3]).length||(ecommerceItems[a][3]=0),isDefined(ecommerceItems[a][4])&&0!==String(ecommerceItems[a][4]).length||(ecommerceItems[a][4]=1),c.push(ecommerceItems[a]));s+="&ec_items="+encodeWrapper(windowAlias.JSON.stringify(c))}sendRequest(s=getRequest(s,configCustomData,"ecommerce"),configTrackerPause),u&&(ecommerceItems={})}function logEcommerceOrder(e,t,n,i,r,o){String(e).length&&isDefined(t)&&logEcommerce(e,t,n,i,r,o)}function logEcommerceCartUpdate(e){isDefined(e)&&logEcommerce("",e,"","","","")}function logPageView(e,t,n){configIdPageViewSetManually||(configIdPageView=generateUniqueId());var i=getRequest("action_name="+encodeWrapper(titleFixup(e||configTitle)),t,"log");configPerformanceTrackingEnabled&&!performanceTracked&&(i=appendAvailablePerformanceMetrics(i)),sendRequest(i,configTrackerPause,n)}function getClassesRegExp(e,t){var n,i="(^| )(piwik[_-]"+t+"|matomo[_-]"+t;if(e)for(n=0;n<e.length;n++)i+="|"+e[n];return i+=")( |$)",new RegExp(i)}function startsUrlWithTrackerUrl(e){return configTrackerUrl&&e&&0===String(e).indexOf(configTrackerUrl)}function getLinkType(e,t,n,i){if(startsUrlWithTrackerUrl(t))return 0;var r=getClassesRegExp(configDownloadClasses,"download"),o=getClassesRegExp(configLinkClasses,"link"),a=new RegExp("\\.("+configDownloadExtensions.join("|")+")([?&#]|$)","i");return o.test(e)?"link":i||r.test(e)||a.test(t)?"download":n?0:"link"}function getSourceElement(e){var t;for(t=e.parentNode;null!==t&&isDefined(t)&&!query.isLinkElement(e);)t=(e=t).parentNode;return e}function getLinkIfShouldBeProcessed(e){if(e=getSourceElement(e),query.hasNodeAttribute(e,"href")&&isDefined(e.href)){query.getAttributeValueFromNode(e,"href");var t=e.pathname||getPathName(e.href),n=e.hostname||getHostName(e.href),i=n.toLowerCase(),r=e.href.replace(n,i);if(!new RegExp("^(javascript|vbscript|jscript|mocha|livescript|ecmascript|mailto|tel):","i").test(r)){var o=getLinkType(e.className,r,isSiteHostPath(i,t),query.hasNodeAttribute(e,"download"));if(o)return{type:o,href:r}}}}function buildContentInteractionRequest(e,t,n,i){var r=content.buildInteractionRequestParams(e,t,n,i);if(r)return getRequest(r,null,"contentInteraction")}function isNodeAuthorizedToTriggerInteraction(e,t){if(!e||!t)return!1;var n=content.findTargetNode(e);return!content.shouldIgnoreInteraction(n)&&!((n=content.findTargetNodeNoDefault(e))&&!containsNodeElement(n,t))}function getContentInteractionToRequestIfPossible(e,t,n){if(e){var i=content.findParentContentNode(e);if(i&&isNodeAuthorizedToTriggerInteraction(i,e)){var r=content.buildContentBlock(i);if(r)return!r.target&&n&&(r.target=n),content.buildInteractionRequestParams(t,r.name,r.piece,r.target)}}}function wasContentImpressionAlreadyTracked(e){if(!trackedContentImpressions||!trackedContentImpressions.length)return!1;var t,n;for(t=0;t<trackedContentImpressions.length;t++)if((n=trackedContentImpressions[t])&&n.name===e.name&&n.piece===e.piece&&n.target===e.target)return!0;return!1}function trackContentImpressionClickInteraction(e){return function(t){if(e){var n,i=content.findParentContentNode(e);if(t&&(n=t.target||t.srcElement),n||(n=e),isNodeAuthorizedToTriggerInteraction(i,n)){if(!i)return!1;var r=content.findTargetNode(i);if(!r||content.shouldIgnoreInteraction(r))return!1;var o=getLinkIfShouldBeProcessed(r);return linkTrackingEnabled&&o&&o.type?o.type:trackerInstance.trackContentInteractionNode(n,"click")}}}}function setupInteractionsTracking(e){var t,n;if(e&&e.length)for(t=0;t<e.length;t++)(n=content.findTargetNode(e[t]))&&!n.contentInteractionTrackingSetupDone&&(n.contentInteractionTrackingSetupDone=!0,addEventListener(n,"click",trackContentImpressionClickInteraction(n)))}function buildContentImpressionsRequests(e,t){if(!e||!e.length)return[];var n,i;for(n=0;n<e.length;n++)wasContentImpressionAlreadyTracked(e[n])?(e.splice(n,1),n--):trackedContentImpressions.push(e[n]);if(!e||!e.length)return[];setupInteractionsTracking(t);var r=[];for(n=0;n<e.length;n++)(i=getRequest(content.buildImpressionRequestParams(e[n].name,e[n].piece,e[n].target),void 0,"contentImpressions"))&&r.push(i);return r}function getContentImpressionsRequestsFromNodes(e){return buildContentImpressionsRequests(content.collectContent(e),e)}function getCurrentlyVisibleContentImpressionsRequestsIfNotTrackedYet(e){if(!e||!e.length)return[];var t;for(t=0;t<e.length;t++)content.isNodeVisible(e[t])||(e.splice(t,1),t--);return e&&e.length?getContentImpressionsRequestsFromNodes(e):[]}function buildContentImpressionRequest(e,t,n){return getRequest(content.buildImpressionRequestParams(e,t,n),null,"contentImpression")}function buildContentInteractionRequestNode(e,t){if(e){var n=content.findParentContentNode(e),i=content.buildContentBlock(n);if(i)return t||(t="Unknown"),buildContentInteractionRequest(t,i.name,i.piece,i.target)}}function buildEventRequest(e,t,n,i){return"e_c="+encodeWrapper(e)+"&e_a="+encodeWrapper(t)+(isDefined(n)?"&e_n="+encodeWrapper(n):"")+(isDefined(i)?"&e_v="+encodeWrapper(i):"")+"&ca=1"}function logEvent(e,t,n,i,r,o){if(!isNumberOrHasLength(e)||!isNumberOrHasLength(t))return logConsoleError("Error while logging event: Parameters `category` and `action` must not be empty or filled with whitespaces"),!1;sendRequest(getRequest(buildEventRequest(e,t,n,i),r,"event"),configTrackerPause,o)}function logSiteSearch(e,t,n,i){sendRequest(getRequest("search="+encodeWrapper(e)+(t?"&search_cat="+encodeWrapper(t):"")+(isDefined(n)?"&search_count="+n:""),i,"sitesearch"),configTrackerPause)}function logGoal(e,t,n,i){sendRequest(getRequest("idgoal="+e+(t?"&revenue="+t:""),n,"goal"),configTrackerPause,i)}function logLink(e,t,n,i,r){var o=t+"="+encodeWrapper(purify(e)),a=getContentInteractionToRequestIfPossible(r,"click",e);a&&(o+="&"+a),sendRequest(getRequest(o,n,"link"),configTrackerPause,i)}function prefixPropertyName(e,t){return""!==e?e+t.charAt(0).toUpperCase()+t.slice(1):t}function trackCallback(e){var t,n,i,r=["","webkit","ms","moz"];if(!configCountPreRendered)for(n=0;n<r.length;n++)if(i=r[n],Object.prototype.hasOwnProperty.call(documentAlias,prefixPropertyName(i,"hidden"))){"prerender"===documentAlias[prefixPropertyName(i,"visibilityState")]&&(t=!0);break}t?addEventListener(documentAlias,i+"visibilitychange",function t(){documentAlias.removeEventListener(i+"visibilitychange",t,!1),e()}):e()}function getCrossDomainVisitorId(){return trackerInstance.getVisitorId()+makeCrossDomainDeviceId()}function replaceHrefForCrossDomainLink(e){if(e&&query.hasNodeAttribute(e,"href")){var t=query.getAttributeValueFromNode(e,"href");if(t&&!startsUrlWithTrackerUrl(t)&&trackerInstance.getVisitorId()){t=removeUrlParameter(t,configVisitorIdUrlParameter);var n=getCrossDomainVisitorId();t=addUrlParameter(t,configVisitorIdUrlParameter,n),query.setAnyAttribute(e,"href",t)}}}function isLinkToDifferentDomainButSameMatomoWebsite(e){var t=query.getAttributeValueFromNode(e,"href");if(!t)return!1;if(!(0===(t=String(t)).indexOf("//")||0===t.indexOf("http://")||0===t.indexOf("https://")))return!1;var n=e.pathname||getPathName(e.href),i=(e.hostname||getHostName(e.href)).toLowerCase();return!!isSiteHostPath(i,n)&&!isSameHost(domainAlias,domainFixup(i))}function processClick(e){var t=getLinkIfShouldBeProcessed(e);if(t&&t.type)return t.href=safeDecodeWrapper(t.href),void logLink(t.href,t.type,void 0,null,e);crossDomainTrackingEnabled&&isLinkToDifferentDomainButSameMatomoWebsite(e=getSourceElement(e))&&replaceHrefForCrossDomainLink(e)}function isIE8orOlder(){return documentAlias.all&&!documentAlias.addEventListener}function getKeyCodeFromEvent(e){var t=e.which,n=typeof e.button;return t||"undefined"===n||(isIE8orOlder()?1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2):0===e.button||"0"===e.button?t=1:1&e.button?t=2:2&e.button&&(t=3)),t}function getNameOfClickedButton(e){switch(getKeyCodeFromEvent(e)){case 1:return"left";case 2:return"middle";case 3:return"right"}}function getTargetElementFromEvent(e){return e.target||e.srcElement}function isClickNode(e){return"A"===e||"AREA"===e}function clickHandler(e){return function(t){var n=function(e){for(var t=getTargetElementFromEvent(e),n=t.nodeName,i=getClassesRegExp(configIgnoreClasses,"ignore");!isClickNode(n)&&t&&t.parentNode;)n=(t=t.parentNode).nodeName;if(t&&isClickNode(n)&&!i.test(t.className))return t}(t=t||windowAlias.event);if(n){var i=getNameOfClickedButton(t);if("click"===t.type){var r=!1;e&&"middle"===i&&(r=!0),n&&!r&&processClick(n)}else"mousedown"===t.type?"middle"===i&&n?(lastButton=i,lastTarget=n):lastButton=lastTarget=null:"mouseup"===t.type?(i===lastButton&&n===lastTarget&&processClick(n),lastButton=lastTarget=null):"contextmenu"===t.type&&processClick(n)}}}function addClickListener(e,t,n){"undefined"===typeof t&&(t=!0),addEventListener(e,"click",clickHandler(t),n),t&&(addEventListener(e,"mouseup",clickHandler(t),n),addEventListener(e,"mousedown",clickHandler(t),n),addEventListener(e,"contextmenu",clickHandler(t),n))}function enableTrackOnlyVisibleContent(e,t,n){if(isTrackOnlyVisibleContentEnabled)return!0;isTrackOnlyVisibleContentEnabled=!0;var i,r,o=!1;function a(){o=!0}trackCallbackOnLoad(function(){if(e){for(i=["scroll","resize"],r=0;r<i.length;r++)documentAlias.addEventListener?documentAlias.addEventListener(i[r],a,!1):windowAlias.attachEvent("on"+i[r],a);!function e(t){setTimeout(function(){isTrackOnlyVisibleContentEnabled&&(o&&(o=!1,n.trackVisibleContentImpressions()),e(t))},t)}(100)}t&&t>0&&function e(t){setTimeout(function(){isTrackOnlyVisibleContentEnabled&&(o=!1,n.trackVisibleContentImpressions(),e(t))},t)}(t=parseInt(t,10))})}function registerHook(hookName,userHook){var hookObj=null;if(isString(hookName)&&!isDefined(registeredHooks[hookName])&&userHook){if(isObject(userHook))hookObj=userHook;else if(isString(userHook))try{eval("hookObj ="+userHook)}catch(e){}registeredHooks[hookName]=hookObj}return hookObj}configHasConsent=!getCookie(CONSENT_REMOVED_COOKIE_NAME),heartBeatPingIfActivityAlias=function(){var e=new Date;return e=e.getTime(),!!lastTrackerRequestTime&&(lastTrackerRequestTime+configHeartBeatDelay<=e&&(trackerInstance.ping(),!0))};var requestQueue={enabled:!0,requests:[],timeout:null,interval:2500,sendRequests:function(){var e=this.requests;this.requests=[],1===e.length?sendRequest(e[0],configTrackerPause):sendBulkRequest(e,configTrackerPause)},canQueue:function(){return!isPageUnloading&&this.enabled},pushMultiple:function(e){var t;if(this.canQueue())for(t=0;t<e.length;t++)this.push(e[t]);else sendBulkRequest(e,configTrackerPause)},push:function(e){if(e)if(this.canQueue()){requestQueue.requests.push(e),this.timeout&&(clearTimeout(this.timeout),this.timeout=null),this.timeout=setTimeout(function(){requestQueue.timeout=null,requestQueue.sendRequests()},requestQueue.interval);var t="RequestQueue"+uniqueTrackerId;Object.prototype.hasOwnProperty.call(plugins,t)||(plugins[t]={unload:function(){requestQueue.timeout&&clearTimeout(requestQueue.timeout),requestQueue.sendRequests()}})}else sendRequest(e,configTrackerPause)}};updateDomainHash(),executePluginMethod("run",null,registerHook),this.hook=registeredHooks,this.getHook=function(e){return registeredHooks[e]},this.getQuery=function(){return query},this.getContent=function(){return content},this.isUsingAlwaysUseSendBeacon=function(){return configAlwaysUseSendBeacon},this.buildContentImpressionRequest=buildContentImpressionRequest,this.buildContentInteractionRequest=buildContentInteractionRequest,this.buildContentInteractionRequestNode=buildContentInteractionRequestNode,this.getContentImpressionsRequestsFromNodes=getContentImpressionsRequestsFromNodes,this.getCurrentlyVisibleContentImpressionsRequestsIfNotTrackedYet=getCurrentlyVisibleContentImpressionsRequestsIfNotTrackedYet,this.trackCallbackOnLoad=trackCallbackOnLoad,this.trackCallbackOnReady=trackCallbackOnReady,this.buildContentImpressionsRequests=buildContentImpressionsRequests,this.wasContentImpressionAlreadyTracked=wasContentImpressionAlreadyTracked,this.appendContentInteractionToRequestIfPossible=getContentInteractionToRequestIfPossible,this.setupInteractionsTracking=setupInteractionsTracking,this.trackContentImpressionClickInteraction=trackContentImpressionClickInteraction,this.internalIsNodeVisible=isVisible,this.isNodeAuthorizedToTriggerInteraction=isNodeAuthorizedToTriggerInteraction,this.getDomains=function(){return configHostsAlias},this.getExcludedReferrers=function(){return configExcludedReferrers},this.getConfigIdPageView=function(){return configIdPageView},this.getConfigDownloadExtensions=function(){return configDownloadExtensions},this.enableTrackOnlyVisibleContent=function(e,t){return enableTrackOnlyVisibleContent(e,t,this)},this.clearTrackedContentImpressions=function(){trackedContentImpressions=[]},this.getTrackedContentImpressions=function(){return trackedContentImpressions},this.clearEnableTrackOnlyVisibleContent=function(){isTrackOnlyVisibleContentEnabled=!1},this.disableLinkTracking=function(){linkTrackingInstalled=!1,linkTrackingEnabled=!1},this.getConfigVisitorCookieTimeout=function(){return configVisitorCookieTimeout},this.getConfigCookieSameSite=function(){return configCookieSameSite},this.getCustomPagePerformanceTiming=function(){return customPagePerformanceTiming},this.removeAllAsyncTrackersButFirst=function(){var e=asyncTrackers[0];asyncTrackers=[e]},this.getConsentRequestsQueue=function(){return consentRequestsQueue},this.getRequestQueue=function(){return requestQueue},this.getJavascriptErrors=function(){return javaScriptErrors},this.unsetPageIsUnloading=function(){isPageUnloading=!1},this.getRemainingVisitorCookieTimeout=getRemainingVisitorCookieTimeout,this.hasConsent=function(){return configHasConsent},this.getVisitorInfo=function(){return getCookie(getCookieName("id"))||setVisitorIdCookie(),loadVisitorIdCookie()},this.getVisitorId=function(){return this.getVisitorInfo()[1]},this.getAttributionInfo=function(){return loadReferrerAttributionCookie()},this.getAttributionCampaignName=function(){return loadReferrerAttributionCookie()[0]},this.getAttributionCampaignKeyword=function(){return loadReferrerAttributionCookie()[1]},this.getAttributionReferrerTimestamp=function(){return loadReferrerAttributionCookie()[2]},this.getAttributionReferrerUrl=function(){return loadReferrerAttributionCookie()[3]},this.setTrackerUrl=function(e){configTrackerUrl=e},this.getTrackerUrl=function(){return configTrackerUrl},this.getMatomoUrl=function(){return getMatomoUrlForOverlay(this.getTrackerUrl(),configApiUrl)},this.getPiwikUrl=function(){return this.getMatomoUrl()},this.addTracker=function(e,t){isDefined(e)&&null!==e||(e=this.getTrackerUrl());var n=new Tracker(e,t);return asyncTrackers.push(n),Matomo.trigger("TrackerAdded",[this]),n},this.getSiteId=function(){return configTrackerSiteId},this.setSiteId=function(e){setSiteId(e)},this.resetUserId=function(){configUserId=""},this.setUserId=function(e){isNumberOrHasLength(e)&&(configUserId=e)},this.setVisitorId=function(e){isString(e)&&/[0-9A-Fa-f]{16}/g.test(e)?visitorUUID=e:logConsoleError("Invalid visitorId set"+e)},this.getUserId=function(){return configUserId},this.setCustomData=function(e,t){isObject(e)?configCustomData=e:(configCustomData||(configCustomData={}),configCustomData[e]=t)},this.getCustomData=function(){return configCustomData},this.setCustomRequestProcessing=function(e){configCustomRequestContentProcessing=e},this.appendToTrackingUrl=function(e){configAppendToTrackingUrl=e},this.getRequest=function(e){return getRequest(e)},this.addPlugin=function(e,t){plugins[e]=t},this.setCustomDimension=function(e,t){(e=parseInt(e,10))>0&&(isDefined(t)||(t=""),isString(t)||(t=String(t)),customDimensions[e]=t)},this.getCustomDimension=function(e){if((e=parseInt(e,10))>0&&Object.prototype.hasOwnProperty.call(customDimensions,e))return customDimensions[e]},this.deleteCustomDimension=function(e){(e=parseInt(e,10))>0&&delete customDimensions[e]},this.setCustomVariable=function(e,t,n,i){var r;isDefined(i)||(i="visit"),isDefined(t)&&(isDefined(n)||(n=""),e>0&&(t=isString(t)?t:String(t),n=isString(n)?n:String(n),r=[t.slice(0,customVariableMaximumLength),n.slice(0,customVariableMaximumLength)],"visit"===i||2===i?(loadCustomVariables(),customVariables[e]=r):"page"===i||3===i?customVariablesPage[e]=r:"event"===i&&(customVariablesEvent[e]=r)))},this.getCustomVariable=function(e,t){var n;return isDefined(t)||(t="visit"),"page"===t||3===t?n=customVariablesPage[e]:"event"===t?n=customVariablesEvent[e]:"visit"!==t&&2!==t||(loadCustomVariables(),n=customVariables[e]),!(!isDefined(n)||n&&""===n[0])&&n},this.deleteCustomVariable=function(e,t){this.getCustomVariable(e,t)&&this.setCustomVariable(e,"","",t)},this.deleteCustomVariables=function(e){"page"===e||3===e?customVariablesPage={}:"event"===e?customVariablesEvent={}:"visit"!==e&&2!==e||(customVariables={})},this.storeCustomVariablesInCookie=function(){configStoreCustomVariablesInCookie=!0},this.setLinkTrackingTimer=function(e){configTrackerPause=e},this.getLinkTrackingTimer=function(){return configTrackerPause},this.setDownloadExtensions=function(e){isString(e)&&(e=e.split("|")),configDownloadExtensions=e},this.addDownloadExtensions=function(e){var t;for(isString(e)&&(e=e.split("|")),t=0;t<e.length;t++)configDownloadExtensions.push(e[t])},this.removeDownloadExtensions=function(e){var t,n=[];for(isString(e)&&(e=e.split("|")),t=0;t<configDownloadExtensions.length;t++)-1===indexOfArray(e,configDownloadExtensions[t])&&n.push(configDownloadExtensions[t]);configDownloadExtensions=n},this.setDomains=function(e){configHostsAlias=isString(e)?[e]:e;for(var t,n=!1,i=0;i<configHostsAlias.length;i++){if(t=String(configHostsAlias[i]),isSameHost(domainAlias,domainFixup(t))){n=!0;break}var r=getPathName(t);if(r&&"/"!==r&&"/*"!==r){n=!0;break}}n||configHostsAlias.push(domainAlias)},this.setExcludedReferrers=function(e){configExcludedReferrers=isString(e)?[e]:e},this.enableCrossDomainLinking=function(){crossDomainTrackingEnabled=!0},this.disableCrossDomainLinking=function(){crossDomainTrackingEnabled=!1},this.isCrossDomainLinkingEnabled=function(){return crossDomainTrackingEnabled},this.setCrossDomainLinkingTimeout=function(e){configVisitorIdUrlParameterTimeoutInSeconds=e},this.getCrossDomainLinkingUrlParameter=function(){return encodeWrapper(configVisitorIdUrlParameter)+"="+encodeWrapper(getCrossDomainVisitorId())},this.setIgnoreClasses=function(e){configIgnoreClasses=isString(e)?[e]:e},this.setRequestMethod=function(e){"GET"===(configRequestMethod=e?String(e).toUpperCase():defaultRequestMethod)&&this.disableAlwaysUseSendBeacon()},this.setRequestContentType=function(e){configRequestContentType=e||defaultRequestContentType},this.setGenerationTimeMs=function(e){logConsoleError("setGenerationTimeMs is no longer supported since Matomo 4. The call will be ignored. The replacement is setPagePerformanceTiming.")},this.setPagePerformanceTiming=function(e,t,n,i,r,o){var a={pf_net:e,pf_srv:t,pf_tfr:n,pf_dm1:i,pf_dm2:r,pf_onl:o};try{if(a=onlyPositiveIntegers(a=filterIn(a,isDefined)),""===(customPagePerformanceTiming=queryStringify(a)))return void logConsoleError("setPagePerformanceTiming() called without parameters. This function needs to be called with at least one performance parameter.");performanceTracked=!1,performanceAvailable=!0}catch(e){logConsoleError("setPagePerformanceTiming: "+e.toString())}},this.setReferrerUrl=function(e){configReferrerUrl=e},this.setCustomUrl=function(e){configCustomUrl=resolveRelativeReference(locationHrefAlias,e)},this.getCurrentUrl=function(){return configCustomUrl||locationHrefAlias},this.setDocumentTitle=function(e){configTitle=e},this.setPageViewId=function(e){configIdPageView=e,configIdPageViewSetManually=!0},this.setAPIUrl=function(e){configApiUrl=e},this.setDownloadClasses=function(e){configDownloadClasses=isString(e)?[e]:e},this.setLinkClasses=function(e){configLinkClasses=isString(e)?[e]:e},this.setCampaignNameKey=function(e){configCampaignNameParameters=isString(e)?[e]:e},this.setCampaignKeywordKey=function(e){configCampaignKeywordParameters=isString(e)?[e]:e},this.discardHashTag=function(e){configDiscardHashTag=e},this.setCookieNamePrefix=function(e){configCookieNamePrefix=e,customVariables&&(customVariables=getCustomVariablesFromCookie())},this.setCookieDomain=function(e){var t=domainFixup(e);configCookiesDisabled||isPossibleToSetCookieOnDomain(t)?(configCookieDomain=t,updateDomainHash()):logConsoleError("Can't write cookie on domain "+e)},this.setExcludedQueryParams=function(e){configExcludedQueryParams=isString(e)?[e]:e},this.getCookieDomain=function(){return configCookieDomain},this.hasCookies=function(){return"1"===hasCookies()},this.setSessionCookie=function(e,t,n){if(!e)throw new Error("Missing cookie name");isDefined(n)||(n=configSessionCookieTimeout),configCookiesToDelete.push(e),setCookie(getCookieName(e),t,n,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite)},this.getCookie=function(e){var t=getCookie(getCookieName(e));return 0===t?null:t},this.setCookiePath=function(e){configCookiePath=e,updateDomainHash()},this.getCookiePath=function(e){return configCookiePath},this.setVisitorCookieTimeout=function(e){configVisitorCookieTimeout=1e3*e},this.setSessionCookieTimeout=function(e){configSessionCookieTimeout=1e3*e},this.getSessionCookieTimeout=function(){return configSessionCookieTimeout},this.setReferralCookieTimeout=function(e){configReferralCookieTimeout=1e3*e},this.setConversionAttributionFirstReferrer=function(e){configConversionAttributionFirstReferrer=e},this.setSecureCookie=function(e){e&&"https:"!==location.protocol?logConsoleError("Error in setSecureCookie: You cannot use `Secure` on http."):configCookieIsSecure=e},this.setCookieSameSite=function(e){"None"===(e=(e=String(e)).charAt(0).toUpperCase()+e.toLowerCase().slice(1))||"Lax"===e||"Strict"===e?("None"===e&&("https:"===location.protocol?this.setSecureCookie(!0):(logConsoleError("sameSite=None cannot be used on http, reverted to sameSite=Lax."),e="Lax")),configCookieSameSite=e):logConsoleError("Ignored value for sameSite. Please use either Lax, None, or Strict.")},this.disableCookies=function(){configCookiesDisabled=!0,configTrackerSiteId&&deleteCookies()},this.areCookiesEnabled=function(){return!configCookiesDisabled},this.setCookieConsentGiven=function(){configCookiesDisabled&&!configDoNotTrack&&(configCookiesDisabled=!1,configBrowserFeatureDetection=!0,configTrackerSiteId&&hasSentTrackingRequestYet&&(setVisitorIdCookie(),sendRequest(getRequest("ping=1",null,"ping"),configTrackerPause)))},this.requireCookieConsent=function(){return!this.getRememberedCookieConsent()&&(this.disableCookies(),!0)},this.getRememberedCookieConsent=function(){return getCookie(COOKIE_CONSENT_COOKIE_NAME)},this.forgetCookieConsentGiven=function(){deleteCookie(COOKIE_CONSENT_COOKIE_NAME,configCookiePath,configCookieDomain),this.disableCookies()},this.rememberCookieConsentGiven=function(e){e=e?60*e*60*1e3:94608e7,this.setCookieConsentGiven();var t=(new Date).getTime();setCookie(COOKIE_CONSENT_COOKIE_NAME,t,e,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite)},this.deleteCookies=function(){deleteCookies()},this.setDoNotTrack=function(e){var t=navigatorAlias.doNotTrack||navigatorAlias.msDoNotTrack;(configDoNotTrack=e&&("yes"===t||"1"===t))&&this.disableCookies()},this.alwaysUseSendBeacon=function(){configAlwaysUseSendBeacon=!0},this.disableAlwaysUseSendBeacon=function(){configAlwaysUseSendBeacon=!1},this.addListener=function(e,t){addClickListener(e,t,!1)},this.enableLinkTracking=function(e){if(!linkTrackingEnabled){linkTrackingEnabled=!0;trackCallbackOnReady(function(){linkTrackingInstalled=!0,addClickListener(documentAlias.body,e,!0)})}},this.enableJSErrorTracking=function(){if(!enableJSErrorTracking){enableJSErrorTracking=!0;var e=windowAlias.onerror;windowAlias.onerror=function(t,n,i,r,o){return trackCallback(function(){var e="JavaScript Errors",o=n+":"+i;r&&(o+=":"+r),-1===indexOfArray(javaScriptErrors,e+o+t)&&(javaScriptErrors.push(e+o+t),logEvent(e,o,t))}),!!e&&e(t,n,i,r,o)}}},this.disablePerformanceTracking=function(){configPerformanceTrackingEnabled=!1},this.enableHeartBeatTimer=function(e){e=Math.max(e||15,5),configHeartBeatDelay=1e3*e,null!==lastTrackerRequestTime&&setUpHeartBeat()},this.disableHeartBeatTimer=function(){(configHeartBeatDelay||heartBeatSetUp)&&(windowAlias.removeEventListener?(windowAlias.removeEventListener("focus",heartBeatOnFocus),windowAlias.removeEventListener("blur",heartBeatOnBlur),windowAlias.removeEventListener("visibilitychange",heartBeatOnVisible)):windowAlias.detachEvent&&(windowAlias.detachEvent("onfocus",heartBeatOnFocus),windowAlias.detachEvent("onblur",heartBeatOnBlur),windowAlias.detachEvent("visibilitychange",heartBeatOnVisible))),configHeartBeatDelay=null,heartBeatSetUp=!1},this.killFrame=function(){windowAlias.location!==windowAlias.top.location&&(windowAlias.top.location=windowAlias.location)},this.redirectFile=function(e){"file:"===windowAlias.location.protocol&&(windowAlias.location=e)},this.setCountPreRendered=function(e){configCountPreRendered=e},this.trackGoal=function(e,t,n,i){trackCallback(function(){logGoal(e,t,n,i)})},this.trackLink=function(e,t,n,i){trackCallback(function(){logLink(e,t,n,i)})},this.getNumTrackedPageViews=function(){return numTrackedPageviews},this.trackPageView=function(e,t,n){trackedContentImpressions=[],consentRequestsQueue=[],javaScriptErrors=[],isOverlaySession(configTrackerSiteId)?trackCallback(function(){injectOverlayScripts(configTrackerUrl,configApiUrl,configTrackerSiteId)}):trackCallback(function(){numTrackedPageviews++,logPageView(e,t,n)})},this.disableBrowserFeatureDetection=function(){configBrowserFeatureDetection=!1},this.enableBrowserFeatureDetection=function(){configBrowserFeatureDetection=!0},this.trackAllContentImpressions=function(){isOverlaySession(configTrackerSiteId)||trackCallback(function(){trackCallbackOnReady(function(){var e=getContentImpressionsRequestsFromNodes(content.findContentNodes());requestQueue.pushMultiple(e)})})},this.trackVisibleContentImpressions=function(e,t){isOverlaySession(configTrackerSiteId)||(isDefined(e)||(e=!0),isDefined(t)||(t=750),enableTrackOnlyVisibleContent(e,t,this),trackCallback(function(){trackCallbackOnLoad(function(){var e=getCurrentlyVisibleContentImpressionsRequestsIfNotTrackedYet(content.findContentNodes());requestQueue.pushMultiple(e)})}))},this.trackContentImpression=function(e,t,n){isOverlaySession(configTrackerSiteId)||(e=trim(e),t=trim(t),n=trim(n),e&&(t=t||"Unknown",trackCallback(function(){var i=buildContentImpressionRequest(e,t,n);requestQueue.push(i)})))},this.trackContentImpressionsWithinNode=function(e){!isOverlaySession(configTrackerSiteId)&&e&&trackCallback(function(){isTrackOnlyVisibleContentEnabled?trackCallbackOnLoad(function(){var t=getCurrentlyVisibleContentImpressionsRequestsIfNotTrackedYet(content.findContentNodesWithinNode(e));requestQueue.pushMultiple(t)}):trackCallbackOnReady(function(){var t=getContentImpressionsRequestsFromNodes(content.findContentNodesWithinNode(e));requestQueue.pushMultiple(t)})})},this.trackContentInteraction=function(e,t,n,i){isOverlaySession(configTrackerSiteId)||(e=trim(e),t=trim(t),n=trim(n),i=trim(i),e&&t&&(n=n||"Unknown",trackCallback(function(){var r=buildContentInteractionRequest(e,t,n,i);r&&requestQueue.push(r)})))},this.trackContentInteractionNode=function(e,t){if(!isOverlaySession(configTrackerSiteId)&&e){var n=null;return trackCallback(function(){(n=buildContentInteractionRequestNode(e,t))&&requestQueue.push(n)}),n}},this.logAllContentBlocksOnPage=function(){var e=content.findContentNodes(),t=content.collectContent(e);"undefined"!==typeof console&&console&&console.log&&console.log(t)},this.trackEvent=function(e,t,n,i,r,o){trackCallback(function(){logEvent(e,t,n,i,r,o)})},this.trackSiteSearch=function(e,t,n,i){trackedContentImpressions=[],trackCallback(function(){logSiteSearch(e,t,n,i)})},this.setEcommerceView=function(e,t,n,i){ecommerceProductView={},isNumberOrHasLength(n)&&(n=String(n)),isDefined(n)&&null!==n&&!1!==n&&n.length?n instanceof Array&&(n=windowAlias.JSON.stringify(n)):n="";var r="_pkc";ecommerceProductView[r]=n,isDefined(i)&&null!==i&&!1!==i&&String(i).length&&(ecommerceProductView[r="_pkp"]=i),(isNumberOrHasLength(e)||isNumberOrHasLength(t))&&(isNumberOrHasLength(e)&&(ecommerceProductView[r="_pks"]=e),isNumberOrHasLength(t)||(t=""),ecommerceProductView[r="_pkn"]=t)},this.getEcommerceItems=function(){return JSON.parse(JSON.stringify(ecommerceItems))},this.addEcommerceItem=function(e,t,n,i,r){isNumberOrHasLength(e)&&(ecommerceItems[e]=[String(e),t,n,i,r])},this.removeEcommerceItem=function(e){isNumberOrHasLength(e)&&(e=String(e),delete ecommerceItems[e])},this.clearEcommerceCart=function(){ecommerceItems={}},this.trackEcommerceOrder=function(e,t,n,i,r,o){logEcommerceOrder(e,t,n,i,r,o)},this.trackEcommerceCartUpdate=function(e){logEcommerceCartUpdate(e)},this.trackRequest=function(e,t,n,i){trackCallback(function(){sendRequest(getRequest(e,t,i),configTrackerPause,n)})},this.ping=function(){this.trackRequest("ping=1",null,null,"ping")},this.disableQueueRequest=function(){requestQueue.enabled=!1},this.setRequestQueueInterval=function(e){if(e<1e3)throw new Error("Request queue interval needs to be at least 1000ms");requestQueue.interval=e},this.queueRequest=function(e){trackCallback(function(){var t=getRequest(e);requestQueue.push(t)})},this.isConsentRequired=function(){return configConsentRequired},this.getRememberedConsent=function(){var e=getCookie(CONSENT_COOKIE_NAME);return getCookie(CONSENT_REMOVED_COOKIE_NAME)?(e&&deleteCookie(CONSENT_COOKIE_NAME,configCookiePath,configCookieDomain),null):e&&0!==e?e:null},this.hasRememberedConsent=function(){return!!this.getRememberedConsent()},this.requireConsent=function(){configConsentRequired=!0,(configHasConsent=this.hasRememberedConsent())||(configCookiesDisabled=!0),plugins["CoreConsent"+ ++coreConsentCounter]={unload:function(){configHasConsent||deleteCookies()}}},this.setConsentGiven=function(e){var t,n;for(configHasConsent=!0,configBrowserFeatureDetection=!0,deleteCookie(CONSENT_REMOVED_COOKIE_NAME,configCookiePath,configCookieDomain),t=0;t<consentRequestsQueue.length;t++)"string"===(n=typeof consentRequestsQueue[t])?sendRequest(consentRequestsQueue[t],configTrackerPause):"object"===n&&sendBulkRequest(consentRequestsQueue[t],configTrackerPause);consentRequestsQueue=[],isDefined(e)&&!e||this.setCookieConsentGiven()},this.rememberConsentGiven=function(e){e=e?60*e*60*1e3:94608e7;this.setConsentGiven(!0);var t=(new Date).getTime();setCookie(CONSENT_COOKIE_NAME,t,e,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite)},this.forgetConsentGiven=function(e){e=e?60*e*60*1e3:94608e7,deleteCookie(CONSENT_COOKIE_NAME,configCookiePath,configCookieDomain),setCookie(CONSENT_REMOVED_COOKIE_NAME,(new Date).getTime(),e,configCookiePath,configCookieDomain,configCookieIsSecure,configCookieSameSite),this.forgetCookieConsentGiven(),this.requireConsent()},this.isUserOptedOut=function(){return!configHasConsent},this.optUserOut=this.forgetConsentGiven,this.forgetUserOptOut=function(){this.setConsentGiven(!1)},trackCallbackOnLoad(function(){setTimeout(function(){performanceAvailable=!0},0)}),Matomo.trigger("TrackerSetup",[this]),Matomo.addPlugin("TrackerVisitorIdCookie"+uniqueTrackerId,{unload:function(){hasSentTrackingRequestYet||(setVisitorIdCookie(),detectReferrerAttribution())}})}function TrackerProxy(){return{push:apply}}function applyMethodsInOrder(e,t){var n,i,r={};for(n=0;n<t.length;n++){var o=t[n];for(r[o]=1,i=0;i<e.length;i++)if(e[i]&&e[i][0]){var a=e[i][0];o===a&&(apply(e[i]),delete e[i],r[a]>1&&"addTracker"!==a&&"enableLinkTracking"!==a&&logConsoleError("The method "+a+' is registered more than once in "_paq" variable. Only the last call has an effect. Please have a look at the multiple Matomo trackers documentation: https://developer.matomo.org/guides/tracking-javascript-guide#multiple-piwik-trackers'),r[a]++)}}return e}var applyFirst=["addTracker","forgetCookieConsentGiven","requireCookieConsent","disableBrowserFeatureDetection","disableCookies","setTrackerUrl","setAPIUrl","enableCrossDomainLinking","setCrossDomainLinkingTimeout","setSessionCookieTimeout","setVisitorCookieTimeout","setCookieNamePrefix","setCookieSameSite","setSecureCookie","setCookiePath","setCookieDomain","setDomains","setUserId","setVisitorId","setSiteId","alwaysUseSendBeacon","disableAlwaysUseSendBeacon","enableLinkTracking","setCookieConsentGiven","requireConsent","setConsentGiven","disablePerformanceTracking","setPagePerformanceTiming","setExcludedQueryParams","setExcludedReferrers"];function createFirstTracker(e,t){var n=new Tracker(e,t);for(asyncTrackers.push(n),_paq=applyMethodsInOrder(_paq,applyFirst),iterator=0;iterator<_paq.length;iterator++)_paq[iterator]&&apply(_paq[iterator]);return _paq=new TrackerProxy,Matomo.trigger("TrackerAdded",[n]),n}return addEventListener(windowAlias,"beforeunload",beforeUnloadHandler,!1),addEventListener(windowAlias,"visibilitychange",function(){isPageUnloading||"hidden"===documentAlias.visibilityState&&executePluginMethod("unload")},!1),addEventListener(windowAlias,"online",function(){isDefined(navigatorAlias.serviceWorker)&&navigatorAlias.serviceWorker.ready.then(function(e){if(e&&e.sync)return e.sync.register("matomoSync")},function(){})},!1),addEventListener(windowAlias,"message",function(e){if(e&&e.origin){var t,n,i=getHostName(e.origin),r=Matomo.getAsyncTrackers();for(n=0;n<r.length;n++)if(getHostName(r[n].getMatomoUrl())===i){t=r[n];break}if(t){var o=null;try{o=JSON.parse(e.data)}catch(e){return}if(o)if(isDefined(o.maq_initial_value))a({maq_opted_in:o.maq_initial_value&&t.hasConsent(),maq_url:t.getMatomoUrl(),maq_optout_by_default:t.isConsentRequired()});else if(isDefined(o.maq_opted_in)){for(r=Matomo.getAsyncTrackers(),n=0;n<r.length;n++)t=r[n],o.maq_opted_in?t.rememberConsentGiven():t.forgetConsentGiven();a({maq_confirm_opted_in:t.hasConsent(),maq_url:t.getMatomoUrl(),maq_optout_by_default:t.isConsentRequired()})}}}function a(t){var r=documentAlias.getElementsByTagName("iframe");for(n=0;n<r.length;n++){var o=r[n],a=getHostName(o.src);if(o.contentWindow&&isDefined(o.contentWindow.postMessage)&&a===i){var s=JSON.stringify(t);o.contentWindow.postMessage(s,e.origin)}}}},!1),Date.prototype.getTimeAlias=Date.prototype.getTime,Matomo={initialized:!1,JSON:windowAlias.JSON,DOM:{addEventListener:function(e,t,n,i){"undefined"===typeof i&&(i=!1),addEventListener(e,t,n,i)},onLoad:trackCallbackOnLoad,onReady:trackCallbackOnReady,isNodeVisible:isVisible,isOrWasNodeVisible:content.isNodeVisible},on:function(e,t){eventHandlers[e]||(eventHandlers[e]=[]),eventHandlers[e].push(t)},off:function(e,t){if(eventHandlers[e])for(var n=0;n<eventHandlers[e].length;n++)eventHandlers[e][n]===t&&eventHandlers[e].splice(n,1)},trigger:function(e,t,n){if(eventHandlers[e])for(var i=0;i<eventHandlers[e].length;i++)eventHandlers[e][i].apply(n||windowAlias,t)},addPlugin:function(e,t){plugins[e]=t},getTracker:function(e,t){return isDefined(t)||(t=this.getAsyncTracker().getSiteId()),isDefined(e)||(e=this.getAsyncTracker().getTrackerUrl()),new Tracker(e,t)},getAsyncTrackers:function(){return asyncTrackers},addTracker:function(e,t){return asyncTrackers.length?asyncTrackers[0].addTracker(e,t):createFirstTracker(e,t)},getAsyncTracker:function(e,t){var n;if(!(asyncTrackers&&asyncTrackers.length&&asyncTrackers[0]))return createFirstTracker(e,t);if(n=asyncTrackers[0],!t&&!e)return n;isDefined(t)&&null!==t||!n||(t=n.getSiteId()),isDefined(e)&&null!==e||!n||(e=n.getTrackerUrl());for(var i,r=0;r<asyncTrackers.length;r++)if((i=asyncTrackers[r])&&String(i.getSiteId())===String(t)&&i.getTrackerUrl()===e)return i},retryMissedPluginCalls:function(){var e=missedPluginTrackerCalls;missedPluginTrackerCalls=[];for(var t=0;t<e.length;t++)apply(e[t])}},"function"==typeof define&&define.amd&&(define("piwik",[],function(){return Matomo}),define("matomo",[],function(){return Matomo})),Matomo}())
/*!! pluginTrackerHook */,
/*!! pluginTrackerHook */
function(){"use strict";if(window&&"object"==typeof window.matomoPluginAsyncInit&&window.matomoPluginAsyncInit.length)for(var e=0;e<window.matomoPluginAsyncInit.length;e++)"function"==typeof window.matomoPluginAsyncInit[e]&&window.matomoPluginAsyncInit[e]();window&&window.piwikAsyncInit&&window.piwikAsyncInit(),window&&window.matomoAsyncInit&&window.matomoAsyncInit(),window.Matomo.getAsyncTrackers().length||("object"==typeof _paq&&void 0!==_paq.length&&_paq.length?window.Matomo.addTracker():_paq={push:function(e){"undefined"!==typeof console&&console&&console.error&&console.error("_paq.push() was used but Matomo tracker was not initialized before the matomo.js file was loaded. Make sure to configure the tracker via _paq.push before loading matomo.js. Alternatively, you can create a tracker via Matomo.addTracker() manually and then use _paq.push but it may not fully work as tracker methods may not be executed in the correct order.",e)}}),window.Matomo.trigger("MatomoInitialized",[]),window.Matomo.initialized=!0}(),void 0===window.AnalyticsTracker&&(window.AnalyticsTracker=window.Matomo),"function"!=typeof window.piwik_log&&(window.piwik_log=function(e,t,n,i){"use strict";function r(e){try{if(window["piwik_"+e])return window["piwik_"+e]}catch(e){}}var o,a=window.Matomo.getTracker(n,t);a.setDocumentTitle(e),a.setCustomData(i),(o=r("tracker_pause"))&&a.setLinkTrackingTimer(o),(o=r("download_extensions"))&&a.setDownloadExtensions(o),(o=r("hosts_alias"))&&a.setDomains(o),(o=r("ignore_classes"))&&a.setIgnoreClasses(o),a.trackPageView(),r("install_tracker")&&(piwik_track=function(e,t,n,i){a.setSiteId(t),a.setTrackerUrl(n),a.trackLink(e,i)},a.enableLinkTracking())})
/*! @license-end */;